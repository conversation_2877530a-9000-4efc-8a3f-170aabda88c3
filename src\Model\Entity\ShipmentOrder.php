<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * ShipmentOrder Entity
 *
 * @property int $id
 * @property int $shipment_id
 * @property int $order_id
 * @property int|null $driver_id
 * @property int $customer_id
 * @property int $customer_address_id
 * @property int $city_id
 * @property int|null $zone_id
 * @property int|null $municipality_id
 * @property \Cake\I18n\Date|null $expected_delivery_date
 * @property \Cake\I18n\Date|null $actual_delivery_date
 * @property int|null $delivery_attempts
 * @property bool|null $is_expedited
 * @property string|null $shipping_cost
 * @property string|null $order_delivery_status
 * @property \Cake\I18n\DateTime $delivery_status_date
 * @property string|null $cash_collected
 * @property string|null $proof_of_delivery
 * @property string|null $driver_comments
 * @property string|null $special_instructions
 *
 * @property \App\Model\Entity\Shipment $shipment
 * @property \App\Model\Entity\Order $order
 * @property \App\Model\Entity\Driver $driver
 * @property \App\Model\Entity\Customer $customer
 * @property \App\Model\Entity\CustomerAddress $customer_address
 * @property \App\Model\Entity\City $city
 * @property \App\Model\Entity\Zone $zone
 * @property \App\Model\Entity\Municipality $municipality
 * @property \App\Model\Entity\ShipmentOrderItem[] $shipment_order_items
 */
class ShipmentOrder extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'shipment_id' => true,
        'order_id' => true,
        'driver_id' => true,
        'customer_id' => true,
        'customer_address_id' => true,
        'city_id' => true,
        'zone_id' => true,
        'municipality_id' => true,
        'expected_delivery_date' => true,
        'actual_delivery_date' => true,
        'delivery_attempts' => true,
        'is_expedited' => true,
        'shipping_cost' => true,
        'order_delivery_status' => true,
        'delivery_status_date' => true,
        'cash_collected' => true,
        'cash_collected_amount' => true,
        'proof_of_delivery' => true,
        'driver_comments' => true,
        'special_instructions' => true,
        'shipment' => true,
        'order' => true,
        'driver' => true,
        'customer' => true,
        'customer_address' => true,
        'city' => true,
        'zone' => true,
        'municipality' => true,
        'shipment_order_items' => true,
    ];
}
