<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Brand $brand
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style type="text/css">
    .is-invalid {
        border: 1px solid red !important;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    select.is-invalid {
        border-color: #dc3545 !important;
    }

    .error {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 4px;
    }

    .bootstrap-tagsinput {
        width: 100%;
    }
    .content {
        color: black;
    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Reviews', 'action' => 'index']) ?>"><?= __("Reviews") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Edit") ?></li>
    </ul>
    <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
        <small class="p-10 fw-bold"><?= __('BACK') ?></small>
        <span class="rotate me-2">⤣</span>
    </button>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20" style="color: #004958"><?= __("Edit Review") ?></h6>
            <?php echo $this->Form->create($review, ['id' => 'edit', 'novalidate' => true, 'type' => 'file']); ?>
            <?php if (!empty($review->getErrors())): ?>
                <div class="validation-errors">
                    <?php foreach ($review->getErrors() as $field => $errors): ?>
                        <div class="field-errors">
                            <strong><?php echo h(ucwords($field)); ?>:</strong>
                            <ul>
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo h($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="form-group row">
                <label for="content_category" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Customer") ?></label>
                <div class="col-sm-5 main-field">
                    <p class="ps-5" style="color: black;"><?php echo !empty($review->customer->user->full_name) ? h($review->customer->user->full_name) : '-'; ?></p>
                </div>
            </div>

            <div class="form-group row">
                <label style="width: 160px;" for="content_category_identifier" class="col-sm-2 col-form-label fw-bold"><?= __("Product") ?></label>
                <div class="col-sm-5 main-field">
                    <p class="ps-5" style="color: black;"><?php echo !empty($review->product->name) ? h($review->product->name) : '-'; ?></p>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label style="width: 160px;" for="title" class="col-sm-2 col-form-label fw-bold"><?= __("Rating") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('rating', [
                        'type' => 'number',
                        'class' => 'form-control',
                        'id' => 'rating',
                        'min' => 1,
                        'max' => 5,
                        'placeholder' => __('Enter rating (1-5)'),
                        'label' => false,
                        'onpaste' => 'return false;',  // disables paste
                        'oncopy'  => 'return false;',  // disables copy
                        'oncut'   => 'return false;'   // disables cut
                    ]);
                    ?>
                </div>
            </div> -->

            <div class="form-group row">
                <label style="width: 160px;" for="product-size" class="col-sm-2 col-form-label fw-bold"><?= __('Rating') ?></label>
                <div class="col-sm-5 main-field">
                    <p class="ps-5" style="color: black;"><?php echo !empty($review->rating) ? h($review->rating) : '-'; ?></p>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label style="width: 160px;" for="content" class="col-sm-2 col-form-label fw-bold">< ?= __("Comment") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-10 main-field">
                    < ?php echo $this->Form->control('comment', ['class' => 'form-control', 'id' => 'ckeditor', 'label' => false]); ?>
                </div>
            </div> -->

            <div class="form-group row">
                <label style="width: 160px;" for="product-weight" class="col-sm-2 col-form-label fw-bold"><?= __('Comment') ?></label>
                <div class="col-sm-5 main-field">
                    <p class="ps-5" style="color: black;"><?php echo !empty($review->comment) ? $this->Html->div('content ps-5', $review->comment, ['escape' => false]) : '-'; ?></p>
                </div>
            </div>

            <div class="form-group row">
                <label style="width: 160px;" for="product-title" class="col-sm-2 col-form-label fw-bold"><?= __('Image(s)') ?> </label>
                <div class="col-sm-5 ps-5">
                    <p class="ps-5">
                        <div id="previeContainer" class="ps-5">
                            <ul id="imagePreviewContainer">
                            </ul>
                        </div>
                    </p>
                </div>
            </div>

            <!-- <div class="form-group row">
                <label style="width: 160px;" for="review_image" class="col-sm-2 col-form-label fw-bold">< ?= __("Image(s)") ?></label>
                <div class="col-sm-5 main-field">
                    < ?php
                    echo $this->Form->control('review_image[]', ['type' => 'file', 'class' => 'form-control', 'label' => false, 'accept' => 'image/*' ,'multiple' => 'multiple', 'id' => 'imageInput']);

                    echo $this->Form->hidden('deleted_images', [
                        'id' => 'deletedImagesInput',
                        'value' => ''
                    ]); ?>
                    <div id="previeContainer">
                        <ul id="imagePreviewContainer">
                        </ul>
                    </div>
                </div>
            </div> -->

            <div class="form-group row">
                <label style="width: 160px;" for="status" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('status', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'id' => 'status',
                        'options' => [
                            'A' => 'Active',
                            'I' => 'Inactive'
                        ],
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" class="btn"><?= __("Save") ?></button>
                </div>
            </div>
        </div>
        </form>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    <?php
    $images = !empty($review->review_images) ? array_map(function ($image) {
        return [
            'id' => $image->id,
            'url' => $image->image_url
        ];
    }, $review->review_images) : [];
    ?>

    const existingImages = <?php echo json_encode($images, JSON_HEX_TAG); ?>;

    let allFiles = [];
    let deletedImages = [];

    function initializeExistingImages(existingImages) {
        existingImages.forEach((image, index) => {
            let file = {
                id: image.id,
                name: `existing-image-${index}.jpg`,
                type: 'image/jpeg',
                url: image.url
            };
            allFiles.push(file);
        });
        renderPreviews();
    }

    initializeExistingImages(existingImages);

    document.getElementById('imageInput').addEventListener('change', function (event) {

        var files = Array.from(event.target.files);
        
        if (files.length == 0) {
            allFiles = [];
            renderPreviews();
            return false;
        }

        var validFiles = [];
        var invalidFiles = [];

        let processedFiles = 0;
        let totalFiles = files.length;

        files.forEach(function(file) {

            var fileExtension = file.name.split('.').pop().toLowerCase();
            var allowedExtensions = ['jpg', 'jpeg', 'png'];

            // ✅ Only check file extension now
            if (allowedExtensions.includes(fileExtension)) {
                validFiles.push(file);
            } else {
                invalidFiles.push({
                    file: file.name, 
                    reason: '<?= __('Invalid file type. Only image/jpg,image/jpeg,image/png,image/svg are allowed.') ?>'
                });
            }

            processedFiles++;

            if (processedFiles === totalFiles) {
                finalizeFileProcessing(validFiles, invalidFiles);
            }
        });

    });

    // Finalize file processing
    function finalizeFileProcessing(validFiles, invalidFiles) {
        var html = "<ul>";
        for (var i = 0; i < invalidFiles.length; i++) {
            html += `<li>${invalidFiles[i].file} - ${invalidFiles[i].reason}</li>`;
        }
        html += "</ul>";

        if (invalidFiles.length > 0) {
            const wrapper = document.createElement("div");
            wrapper.innerHTML = html;
            swal({
                title: "<?= __("Error") ?>",
                content: wrapper,
                icon: "error",
                confirmButtonText: "<?= __("OK") ?>",
                allowOutsideClick: true
            });
        }

        // Add new files to allFiles
        allFiles = [...allFiles, ...validFiles];

        // ✅ Rebuild file input with ALL non-URL (newly selected) files
        let dataTransfer = new DataTransfer();
        allFiles.forEach(file => {
            if (!file.url) { // skip existing images from DB
                dataTransfer.items.add(file);
            }
        });
        document.getElementById("imageInput").files = dataTransfer.files;

        // Render previews
        renderPreviews();
    }


    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';

        allFiles.forEach((file, index) => {
            let li = document.createElement('li');
            li.classList.add('image-thumbnail');

            let fileName = file.name;
            let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
            let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);

            let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
            shortName += '.' + extension;

            // if (file.url) {
            //     li.innerHTML = `
            //     <img src="${file.url}" alt="Image Preview" class="preview-img"/>
            //     <span class="image-name" title="${fileName}">${shortName}</span>
            //     <button type="button" class="delete-img-btn" data-index="${index}">
            //         <i class="fas fa-times"></i>
            //     </button>
            // `;
            // } else {
            //     let reader = new FileReader();
            //     reader.onload = function (e) {
            //         li.innerHTML = `
            //         <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
            //         <span class="image-name" title="${fileName}">${shortName}</span>
            //         <button type="button" class="delete-img-btn" data-index="${index}">
            //             <i class="fas fa-times"></i>
            //         </button>
            //     `;
            //     };
            //     reader.readAsDataURL(file);
            // }

            if (file.url) {
                li.innerHTML = `<img src="${file.url}" alt="Image Preview" class="preview-img"/>`;
            } else {
                let reader = new FileReader();
                reader.onload = function (e) {
                    li.innerHTML = `
                    <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
                    <span class="image-name" title="${fileName}">${shortName}</span>
                    <button type="button" class="delete-img-btn" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                };
                reader.readAsDataURL(file);
            }

            previewContainer.appendChild(li);
        });
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function (e) {
        if (e.target.closest('.delete-img-btn')) {
            let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
            let removedFile = allFiles.splice(index, 1)[0];
            if (removedFile.url && removedFile.id) {
                deletedImages.push(removedFile.id);
            }

            renderPreviews();
            updateFileInput();
            updateDeletedImagesInput();
        }
    });
    function updateDeletedImagesInput() {
        document.getElementById('deletedImagesInput').value = JSON.stringify(deletedImages);
    }

    function updateFileInput() {
        let dataTransfer = new DataTransfer();
        allFiles.forEach(file => {
            if (!file.url) {
                dataTransfer.items.add(file);
            }
        });
        document.getElementById('imageInput').files = dataTransfer.files;
    }

    $(function () {
        CKEDITOR.replace("ckeditor");
        CKEDITOR.config.height = 300;

        for (instance in CKEDITOR.instances) {
            CKEDITOR.instances[instance].on('blur', function () {
                CKEDITOR.instances[this.name].updateElement();
            });
        }
    });

    $(document).ready(function () {

        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });
    });

    var validationMessages = {
        ratingRequired: "<?= __('Please enter rating') ?>",
        commentRequired: "<?= __('Please enter comment') ?>",
        patternError: "<?= __('Only lowercase letters and underscores are allowed') ?>"
    };

    $(document).ready(function () {
        $.validator.addMethod("pattern", function (value, element) {
            return this.optional(element) || /^[a-z-]+$/.test(value);
        }, validationMessages.patternError);

        $("#edit").validate({

            ignore: "",
            rules: {
                'rating': {
                    required: true
                },
                'comment': {
                    required: true
                }
            },
            messages: {
                'rating': {
                    required: validationMessages.ratingRequired
                },
                'comment': {
                    required: validationMessages.commentRequired
                }
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');

                // If it's a select2 or select box, also add to the container
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').addClass('is-invalid');
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');

                // Remove from select2 or select container too
                if ($(element).is('select')) {
                    $(element).closest('.main-field').find('.select2-selection').removeClass('is-invalid');
                }
            },
            submitHandler: function (form) {

                $('button[type="submit"]').attr('disabled', 'disabled');

                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].updateElement();
                }
                
                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            }
        });
    });
</script>
<?php $this->end(); ?>