<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * MerchantDocuments Model
 *
 * @method \App\Model\Entity\MerchantDocument newEmptyEntity()
 * @method \App\Model\Entity\MerchantDocument newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\MerchantDocument> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MerchantDocument get($primaryKey, $options = [])
 * @method \App\Model\Entity\MerchantDocument findOrCreate($search, callable $callback = null, $options = [])
 * @method \App\Model\Entity\MerchantDocument patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\MerchantDocument> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\MerchantDocument|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MerchantDocument saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method array<\App\Model\Entity\MerchantDocument>|
 *   \Cake\Datasource\ResultSetInterface<\App\Model\Entity\MerchantDocument>|false saveMany(iterable $entities, $options = [])
 * @method array<\App\Model\Entity\MerchantDocument>|
 *   \Cake\Datasource\ResultSetInterface<\App\Model\Entity\MerchantDocument> saveManyOrFail(iterable $entities, $options = [])
 * @method array<\App\Model\Entity\MerchantDocument>|
 *   \Cake\Datasource\ResultSetInterface<\App\Model\Entity\MerchantDocument>|false deleteMany(iterable $entities, $options = [])
 * @method array<\App\Model\Entity\MerchantDocument>|
 *   \Cake\Datasource\ResultSetInterface<\App\Model\Entity\MerchantDocument> deleteManyOrFail(iterable $entities, $options = [])
 */
class MerchantDocumentsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('merchant_documents');
        $this->setDisplayField('document_type');
        $this->setPrimaryKey('id');
        $this->addBehavior('Timestamp');
        $this->belongsTo('Merchants', [
            'foreignKey' => 'merchant_id',
            'joinType' => 'INNER',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('merchant_id')
            ->requirePresence('merchant_id', 'create')
            ->notEmptyString('merchant_id');

        $validator
            ->scalar('uploaded_file')
            ->maxLength('uploaded_file', 255)
            ->requirePresence('uploaded_file', 'create')
            ->notEmptyString('uploaded_file');

        $validator
            ->scalar('document_type')
            ->inList('document_type', ['cerificate', 'licence', 'others'])
            ->requirePresence('document_type', 'create')
            ->notEmptyString('document_type');

        $validator
            ->scalar('status')
            ->inList('status', ['A', 'I', 'D'])
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Save document for a merchant
     *
     * @param int $merchantId
     * @param string $documentType
     * @param object $file
     * @param string|null $notes
     * @return bool|string
     */
    public function saveDocument($merchantId, $documentType, $file, $notes = null)
    {
        try {
            if (!$file || $file->getError() !== UPLOAD_ERR_OK) {
                return 'No file uploaded or file upload error occurred.';
            }

            $fileName = trim($file->getClientFilename());
            $fileSize = $file->getSize();
            $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            
            // Validation
            $allowedFormats = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];
            $maxFileSize = 10 * 1024 * 1024; // 10MB
            
            if (!in_array($fileExt, $allowedFormats)) {
                return 'Invalid file type. Allowed formats: ' . implode(', ', $allowedFormats) . '. You uploaded: ' . $fileExt;
            }
            
            if ($fileSize > $maxFileSize) {
                return 'File size exceeds the maximum allowed size (10MB). Your file size: ' . round($fileSize / (1024 * 1024), 2) . 'MB';
            }
            
            // Additional MIME type validation for security
            $allowedMimeTypes = [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
                'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ];
            
            $fileMimeType = $file->getClientMediaType();
            if (!in_array($fileMimeType, $allowedMimeTypes)) {
                return 'Invalid file MIME type. Allowed types: ' . implode(', ', $allowedMimeTypes) . '. You uploaded: ' . $fileMimeType;
            }

            // File upload handling
            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
            $uploadFolder = 'uploads/merchant_documents/';
            $targetdir = WWW_ROOT . $uploadFolder;
            
            // Create directory if it doesn't exist
            if (!is_dir($targetdir)) {
                mkdir($targetdir, 0755, true);
            }
            
            $targetfile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $fileExt;
            $fileTmpName = $file->getStream()->getMetadata('uri');
            
            // Simple file upload without Media component
            if (!move_uploaded_file($fileTmpName, $targetdir . $targetfile)) {
                return 'Failed to upload file.';
            }

            // Create database record
            $document = $this->newEmptyEntity();
            $documentData = [
                'merchant_id' => $merchantId,
                'document_type' => $documentType,
                'uploaded_file' => $uploadFolder . $targetfile,
                'status' => 'A'
            ];
            
            $document = $this->patchEntity($document, $documentData);

            if ($this->save($document)) {
                // Update verification status after successful document upload
                $this->updateMerchantVerificationStatus($merchantId);
                return true;
            } else {
                $errors = $document->getErrors();
                $errorMessages = [];
                foreach ($errors as $field => $fieldErrors) {
                    $errorMessages[] = $field . ': ' . implode(', ', $fieldErrors);
                }
                return 'Document could not be saved. Errors: ' . implode('; ', $errorMessages);
            }
        } catch (\Exception $e) {
            return 'Error saving document: ' . $e->getMessage();
        }
    }

    /**
     * Save multiple documents for a merchant (for KYC documents from add form)
     *
     * @param int $merchantId
     * @param array $documentsData Array with document_type as key and file object as value
     * @return array Array of results for each document
     */
    public function saveMultipleDocuments($merchantId, $documentsData)
    {
        $results = [];
        
        foreach ($documentsData as $documentType => $file) {
            if ($file && $file->getError() === UPLOAD_ERR_OK) {
                $result = $this->saveDocument($merchantId, $documentType, $file);
                $results[$documentType] = $result;
            } else if ($file && $file->getError() !== UPLOAD_ERR_NO_FILE) {
                // Handle file upload errors
                $errorMessage = 'File upload error: ';
                switch ($file->getError()) {
                    case UPLOAD_ERR_INI_SIZE:
                        $errorMessage .= 'File exceeds PHP upload_max_filesize limit';
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        $errorMessage .= 'File exceeds HTML form MAX_FILE_SIZE limit';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $errorMessage .= 'File was only partially uploaded';
                        break;
                    case UPLOAD_ERR_NO_TMP_DIR:
                        $errorMessage .= 'Missing temporary folder';
                        break;
                    case UPLOAD_ERR_CANT_WRITE:
                        $errorMessage .= 'Failed to write file to disk';
                        break;
                    case UPLOAD_ERR_EXTENSION:
                        $errorMessage .= 'A PHP extension stopped the file upload';
                        break;
                    default:
                        $errorMessage .= 'Unknown upload error (code: ' . $file->getError() . ')';
                }
                $results[$documentType] = $errorMessage;
            }
        }
        
        // Update verification status after all documents are processed
        if (!empty($results)) {
            $this->updateMerchantVerificationStatus($merchantId);
        }
        
        return $results;
    }

    /**
     * Get available document types for dropdowns
     *
     * @return array
     */
    public function getDocumentTypes()
    {
        return [
            'cerificate' => 'Certificate',
            'licence' => 'License',
            'others' => 'Others'
        ];
    }

    /**
     * Get documents by merchant ID with optional filtering
     *
     * @param int $merchantId
     * @param string|null $documentType
     * @return \Cake\ORM\Query
     */
    public function getMerchantDocuments($merchantId, $documentType = null)
    {
        $query = $this->find()->where(['merchant_id' => $merchantId]);
        
        if ($documentType) {
            $query->where(['document_type' => $documentType]);
        }
        
        return $query->order(['created' => 'DESC']);
    }

    /**
     * Update merchant verification status based on uploaded documents
     *
     * @param int $merchantId
     * @return string The calculated verification status
     */
    public function updateMerchantVerificationStatus($merchantId)
    {
        // Get all documents for this merchant
        $documents = $this->find()
            ->where(['merchant_id' => $merchantId, 'status' => 'A'])
            ->select(['document_type'])
            ->toArray();

        $uploadedDocumentTypes = array_column($documents, 'document_type');
        
        // Count how many documents are uploaded
        $uploadedCount = count($uploadedDocumentTypes);

        // Calculate verification status
        if ($uploadedCount === 0) {
            $verificationStatus = 'Not Started';
        } elseif ($uploadedCount < 2) {
            $verificationStatus = 'In Progress';
        } else {
            $verificationStatus = 'Verified';
        }

        // Update merchant's verification status
        $Merchants = \Cake\Datasource\FactoryLocator::get('Table')->get('Merchants');
        $merchant = $Merchants->get($merchantId);
        $merchant->verification_status = $verificationStatus;
        $merchant->document_submitted = $uploadedCount;
        $Merchants->save($merchant);

        return $verificationStatus;
    }
} 