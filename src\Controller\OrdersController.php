<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;
use DateTime;
use Mpdf\Mpdf;
use Cake\Http\Response;
use Cake\Routing\Router;
use Cake\ORM\TableRegistry;
use Cake\View\View;
use Cake\I18n\FrozenTime;

/**
 * Orders Controller
 *
 * @property \App\Model\Table\OrdersTable $Orders
 */
class OrdersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected \App\Model\Table\CustomersTable $Customers;
    protected \App\Model\Table\ProductsTable $Products;
    protected \App\Model\Table\UsersTable $Users;
    protected \App\Model\Table\ShowroomsTable $Showrooms;
    protected \App\Model\Table\TransactionsTable $Transactions;
    protected \App\Model\Table\CitiesTable $Cities;
    protected \App\Model\Table\MunicipalitiesTable $Municipalities;
    protected \App\Model\Table\ShowroomUsersTable $ShowRoomUsers;
    protected \App\Model\Table\CustomerAddressesTable $CustomerAddresses;
    protected \App\Model\Table\ShowroomUsersTable $ShowroomUsers;
    protected \App\Model\Table\SiteSettingsTable $SiteSettings;
    protected \App\Model\Table\ShipmentsTable $Shipments;
    protected \App\Model\Table\ShipmentOrdersTable $ShipmentOrders;
    protected \App\Model\Table\OrderCancellationsTable $OrderCancellations;
    protected \App\Model\Table\OrderCancellationCategoriesTable $OrderCancellationCategories;
    protected \App\Model\Table\OrderItemsTable $OrderItems;
    protected \App\Model\Table\OrderReturnsTable $OrderReturns;
    protected \App\Model\Table\ProductStocksTable $ProductStocks;
    protected \App\Model\Table\LoyaltyTable $Loyalty;
    protected \App\Model\Table\LoyaltySettingsTable $LoyaltySettings;
    protected \App\Model\Table\CustomerGroupMappingsTable $CustomerGroupMappings;
    protected \App\Model\Table\PartnersTable $Partners;
    protected $ZohoSettings;
    protected $zohoSettings;
    protected $Roles;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('WebsiteFunction');
        $this->loadComponent('Zoho');
        $this->loadComponent('Mtn');
        $this->loadComponent('Wave');
        $this->Customers = $this->fetchTable('Customers');
        $this->Products = $this->fetchTable('Products');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->Cities = $this->fetchTable('Cities');
        $this->Municipalities = $this->fetchTable('Municipalities');
        $this->ShowRoomUsers = $this->fetchTable('ShowroomUsers');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->Users = $this->fetchTable('Users');
        $this->ShowroomUsers = $this->fetchTable('ShowroomUsers');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Loyalty = $this->fetchTable('Loyalty');
        $this->LoyaltySettings = $this->fetchTable('LoyaltySettings');
        $this->CustomerGroupMappings = $this->fetchTable('CustomerGroupMappings');
        $this->Partners = $this->fetchTable('Partners');
        $this->ZohoSettings = $this->fetchTable('ZohoSettings');
        $this->zohoSettings = $this->fetchTable('zohoSettings');
        $this->Roles = $this->fetchTable('Roles');
    }

    public function index()
    {

        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
        $SalesPersonRoleId = Configure::read('Constants.SALES_PERSON_ROLE_ID');
        $AdminRoleId = Configure::read('Constants.ADMIN_ROLE_ID');

        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');

        $OrderDetails = $this->Orders->getOrderDetails();
        $approvalShowrooms = [];
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $OrderDetails = $this->Orders->getOrderDetails($user->id, 'Showroom_Manager');
            } elseif ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $OrderDetails = $this->Orders->getOrderDetails($user->id, 'Showroom_Supervisor');
            } elseif ($user->role_id == $SalesPersonRoleId) {
                $OrderDetails = $this->Orders->getOrderDetails($user->id, 'SalesPerson');
            }
        }

        // Add controller-level filtering
        if (!empty($user) && in_array($user->role_id, [$ShowroomManagerRoleId, $ShowroomSupervisorRoleId])) {
            $OrderDetails->where([
                'NOT' => [
                    'AND' => [
                        'Orders.order_type' => 'Online',
                        'Orders.status' => 'Pending'
                    ]
                ]
            ]);
        }

        $orders = $OrderDetails->all();
        $ordersShowrooms = $OrderDetails->toArray();
        $approvalShowrooms = [];

        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $ShowroomsIDs = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $user->id, 'Showrooms.status' => 'A'])
                    ->toArray();
                $approvalShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            }
            // elseif ($user->role_id == $ShowroomSupervisorRoleId) {
            //     $ShowroomsIDs = $this->Showrooms->find()
            //         ->select(['id'])
            //         ->where(['Showrooms.showroom_supervisor' => $user->id, 'Showrooms.status' => 'A'])
            //         ->toArray();
            //     $approvalShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            // } elseif ($user->role_id == $AdminRoleId) {
            //     if (!empty($ordersShowrooms)) {
            //         $approvalShowrooms = array_column($ordersShowrooms, 'showroom_id'); // Corrected field name
            //     }
            // }
        }

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $creditPartners = $this->Partners->find('list', [
            'keyField' => 'id',
            'valueField' => 'business_name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($creditPartners)) {
            $creditPartners  = ['' => 'No partners available'];
        }

        $this->set(compact('orders', 'orderstatuses', 'paymentstatuses', 'dateFormat', 'timeFormat', 'currencySymbol', 'approvalShowrooms', 'decimalSeparator', 'thousandSeparator', 'creditPartners'));
    }

    /**
     * Seller/Merchant Orders listing
     * Reuses the same dataset as admin but scopes by the logged-in merchant's products.
     */
    public function sellerIndex()
    {
        // Ensure only merchants can access this listing
        if ($this->userType !== 'Merchant' || empty($this->merchantId)) {
            return $this->redirect(['controller' => 'Dashboards', 'action' => 'noaccess']);
        }

        // Base query reused from admin index
        $OrderDetails = $this->Orders->getOrderDetails();
        $OrderDetails->where(['Orders.merchant_id' => $this->merchantId]);
        // Restrict to orders that have at least one item for this merchant
        $OrderDetails->innerJoinWith('OrderItems.Products')
            ->where(['Products.merchant_id' => $this->merchantId]);

        $orders = $OrderDetails->all();

        // View data (same as admin index)
        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $creditPartners = $this->Partners->find('list', [
            'keyField' => 'id',
            'valueField' => 'business_name'
        ])->where(['status' => 'A'])->all()->toArray();
        if (empty($creditPartners)) {
            $creditPartners  = ['' => 'No partners available'];
        }

        // Render seller-specific view
        $this->viewBuilder()->setTemplate('seller_index');
        $this->set(compact('orders', 'orderstatuses', 'paymentstatuses', 'dateFormat', 'timeFormat', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'creditPartners'));
    }


    /**
     * View method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $isMerchantOrder = false;
        // If a merchant is logged in, ensure they can access this order
        if ($this->userType === 'Merchant' && !empty($this->merchantId)) {
            $isMerchantOrder = $this->Orders->OrderItems->find()
                ->matching('Products', function ($q) {
                    return $q->where(['Products.merchant_id' => $this->merchantId]);
                })
                ->where(['OrderItems.order_id' => $id])
                ->count() > 0;

            if (!$isMerchantOrder) {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'noaccess']);
            }
        }

        // $order = $this->Orders->get($id, contain: ['Customers', 'CustomerAddresses', 'Offers', 'Showrooms', 'OrderItems', 'Returns', 'Shipments', 'Transactions']);

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'CustomerAddresses' => ['Cities', 'Municipalities'],
                'Offers',
                'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductVariants' => [
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ]
                ],
                'Transactions'
            ]
        ]);

        // Assign the CloudFront URL to product images
        foreach ($order->order_items as $item) {
            if (!empty($item->product->product_images)) {
                foreach ($item->product->product_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
            if (!empty($item->product_variant->product_variant_images)) {
                foreach ($item->product_variant->product_variant_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        $orderStatusMap = Configure::read('Constants.ORDER_STATUSES_MAP');
        $orderStatusProgress  = Configure::read('Constants.ORDER_STATUS_PROGRESS_COLOR');
        $orderStatusProgressBar  = Configure::read('Constants.ORDER_STATUS_PROGRESS_BAR');

        $paymentStatusProgress  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_COLOR');
        $paymentStatusProgressBar  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_BAR');

        $shippedStatusProgress  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_COLOR');
        $shippedStatusProgressBar  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_BAR');

        $deliveryStatusProgress  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_COLOR');
        $deliveryStatusProgressBar  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_BAR');

        $orderStatus = Configure::read('Constants.ORDER_STATUSES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $orderItemCount = $this->Orders->OrderItems->find()
            ->where(['order_id' => $id])
            ->count();

        $OrderShipment = $this->ShipmentOrders->find()
            ->select([
                // 'Shipments.shipment_status',
                'Shipments.delivery_status'
            ])->contain('Shipments')
            ->where(['ShipmentOrders.order_id' => $id])
            ->order(['ShipmentOrders.id' => 'DESC']) // Sort by latest record
            ->first(); // Get only the last record


        // $this->log('test'.$orderStatusProgress,'debug');

        $orderCancellationCategories = $this->OrderCancellationCategories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($orderCancellationCategories)) {
            $orderCancellationCategories = ['' => 'No Categories available'];
        }

        $orderReturn = $this->OrderReturns->find()
            ->contain([
                'OrderItems' => [
                    'Products' => function ($q) {
                        return $q->select(['Products.id', 'Products.name', 'Products.sku']);
                    },
                    'ProductVariants' => function ($q) {
                        return $q->select(['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.sku']);
                    },
                    'ProductAttributes' => function ($q) {
                        return $q->select(['ProductAttributes.id', 'ProductAttributes.attribute_id', 'ProductAttributes.attribute_value_id']);
                    }
                ],
                'Orders' => [
                    'Customers' => ['Users']
                ],
                'Users' => function ($q) {
                    return $q->select(['Users.id', 'Users.first_name', 'Users.last_name']);
                },
                'VerifiedByUser' => function ($q) {
                    return $q->select(['VerifiedByUser.id', 'VerifiedByUser.first_name', 'VerifiedByUser.last_name']);
                },
                'OrderReturnCategories' => function ($q) {
                    return $q->select(['OrderReturnCategories.id', 'OrderReturnCategories.name']);
                },
                'RefundTransactions' => function ($q) {
                    return $q->order(['RefundTransactions.created_at' => 'DESC']);
                }
            ])
            ->where(['OrderReturns.order_id' => $id])
            ->all();

        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $user = $this->Authentication->getIdentity();
        $loggedInUserId = $user ? $user->id : null;


        $UsersTable = TableRegistry::getTableLocator()->get('Users');
        $RolesTable = TableRegistry::getTableLocator()->get('Roles');

        // $orderPlacedBy = null;

        // if (!empty($order->created_by_role)) {
        //     $role = $RolesTable->find()
        //         ->where(['Roles.id' => $order->created_by_role])
        //         ->first();

        //     if ($role && strtolower($role->name) === 'showroom manager') {
        //         $createdByUser = $UsersTable->find()
        //             ->where(['Users.id' => $order->created_by])
        //             ->contain(['Showrooms'])
        //             ->first();

        //         if (!empty($createdByUser->showroom)) {
        //             if ($order->showroom_id != $createdByUser->showroom->id) {
        //                 $orderPlacedBy = $createdByUser->showroom->name;
        //             }
        //         }
        //     }
        // }

        $orderPlacedBy = null;

        if (!empty($order->created_by_role)) {
            $role = $RolesTable->find()
                ->where(['Roles.id' => $order->created_by_role])
                ->first();

            if ($role) {
                $roleName = strtolower($role->name);

                if ($roleName === 'showroom manager') {
                    $createdByUser = $UsersTable->find()
                        ->where(['Users.id' => $order->created_by])
                        ->contain(['Showrooms'])
                        ->first();

                    if (!empty($createdByUser->showroom)) {
                        if ($order->showroom_id != $createdByUser->showroom->id) {
                            $orderPlacedBy = $createdByUser->showroom->name;
                        }
                    }

                } elseif ($roleName === 'sales person') {
                    // Lookup showroom_users mapping for the user
                    $showroomUser = $this->fetchTable('ShowroomUsers')->find()
                        ->where([
                            'user_id' => $order->created_by,
                            'ShowroomUsers.status' => 'A'
                        ])
                        ->contain(['Showrooms'])
                        ->first();

                    if (!empty($showroomUser) && !empty($showroomUser->showroom)) {
                        if ($order->showroom_id != $showroomUser->showroom->id) {
                            $orderPlacedBy = $showroomUser->showroom->name;
                        }
                    }
                }
            }
        }

        // Add sales person name if available
        $salesPersonName = null;
        if (!empty($order->sales_person_id)) {
            $salesPerson = $this->Users->find()
                ->select(['first_name', 'last_name'])
                ->where(['Users.id' => $order->sales_person_id])
                ->first();

            if ($salesPerson) {
                $salesPersonName = $salesPerson->first_name . ' ' . $salesPerson->last_name;
            }
        }


        $this->set(compact('order', 'currencySymbol', 'orderItemCount', 'orderStatusMap', 'orderStatus', 'orderStatusProgress', 'orderStatusProgressBar', 'paymentStatusProgressBar', 'paymentStatusProgress', 'shippedStatusProgress', 'shippedStatusProgressBar', 'deliveryStatusProgress', 'deliveryStatusProgressBar', 'OrderShipment', 'orderCancellationCategories', 'decimalSeparator', 'thousandSeparator', 'orderReturn', 'loggedInUserId', 'orderPlacedBy', 'salesPersonName', 'isMerchantOrder'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $order = $this->Orders->newEmptyEntity();
        $uniqueOrderId = $this->Orders->generateUniqueOrderNum('Admin');
        $customerList = $this->Customers->find()
            ->select([
                'customer_id' => 'Customers.id',
                'full_name' => $this->Customers->Users->find()
                    ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
                    ->where(['Users.id = Customers.user_id', 'Users.status' => 'A']),
                'phone' => $this->Customers->Users->find()
                    ->func()
                    ->concat(['+', 'Users.country_code' => 'identifier', 'Users.mobile_no' => 'identifier']),
                'email' => 'Users.email'
            ])
            // ->select(['phone' => 'Customers.phone_number'])
            ->contain(['Users'])
            ->where(['Users.user_type' => 'Customer', 'Users.status' => 'A'])
            ->all();

        $customers = [];
        foreach ($customerList as $customer) {
            $customers[$customer->customer_id] = $customer->full_name . ' (' . $customer->phone . ')' . '(' . $customer->email . ')';
        }
        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
            ->where(['Products.status' => 'A', 'approval_status' => 'Approved'])
            ->order(['Products.name' => 'ASC'])
            ->all()
            ->toArray();

        if (empty($products)) {
            $products = ['' => 'No products available'];
        }
        $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
        $user = $this->Authentication->getIdentity();
        
        $selectedSalespersonId = null; // default

        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
        $IsShowroomanager = '';
        $IsShowroomsupervisor = '';
        $loggedInShowroomId = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'Showrooms.status' => 'A'])
                    ->toArray();

                $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
                $showroom_salesperson = $this->ShowroomUsers->find()
                    ->select(['user_id'])
                    ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
                    ->toArray();

                $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

                $loggedInShowroomId = !empty($showrooms) ? $showroomIds[0] : '';

                // Get active salespersons only if there are valid salesperson IDs


                // Apply whereInList() only if salespersonIds is not empty
                if (!empty($salespersonIds)) {
                    $salespersonQuery = $this->Users->find('list', [
                        'keyField' => 'id',
                        'valueField' => function ($row) {
                            return $row->first_name . ' ' . $row->last_name;
                        }
                    ])
                        ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                        ->order(['Users.first_name' => 'ASC']);
                    $salespersonQuery->whereInList('Users.id', $salespersonIds);
                    $salesperson = $salespersonQuery->toArray();
                } else {
                    $salesperson = [];
                }
            } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'Showrooms.status' => 'A'])
                    ->toArray();

                $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
                $showroom_salesperson = $this->ShowroomUsers->find()
                    ->select(['user_id'])
                    ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
                    ->toArray();

                $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];
                $loggedInShowroomId = !empty($showrooms) ? $showroomIds[0] : '';
                // Get active salespersons only if there are valid salesperson IDs


                // Apply whereInList() only if salespersonIds is not empty
                if (!empty($salespersonIds)) {
                    $salespersonQuery = $this->Users->find('list', [
                        'keyField' => 'id',
                        'valueField' => function ($row) {
                            return $row->first_name . ' ' . $row->last_name;
                        }
                    ])
                        ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                        ->order(['Users.first_name' => 'ASC']);
                    $salespersonQuery->whereInList('Users.id', $salespersonIds);
                    $salesperson = $salespersonQuery->toArray();
                } else {
                    $salesperson = [];
                }
            } else {
                $salesperson = $this->Users->find('list', [
                    'keyField' => 'id',
                    'valueField' => function ($row) {
                        return $row->first_name . ' ' . $row->last_name;
                    }
                ])
                    ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                    ->order(['Users.first_name' => 'ASC'])
                    ->all()
                    ->toArray();
            }
        } else {
            $salesperson = $this->Users->find('list', [
                'keyField' => 'id',
                'valueField' => function ($row) {
                    return $row->first_name . ' ' . $row->last_name;
                }
            ])
                ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                ->order(['Users.first_name' => 'ASC'])
                ->all()
                ->toArray();
        }


        if (empty($salesperson)) {
            $salesperson = ['' => 'No sales person available'];
        }



        $IsShowroomanager = '';
        $IsShowroomsupervisor = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'status' => 'A'])->toArray();
            } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'status' => 'A'])->toArray();
            } else {
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['status' => 'A'])->toArray();
            }
        } else {
            $showrooms = $this->Showrooms->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['status' => 'A'])->toArray();
        }

        if (empty($showrooms)) {
            $showrooms = ['' => 'No showrooms available'];
        }
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])
            //->where(['Cities.status' => 'A'])
            ->all()
            ->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No cities available'];
        }

        $municipalities = $this->Municipalities->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($municipalities)) {
            $municipalities = ['' => 'No Municipalities available'];
        }
        $statuses = Configure::read('Constants.ORDER_STATUSES');
        $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
        $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $Loyalty_Redeem = Configure::read('Constants.REDEEM_LOYALTY');
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');

        $showroomPreSel = '';
        $IsShowroomanager = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showroomPreSel = $this->ShowRoomUsers->find()
                    ->select(['showroom_id'])
                    ->where(['user_id' => $user->id, 'status' => 'A'])
                    ->first();

                $showroomPreSel = $showroomPreSel ? $showroomPreSel->showroom_id : '';
            }
        }
        $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');

        $StockShowrooms = [];

        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $ShowroomsIDs = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $user->id, 'Showrooms.status' => 'A'])
                    ->toArray();
                $StockShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            } elseif ($user->role_id == $ShowroomSupervisorRoleId) {
                $ShowroomsIDs = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_supervisor' => $user->id, 'Showrooms.status' => 'A'])
                    ->toArray();
                $StockShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];
            } elseif ($user->role_id == $SALES_PERSON_ROLE_ID) {

                $ShowroomsIDs = $this->ShowroomUsers->find()
                    ->select(['showroom_id'])
                    ->where(['ShowroomUsers.user_id' => $user->id, 'ShowroomUsers.status' => 'A'])
                    ->toArray();
                $StockShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];

                $selectedSalespersonId = $user->id;
            }
        }

        if (!empty($user)) {
            // Get the role of the user
            $role = $this->Roles->get($user->role_id);
        }

        $activeShowrooms = $this->Showrooms->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])
            ->where(['status' => 'A'])
            ->toArray();

        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $mapApiKey = Configure::read('Settings.GOOGLE_MAP_API_KEY');

        $this->set(compact('order', 'customers', 'products', 'uniqueOrderId', 'statuses', 'showrooms', 'shipping_methods', 'payment_statuses', 'currencySymbol', 'cities', 'Loyalty_Redeem', 'showroomPreSel', 'IsShowroomanager', 'municipalities', 'ABIDJAN_CITY_ID', 'salesperson', 'loggedInShowroomId', 'StockShowrooms', 'role', 'thousandSeparator', 'decimalSeparator', 'activeShowrooms', 'selectedSalespersonId', 'mapApiKey'));

        if ($this->request->is('post')) {
            $order = $this->Orders->patchEntity($order, $this->request->getData(), [
                'associated' => [
                    'OrderItems' => [
                        'associated' => ['OrderItemAttributes']
                    ],
                    'Transactions',
                    'Customers' => ['Users'],
                ]
            ]);

            $deviceToken = $order->customer->user->fcm_token ?? '';

            $data = $this->request->getData();

            $user = $this->Authentication->getIdentity();
            $CallCentreAgentRoleId = Configure::read('Constants.CALL_CENTRE_AGENT_ROLE_ID');
            $CallCentreSupervisorRoleId = Configure::read('Constants.CALL_CENTRE_SUPERVISOR_ROLE_ID');
            if (!empty($user)) {
                if ($user->role_id == $CallCentreAgentRoleId || $user->role_id == $CallCentreSupervisorRoleId) {
                    $order['order_type'] = 'Online';
                } else {
                    $order['order_type'] = 'Showroom';
                }
            }

            $order['created_by'] = $user->id;
            $order['created_by_role'] = $user->role_id;
            $order->transactions[0]['invoice_number'] = $this->Transactions->generateUniqueInvoiceNum();
            $order->transactions[0]['transaction_number'] = $this->Transactions->generateUniqueTransactionNum();
            $order->transactions[0]['transaction_date'] = date('Y-m-d H:i:s');
            $order->transactions[0]['amount'] = $order->total_amount;
            $order->transactions[0]['payment_method'] = $order->payment_method;

            // handle payment methods
            if ($order->payment_method === 'MTN MoMo') {
                // Default: pending until MTN confirms
                $order->transactions[0]['payment_status'] = 'Pending';
                $order->transactions[0]['payment_collected'] = 'No';

                $mtnPhone = $this->request->getData('mtn_phone');

                try {
                    // Call MTN component to initiate payment
                    $paymentResponse = $this->Mtn->initiatePayment($order->order_number, $order->total_amount, $mtnPhone);

                    // echo '<pre>';print_r($paymentResponse);die;

                    // Save tracking references
                    $order->transactions[0]['transactionID'] = $paymentResponse['data']['financialTransactionId'] ?? null;
                    $order->transactions[0]['momo_payerID']     = $paymentResponse['data']['payer']['partyId'] ?? $mtnPhone;
                    $order->transactions[0]['momo_referenceID'] = $paymentResponse['data']['financialTransactionId'] ?? null;
                    $order->transactions[0]['momo_externalID']  = $paymentResponse['data']['externalId'] ?? $order->order_number;

                    if (!empty($paymentResponse['data']['status']) && strtoupper($paymentResponse['data']['status']) === 'SUCCESSFUL') {
                        $order->transactions[0]['payment_status'] = 'Paid';
                        $order->transactions[0]['payment_collected'] = 'Yes';
                    } else {
                        // still pending, wait for callback or status check
                        $order->transactions[0]['payment_status'] = 'Pending';
                    }
                } catch (\Exception $e) {
                    $this->log('MTN MoMo payment error: ' . $e->getMessage(), 'error');
                    $order->transactions[0]['payment_status']   = 'Pending';
                    $order->transactions[0]['momo_payerID']     = $mtnPhone;
                    $order->transactions[0]['momo_referenceID'] = null;
                    $order->transactions[0]['momo_externalID']  = $order->order_number;
                }

            } elseif ($order->payment_method === 'Wave') {

                $order->transactions[0]['payment_status']    = 'Pending';
                $order->transactions[0]['payment_collected'] = 'No';

                try {
                    $clientRef = $order->order_number;
                    $amount    = $order->total_amount;
                    $waveResponse = $this->Wave->createCheckoutSession($clientRef, $amount);

                    if (!empty($waveResponse['status']) && $waveResponse['status'] === 'success') {
                        $checkoutData = $waveResponse['data'];

                        $order->transactions[0]['wave_checkout_sessionID']  = $checkoutData['id'] ?? null;
                        $order->transactions[0]['wave_checkout_status']     = $checkoutData['checkout_status'] ?? 'processing';
                        $order->transactions[0]['wave_client_referenceID']  = $checkoutData['client_reference'] ?? null;

                        // payment remains Pending until Wave notifies
                        $order->transactions[0]['payment_status']           = 'Pending';
                    } else {
                        $this->log('Wave error: ' . json_encode($waveResponse), 'error');
                        $order->transactions[0]['payment_status'] = 'Pending';
                    }

                } catch (\Exception $e) {
                    $this->log('Wave API error: ' . $e->getMessage(), 'error');
                    $order->transactions[0]['payment_status'] = 'Pending';
                }
            }


            // Set payment_collected based on user role
            $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
            if ($user->role_id == $SALES_PERSON_ROLE_ID) {
                $order->status = 'Pending';
                $order->transactions[0]['payment_collected'] = 'No';
            } else {
                $order->transactions[0]['payment_collected'] = 'Yes';
            }
            if ($order['delivery_mode'] == 'delivery') {
                $order['delivery_date'] = $this->CalculateEstimatedDelivery($data['delivery_mode_type']);
            }
            $errors = $order->getErrors();


            // foreach ($order->order_items as &$item) {
            //     if (!empty($item['order_item_attributes'])) {
            //         $item['order_item_attributes'] = array_filter($item['order_item_attributes'], function ($attribute) {
            //             return !empty($attribute['product_attribute_id']); // Keep only attributes with a value
            //         });
            //     }
            // }

            if ($order['order_type'] == 'Showroom' && $order['delivery_mode'] == 'delivery') {

                $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
                $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
                if (!empty($user)) {
                    $UserId = $user->id;
                    if ($user->role_id == $ShowroomManagerRoleId) {
                        $order['showroom_id'] = $this->Showrooms
                            ->find()
                            ->select(['id'])
                            ->where([
                                'Showrooms.showroom_manager' => $UserId,
                                'Showrooms.status' => 'A'
                            ])
                            ->first()
                            ->id;
                    } elseif ($user->role_id == $ShowroomSupervisorRoleId) {
                        $order['showroom_id'] = $this->Showrooms
                            ->find()
                            ->select(['id'])
                            ->where([
                                'Showrooms.showroom_supervisor' => $UserId,
                                'Showrooms.status' => 'A'
                            ])
                            ->first()
                            ->id;
                    }
                }
            }

            $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
            if ($user->role_id == $SALES_PERSON_ROLE_ID) {
                $UserId = $user->id;
                $order['sales_person_id'] = $UserId;
                $total = (float)$order['total_amount'];

                $salesperson_commissionpercent = (float)$this->SiteSettings->find()
                    ->select(['salesperson_commissionpercent'])
                    ->first()->salesperson_commissionpercent;

                $bonus = round($total * ($salesperson_commissionpercent / 100), 2);

                $order['sales_person_bonus'] = $bonus;
            }
            $order['status'] = 'Pending';

            if ($order['delivery_mode'] === 'delivery') {
                // $order['status'] = 'Approved';
                if ($user->role_id == $SALES_PERSON_ROLE_ID) {
                    $order->status = 'Pending';
                } else {
                    $order['status'] = 'Approved';
                }

            } elseif ($order['delivery_mode'] === 'pickup' && !empty($user) && $user->role_id === $ShowroomManagerRoleId) {
                $ShowroomsIDs = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $user->id, 'Showrooms.status' => 'A'])
                    ->toArray();

                $StockShowrooms = !empty($ShowroomsIDs) ? array_column($ShowroomsIDs, 'id') : [];

                if (in_array($order['showroom_id'], $StockShowrooms, true)) {
                    $order['status'] = 'Approved';
                }
            }

            if ($this->Orders->save($order, [
                'associated' => [
                    'OrderItems',
                    'Transactions'
                ]
            ])) {

                /* -----Start add order to zoho crm----- */
                
                $order_id = $order->id;                
                $ordered_from = "Admin Panel";
                $zoho_res = $this->Zoho->createCRMSalesOrder($order_id, $ordered_from);

                /* -----End add order to zoho crm----- */ 

                $this->WebsiteFunction->OrderConfirmationEmail($order->id);


                if (!empty($deviceToken)) {
                    $title = 'Order Confirmed - #' . $order['order_number'];
                    $body  = 'Your order has been confirmed and is now being processed.';
                    $customData = [
                        "notification_type" => "order",
                        "id" => (string)$order->id,
                        "category" => "order"
                    ]; // Optional

                    $response = $this->Global->sendNotification(
                        [$deviceToken],
                        $title,
                        $body,
                        $customData
                    );
                }


                $showroomsaved = $order['showroom_id'];

                if ($order['loyalty_amount'] > 0 && $order['loyalty_points_redeemed'] > 0) {
                    $loyalty = $this->Loyalty->find()->where(['customer_id' => $order['customer_id'], 'status' => 'A'])->first();
                    if ($loyalty) {
                        $loyalty->spent_amount += $order['loyalty_amount'];
                        $loyalty->points -= $order['loyalty_points_redeemed'];
                        $this->Loyalty->save($loyalty); // ✅ Save the changes
                    }
                }
                $customerGroupsQuery = $this->CustomerGroupMappings
                    ->find()
                    ->select(['customer_group_id'])
                    ->where(['customer_id' => $order['customer_id'], 'status' => 'A'])
                    ->all();

                $customerGroupIds = [];
                foreach ($customerGroupsQuery as $row) {
                    $customerGroupIds[] = $row->customer_group_id;
                }

                if (!empty($customerGroupIds)) {
                    $loyalty_setting = $this->LoyaltySettings
                        ->find()
                        ->where([
                            'customer_group_id IN' => $customerGroupIds,
                            // 'earning_threshold_amount <=' => $order['subtotal_amount'],
                            'status' => 'A'
                        ])
                        ->order(['earning_threshold_amount' => 'DESC'])
                        ->first();

                    if ($loyalty_setting) {
                        $subtotal_amount = round((float)$order['total_amount'] - (float)$order['delivery_charge'], 2);
                        $earning_threshold_amount = $loyalty_setting->earning_threshold_amount;
                        $points = $loyalty_setting->earning_points;

                        $earned_points = 0;
                        if ($earning_threshold_amount > 0) {
                            $earned_points = floor($subtotal_amount / $earning_threshold_amount) * $points;
                        }

                        // Use new loyalty earning method with expiration extension
                        if ($earned_points > 0) {
                            $loyaltyResult = $this->Loyalty->earnLoyaltyPoints(
                                $order['customer_id'], 
                                $earned_points, 
                                'standard'
                            );
                            
                        }
                    }
                }



                // foreach ($order->order_items as &$item) {
                //     if (!empty($item['product_variant_id'])) {
                //         $conditions = ['product_variant_id' => $item['product_variant_id']];
                //         if (!empty($showroomsaved)) {
                //             $conditions['showroom_id'] = $showroomsaved;
                //         }

                //         $productStock = $this->ProductStocks->find()->where($conditions)->first();

                //         if ($productStock) {
                //             $productStock->reserved_stock += $item['quantity'];
                //         } else {
                //             $data = [
                //                 'product_variant_id' => $item['product_variant_id'],
                //                 'reserved_stock' => $item['quantity']
                //             ];
                //             if (!empty($showroomsaved)) {
                //                 $data['showroom_id'] = $showroomsaved;
                //             }
                //             $productStock = $this->ProductStocks->newEntity($data);
                //         }

                //         $this->ProductStocks->save($productStock);
                //     } elseif (!empty($item['product_id'])) {
                //         $conditions = [
                //             'product_id' => $item['product_id'],
                //             'product_variant_id IS' => null
                //         ];
                //         if (!empty($showroomsaved)) {
                //             $conditions['showroom_id'] = $showroomsaved;
                //         }

                //         $productStock = $this->ProductStocks->find()->where($conditions)->first();

                //         if ($productStock) {
                //             $productStock->reserved_stock += $item['quantity'];
                //         } else {
                //             $data = [
                //                 'product_id' => $item['product_id'],
                //                 'reserved_stock' => $item['quantity']
                //             ];
                //             if (!empty($showroomsaved)) {
                //                 $data['showroom_id'] = $showroomsaved;
                //             }
                //             $productStock = $this->ProductStocks->newEntity($data);
                //         }

                //         $this->ProductStocks->save($productStock);
                //     }
                // }

                $response = ['success' => true, 'message' => 'The Order has been saved.'];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                $this->Flash->success(__('The order has been saved.'));
                return $this->response;
                // return $this->redirect(['action' => 'index']);
            } else {
                $this->log('data: ' . json_encode($order->getErrors()), 'debug');
                $this->Flash->error(__('The order could not be saved. Please, try again.'));
            }
        }
        // $customerAddresses = $this->Orders->CustomerAddresses->find('list', limit: 200)->all();
        // $offers = $this->Orders->Offers->find('list', limit: 200)->all();
        // $showrooms = $this->Orders->Showrooms->find('list', limit: 200)->all();
        //$this->set(compact('order', 'customers', 'customerAddresses', 'offers', 'showrooms'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => [
                    'Users'
                ],
                'CustomerAddresses' => [
                    'Cities',
                    'Municipalities'
                ],
                'Offers',
                'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductVariants',
                        'ProductAttributes'
                    ],
                    'OrderItemAttributes' // Added association
                ],
                'Transactions'
            ]
        ]);
        $orderItems = $order->order_items;

        /** EDIT LOCK CODE STARTS **/
        $requested_user = $this->Authentication->getIdentity();

        $editLockTime = new FrozenTime($order->edit_lock_time);
        $now = FrozenTime::now();
        $minutesPassed = $editLockTime->diffInMinutes($now);

        if (
            !empty($order->edit_lock_by) &&
            $order->edit_lock_by != $requested_user->id &&
            $minutesPassed < 10
        ) {
            $minutesRemaining = 10 - $minutesPassed;
            $this->Flash->error(__('This order is currently being edited/validated. Please try again in {0} minute(s).', $minutesRemaining));
            return $this->redirect(['action' => 'index']);
        }

        // Lock the request for the current user
        $order->edit_lock_by = $requested_user->id;
        $order->edit_lock_time = FrozenTime::now();
        $this->Orders->save($order);

        /** EDIT LOCK CODE ENDS **/

        $customerList = $this->Customers->find()
            ->select([
                'customer_id' => 'Customers.id',
                'full_name' => $this->Customers->Users->find()
                    ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
                    ->where(['Users.id = Customers.user_id', 'Users.status' => 'A']),
                'phone' => $this->Customers->Users->find()
                    ->func()
                    ->concat(['+', 'Users.country_code' => 'identifier', 'Users.mobile_no' => 'identifier']),
                'email' => 'Users.email'
            ])
            // ->select(['phone' => 'Customers.phone_number'])
            ->contain(['Users'])
            ->where(['Users.user_type' => 'Customer', 'Users.status' => 'A'])
            ->all();

        $customers = [];
        foreach ($customerList as $customer) {
            $customers[$customer->customer_id] = $customer->full_name . ' (' . $customer->phone . ')' . '(' . $customer->email . ')';
        }
        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
            ->where(['Products.status' => 'A', 'approval_status' => 'Approved'])
            ->order(['Products.name' => 'ASC'])
            ->all()
            ->toArray();

        if (empty($products)) {
            $products = ['' => 'No products available'];
        }

        $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
        $IsShowroomanager = '';
        $IsShowroomsupervisor = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'Showrooms.status' => 'A'])
                    ->toArray();

                $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
                $showroom_salesperson = $this->ShowroomUsers->find()
                    ->select(['user_id'])
                    ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
                    ->toArray();

                $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

                // Get active salespersons only if there are valid salesperson IDs


                // Apply whereInList() only if salespersonIds is not empty
                if (!empty($salespersonIds)) {
                    $salespersonQuery = $this->Users->find('list', [
                        'keyField' => 'id',
                        'valueField' => function ($row) {
                            return $row->first_name . ' ' . $row->last_name;
                        }
                    ])
                        ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                        ->order(['Users.first_name' => 'ASC']);
                    $salespersonQuery->whereInList('Users.id', $salespersonIds);
                    $salesperson = $salespersonQuery->toArray();
                } else {
                    $salesperson = [];
                }
            } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'Showrooms.status' => 'A'])
                    ->toArray();

                $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
                $showroom_salesperson = $this->ShowroomUsers->find()
                    ->select(['user_id'])
                    ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
                    ->toArray();

                $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

                // Get active salespersons only if there are valid salesperson IDs


                // Apply whereInList() only if salespersonIds is not empty
                if (!empty($salespersonIds)) {
                    $salespersonQuery = $this->Users->find('list', [
                        'keyField' => 'id',
                        'valueField' => function ($row) {
                            return $row->first_name . ' ' . $row->last_name;
                        }
                    ])
                        ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                        ->order(['Users.first_name' => 'ASC']);
                    $salespersonQuery->whereInList('Users.id', $salespersonIds);
                    $salesperson = $salespersonQuery->toArray();
                } else {
                    $salesperson = [];
                }
            } else {
                $salesperson = $this->Users->find('list', [
                    'keyField' => 'id',
                    'valueField' => function ($row) {
                        return $row->first_name . ' ' . $row->last_name;
                    }
                ])
                    ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                    ->order(['Users.first_name' => 'ASC'])
                    ->all()
                    ->toArray();
            }
        } else {
            $salesperson = $this->Users->find('list', [
                'keyField' => 'id',
                'valueField' => function ($row) {
                    return $row->first_name . ' ' . $row->last_name;
                }
            ])
                ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
                ->order(['Users.first_name' => 'ASC'])
                ->all()
                ->toArray();
        }


        if (empty($salesperson)) {
            $salesperson = ['' => 'No sales person available'];
        }



        $IsShowroomanager = '';
        $IsShowroomsupervisor = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $IsShowroomanager = $user->id;
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'status' => 'A'])->toArray();
            } else if ($user->role_id == $ShowroomSupervisorRoleId) {
                $IsShowroomsupervisor = $user->id;
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'status' => 'A'])->toArray();
            } else {
                $showrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['status' => 'A'])->toArray();
            }
        } else {
            $showrooms = $this->Showrooms->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['status' => 'A'])->toArray();
        }

        if (empty($showrooms)) {
            $showrooms = ['' => 'No showrooms available'];
        }
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])
            //->where(['Cities.status' => 'A'])
            ->all()
            ->toArray();

        if (empty($cities)) {
            $cities = ['' => 'No cities available'];
        }

        $municipalities = $this->Municipalities->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($municipalities)) {
            $municipalities = ['' => 'No Municipalities available'];
        }
        $statuses = Configure::read('Constants.ORDER_STATUSES');
        $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
        $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $Loyalty_Redeem = Configure::read('Constants.REDEEM_LOYALTY');
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');

        $showroomPreSel = '';
        $IsShowroomanager = '';
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $IsShowroomanager = $user->id;
                $showroomPreSel = $this->ShowRoomUsers->find()
                    ->select(['showroom_id'])
                    ->where(['user_id' => $user->id, 'status' => 'A'])
                    ->first();

                $showroomPreSel = $showroomPreSel ? $showroomPreSel->showroom_id : '';
            }
        }
        $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');
        // $uniqueOrderId = $order['order_number'];
        // $customerList = $this->Customers->find()
        //     ->select([
        //         'customer_id' => 'Customers.id',
        //         'full_name' => $this->Customers->Users->find()
        //             ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
        //             ->where(['Users.id = Customers.user_id', 'Users.status' => 'A'])
        //     ])
        //     ->select(['phone' => 'Customers.phone_number'])
        //     ->contain(['Users'])
        //     ->all();

        // $customers = [];
        // foreach ($customerList as $customer) {
        //     if ($customer->phone) {
        //         $formattedPhone = preg_replace('/(\d{3})(\d{3})(\d{4})/', '$1-$2-$3', $customer->phone);
        //     } else {
        //         $formattedPhone = '';
        //     }

        //     $customers[$customer->customer_id] = $customer->full_name . ' (' . $formattedPhone . ')';
        // }
        // $products = $this->Products->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])
        //     ->where(['Products.status' => 'A'])
        //     ->all()
        //     ->toArray();

        // if (empty($products)) {
        //     $products = ['' => 'No products available'];
        // }
        // $showrooms = $this->Showrooms->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])
        //     ->where(['Showrooms.status' => 'A'])
        //     ->all()
        //     ->toArray();

        // if (empty($showrooms)) {
        //     $showrooms = ['' => 'No showrooms available'];
        // }
        // $cities = $this->Cities->find('list', [
        //     'keyField' => 'id',
        //     'valueField' => 'name'
        // ])
        //     //->where(['Cities.status' => 'A'])
        //     ->all()
        //     ->toArray();

        // if (empty($cities)) {
        //     $cities = ['' => 'No products available'];
        // }
        // $statuses = Configure::read('Constants.ORDER_STATUSES');
        // $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
        // $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
        // $currencyConfig = Configure::read('Settings.Currency.format');
        // $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        if (!empty($user)) {
            // Get the role of the user
            $role = $this->Roles->get($user->role_id);
        }

        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $allShowrooms = $this->Showrooms->find('list', [
                    'keyField' => 'id',
                    'valueField' => 'name'
                ])->where(['status' => 'A'])->toArray();

        $this->set(compact('order', 'customers', 'products', 'statuses', 'showrooms', 'allShowrooms', 'shipping_methods', 'payment_statuses', 'currencySymbol', 'cities', 'Loyalty_Redeem', 'showroomPreSel', 'IsShowroomanager', 'municipalities', 'ABIDJAN_CITY_ID', 'salesperson', 'orderItems', 'thousandSeparator', 'decimalSeparator', 'role'));

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();

            // If transactions array is provided, update each transaction
            if (!empty($data['transactions']) && isset($data['transactions'][0])) {
                
                $paymentStatus = $data['transactions'][0]['payment_status'] ?? 'Pending';

                // 1. Delete all existing transactions for this order
                $this->Transactions->deleteAll(['order_id' => $order->id]);

                // 2. Prepare new transaction data
                $newTransaction = [
                    'order_id'         => $order->id,
                    'invoice_number'   => $this->Transactions->generateUniqueInvoiceNum(),
                    'transaction_number' => $this->Transactions->generateUniqueTransactionNum(),
                    'transaction_date' => date('Y-m-d H:i:s'),
                    'amount'           => $data['total_amount'],
                    'payment_method'   => $data['payment_method'],
                    'payment_status'   => $paymentStatus,
                    'payment_collected'   => 'Yes'
                ];

                // Handle MTN MoMo
                if ($data['payment_method'] === 'MTN MoMo') {
                    $mtnPhone = $this->request->getData('mtn_phone');

                    try {
                        $paymentResponse = $this->Mtn->initiatePayment(
                            $order->order_number,
                            $order->total_amount,
                            $mtnPhone
                        );

                        $newTransaction['transactionID']    = $paymentResponse['data']['financialTransactionId'] ?? null;
                        $newTransaction['momo_payerID']     = $paymentResponse['data']['payer']['partyId'] ?? $mtnPhone;
                        $newTransaction['momo_referenceID'] = $paymentResponse['data']['financialTransactionId'] ?? null;
                        $newTransaction['momo_externalID']  = $paymentResponse['data']['externalId'] ?? $order->order_number;

                        if (!empty($paymentResponse['data']['status']) &&
                            strtoupper($paymentResponse['data']['status']) === 'SUCCESSFUL') {
                            $newTransaction['payment_status']    = 'Paid';
                            $newTransaction['payment_collected'] = 'Yes';
                        }

                    } catch (\Exception $e) {
                        $this->log('MTN MoMo payment error: ' . $e->getMessage(), 'error');
                        $newTransaction['momo_payerID']     = $mtnPhone;
                        $newTransaction['momo_referenceID'] = null;
                        $newTransaction['momo_externalID']  = $order->order_number;
                    }
                }

                // Handle Wave
                elseif ($data['payment_method'] === 'Wave') {
                    try {
                        $waveResponse = $this->Wave->createCheckoutSession($order->order_number, $order->total_amount);

                        if (!empty($waveResponse['status']) && $waveResponse['status'] === 'success') {
                            $checkoutData = $waveResponse['data'];

                            $newTransaction['wave_checkout_sessionID'] = $checkoutData['id'] ?? null;
                            $newTransaction['wave_checkout_status']    = $checkoutData['checkout_status'] ?? 'processing';
                            $newTransaction['wave_client_referenceID'] = $checkoutData['client_reference'] ?? null;
                        }
                        // Payment stays pending until Wave webhook notifies
                    } catch (\Exception $e) {
                        $this->log('Wave API error: ' . $e->getMessage(), 'error');
                    }
                }

                // 3. Insert the new transaction
                $transactionEntity = $this->Transactions->newEntity($newTransaction);
                $this->Transactions->save($transactionEntity);

            }

            // Only update order_items if NOT paid
            $isPaid = !empty($order->transactions[0]) && $order->transactions[0]->payment_status === 'Paid';

            if (!$isPaid) {
                // Delete all existing order_items for this order
                $this->Orders->OrderItems->deleteAll(['order_id' => $order->id]);
            } else {
                // If paid, ignore order_items coming from request
                unset($data['order_items']);
            }

            // remove transactions/order_items completely
            unset($data['transactions']);
            // unset($data['order_items']);

            if (!empty($data['delivery_date'])) {
                // convert to datetime
                $data['delivery_date'] = date('Y-m-d H:i:s', strtotime($data['delivery_date']));
            }

            $order->edit_lock_by = null;
            $order->edit_lock_time = null;

            // store before patching
            $originalStatus = $order->status;

            $order = $this->Orders->patchEntity($order, $data);

            // if ($this->Orders->save($order)) {

            if ($this->Orders->save($order, [
                'associated' => [
                    'OrderItems'
                ]
            ])) {

                if (
                    isset($order->delivery_mode) &&
                    $order->delivery_mode === 'pickup' &&
                    isset($order->status) &&
                    strtolower($order->status) === 'approved' &&
                    !empty($order->showroom_id)
                ) {
                    $showroomsTable = $this->fetchTable('Showrooms');
                    $usersTable = $this->fetchTable('Users');
                    $orderItemsTable = $this->fetchTable('OrderItems');

                    $showroom = $showroomsTable->find()
                        ->select(['id', 'name', 'showroom_manager'])
                        ->where(['id' => $order->showroom_id])
                        ->first();

                    if ($showroom && $showroom->showroom_manager) {
                        $manager = $usersTable->find()
                            ->select(['id', 'email', 'first_name', 'last_name'])
                            ->where(['id' => $showroom->showroom_manager])
                            ->first();

                        if ($manager && $manager->email) {
                            $siteUrl = Router::url('/', true);
                            $logoUrl = $siteUrl . 'img/logo.png';

                            $managerName = trim(($manager->first_name ?? '') . ' ' . ($manager->last_name ?? '')) ?: __('Manager');
                            $greeting = __('Dear {0},', $managerName);

                            // Load order items
                            $orderItems = $orderItemsTable->find()
                                ->contain(['Products'])
                                ->where(['order_id' => $order->id])
                                ->all();

                            $orderItemsArr = [];
                            foreach ($orderItems as $item) {
                                $orderItemsArr[] = [
                                    'product_name' => $item->product->name ?? __('Product'),
                                    'quantity' => $item->quantity,
                                    'price' => $item->price,
                                    'total_price' => $item->price * $item->quantity,
                                    'thumb_image' => !empty($item->product->thumb_image) 
                                        ? $siteUrl . $item->product->thumb_image 
                                        : $siteUrl . 'img/default-product.png',
                                ];
                            }

                            $emailData = [
                                'greeting' => $greeting,
                                'message' => __("An order has been approved and assigned to your showroom."),
                                'site_url' => $siteUrl,
                                'logo_url' => $logoUrl,
                                'customer_name' => $managerName,
                                'order_number' => $order->order_number ?? $order->id,
                                'order_date' => $order->order_date ? $order->order_date->format('Y-m-d') : date('Y-m-d'),
                                'payment_method' => $order->payment_method ?? __('N/A'),
                                'order_type' => __('Pickup'),
                                'delivery_preference' => __('Showroom: ') . ($showroom->name ?? __('N/A')),
                                'order_items' => $orderItemsArr,
                                'subtotal' => $order->subtotal ?? 0,
                                'delivery_charge' => $order->delivery_charge ?? 0,
                                'discount' => $order->discount ?? 0,
                                'total' => $order->total_amount ?? 0,
                                'view_order_link' => $siteUrl . 'orders/view/' . $order->id,
                            ];

                            $subject = __("Order Approved - Order #{$order->order_number}");

                            $toEmails = [$manager->email];
                            // $adminEmail = Configure::read('AdminEmail') ?? '<EMAIL>';
                            // if (!in_array($adminEmail, $toEmails)) {
                            //     $toEmails[] = $adminEmail;
                            // }

                            $this->Global->send_email(
                                $toEmails,
                                null,
                                $subject,
                                'showroom_order_approved',
                                $emailData
                            );
                        }
                    }
                }

                // Only send email when status is CHANGED to Approved
                if (
                    $originalStatus !== 'Approved' &&
                    $order->status === 'Approved'
                ) {
                    if (!empty($order->customer) && !empty($order->customer->user) && !empty($order->customer->user->email)) {
                        $customerUser = $order->customer->user;

                        $customerName = trim(($customerUser->first_name ?? '') . ' ' . ($customerUser->last_name ?? '')) ?: __('Customer');
                        $customerGreeting = __('Dear {0},', $customerName);

                        // Build email data
                        $customerEmailData = [
                            'greeting'     => $customerGreeting,
                            'logo_url'     => $logoUrl,
                            'order_id'     => $order->id,
                            'order_number' => $order->order_number ?? $order->id,
                            'order_date'   => $order->order_date ? $order->order_date->format('Y-m-d') : date('Y-m-d'),
                        ];

                        // Subject for customer
                        $customerSubject = __("Your Order #{$order->order_number} Has Been Approved");

                        // Send customer email
                        $this->Global->send_email(
                            [$customerUser->email],
                            null,
                            $customerSubject,
                            'customer_order_approved',
                            $customerEmailData
                        );

                        // Send push notification to customer (if token exists)
                        if (!empty($customerUser->fcm_token)) {
                            $title = __("Order Approved");
                            $body = __("Your order #{$order->order_number} has been approved.");
                            $customData = [
                                'type' => 'order',
                                'action' => 'approved',
                                'order_id' => $order->id,
                                'order_number' => $order->order_number,
                            ];
                            $this->Global->sendNotification([$customerUser->fcm_token], $title, $body, $customData);
                        }

                    }
                }


                $response = ['success' => true, 'message' => 'The Order has been updated.'];
                $this->response = $this->response->withType('application/json');
                $this->response = $this->response->withStringBody(json_encode($response));
                $this->Flash->success(__('The order has been updated.'));
                return $this->response;
                // return $this->redirect(['action' => 'index']);
            } else {
                $this->log('data: ' . json_encode($order->getErrors()), 'debug');
                $this->Flash->error(__('The order could not be saved. Please, try again.'));
            }

        }
        // $customers = $this->Orders->Customers->find('list', limit: 200)->all();
        // $customerAddresses = $this->Orders->CustomerAddresses->find('list', limit: 200)->all();
        // $offers = $this->Orders->Offers->find('list', limit: 200)->all();
        // $showrooms = $this->Orders->Showrooms->find('list', limit: 200)->all();
        // $this->set(compact('order', 'customers', 'customerAddresses', 'offers', 'showrooms'));
    }

    public function clearEditLock()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $orderId = $this->request->getData('order_id');

        if ($orderId) {
            $order = $this->Orders->get($orderId);
            $order->edit_lock_by = null;
            $order->edit_lock_time = null;

            if ($this->Orders->save($order)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'success']));
            }
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['status' => 'error']));
    }


    /**
     * Delete method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    // public function delete($id = null)
    // {
    //     $this->request->allowMethod(['post', 'delete']);
    //     $order = $this->Orders->get($id);
    //     $response = ['success' => false, 'message' => 'The order could not be cancelled. Please, try again.'];
    //     if ($order) {
    //         if ($this->Orders->delete($order)) {
    //             $response = ['success' => true, 'message' => 'The order has been marked as Cancelled.'];
    //         } else {
    //             $response = ['success' => false, 'message' => 'The order could not be cancelled. Please, try again.'];
    //         }
    //     } else {
    //         $response = ['success' => false, 'message' => 'The order does not exist.'];
    //     }

    //     if ($this->request->is('ajax')) {
    //         $this->response = $this->response->withType('application/json');
    //         $this->response = $this->response->withStringBody(json_encode($response));
    //         return $this->response;
    //     } else {
    //         if ($response['success']) {
    //             $this->Flash->success($response['message']);
    //         } else {
    //             $this->Flash->error($response['message']);
    //         }
    //         return $this->redirect(['action' => 'index']);
    //     }
    // }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The order could not be cancelled. Please, try again.'];

        $order = $this->Orders->get($id, [
            'contain' => ['OrderItems']
        ]);

        if (!$order) {
            $response['message'] = 'The order does not exist.';
        } else {
            $user = $this->Authentication->getIdentity();
            $requestedBy = $user->id;

            $role = $this->Roles->get($user->role_id);

            $note = __('Cancelled by '.$role->name);
            $pickupRequired = __('No');
            $pickupCharge = 0;
            $return_to = null;
            $return_to_id = null;
            $customerAddressId = null;

            $hasError = false;

            foreach ($order->order_items as $item) {
                $returnAmount = (float)$item->quantity * (float)$item->price;

                $orderReturnData = [
                    'order_id' => $order->id,
                    'order_item_id' => $item->id,
                    'order_return_category_id' => 1, // you might want to pick a default cancellation reason here
                    'return_quantity' => $item->quantity,
                    'return_product_image' => null,
                    'request_type' => __('Cancellation'),
                    'status' => 'Pending',
                    'return_to' => $return_to,
                    'return_to_id' => $return_to_id,
                    'pickup_required' => $pickupRequired,
                    'pickup_charge' => $pickupCharge,
                    'note' => $note,
                    'requested_by' => $requestedBy,
                    'requested_at' => date('Y-m-d H:i:s'),
                    'return_amount' => $returnAmount,
                    'customer_address_id' => $customerAddressId,
                ];

                $orderReturn = $this->OrderReturns->newEntity($orderReturnData);

                if (!$this->OrderReturns->save($orderReturn)) {
                    $hasError = true;
                    break;
                }

                // Update order item
                $item->status = 'Pending Cancellation';
                $this->OrderItems->save($item);
            }

            if (!$hasError) {
                $order->status = 'Pending Cancellation';
                $this->Orders->save($order);

                // Reduce loyalty points for this cancelled order
                $orderSubtotal = (float)$order->subtotal_amount;
                $loyaltyResult = $this->Loyalty->reduceLoyaltyPointsForOrder(
                    $order->customer_id, 
                    $orderSubtotal, 
                    'Order cancellation'
                );
                

                $response = [
                    'success' => true,
                    'message' => 'The order and all items have been marked as Pending Cancellation.'
                ];
            } else {
                $response['message'] = 'Failed to save cancellation request for one or more items.';
            }
        }

        if ($this->request->is('ajax')) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function exportToCsv()
    {
        $OrderDetails = $this->Orders->getOrderDetails();
        // If merchant logged in, scope export
        if ($this->userType === 'Merchant' && !empty($this->merchantId)) {
            $OrderDetails->where(['Orders.merchant_id' => $this->merchantId]);
            $OrderDetails->innerJoinWith('OrderItems.Products')
                ->where(['Products.merchant_id' => $this->merchantId]);

        }
        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $header = ['ID', 'Order Number', 'Customer Name', 'Total Amount', 'Quantity', 'Order Status', 'Payment Method', 'Payment Status', 'Date & Time'];

        $this->response = $this->response->withType('text/csv');
        $this->response = $this->response->withDownload('orders_export.csv');
        $output = fopen('php://output', 'w');

        fputcsv($output, $header);

        foreach ($OrderDetails as $order) {
            fputcsv($output, [
                $order->id,
                (string)$order->order_number,
                $order->full_name . ' Customer Mo. No. ' . $order->phone_number,
                $order->total_amount,
                $order->quantity,
                isset($orderstatuses[$order->status]) ? $orderstatuses[$order->status] : '',
                $order->payment_method,
                isset($paymentstatuses[$order->transaction_status]) ? $paymentstatuses[$order->transaction_status] : '',
                $order->order_date ? $order->order_date->format($dateFormat . ' ' . $timeFormat) : ''
            ]);
        }

        fclose($output);
        return $this->response;
    }

    public function exportToExcel()
    {

        $orders = $this->Orders->getOrderDetails();
        if ($this->userType === 'Merchant' && !empty($this->merchantId)) {
            $orders->where(['Orders.merchant_id' => $this->merchantId]);
            $orders->innerJoinWith('OrderItems.Products')
                ->where(['Products.merchant_id' => $this->merchantId]);

        }
        $orders = $orders->all();
        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->response = $this->response->withType('application/vnd.ms-excel');
        $this->response = $this->response->withDownload('orders_export.xls');

        $output = '<table border="1">';
        $output .= '<tr>';
        $output .= '<th>ID</th>';
        $output .= '<th>Order Number</th>';
        $output .= '<th>Customer Name</th>';
        $output .= '<th>Total Amount</th>';
        $output .= '<th>Quantity</th>';
        $output .= '<th>Order Status</th>';
        $output .= '<th>Payment Method</th>';
        $output .= '<th>Payment Status</th>';
        $output .= '<th>Date & Time</th>';
        $output .= '</tr>';

        foreach ($orders as $order) {
            $output .= '<tr>';
            $output .= '<td>' . h($order->id) . '</td>';
            $output .= '<td>' . h((string)$order->order_number) . '</td>';
            $output .= '<td>' . h($order->full_name . ' Customer Mo. No. ' . $order->phone_number) . '</td>';
            $output .= '<td>' . h($order->total_amount) . '</td>';
            $output .= '<td>' . h($order->quantity) . '</td>';
            $output .= '<td>' . h(isset($orderstatuses[$order->status]) ? $orderstatuses[$order->status] : '') . '</td>';
            $output .= '<td>' . h($order->payment_method) . '</td>';
            $output .= '<td>' . h(isset($paymentstatuses[$order->transaction_status]) ? $paymentstatuses[$order->transaction_status] : '') . '</td>';
            $output .= '<td>' . h($order->order_date ? $order->order_date->format($dateFormat . ' ' . $timeFormat) : '') . '</td>';
            $output .= '</tr>';
        }

        $output .= '</table>';

        echo $output;

        return $this->response;
    }

    public function getCustomerOrders()
    {
        $customer_id = $this->request->getQuery('customer_id');

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $orders = $this->Orders->find()
            ->where(['Orders.customer_id' => $customer_id])
            ->select([
                'id' => 'Orders.id',
                'order_number' => 'Orders.order_number',
                'total_amount' => 'Orders.total_amount',
                'status' => 'Orders.status',
                'payment_method' => 'Orders.payment_method',
                'order_date' => 'Orders.order_date',
                'quantity' => $this->Orders->OrderItems->find()
                    ->func()
                    ->sum('OrderItems.quantity'),
                'payment_status' => 'Transactions.payment_status',
            ])
            ->leftJoinWith('OrderItems')
            ->leftJoinWith('Transactions')
            ->group('Orders.id')
            ->enableAutoFields(false)
            ->toArray();

        foreach ($orders as &$order) {
            $orderDate = $order['order_date'];
            $formattedDate = $orderDate->format($dateFormat . ' ' . $timeFormat);
            $order['order_date'] = $formattedDate;
        }

        $response = ['orders' => $orders];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function downloadInvoice($id = null)
    {
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'CustomerAddresses' => ['Cities', 'Municipalities'],
                'Offers',
                'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductVariants' => [
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ]
                ],
                'Transactions'
            ]
        ]);
        $isMerchantOrder = false;
        // If a merchant is logged in, ensure they can access this order
        if ($this->userType === 'Merchant' && !empty($this->merchantId)) {
            $isMerchantOrder = $this->Orders->OrderItems->find()
                ->matching('Products', function ($q) {
                    return $q->where(['Products.merchant_id' => $this->merchantId]);
                })
                ->where(['OrderItems.order_id' => $id])
                ->count() > 0;

            if (!$isMerchantOrder) {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'noaccess']);
            }
        }

        $customer_care_no = $this->SiteSettings->find()
            ->select(['customer_support_no'])
            ->first()->customer_support_no;
        $customer_care_email = $this->SiteSettings->find()
            ->select(['support_email'])
            ->first()->support_email;
        $call_center_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;
        $whatsapp_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;
        $after_sales_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;

        // Assign the CloudFront URL to product images
        foreach ($order->order_items as $item) {
            if (!empty($item->product->product_images)) {
                foreach ($item->product->product_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
            if (!empty($item->product_variant->product_variant_images)) {
                foreach ($item->product_variant->product_variant_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        $orderStatusMap = Configure::read('Constants.ORDER_STATUSES_MAP');
        $orderStatusProgress  = Configure::read('Constants.ORDER_STATUS_PROGRESS_COLOR');
        $orderStatusProgressBar  = Configure::read('Constants.ORDER_STATUS_PROGRESS_BAR');

        $paymentStatusProgress  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_COLOR');
        $paymentStatusProgressBar  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_BAR');

        $shippedStatusProgress  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_COLOR');
        $shippedStatusProgressBar  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_BAR');

        $deliveryStatusProgress  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_COLOR');
        $deliveryStatusProgressBar  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_BAR');

        $orderStatus = Configure::read('Constants.ORDER_STATUSES');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $orderItemCount = $this->Orders->OrderItems->find()
            ->where(['order_id' => $id])
            ->count();

        // $this->log('test'.$orderStatusProgress,'debug');
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('order', 'currencySymbol', 'orderItemCount', 'orderStatusMap', 'orderStatus', 'orderStatusProgress', 'orderStatusProgressBar', 'paymentStatusProgressBar', 'paymentStatusProgress', 'shippedStatusProgress', 'shippedStatusProgressBar', 'deliveryStatusProgress', 'deliveryStatusProgressBar', 'customer_care_no', 'customer_care_email', 'call_center_no', 'whatsapp_no', 'after_sales_no', 'thousandSeparator', 'decimalSeparator', 'isMerchantOrder'));
        $this->render('invoice');
    }

    function CalculateEstimatedDelivery($deliveryMode)
    {
        $now = new DateTime();
        $currentHour = (int)$now->format('H');

        $siteSettings = $this->SiteSettings->find()
            ->select(['express_delivery_order_cutoff_time'])
            ->first();
        $express_delivery_order_cutoff_time = (int)$siteSettings->express_delivery_order_cutoff_time;

        if ($deliveryMode === 'express') {
            if ($currentHour >= $express_delivery_order_cutoff_time) {
                $now->modify('+1 day'); // Move to next day if after cutoff
            }
        } else {
            $now->modify('+2 days'); // Standard delivery in 48 hours
        }

        return $now->format('Y-m-d');
    }

    // public function printInvoicePdf()
    // {
    //     $this->request->allowMethod(['post']); // Only allow POST requests
    //     $htmlContent = $this->request->getData('html');

    //     if (empty($htmlContent)) {
    //         return $this->response->withType('application/json')->withStringBody(json_encode([
    //             'status' => 'error',
    //             'message' => 'No HTML content received'
    //         ]));
    //     }

    //     try {
    //         // Initialize MPDF with portrait mode (default)
    //         $mpdf = new Mpdf([
    //             'mode' => 'utf-8',
    //             'format' => 'A4', // A4 portrait
    //             'orientation' => 'P' // "P" for Portrait, "L" for Landscape
    //         ]);

    //         // Read all CSS files from webroot/css directory
    //         $cssDirectory = WWW_ROOT . 'css' . DS;
    //         $cssFiles = glob($cssDirectory . '*.css'); // Get all CSS files
    //         $allCss = '';

    //         foreach ($cssFiles as $file) {
    //             $allCss .= file_get_contents($file) . "\n"; // Append CSS content
    //         }

    //         // Apply styles
    //         $mpdf->WriteHTML($allCss, \Mpdf\HTMLParserMode::HEADER_CSS);
    //         $mpdf->WriteHTML($htmlContent, \Mpdf\HTMLParserMode::HTML_BODY);

    //         // Output PDF
    //         $pdfFileName = "Invoice_" . time() . ".pdf";
    //         $mpdf->Output($pdfFileName, "I"); // Open in browser

    //     } catch (\Exception $e) {
    //         return $this->response->withType('application/json')->withStringBody(json_encode([
    //             'status' => 'error',
    //             'message' => $e->getMessage()
    //         ]));
    //     }

    //     return $this->response->withType('application/pdf');
    // }

    public function printInvoicePdf($id)
    {
        $this->autoRender = false;
        $this->request->allowMethod(['get']);

        $identity = $this->request->getAttribute('identity'); // logged-in user

        if ($identity && $identity->user_type === 'Customer') {
            // 1. Get customer_id from Customers table
            $customer = $this->Orders->Customers->find()
                ->select(['id'])
                ->where(['user_id' => $identity->id])
                ->first();

            if (!$customer) {
                throw new \Cake\Http\Exception\ForbiddenException(__('Access denied. Customer not found.'));
            }

            // 2. Check if order belongs to this customer
            $orderExists = $this->Orders->exists([
                'id' => $id,
                'customer_id' => $customer->id
            ]);

            if (!$orderExists) {
                throw new \Cake\Http\Exception\ForbiddenException(__('Access denied. You do not have permission to view this invoice.'));
            }
        }

        // 1. Get order with associations
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'CustomerAddresses' => ['Cities', 'Municipalities'],
                'Offers',
                'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductVariants' => [
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ]
                ],
                'Transactions'
            ]
        ]);
        $isMerchantOrder = false;
        // If a merchant is logged in, ensure they can access this order
        if ($this->userType === 'Merchant' && !empty($this->merchantId)) {
            $isMerchantOrder = $this->Orders->OrderItems->find()
                ->matching('Products', function ($q) {
                    return $q->where(['Products.merchant_id' => $this->merchantId]);
                })
                ->where(['OrderItems.order_id' => $id])
                ->count() > 0;

            if (!$isMerchantOrder) {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'noaccess']);
            }
        }
        // 2. Site settings
        $customer_care_no    = $this->SiteSettings->find()->select(['customer_support_no'])->first()->customer_support_no;
        $customer_care_email = $this->SiteSettings->find()->select(['support_email'])->first()->support_email;
        $call_center_no      = $this->SiteSettings->find()->select(['contact_no'])->first()->contact_no;
        $whatsapp_no         = $call_center_no;
        $after_sales_no      = $call_center_no;

        // 3. Currency & formatting
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '';
        $thousandSeparator = $currencyConfig['thousand_separator'] ?? '';

        $orderItemCount = $this->Orders->OrderItems->find()
            ->where(['order_id' => $id])
            ->count();

        // 4. CloudFront URLs for product images
        foreach ($order->order_items as $item) {
            if (!empty($item->product->product_images)) {
                foreach ($item->product->product_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
            if (!empty($item->product_variant->product_variant_images)) {
                foreach ($item->product_variant->product_variant_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        // 5. Render view to HTML without layout
        $view = new View();
        $view->disableAutoLayout();
        $view->set(compact(
            'order',
            'customer_care_no',
            'customer_care_email',
            'call_center_no',
            'whatsapp_no',
            'after_sales_no',
            'currencySymbol',
            'decimalSeparator',
            'thousandSeparator',
            'orderItemCount',
            'isMerchantOrder'
        ));
        $html = $view->render('Invoices/pdf_invoice');

        // 6. Generate PDF
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_top' => 10,
            'margin_bottom' => 10,
            'margin_left' => 10,
            'margin_right' => 10
        ]);

        // 7. Load CSS from /webroot/css
        $cssDirectory = WWW_ROOT . 'css' . DS;
        $cssFiles = glob($cssDirectory . '*.css');
        $allCss = '';
        foreach ($cssFiles as $file) {
            $allCss .= file_get_contents($file) . "\n";
        }

        // 8. Apply styles
        $mpdf->WriteHTML($allCss, \Mpdf\HTMLParserMode::HEADER_CSS);
        $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);

        // 9. Output PDF inline
        $mpdf->Output("Invoice-{$id}.pdf", 'I');
    }

    public function approveOrder($id = null)
    {

        $this->request->allowMethod(['post']);
        $id = $this->request->getData('id');
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'OrderItems'
            ]
        ]);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            $order->status = 'Approved';

            if ($this->Orders->save($order)) {
                $this->WebsiteFunction->OrderApprovedEmail($order->id);
                $deviceToken = $order->customer->user->fcm_token;

                if (!empty($deviceToken)) {
                    $title = 'Order Approved - #' . $order['order_number'];
                    $body  = 'Your order has been approved and is now being processed.';
                    $customData = [
                        "notification_type" => "order",
                        "id" => (string)$order->id,
                        "category" => "order"
                    ];
                    $response = $this->Global->sendNotification(
                        [$deviceToken],
                        $title,
                        $body,
                        $customData
                    );
                }
                $response = [
                    'status' => 'success',
                    'message' => __('Order has been approved.')
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to approve the order. Please try again.')
                ];
            }
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function approveCancellation($id = null)
    {
        $this->request->allowMethod(['post']);

        $id = $this->request->getData('id');
        $condition = $this->request->getData('condition');
        // $order_cancellation_category_id = $this->request->getData('order_cancellation_category_id');
        // $reason = $this->request->getData('reason');

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'OrderItems'
            ]
        ]);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            if ($condition == 'Approved') {
                $order->status = 'Cancelled';
            } else if ($condition == 'Rejected') {
                $order->status = 'Cancellation Rejected';
            } else {
                $order->status = 'Pending Cancellation';
            }


            if ($this->Orders->save($order)) {
                $lastOrderCancellation = null;

                foreach ($order->order_items as $item) {
                    $orderItem = $this->OrderItems
                        ->find()
                        ->where(['id' => $item->id])
                        ->first();
                    if ($orderItem) {
                        if ($condition == 'Approved') {
                            $orderItem->status = 'Cancelled';
                        } else if ($condition == 'Rejected') {
                            $orderItem->status = 'Cancellation Rejected';
                        } else {
                            $orderItem->status = 'Pending Cancellation';
                        }
                        $this->OrderItems->save($orderItem);
                    }
                    $orderCancellation = $this->OrderCancellations
                        ->find()
                        ->where(['order_item_id' => $item->id])
                        ->first();

                    if ($orderCancellation) {
                        if ($condition == 'Approved') {
                            $orderCancellation->status = 'Approved';
                        } else if ($condition == 'Rejected') {
                            $orderCancellation->status = 'Rejected';
                        } else {
                            $orderCancellation->status = 'Pending';
                        }

                        $orderCancellation->canceled_at = new DateTime();
                        // $orderCancellation = $this->OrderCancellations->newEmptyEntity();
                        // $orderCancellation->order_item_id = $item->id;
                        // $orderCancellation->order_id = $order->id;
                        // $orderCancellation->order_cancellation_category_id = $order_cancellation_category_id;
                        // $orderCancellation->reason = $reason;
                        // $orderCancellation->customer_id = $order->customer_id;
                        if ($this->OrderCancellations->save($orderCancellation)) {
                            $lastOrderCancellation = $orderCancellation; // Store last saved record
                        }
                    }
                }
                if ($lastOrderCancellation) {
                    $this->sendCancelledEmails($order, $lastOrderCancellation, $condition);
                    if ($condition == 'Approved') {
                        $this->WebsiteFunction->OrderCancellationApprovedEmail($order->id);
                        $deviceToken = $order->customer->user->fcm_token;;

                        if (!empty($deviceToken)) {
                            $title = 'Order Cancellation Approved - #' . $order['order_number'];
                            $body  = 'Your order cancellation has been approved and is now being processed.';
                            $customData = [
                                "notification_type" => "order",
                                "id" => (string)$order->id,
                                "category" => "order"
                            ]; // Optional

                            $response = $this->Global->sendNotification(
                                [$deviceToken],
                                $title,
                                $body,
                                $customData
                            );
                        }
                        $response = [
                            'status' => 'success',
                            'message' => __('Order cancellation has been approved.')
                        ];
                    } else if ($condition == 'Rejected') {
                        $this->WebsiteFunction->OrderCancellationRejectedEmail($order->id);
                        $deviceToken = $order->customer->user->fcm_token;;

                        if (!empty($deviceToken)) {
                            $title = 'Order Cancellation Rejected - #' . $order['order_number'];
                            $body  = 'Your order cancellation has been rejected.';
                            $customData = [
                                "notification_type" => "order",
                                "id" => (string)$order->id,
                                "category" => "order"
                            ]; // Optional

                            $response = $this->Global->sendNotification(
                                [$deviceToken],
                                $title,
                                $body,
                                $customData
                            );
                        }
                        $response = [
                            'status' => 'success',
                            'message' => __('Order cancellation has been rejected.')
                        ];
                    }
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => __('Order was not cancelled before and email not sent to client.')
                    ];
                }
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to cancel the order. Please try again.')
                ];
            }
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }


    private function sendCancelledEmails($order, $orderCancellation, $condition)
    {
        if ($order && $orderCancellation) {
            $toEmails = [$order->customer->user->email];

            if (empty($toEmails)) {
                \Cake\Log\Log::warning("No valid recipients found for order ID: {$order->id}");
                return;
            }

            $emailData = [
                'order_number' => $order->order_number,
                'customer_name' => $order->customer->user->first_name . ' ' . $order->customer->user->last_name,
                'reason' => $orderCancellation->reason,  // Fixed double '$' typo
                'comment' => $orderCancellation->reason, // Fixed double '$' typo
                'canceled_at' => $orderCancellation->canceled_at, // Fixed double '$' typo & incorrect field name
                'status' => $orderCancellation->status
            ];

            $subject = "Order #{$order->order_number} Cancellation has been " . $condition;

            $this->Global->send_email(
                $toEmails,
                null,
                $subject,
                'order_cancellation',
                $emailData
            );
        }
    }

    public function approveReturn($id = null)
    {
        $this->request->allowMethod(['post']);

        $id = $this->request->getData('id');
        $condition = $this->request->getData('condition');
        // $order_cancellation_category_id = $this->request->getData('order_cancellation_category_id');
        // $reason = $this->request->getData('reason');

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'OrderItems'
            ]
        ]);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            if ($condition == 'Approved') {
                $order->status = 'Return Approved';
            } else if ($condition == 'Rejected') {
                $order->status = 'Return Rejected';
            } else {
                $order->status = 'Pending Return';
            }


            if ($this->Orders->save($order)) {
                $lastOrderRejected = null;

                foreach ($order->order_items as $item) {
                    $orderItem = $this->OrderItems
                        ->find()
                        ->where(['id' => $item->id])
                        ->first();
                    if ($orderItem) {
                        if ($condition == 'Approved') {
                            $orderItem->status = 'Return Approved';
                        } else if ($condition == 'Rejected') {
                            $orderItem->status = 'Return Rejected';
                        } else {
                            $orderItem->status = 'Pending Return';
                        }
                        $this->OrderItems->save($orderItem);
                    }
                    $orderReturn = $this->OrderReturns
                        ->find()
                        ->where(['order_item_id' => $item->id])
                        ->first();

                    if ($orderReturn) {
                        if ($condition == 'Approved') {
                            $orderReturn->status = 'Approved';
                        } else if ($condition == 'Rejected') {
                            $orderReturn->status = 'Rejected';
                        } else {
                            $orderReturn->status = 'Pending';
                        }

                        $orderReturn->processed_at = new DateTime();
                        // $orderCancellation = $this->OrderCancellations->newEmptyEntity();
                        // $orderCancellation->order_item_id = $item->id;
                        // $orderCancellation->order_id = $order->id;
                        // $orderCancellation->order_cancellation_category_id = $order_cancellation_category_id;
                        // $orderCancellation->reason = $reason;
                        // $orderCancellation->customer_id = $order->customer_id;
                        if ($this->OrderReturns->save($orderReturn)) {
                            $lastOrderRejected = $orderReturn; // Store last saved record
                        }
                    }
                }
                if ($lastOrderRejected) {
                    $this->sendReturnedEmails($order, $lastOrderRejected, $condition);
                    if ($condition == 'Approved') {
                        $response = [
                            'status' => 'success',
                            'message' => __('Order return has been approved.')
                        ];
                    } else if ($condition == 'Rejected') {
                        $response = [
                            'status' => 'success',
                            'message' => __('Order return has been rejected.')
                        ];
                    }
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => __('Order was not returned before and email not sent to client.')
                    ];
                }
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to retunr the order. Please try again.')
                ];
            }
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }
    private function sendReturnedEmails($order, $orderReturn, $condition)
    {
        if ($order && $orderReturn) {
            $toEmails = [$order->customer->user->email];

            if (empty($toEmails)) {
                \Cake\Log\Log::warning("No valid recipients found for order ID: {$order->id}");
                return;
            }

            $emailData = [
                'order_number' => $order->order_number,
                'customer_name' => $order->customer->user->first_name . ' ' . $order->customer->user->last_name,
                'reason' => $orderReturn->reason,
                'requested_at' => $orderReturn->requested_at,
                'processed_at' => $orderReturn->processed_at,
                'status' => $orderReturn->status
            ];

            $subject = "Order #{$order->order_number} Return has been " . $condition;

            $this->Global->send_email(
                $toEmails,
                null,
                $subject,
                'order_cancellation',
                $emailData
            );
        }
    }

    public function checkCodAvailability()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $data = $this->request->getData();

        $cityId = $data['city_id'] ?? null;
        $abidjanCityId = $data['abidjanCityId'] ?? null;
        $productIds = $data['product_ids'] ?? [];

        if (!$cityId || empty($productIds)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Invalid request: missing city or products.'),
                    'unavailable_products' => []
                ]));
        }

        // Fetch all selected products
        $products = $this->Products->find()
            ->select(['id', 'name', 'COD_in_city', 'COD_out_city'])
            ->where(['id IN' => $productIds])
            ->all();

        $unavailableProducts = [];

        foreach ($products as $product) {
            // If COD_in_city = 1 and COD_out_city != 1 and city != Abidjan => block
            $isBlocked =
                ($product->COD_in_city == 1) &&
                ($product->COD_out_city != 1) &&
                ($cityId != $abidjanCityId);

            if ($isBlocked) {
                $unavailableProducts[] = $product->name;
            }
        }

        if (!empty($unavailableProducts)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Some products are not available for Cash on Delivery outside Abidjan.'),
                    'unavailable_products' => $unavailableProducts
                ]));
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'status' => 'success',
                'message' => __('Cash on Delivery is available for selected products.'),
                'unavailable_products' => []
            ]));
    }


}
