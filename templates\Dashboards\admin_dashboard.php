<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
    }

    .pending_actions {
    min-height: max-content;
    }

    .content {
        max-height: 250px;
        overflow-y:scroll;
    }

</style>
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
<?php $this->end(); ?>

<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
        </ul>
    </div>

    <div class="section-body1">
        <div class="container-fluid">
            <?= $this->Flash->render() ?>
        </div>
    </div>

    <div class="row mt-5 pe-3 date_picker">
        <div class="col-sm-3 m-r-50">
        </div>

        <div class="col-sm-7 text-end">
            <div class="row align-items-center mb-2">
                <div class="col-md-7">

                </div>
                <div class="col-sm-5">
                    <div>
                        <div class="col-sm-7">
                        </div>
                        <div class="col-sm-9 m-l-70">
                            <select id="date-period" class="form-select m-l-100" onchange="handleChange(this)">
                                <option value="current_month"><?= __('Current Month') ?></option>
                                <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                                <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                                <option value="current_year"><?= __('Current Year') ?></option>
                                <option value="4"><?= __('Custom') ?></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row align-items-center mb-2 d-none" id="datesappear">
                <form id="dateRangeForm" class="d-flex">
                    <div class="col-sm-3">
                        <label for="from-date" class="col-form-label fw-400 m-r-10"><?= __('From Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="from-date" name="from-date" />
                    </div>
                    <div class="col-sm-3">
                        <label for="to-date" class="col-form-label fw-400 m-r-10"><?= __('To Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="to-date" name="to-date" />
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-primary btn-sm" type="submit"><?= __('Submit') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="section-body mt-4">
        <div class="container-fluid">
            <div class="row ">
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style1 dashboard_box">
                        <div class="card-statistic-3 bg1">
                            <div class="card-icon card-icon-large"><i class="fa fa-award"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Number of Orders') ?></h4>
                                <p><strong><?= __('Online Orders') ?>:</strong> <span id="onlineOrders"><?php echo !empty($onlineOrders) ? h($onlineOrders).' No.s' : '0 No.s'; ?></span></p>
                                <p><strong><?= __('Showroom Orders') ?>:</strong> <span id="showroomOrders"><?php echo !empty($showroomOrders) ? h($showroomOrders).' No.s' : '0 No.s'; ?></span></p>
                                <p><strong><?= __('Call Center Orders') ?>:</strong> <span id="callCenterOrders"><?php echo !empty($callCenterOrders) ? h($callCenterOrders).' No.s' : '0 No.s'; ?></span></p>
                                <span id="totalOrders"><?php echo !empty($totalOrders) ? h($totalOrders).' No.s' : '0 No.s'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="orderProgressBar" role="progressbar" data-width="<?= h($percentageOrders) ?>%"
                                        aria-valuenow="<?= h($percentageOrders) ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalSalesAmount"><?php echo !empty($totalSalesAmount) ? h(number_format((float)$totalSalesAmount, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) : h(number_format((float)0, 0, '', $thousandSeparator)). ' ' . h($currencySymbol); ?></span></span>
                                    <span class="text-nowrap" style="opacity: 0;" id="userText"><?= __('New users since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style2 dashboard_box">
                        <div class="card-statistic-3 bg2">
                            <div class="card-icon card-icon-large"><i class="fa fa-briefcase"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Users') ?></h4>
                                <span id="totalUsers"><?php echo !empty($totalUsers) ? h($totalUsers) : '0'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="userProgressBar" role="progressbar" data-width="<?= $newUsersPercentage ?>%"
                                        aria-valuenow="<?= $newUsersPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewUsers"><?php echo !empty($newUsers) ? h($newUsers) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="userText"><?= __('New users since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style3 dashboard_box">
                        <div class="card-statistic-3 bg3">
                            <div class="card-icon card-icon-large"><i class="fa fa-globe"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Products') ?></h4>
                                <span id="totalProducts"><?php echo !empty($totalActiveProducts) ? h($totalActiveProducts) : '0'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="productProgressBar" role="progressbar" data-width="<?= $newProductsPercentage ?>%"
                                        aria-valuenow="<?= $newProductsPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewProducts"><?php echo !empty($newProducts) ? h($newProducts) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="productText"><?= __('New products since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style4 dashboard_box">
                        <div class="card-statistic-3 bg4">
                            <div class="card-icon card-icon-large"><i class="fa fa-money-bill-alt"></i>
                            </div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Showrooms') ?></h4>
                                <span id="totalShowrooms"><?php echo !empty($totalShowrooms) ? h($totalShowrooms) : '0'; ?> <?= __('Showrooms') ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="showroomProgressBar" role="progressbar" data-width="<?= $newShowroomsPercentage ?>%"
                                        aria-valuenow="<?= $newShowroomsPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewShowrooms"><?php echo !empty($newShowrooms) ? h($newShowrooms) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="showroomText"><?= __('New showrooms since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12 col-lg-6 col-xl-6">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Total Orders this year') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart7"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-6">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Payment Methods') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart8"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-6">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Pickup and Deliveries') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart9"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-6">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Refunds and Returns') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart10"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12 col-lg-6 col-xl-6 ">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Order Trends') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="order_trends_chart"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-6 ">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Revenues and Expenses Trends') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="revenue_chart"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row m-t-10">
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('ONLINE ORDERS') ?></h4>
                        </div>
                        <div class="card-body product_categories" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border" style="position: relative; max-height: 320px;" [perfectScrollbar]>
                                <?php
                                    $total = $totalOnlineOrders ?: 1; // avoid divide by zero
                                    $webOrders = $onlineStats->online_web_order_count;
                                    $mobileOrders = $onlineStats->online_mobile_order_count;
                                    $socialOrders = $onlineStats->online_social_order_count;
                                    $accounts = $onlineStats->online_accounts_created;

                                    $webPercent = round(($webOrders / $total) * 100);
                                    $mobilePercent = round(($mobileOrders / $total) * 100);
                                    $socialPercent = round(($socialOrders / $total) * 100);
                                ?>
                                <li class="product-list">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15"><?= __('Number of accounts created') ?></div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square bg-success" style="width: 100%;"></div>
                                                <div class="budget-price-label"><?= h($accounts) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15"><?= __('Number of Website orders') ?></div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square bg-success" style="width: <?= $webPercent ?>%;"></div>
                                                <div class="budget-price-label"><?= h($webOrders) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15"><?= __('Number of Mobile App orders') ?></div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square bg-info" style="width: <?= $mobilePercent ?>%;"></div>
                                                <div class="budget-price-label"><?= h($mobileOrders) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15"><?= __('Number of order via Social Network') ?></div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square bg-warning" style="width: <?= $socialPercent ?>%;"></div>
                                                <div class="budget-price-label"><?= h($socialOrders) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('SHOWROOM ORDERS') ?></h4>
                        </div>
                        <div class="card-body product_categories" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border"
                                style="position: relative; max-height: 260px;overflow-y:scroll" [perfectScrollbar]>
                                <?php foreach ($allShowroomOrders as $order): ?>
                                <li class="product-list">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15"><?= h($order['showroom_name']) ?></div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: <?= $order['percentage'] ?>%;"></div>
                                                <div class="budget-price-label"><?= h($order['order_count']) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-lg-4 col-xl-4">
                    <div class="row">
                    <div class="col-12">
    <div class="pending_actions mb-3">
        <div class="card">
            <!-- Supplier Pending Bills -->
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <?= __('Supplier Pending Bills') ?>
                </h4>
                <a class="text-decoration-none" data-bs-toggle="collapse" href="#supplierPendingBills" role="button" aria-expanded="false" aria-controls="supplierPendingBills">
                    <i class="bi bi-chevron-down"></i> <!-- Bootstrap Icon -->
                </a>
            </div>

            <!-- Calculate total pending amount -->
            <?php 
                $totalPendingAmount = 0;
                foreach ($supplier_payment_pendings as $supplier) {
                    $totalPendingAmount += (float) str_replace(',', '', $supplier['pending_amount']);
                }
            ?>

            <div id="supplierPendingBills" class="collapse">
                <div class="content">

                    <!-- Display Pending Total -->
                    <div class="mb-3">
                        <ul>
                        <strong><?= __('Pending Total: ') ?></strong>
                        <span>
                            <?= h(number_format($totalPendingAmount, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?>
                        </span>
                        </ul>
                    </div>


                    <ul>
                        <?php foreach ($supplier_payment_pendings as $supplier): ?>
                            <li style="width:200px">
                                <b><?= h($supplier['supplier_name']) ?> - <?= h($supplier['bill_number']) ?> (<?= h(number_format((float)$supplier['pending_amount'], 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?>) - </b>
                                <?= __('Pending') ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>

            <!-- Stock Pendings -->
            <div class="card-header d-flex justify-content-between align-items-center mt-3">
                <h4 class="mb-0 mt-0">
                    <?= __('Stock Pendings') ?>
                </h4>
                <a class="text-decoration-none" data-bs-toggle="collapse" href="#stockPendings" role="button" aria-expanded="false" aria-controls="stockPendings">
                    <i class="bi bi-chevron-down"></i> <!-- Bootstrap Icon -->
                </a>
            </div>
            <div id="stockPendings" class="collapse">
                <div class="content">
                    <ul>
                        <li><b><?= __('Stock Return') ?> - </b><?= __('Approval Pending') ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
document.querySelectorAll('[data-bs-toggle="collapse"]').forEach((toggle) => {
    toggle.addEventListener('click', function () {
        const icon = this.querySelector('i');
        icon.classList.toggle('bi-chevron-down');
        icon.classList.toggle('bi-chevron-up');
    });
});
</script>

                        <div class="col-12">
                        <div class="pending_actions">
    <div class="card">
        <!-- Card Header with Collapse Icon -->
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?= __('Refunds Pending and Returns Pending') ?></h4>
            <a class="text-decoration-none" data-bs-toggle="collapse" href="#refundsReturnsSection" role="button" aria-expanded="false" aria-controls="refundsReturnsSection">
                <i class="bi bi-chevron-down"></i> <!-- Bootstrap Icon -->
            </a>
        </div>

        <!-- Collapsible Content -->
        <div id="refundsReturnsSection" class="collapse">
            <div class="content">
                <ul>
                    <li><b><?= __('Refunds Pending') ?> - </b><?= __('100') ?></li>
                    <li><b><?= __('Returns Pending') ?> - </b><?= __('200') ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-8 col-xl-8">
                <div class="card supplier_payments">
    <!-- Card Header with Collapse Icon -->
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="mb-0 mt-0"><?= __('Supplier Payments') ?></h4>
        <a class="text-decoration-none" data-bs-toggle="collapse" href="#supplierPaymentsTable" role="button" aria-expanded="false" aria-controls="supplierPaymentsTable">
            <i class="bi bi-chevron-down"></i> <!-- Bootstrap Icon -->
        </a>
    </div>

    <!-- Collapsible Table Content -->
    <div id="supplierPaymentsTable" class="collapse">
        <div class="card-body">
            <div class="media-list position-relative">
                <div class="table-responsive" tabindex="1" style="height: 300px; overflow-y: scroll; outline: none; touch-action: none;">
                    <table class="table table-hover table-xl mb-0">
                        <thead>
                            <tr>
                                <th><?= __('Supplier Name') ?></th>
                                <th><?= __('Amount') ?></th>
                                <th><?= __('Status') ?></th>
                                <th><?= __('Total Payment') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($supplier_payment as $payment): ?>
                            <tr>
                                <td class="text-truncate">
                                    <?= !empty($payment->supplier->name) ? h($payment->supplier->name) : 'N/A'; ?>
                                </td>
                                <td class="text-truncate">
                                    <?php
                                        $paidAmount = number_format((float)$payment->amount, 0, '', $thousandSeparator) . ' ' . h($currencySymbol);
                                        echo $paidAmount;
                                    ?>
                                </td>
                                <td class="text-truncate">
                                    <?php
                                    $paymentStatusMap = [
                                        __('Paid') => ['label' => __('Paid'), 'class' => 'col-green'],
                                        __('Partially Paid') => ['label' => __('Partially Paid'), 'class' => 'col-orange'],
                                        __('Pending') => ['label' => __('Pending'), 'class' => 'col-red']
                                    ];

                                    $payment_status = $paymentStatusMap[$payment->supplier_purchase_order->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                    ?>
                                    <div class="badge-outline <?= $payment_status['class'] ?>">
                                        <?= h($payment_status['label']) ?>
                                    </div>
                                </td>
                                <td><?php
                                        $paidAmount = number_format((float)$payment->amount, 0, '', $thousandSeparator) . ' ' . h($currencySymbol);
                                        if (
                                            isset($payment->supplier_purchase_order->payment_status)
                                            && $payment->supplier_purchase_order->payment_status === 'Partially Paid'
                                            && isset($payment->total_order_amount)
                                        ) {
                                            $totalAmount = number_format((float)$payment->total_order_amount, 0, '', $thousandSeparator) . ' ' . h($currencySymbol);
                                            echo "{$totalAmount}";
                                        } else {
                                            echo $paidAmount;
                                        }
                                    ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

                </div>
            </div>

            <div class="row m-t-10">
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Top 5 Products') ?></h4>
                        </div>
                        <div class="card-footer pt-3 d-flex justify-content-center">
                            <div class="budget-price justify-content-center">
                                <div class="budget-price-square" data-width="30"></div>
                                <div class="budget-price-label"><?= __('Online') ?></div>
                            </div>
                            <div class="budget-price justify-content-center">
                                <div class="budget-price-square product_cost" data-width="30"></div>
                                <div class="budget-price-label"><?= __('Showrooms') ?></div>
                            </div>
                        </div>
                        <div class="card-body top_products" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border" style="position: relative;max-height: 258px;overflow-y: scroll;" [perfectScrollbar]>
                                <!-- Online Products -->
                                <?php foreach ($topSellingProducts['online'] as $product): ?>
                                    <li class="product-list">
                                        <?php

                                        // Check if the product has an image
                                        $imagePath = !empty($product->product->product_images) ? $product->product->product_images[0]->image : 'default-product-image.png';
                                        ?>
                                        <img class="msr-3 rounded" width="50" src="<?= $imagePath ?>" alt="product">
                                        <div class="set-flex">
                                            <div class="float-end">
                                                <div class="font-weight-600 text-muted text-small"><?= $product->total_units_sold ?> Sales</div>
                                            </div>
                                            <div class="fw-bold font-15"><?= h($product->product->name) ?></div>
                                            <div class="mt-1">
                                                <div class="budget-price">
                                                    <div class="budget-price-square" style="width: <?= $product->total_sales_amount / 1000 ?>%;"></div>
                                                    <div class="budget-price-label"><?= h(number_format((float)$product->total_sales_amount, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                                </div>
                                                <div class="budget-price">
                                                    <div class="budget-price-square product_cost" style="width: <?= $product->total_sales_amount * 0.75 / 1000 ?>%;"></div>
                                                    <div class="budget-price-label"><?= h(number_format((float)$product->total_sales_amount * 0.75, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                                <!-- Showroom Products -->
                                <?php foreach ($topSellingProducts['showroom'] as $product): ?>
                                    <li class="product-list">
                                        <?php
                                        // Check if the product has an image
                                        $imagePath = !empty($product->product->product_images) ? $product->product->product_images[0]->image : 'default-product-image.png';
                                        ?>
                                        <img class="msr-3 rounded" width="50" src="<?= $imagePath ?>" alt="product">
                                        <div class="set-flex">
                                            <div class="float-end">
                                                <div class="font-weight-600 text-muted text-small"><?= $product->total_units_sold ?> Sales</div>
                                            </div>
                                            <div class="fw-bold font-15"><?= h($product->product->name) ?></div>
                                            <div class="mt-1">
                                                <div class="budget-price">
                                                    <div class="budget-price-square" style="width: <?= $product->total_sales_amount / 1000 ?>%;"></div>
                                                    <div class="budget-price-label"><?= h(number_format((float)$product->total_sales_amount, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                                </div>
                                                <div class="budget-price">
                                                    <div class="budget-price-square product_cost" style="width: <?= $product->total_sales_amount * 0.75 / 1000 ?>%;"></div>
                                                    <div class="budget-price-label"><?= h(number_format((float)$product->total_sales_amount * 0.75, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Top 5 Product Categories') ?></h4>
                        </div>
                        <div class="card-body product_categories" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border"
                                style="position: position: relative;max-height: 320px;overflow-y: scroll;" [perfectScrollbar]>
                                <?php foreach ($topCategories as $category): ?>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= h($category['category_icon']) ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15"><?= h($category['category_name']) ?></div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: <?= $category['percentage'] ?>%;"></div>
                                                <div class="budget-price-label"><?= h(number_format((float)$category['total_sales'], 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>  
    </div>
</section>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<!-- <script src="< ?= $this->Url->webroot('js/page/chart-apexcharts.js') ?>"></script> -->
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    let chart1Instance;
    let chart2Instance;

    $(function () {
        chart1();
        chart2();
        chart7();
        chart8();
        chart9();
        // chart10();
    });

    function chart1() {
        var options = {
            chart: {
                height: 350,
                type: "bar",
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    endingShape: "rounded",
                    columnWidth: "20%",
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            series: [
                {
                    name: "Orders",
                    type: "column",
                    data: <?= json_encode($orderCounts); ?>
                },
            ],
            colors: ["#0d839b"],
            xaxis: {
                categories: <?= json_encode($monthNames); ?>,
                title: {
                    text: "Months",
                },
                labels: {
                    style: {
                        colors: "#8e8da4",
                    },
                },
            },
            yaxis: {
                title: {
                    text: 'No. Of Orders',  // Updated here
                },
                labels: {
                    style: {
                        color: "#8e8da4",
                    },
                },
            },
            fill: {
                opacity: 1,
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + " Orders";
                    },
                },
            },
            legend: {
                position: "top",
                horizontalAlign: "right",
                floating: true,
            },
        };

        var chart1Instance = new ApexCharts(document.querySelector("#order_trends_chart"), options);

        chart1Instance.render();
    }

    function chart2() {
        var options = {
            chart: {
                height: 350,
                type: "line",
            },
            series: [
                {
                    name: "Revenue",
                    data: <?php echo json_encode($revenueData); ?>
                },
                {
                    name: "Expenses",
                    data: <?php echo json_encode($expensesData); ?>
                },
            ],
            colors: ["#0d839b", "#f77f00"],
            plotOptions: {
                bar: {
                    horizontal: false,
                    endingShape: "rounded",
                    columnWidth: "50%",
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            title: {
                text: ".",
            },
            xaxis: {
                categories: <?php echo json_encode($revenueMonthNames); ?>,
                title: {
                    text: "Data Period",
                },
                labels: {
                    style: {
                colors: "#8e8da4",
                fontSize: "10px", // Adjust the font size for the month labels
            },
                },
            },
            yaxis: [
                // {
                //     title: {
                //         text: "Revenue (in thousands)",  // Revenue title
                //     },
                //     labels: {
                //         style: {
                //             color: "#000000",
                //         },
                //     },
                // },
                {
            title: {
                text: "Revenue (in thousands)", // Revenue title
                offsetX: 6, // Adjust horizontal position of the title
                offsetY: 0, // Adjust vertical position of the title
                style: {
                    fontSize: "10px", // Title font size
                },
            },
            labels: {
                offsetX: 19, // Adjust horizontal position of the labels
                style: {
                    fontSize: "12px", // Label font size
                },
            },
        },

                {
                    opposite: true,
                    title: {
                        text: "Expenses (in thousands)",  // Expenses title
                    },
                    labels: {
                        style: {
                            color: "#000000",
                        },
                    },
                },
            ],
            legend: {
                position: "top",
                horizontalAlign: "right",
                floating: true,
            },
        };

        var chart2Instance = new ApexCharts(document.querySelector("#revenue_chart"), options);

        chart2Instance.render();
    }

    let chart7Instance;
    function chart7() {

        const seriesData = [<?= h($onlineOrdersCount) ?>, <?= h($showroomOrdersCount) ?>, <?= h($callCenterOrdersCount) ?>];

        var options = {
            chart: {
                height: 300,
                type: "pie",
            },
            labels: ["Online Orders", "Showroom Orders", "Call Center Orders"],
            series: seriesData,
            colors: ["#0d839b", "#f77f00", "#8e44ad"],
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200,
                    },
                    legend: {
                        position: "bottom",
                    },
                },
            }, ],
        };

        chart7Instance = new ApexCharts(document.querySelector("#chart7"), options);
        chart7Instance.render() // Render the chart for the first time
            .then(() => {
                // console.log("Chart 7 initialized successfully:", chart7Instance);
            })
            .catch((error) => {
                console.error("Failed to render chart:", error);
            });
    }

    const paymentChartLabels = <?= json_encode($paymentChartLabels) ?>;
    const paymentChartData = <?= json_encode($paymentChartData) ?>;
    let chart8Instance;
    function chart8() {
        const options = {
            chart: {
                height: 300,
                type: "pie",
            },
            labels: paymentChartLabels,
            series: paymentChartData,
            colors: ["#0d839b", "#f77f00", "#8e44ad", "#27ae60", "#c0392b", "#2980b9"],
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: "bottom"
                    }
                }
            }]
        };

        chart8Instance = new ApexCharts(document.querySelector("#chart8"), options);
        chart8Instance.render()
            .then(() => {
                // console.log("Payment method chart rendered");
            })
            .catch((error) => {
                console.error("Failed to render payment method chart:", error);
            });
    }

    const deliveryChartLabels = ['<?= __('Pickups Recorded') ?>', '<?= __('Deliveries Completed') ?>', '<?= __('Deliveries Pending') ?>'];
    const deliveryChartData = [<?= h(intval($pickupsRecorded)) ?>, <?= h(intval($deliveriesCompleted)) ?>, <?= h(intval($deliveriesPending)) ?>];
    let chart9Instance;
    function chart9() {
        const options = {
            chart: {
                height: 300,
                type: "pie",
            },
            labels: deliveryChartLabels,
            series: deliveryChartData,
            colors: ['#0d839b', '#f77f00', '#8e44ad'],
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: "bottom"
                    }
                }
            }]
        };

        chart9Instance = new ApexCharts(document.querySelector("#chart9"), options);
        chart9Instance.render()
            .then(() => {
                // console.log("Payment method chart rendered");
            })
            .catch((error) => {
                console.error("Failed to render payment method chart:", error);
            });
    }


    function handleChange(answer) {
        
        if (answer.value == 4) {
            document.getElementById('datesappear').classList.remove('d-none');
        } else 
        {

            document.getElementById('datesappear').classList.add('d-none');

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'filterDashboardCard']); ?>',
                type: 'GET',
                data: {
                    dateRange: answer.value
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    
                    if(answer.value == '<?= __('current_month') ?>')
                    {
                        var userText = '<?= __('New users since this month') ?>';
                        var productText = '<?= __('New products since this month') ?>';
                        var showroomText = '<?= __('New showrooms since this month') ?>';
                    }
                    else if(answer.value == '<?= __('last_3_months') ?>')
                    {
                        var userText = '<?= __('Users since 3 months') ?>';
                        var productText = '<?= __('Products since 3 months') ?>';
                        var showroomText = '<?= __('Showrooms since 3 months') ?>';
                    }
                    else if(answer.value == '<?= __('last_6_months') ?>')
                    {
                        var userText = '<?= __('Users since 6 months') ?>';
                        var productText = '<?= __('Products since 6 months') ?>';
                        var showroomText = '<?= __('Showrooms since 6 months') ?>';
                    }
                    else if(answer.value == '<?= __('current_year') ?>')
                    {
                        var userText = '<?= __('Users in current year') ?>';
                        var productText = '<?= __('Products in current year') ?>';
                        var showroomText = '<?= __('Showrooms in current year') ?>';
                    }

                    $('#totalOrders').text(response.totalOrders + ' No.s');
                    $('#onlineOrders').text(response.onlineOrders + ' No.s');
                    $('#showroomOrders').text(response.showroomOrders + ' No.s');
                    $('#callCenterOrders').text(response.callCenterOrders + ' No.s');
                    $('#totalSalesAmount').text(response.totalSalesAmount);
                    $('#orderProgressBar').css('width', response.percentageOrders + '%').attr('data-width', response.percentageOrders + '%').attr('aria-valuenow', response.percentageOrders);

                    $('#totalUsers').text(response.totalUsers);
                    $('#totalNewUsers').text(response.newUsers);
                    $('#userProgressBar').css('width', response.newUsersPercentage + '%').attr('data-width', response.newUsersPercentage + '%').attr('aria-valuenow', response.newUsersPercentage);
                    $('#userText').text(userText);

                    $('#totalProducts').text(response.totalActiveProducts);
                    $('#totalNewProducts').text(response.newProducts);
                    $('#productProgressBar').css('width', response.newProductsPercentage + '%').attr('data-width', response.newProductsPercentage + '%').attr('aria-valuenow', response.newProductsPercentage);
                    $('#productText').text(productText);

                    $('#totalShowrooms').text(response.totalShowrooms);
                    $('#totalNewShowrooms').text(response.newShowrooms);
                    $('#showroomProgressBar').css('width', response.newShowroomsPercentage + '%').attr('data-width', response.newShowroomsPercentage + '%').attr('aria-valuenow', response.newShowroomsPercentage);
                    $('#showroomText').text(showroomText);
                },
                error: function() {
                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch data. Please try again.') ?>', 'error');
                }
            });
        }
    }

    $('#dateRangeForm').on('submit', function(event) {
        event.preventDefault(); // Prevent default form submission

        // Get values from the input fields
        const fromDate = $('#from-date').val();
        const toDate = $('#to-date').val();

        // Validate date inputs
        if (!fromDate || !toDate) {
            swal('<?= __('Failed') ?>', '<?= __('Both dates are required.') ?>', 'error');
            return false;
        }

        // Check if the from date is earlier than or equal to the to date
        if (new Date(fromDate) > new Date(toDate)) {
            swal('<?= __('Failed') ?>', '<?= __('The "From Date" must be earlier than or equal to the "To Date".') ?>', 'error');
            return false;
        }

        // Send AJAX request
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'filterDashboardCardByDate']); ?>',
            method: 'GET',
            data: { fromDate: fromDate, toDate: toDate },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                
                $('#totalOrders').text(response.totalOrders + ' No.s');
                $('#onlineOrders').text(response.onlineOrders + ' No.s');
                $('#showroomOrders').text(response.showroomOrders + ' No.s');
                $('#totalSalesAmount').text(response.totalSalesAmount);
                $('#orderProgressBar').css('width', response.percentageOrders + '%').attr('data-width', response.percentageOrders + '%').attr('aria-valuenow', response.percentageOrders);

                $('#totalUsers').text(response.totalUsers);
                $('#totalNewUsers').text(response.newUsers);
                $('#userProgressBar').css('width', response.newUsersPercentage + '%').attr('data-width', response.newUsersPercentage + '%').attr('aria-valuenow', response.newUsersPercentage);
                $('#userText').text(`Users for the period`);

                $('#totalProducts').text(response.totalActiveProducts);
                $('#totalNewProducts').text(response.newProducts);
                $('#productProgressBar').css('width', response.newProductsPercentage + '%').attr('data-width', response.newProductsPercentage + '%').attr('aria-valuenow', response.newProductsPercentage);
                $('#productText').text(`Products for the period`);

                $('#totalShowrooms').text(response.totalShowrooms);
                $('#totalNewShowrooms').text(response.newShowrooms);
                $('#showroomProgressBar').css('width', response.newShowroomsPercentage + '%').attr('data-width', response.newShowroomsPercentage + '%').attr('aria-valuenow', response.newShowroomsPercentage);
                $('#showroomText').text(`Showrooms for the period`);

            },
            error: function(xhr) {
                swal('<?= __('Failed') ?>', 'An error occurred: ' + xhr.responseText, 'error');
            }
        });
    });


</script>
<?php $this->end(); ?>