<?php
declare(strict_types=1);

namespace App\Controller;

use App\Utility\RecentlyViewedHelper;
use Cake\Controller\Controller;
use Cake\Core\Configure;
use Cake\Http\Exception\NotFoundException;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Utility\Text;
use Cake\I18n\FrozenTime;
use Cake\Cache\Cache;
// use Cake\Cache\Engine\RedisEngine;

/**
 * Users Controller
 */
class WebsiteController extends Controller
{
    protected $ProductDeals;
    protected $Cities;
    protected $Categories;
    protected $Users;
    protected $Customers;
    protected $OtpVerifications;
    protected $Banners;
    protected $BannerAds;
    protected $Offers;
    protected $Widgets;
    protected $Products;
    protected $ApiRequestLogs;
    protected $PaymentMethods;
    protected $Carts;
    protected $CartItems;
    protected $WidgetCategoryMappings;
    protected $Orders;
    protected $OrderItems;
    protected $SiteSettings;
    protected $Wishlists;
    protected $Reviews;
    protected $Showrooms;
    protected $Brands;
    protected $CustomerAddresses;
    protected $Loyalty;
    protected $ReviewImages;
    protected $ContentPages;
    protected $CustomerCards;
    protected $Invoices;
    protected $DeliveryCharges;
    protected $Transactions;
    protected $FaqCategories;
    protected $Faqs;
    protected $Wallets;
    protected $OrderCancellationCategories;
    protected $OrderCancellations;
    protected $OrderReturnCategories;
    protected $OrderReturns;
    protected $OrderTrackingHistories;
    protected $ProductImages;
    protected $OrderReturnImages;
    protected $ContactQueryTypes;
    protected $supportCategories;
    protected $ProductPaymentSettings;
    protected $identity;
    protected $CreditApplications;
    protected $CartCreditPayments;
    public function initialize(): void
    {
        parent::initialize();

        $this->loadComponent('WebsiteFunction');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('Flash');
        $this->viewBuilder()->setLayout('web');

        // Add checkStockStatus to unauthenticated actions
        //  $this->Authentication->addUnauthenticatedActions(['checkStockStatus']);

        $guestToken = $this->request->getSession()->read('cartId') ?? null; // 7d1c54ba-05d8-4a5b-bad4-ed4381486858

        $this->identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$this->identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }
        $this->CartCreditPayments = $this->fetchTable('CartCreditPayments');
        $this->CreditApplications = $this->fetchTable('CreditApplications');

        $this->ProductDeals = $this->fetchTable('ProductDeals');
        $this->ProductPaymentSettings = $this->fetchTable('ProductPaymentSettings');
        $this->Cities = $this->fetchTable('Cities');
        $this->Categories = $this->fetchTable('Categories');
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');
        $this->OtpVerifications = $this->fetchTable('OtpVerifications');
        $this->Banners = $this->fetchTable('Banners');
        $this->BannerAds = $this->fetchTable('BannerAds');
        $this->Offers = $this->fetchTable('Offers');
        $this->Widgets = $this->fetchTable('Widgets');
        $this->Products = $this->fetchTable('Products');
        $this->Reviews = $this->fetchTable('Reviews');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Brands = $this->fetchTable('Brands');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->ApiRequestLogs = $this->fetchTable('ApiRequestLogs');
        $this->PaymentMethods = $this->fetchTable('PaymentMethods');
        $this->Carts = $this->fetchTable('Carts');
        $this->CartItems = $this->fetchTable('CartItems');
        $this->WidgetCategoryMappings = $this->fetchTable('WidgetCategoryMappings');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->Wishlists = $this->fetchTable('Wishlists');
        $this->Loyalty = $this->fetchTable('Loyalty');
        $this->ReviewImages = $this->fetchTable('ReviewImages');
        $this->ContentPages = $this->fetchTable('ContentPages');
        $this->DeliveryCharges = $this->fetchTable('DeliveryCharges');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->CustomerCards = $this->fetchTable('CustomerCards');
        $this->Invoices = $this->fetchTable('Invoices');
        $this->FaqCategories = $this->fetchTable('FaqCategories');
        $this->Faqs = $this->fetchTable('Faqs');
        $this->Wallets = $this->fetchTable('Wallets');
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->OrderTrackingHistories = $this->fetchTable('OrderTrackingHistories');
        $this->ProductImages = $this->fetchTable('ProductImages');
        $this->OrderReturnImages = $this->fetchTable('OrderReturnImages');
        $this->ContactQueryTypes = $this->fetchTable('ContactQueryTypes');
        $this->supportCategories = $this->fetchTable('SupportCategories');
    }
    public function beforeRender(\Cake\Event\EventInterface $event)
    {
        parent::beforeRender($event);

        $topSellCarEmptyItemShow = $this->WebsiteFunction->getTopSellItems(10);
        $scrollContentArr = $this->WebsiteFunction->getHomeScrollText();

        // Pass it to all views
        $this->set(compact('topSellCarEmptyItemShow','scrollContentArr'));
    }


    public function ajaxLoadMoreProduct()
    {
        // Fetch POST data instead of GET data
        $categoryId = $this->request->getData('category_id');
        $brandId = $this->request->getData('brand_id');
        $minPrice = $this->request->getData('min_price') ?: 0;
        $maxPrice = $this->request->getData('max_price') ?: 99999;
        $sortBy = $this->request->getData('sortBy');
        $page = $this->request->getData('page') ?: 1;
        $limit = $this->request->getData('limit') ?: 20; // Default limit

        $selectedBrands = $this->request->getData('brands') ? explode(',', $this->request->getData('brands')) : [];

        // Ensure $brandId is added to the array if not already present
        if (!in_array($brandId, $selectedBrands)) {
            $selectedBrands[] = $brandId;
        }

        $brandsString = implode('--', $selectedBrands);
        $price = $minPrice || $maxPrice ? "$minPrice--$maxPrice" : null;

        // API call for category list (unchanged)
        $apiCategoryListUrl = Configure::read('Settings.SITE_URL') . 'api/v1.0/categories.json';
        $categoryList = $this->callApi($apiCategoryListUrl);
        $categoryList = $categoryList->result->data;

        if (empty($categoryId)) {
            $categoryId = reset($categoryList)->id;
        }

        $categoryName = $this->Categories->find()->select(['name'])->where(['id' => $categoryId])->first()->name;

        // Brand List (unchanged)
        $BrandsList = $this->Brands->find()->select(['id', 'name'])->where('status', ['A'])->toArray();
        foreach ($BrandsList as &$brand) {
            if (!empty($brand['web_banner'])) {
                $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
            }
            if (!empty($brand['brand_logo'])) {
                $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
            }
        }
        unset($brand);

        if (empty($brandId)) {
            $brandId = reset($BrandsList)->id;
        }
        $brandDetailPage = $this->Brands->find()->where(['id' => $brandId])->where('status', ['A'])->first();
        $activeFilters['Brands'] = $brandDetailPage->name;

        // Process the brand banners and logos
        if ($brandDetailPage) {
            if (!empty($brandDetailPage->web_banner)) {
                $brandDetailPage->web_banner = $this->Media->getCloudFrontURL($brandDetailPage->web_banner);
            }
            if (!empty($brandDetailPage->brand_logo)) {
                $brandDetailPage->brand_logo = $this->Media->getCloudFrontURL($brandDetailPage->brand_logo);
            }
            if (!empty($brandDetailPage->mobile_banner)) {
                $brandDetailPage->mobile_banner = $this->Media->getCloudFrontURL($brandDetailPage->mobile_banner);
            }
        }

        // Construct API parameters
        $params = "?sort=$sortBy&price=$price&brand=$brandsString&page=$page&limit=$limit";
        $apiProductListUrl = Configure::read('Settings.SITE_URL') . "api/v1.0/productList/$categoryId.json$params";
        $productList = $this->callApi($apiProductListUrl);
        $productList = $productList->result->data->products;


        $userId = null;
        if ($this->request->getSession()->read('Auth.User')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
        }

        if($productList){
            foreach ($productList as &$product) {
                $product->whishlist = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product->id);
            }
        }

        // Build active filters for response (unchanged)
        $activeFilters = [];
        if ($this->request->getData('category')) {
            $activeFilters['Category'] = $this->request->getData('category');
        }
        if ($this->request->getData('rating')) {
            $activeFilters['Rating'] = $this->request->getData('rating') . ' Star Rating';
        }
        if ($this->request->getData('brands')) {
            $brandArr = explode(',', $this->request->getData('brands'));
            $selectedBrandList = $this->Brands->find()->select(['name'])->where('status', ['A'])->where(['id IN' => $brandArr])->all()->toArray();
            $BrandNames = array_map(fn ($brand) => $brand->name, $selectedBrandList);
            $activeFilters['Brands'] = $BrandNames;
        }
        if ($this->request->getData('min_price') || $this->request->getData('max_price')) {
            $minPrice = $this->request->getData('min_price') ?: '0';
            $maxPrice = $this->request->getData('max_price') ?: '99999';
            $activeFilters['Price'] = "$minPrice - $maxPrice";
        }
        if ($this->request->getData('sortBy')) {
            $activeFilters['sortBy'] = $this->request->getData('sortBy') ?: '';
        }
        if ($this->request->getData('rating')) {
            $activeFilters['rating'] = $this->request->getData('rating') ?: '';
        }

        // Return the response as JSON
        $this->response = $this->response->withType('application/json');

        return $this->response->withStringBody(json_encode([
            'status' => 'success',
            'message' => 'Records fetched successfully!',
            'data' => $productList,
            'filters' => $activeFilters,
            'page' => $page,
        ]));
    }
   public function ajaxLoadMoreStoreProduct()
    {
        // Fetch POST data instead of GET data
        $categoryId = $this->request->getData('category_id');
        $brandId = $this->request->getData('brand_id');
        $minPrice = $this->request->getData('min_price') ?: 0;
        $maxPrice = $this->request->getData('max_price') ?: 99999;
        $sortBy = $this->request->getData('sortBy');
        $page = $this->request->getData('page') ?: 1;
        $limit = $this->request->getData('limit') ?: 20; // Default limit

        $selectedBrands = $this->request->getData('brands') ? explode(',', $this->request->getData('brands')) : [];

        // Ensure $brandId is added to the array if not already present
        if (!in_array($brandId, $selectedBrands)) {
            $selectedBrands[] = $brandId;
        }

        $brandsString = implode('--', $selectedBrands);
        $price = $minPrice || $maxPrice ? "$minPrice--$maxPrice" : null;

        // API call for category list (unchanged)
        $apiCategoryListUrl = Configure::read('Settings.SITE_URL') . 'api/v1.0/categories.json';
        $categoryList = $this->callApi($apiCategoryListUrl);
        $categoryList = $categoryList->result->data;

        if (empty($categoryId)) {
            $categoryId = reset($categoryList)->id;
        }

        $categoryName = $this->Categories->find()->select(['name'])->where(['id' => $categoryId])->first()->name;

        // Brand List (unchanged)
        $BrandsList = $this->Brands->find()->select(['id', 'name'])->where('status', ['A'])->toArray();
        foreach ($BrandsList as &$brand) {
            if (!empty($brand['web_banner'])) {
                $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
            }
            if (!empty($brand['brand_logo'])) {
                $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
            }
        }
        unset($brand);

        if (empty($brandId)) {
            $brandId = reset($BrandsList)->id;
        }
        $brandDetailPage = $this->Brands->find()->where(['id' => $brandId])->where('status', ['A'])->first();
        $activeFilters['Brands'] = $brandDetailPage->name;

        // Process the brand banners and logos
        if ($brandDetailPage) {
            if (!empty($brandDetailPage->web_banner)) {
                $brandDetailPage->web_banner = $this->Media->getCloudFrontURL($brandDetailPage->web_banner);
            }
            if (!empty($brandDetailPage->brand_logo)) {
                $brandDetailPage->brand_logo = $this->Media->getCloudFrontURL($brandDetailPage->brand_logo);
            }
            if (!empty($brandDetailPage->mobile_banner)) {
                $brandDetailPage->mobile_banner = $this->Media->getCloudFrontURL($brandDetailPage->mobile_banner);
            }
        }

        // Construct API parameters
        $params = "?sort=$sortBy&price=$price&brand=$brandsString&page=$page&limit=$limit";
        $apiProductListUrl = Configure::read('Settings.SITE_URL') . "api/v1.0/productList/$categoryId.json$params";
        $productList = $this->callApi($apiProductListUrl);
        $productList = $productList->result->data->products;

        $userId = null;
        if ($this->request->getSession()->read('Auth.User')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
        }

        if($productList){
            foreach ($productList as &$product) {
                $product->whishlist = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product->id);
            }
        }
        // Build active filters for response (unchanged)
        $activeFilters = [];
        if ($this->request->getData('category')) {
            $activeFilters['Category'] = $this->request->getData('category');
        }
        if ($this->request->getData('rating')) {
            $activeFilters['Rating'] = $this->request->getData('rating') . ' Star Rating';
        }
        if ($this->request->getData('brands')) {
            $brandArr = explode(',', $this->request->getData('brands'));
            $selectedBrandList = $this->Brands->find()->select(['name'])->where('status', ['A'])->where(['id IN' => $brandArr])->all()->toArray();
            $BrandNames = array_map(fn ($brand) => $brand->name, $selectedBrandList);
            $activeFilters['Brands'] = $BrandNames;
        }
        if ($this->request->getData('min_price') || $this->request->getData('max_price')) {
            $minPrice = $this->request->getData('min_price') ?: '0';
            $maxPrice = $this->request->getData('max_price') ?: '99999';
            $activeFilters['Price'] = "$minPrice - $maxPrice";
        }
        if ($this->request->getData('sortBy')) {
            $activeFilters['sortBy'] = $this->request->getData('sortBy') ?: '';
        }
        if ($this->request->getData('rating')) {
            $activeFilters['rating'] = $this->request->getData('rating') ?: '';
        }

        // Return the response as JSON
        $this->response = $this->response->withType('application/json');

        return $this->response->withStringBody(json_encode([
            'status' => 'success',
            'message' => 'Records fetched successfully!',
            'data' => $productList,
            'filters' => $activeFilters,
            'page' => $page,
        ]));
    }

    public function productBrandList($brandId = null)
    {

        $categoryId = null;
        if (!empty($brandId)) {
            $brandId = $this->Brands->find()
            ->select(['id'])
            ->where(['status' => 'A'])
            ->where(['url_key' => $brandId])
            ->first();

            if ($brandId) {
                $brandId = intval($brandId->id);
            } else {
                $this->Flash->toast(__('The selected brand is invalid or does not exist. Please search for a valid brand.'), [
                    'element' => 'toast',
                    'params' => ['type' => 'warning']
                ]);
                return $this->redirect(['controller' => 'Website', 'action' => 'home']);
            }
        }

        $apiCategoryListUrl = Configure::read('Settings.SITE_URL') . 'api/v1.0/categories.json';
        $storeUrl = Router::url(['controller' => 'Website', 'action' => 'productBrandList'], true);
        $categoryList = $this->Categories->allCategories();
        if (empty($categoryId)) {
            $categoryId = reset($categoryList);
            $categoryId = $categoryId->id;
        }

        $categoryName = $this->Categories->find()->select(['name'])->where('status', ['A'])->where(['id' => $categoryId])->first()->name;
        $BrandsList = $this->Brands->find()
            ->select(['id', 'name'])
            ->where(['status' => 'A'])
            ->order(['TRIM(name)' => 'ASC'])
            ->toArray();
        foreach ($BrandsList as &$brand) {
            if (!empty($brand['web_banner'])) {
                $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
            }
            if (!empty($brand['brand_logo'])) {
                $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
            }
        }
        unset($brand);
        if (empty($brandId)) {
            $brandId = reset($BrandsList);
            $brandId = $brandId->id;
        }
        $brandDetailPage = $this->Brands->find()->where(['id' => $brandId])->where('status', ['A'])->first();
        $activeFilters['Brands'] = $brandDetailPage->name;
        if ($brandDetailPage) {
            if (!empty($brandDetailPage->web_banner)) {
                $brandDetailPage->web_banner = $this->Media->getCloudFrontURL($brandDetailPage->web_banner);
            }
            if (!empty($brandDetailPage->brand_logo)) {
                $brandDetailPage->brand_logo = $this->Media->getCloudFrontURL($brandDetailPage->brand_logo);
            }
            if (!empty($brandDetailPage->mobile_banner)) {
                $brandDetailPage->mobile_banner = $this->Media->getCloudFrontURL($brandDetailPage->mobile_banner);
            }
        }
        $selectedBrands = $this->request->getQuery('brands')
            ? explode(',', $this->request->getQuery('brands'))
            : [];

        if (!in_array($brandId, $selectedBrands)) {
            $selectedBrands[] = $brandId;
        }
        $brandsString = implode('--', $selectedBrands);
        $minPrice = $this->request->getQuery('min_price') ? $this->request->getQuery('min_price') : 0;
        $maxPrice = $this->request->getQuery('max_price') ? $this->request->getQuery('max_price') : 99999;
        $sortBy = $this->request->getQuery('sortBy') ? $this->request->getQuery('sortBy') : null;
        $rating = $this->request->getQuery('rating') ? $this->request->getQuery('rating') : null;
        $page = 1;
        $limit = 20;
        $price = $this->request->getQuery('min_price') || $this->request->getQuery('max_price') ? "$minPrice--$maxPrice" : null;
        $params = "?sort=$sortBy&price=$price&brand=$brandsString&rating=$rating&page=$page&limit=$limit";
        $apiProductListUrl = Configure::read('Settings.SITE_URL') . "api/v1.0/productList/$categoryId.json$params";
        $productList = $this->callApi($apiProductListUrl);
        $productList = $productList->result->data->products;
        $userId = null;
        if ($this->request->getSession()->read('Auth.User')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
        }
        if($productList){
            foreach ($productList as &$product) {
                $product->whishlist = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product->id);
            }
        }
        $activeFilters = [];
        if ($this->request->getQuery('category')) {
            $activeFilters['Category'] = $this->request->getQuery('category');
        }
        if ($this->request->getQuery('rating')) {
            $activeFilters['Rating'] = $this->request->getQuery('rating') . ' Star Rating';
        }
        if ($this->request->getQuery('brands')) {
            $brandArr = explode(',', $this->request->getQuery('brands'));
            $selectedBrandList = $this->Brands->find()->select(['name'])->where('status', ['A'])->where(['id IN' => $brandArr])->all()->toArray();
            $BrandNames = array_map(fn ($brand) => $brand->name, $selectedBrandList);
            $activeFilters['Brands'] = $BrandNames;
        }
        if ($this->request->getQuery('min_price') || $this->request->getQuery('max_price')) {
            $minPrice = $this->request->getQuery('min_price') ?: '0';
            $maxPrice = $this->request->getQuery('max_price') ?: '99999';
            $activeFilters['Price'] = "$minPrice - $maxPrice";
        }
        if ($this->request->getQuery('sortBy')) {
            $activeFilters['sortBy'] = $this->request->getQuery('sortBy') ?: '';
        }
        if ($this->request->getQuery('rating')) {
            $activeFilters['rating'] = $this->request->getQuery('rating') ?: '';
        }


        $banner_ads = $this->BannerAds->getBannerAds('website', 'Banner', 'Brand Page');
        foreach ($banner_ads as &$banner_ad) {
            if (!empty($banner_ad['web_image'])) {
                $banner_ad['web_image'] = $this->Media->getCloudFrontURL($banner_ad['web_image']);
            }
        }
        unset($banner_ad);

      //  dd($banner_ads);

        $this->set(compact('banner_ads','brandId', 'brandDetailPage', 'activeFilters', 'productList', 'categoryList', 'categoryId', 'storeUrl', 'categoryName', 'BrandsList', 'BrandsList', 'selectedBrands', 'minPrice', 'maxPrice', 'rating', 'sortBy'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('brand_list');
    }

    public function productList($categoryId = null)
    {
        if (!empty($categoryId)) {
            $category = $this->Categories->find()
            ->select(['id'])
            ->where(['url_key' => $categoryId])
            ->first();

            if ($category) {
                $categoryId = intval($category->id);
            } else {
                $this->Flash->toast(__('The selected category is invalid or does not exist. Please search for a valid category.'), [
                    'element' => 'toast',
                    'params' => ['type' => 'warning']
                ]);
                return $this->redirect(['controller' => 'Website', 'action' => 'home']);
            }
        }

        $apiCategoryListUrl = Configure::read('Settings.SITE_URL') . 'api/v1.0/categories.json';
        $baseUrl = Configure::read('Settings.SITE_URL') .'product-list';
        $parentCategoryName = "";
        $categoryList = $this->Categories->allCategories();

        foreach ($categoryList as &$category) {
            if (!empty($category['category_icon'])) {
                $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
            }
        }
        unset($category);

        if (empty($categoryId)) {
            $categoryId = reset($categoryList);
            $categoryId = $categoryId->id;
            $categoryName = $categoryId->name;
        }
        $categoryName = $this->Categories->find()->select(['name'])->where(['id' => $categoryId])->first()->name;
        $categoryParentId = $this->Categories->find()->select(['parent_id'])->where(['id' => $categoryId])->first()->parent_id ?? null;
        if ($categoryParentId) {
            $parentCategoryName = $this->Categories->find()->select(['name'])->where(['id' => $categoryParentId])->first()->name;
        }
        $BrandsList = $this->Brands->find()
            ->select(['id', 'name'])
            ->where(['status' => 'A'])
            ->order(['TRIM(name)' => 'ASC'])
            ->toArray();

        foreach ($BrandsList as &$brand) {
            if (!empty($brand['web_banner'])) {
                $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
            }
            if (!empty($brand['brand_logo'])) {
                $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
            }
        }
        unset($brand);

        $selectedBrands = $this->request->getQuery('brands') ? explode(',', $this->request->getQuery('brands')) : [];
        $brandsString = implode('--', $selectedBrands);
        // Read min_price and max_price from the query string
        $minPrice = $this->request->getQuery('min_price') ? $this->request->getQuery('min_price') : 0;
        $maxPrice = $this->request->getQuery('max_price') ? $this->request->getQuery('max_price') : 99999;
        $sortBy = $this->request->getQuery('sortBy') ? $this->request->getQuery('sortBy') : null;
        $rating = $this->request->getQuery('rating') ? $this->request->getQuery('rating') : null;
        $page = 1;
        $limit = 20;
        $price = $this->request->getQuery('min_price') || $this->request->getQuery('max_price') ? "$minPrice--$maxPrice" : null;
        $params = "?sort=$sortBy&price=$price&brand=$brandsString&rating=$rating&page=$page&limit=$limit";
        $apiProductListUrl = Configure::read('Settings.SITE_URL') . "api/v1.0/productList/$categoryId.json$params";
        $productList = $this->callApi($apiProductListUrl);
        $productList = $productList->result->data->products;


        $userId = null;
        $customer_id = null;
         if ($this->request->getSession()->read('Auth.User')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
        }
        if($productList){
            foreach ($productList as &$product) {
                $product->whishlist = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product->id);
            }
        }
        $activeFilters = [];
        if ($this->request->getQuery('category')) {
            $activeFilters['Category'] = $this->request->getQuery('category');
        }
        if ($this->request->getQuery('rating')) {
            $activeFilters['Rating'] = $this->request->getQuery('rating') . ' Star Rating';
        }
        if ($this->request->getQuery('brands')) {
            $brandArr = explode(',', $this->request->getQuery('brands'));
            $selectedBrandList = $this->Brands->find()->select(['name'])->where('status', ['A'])->where(['id IN' => $brandArr])->all()->toArray();
            $BrandNames = array_map(fn ($brand) => $brand->name, $selectedBrandList);
            $activeFilters['Brands'] = $BrandNames;
        }
        if ($this->request->getQuery('min_price') || $this->request->getQuery('max_price')) {
            $minPrice = $this->request->getQuery('min_price') ?: '0';
            $maxPrice = $this->request->getQuery('max_price') ?: '99999';
            $activeFilters['Price'] = "$minPrice - $maxPrice";
        }
        if ($this->request->getQuery('sortBy')) {
            $activeFilters['sortBy'] = $this->request->getQuery('sortBy') ?: '';
        }
        if ($this->request->getQuery('rating')) {
            $activeFilters['rating'] = $this->request->getQuery('rating') ?: '';
        }
        $bannerAds = $this->BannerAds->getPageImage('Product Category');
        if (!empty($bannerAds['web_image'])) {
            $bannerAds['web_image'] = $this->Media->getCloudFrontURL($bannerAds['web_image']);
        }
        $this->set(compact('categoryParentId','parentCategoryName','bannerAds','activeFilters', 'productList', 'categoryList', 'categoryId', 'baseUrl', 'categoryName', 'BrandsList', 'BrandsList', 'selectedBrands', 'minPrice', 'maxPrice', 'rating', 'sortBy'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('list');
    }

    private function convertCart()
    {
            // Get logged-in user and guest token
            $identity = $this->request->getSession()->read('Auth.User');
            $guestToken = $this->request->getSession()->read('cartId') ?? null;

            if (!$identity) {
               return false;
            } elseif (empty($guestToken)) {
                return false;
            } else {

                $customerId = $identity->id;
                $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();
                $customerId = $users->customer->id;


                $guestCart = $this->Carts->find()
                    ->where(['guest_token' => $guestToken])
                    ->contain(['CartItems'])
                    ->first();

                if (!$guestCart) {

                        return false;

                } else {

                    $userCart = $this->Carts->find()
                        ->where(['customer_id' => $customerId])
                        ->contain(['CartItems'])
                        ->first();

                    if ($userCart) {
                        foreach ($guestCart->cart_items as $item) {
                            $existingItem = $this->Carts->CartItems->find()
                                ->where(['cart_id' => $userCart->id, 'product_id' => $item->product_id])
                                ->first();

                            if ($existingItem) {
                                $existingItem->quantity += $item->quantity;
                                $this->Carts->CartItems->save($existingItem);
                            } else {
                                $item->cart_id = $userCart->id;
                                $item->isNew(true);
                                $this->Carts->CartItems->save($item);
                            }
                        }
                        return true;
                    } else {
                        $guestCart->customer_id = $customerId;
                        $guestCart->guest_token = null;

                        if ($this->Carts->save($guestCart)) {
                            return true;
                        } else {
                           return false;
                        }
                    }
                }
            }

    }
    public function home()
    {
        $testimonials = [];
        $userId = null;
        $customer_id = null;
        if ($this->request->getSession()->read('Auth.User')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
        }
        $this->identity = $this->request->getSession()->read('Auth.User');
        if ($this->identity) {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id','Users.first_name','Users.last_name','Users.email','country_code','mobile_no']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $this->identity->id])
                ->first();
                $customer_id = $users->customer->id;
        }


        $data = $this->convertCart();
        $getFaqsList = $this->Faqs->getFAQ();
        $categoryIds = array();
        $response = array();

        $siteSettingsTable = TableRegistry::getTableLocator()->get('SiteSettings');
        $settings = $siteSettingsTable->find()->first();

        $banners = $this->Banners->homeBanners('website');
        $banners = array_filter($banners, function ($banner) {
            return isset($banner['banner_location']) && trim(strtolower($banner['banner_location'])) === trim(strtolower('Home Page'));
        });
        foreach ($banners as &$banner) {
            if (!empty($banner['web_banner'])) {
                $banner['web_banner'] = $this->Media->getCloudFrontURL($banner['web_banner']);
            }
        }
        unset($banner);

        $categories = $this->Categories->parentCategories();

        foreach ($categories as &$category) {
            if (!empty($category['category_icon'])) {
                $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
            }
        }
        unset($category);

        // Banner Ads
        $banner_ads_middle = $this->BannerAds->homeBannerAds('website', ['BannerAds.ad_type' => 'Middle']);
        foreach ($banner_ads_middle as &$banner_ad) {
            if (!empty($banner_ad['web_image'])) {
                $banner_ad['web_image'] = $this->Media->getCloudFrontURL($banner_ad['web_image']);
            }
        }
        unset($banner_ad);
        $banner_ads_middle_sidebar_left = $this->BannerAds->homeBannerAds('website', ['BannerAds.ad_type' => 'Sidebar Left']);
        foreach ($banner_ads_middle_sidebar_left as &$banner_ad) {
            if (!empty($banner_ad['web_image'])) {
                $banner_ad['web_image'] = $this->Media->getCloudFrontURL($banner_ad['web_image']);
            }
        }
        unset($banner_ad);

        $banner_ads_middle_above_footer = $this->BannerAds->homeBannerAds('website', ['BannerAds.ad_type' => 'Above Footer']);
        foreach ($banner_ads_middle_above_footer as &$banner_ad) {
            if (!empty($banner_ad['web_image'])) {
                $banner_ad['web_image'] = $this->Media->getCloudFrontURL($banner_ad['web_image']);
            }
        }
        unset($banner_ad);

        $banner_ads_middle_side_right = $this->BannerAds->homeBannerAds('website', ['BannerAds.ad_type' => 'Sidebar Right']);
        foreach ($banner_ads_middle_side_right as &$banner_ad) {
            if (!empty($banner_ad['web_image'])) {
                $banner_ad['web_image'] = $this->Media->getCloudFrontURL($banner_ad['web_image']);
            }
        }
        unset($banner_ad);

        $topdeals = $this->Offers->getTopDeals($limit = 5, 'web')->toArray();

        foreach ($topdeals as $topdeal) {
            if ($topdeal['web_image'] != '' || $topdeal['web_image'] != null) {
                $topdeal['web_image'] = $this->Media->getCloudFrontURL($topdeal['web_image']);
            }
        }
        unset($topdeal);

        $widgets = $this->Widgets->homeWidgets('website');
        // echo "<pre>"; print_r($widgets); die;

        foreach ($widgets as $widget) {
            $products = [];
            $categoryIds = $this->Widgets->WidgetCategoryMappings->find()
                ->select(['category_id'])
                ->where(['widget_id' => $widget['id'], 'status' => 'A'])
                ->toArray();

            $categoryIds = array_column($categoryIds, 'category_id');

            switch ($widget['widget_type']) {
                case 'Deals of the Day':
                    $widget_type = 'deal';
                    $products = $this->Products->getDealOfTheDayProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $customer_id);
                    break;
                case 'Best Seller':
                    $widget_type = 'best_selling';
                    $products = $this->Products->getTopSellingProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $customer_id);

                    break;
                case 'New Arrivals':
                    $widget_type = 'new_arrival';
                    $products = $this->Products->getNewArrivalProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $customer_id);
                    // echo "<pre>"; print_r($products); die;
                    break;
                case 'Featured':
                    $widget_type = 'featured';
                    $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $customer_id);
                    // echo "<pre>"; print_r($products); die;
                    break;
                case 'Custom':
                    $widget_type = 'Custom';
                    $products = $this->Products->getCustomWidgetProducts($widget['no_of_product'], $categoryIds, $widget['product_preference']);
                    break;
                // case 'Special Offers':
                // $widget = 'special_offers';
                // $products = $this->Products->getSpecialOffersProducts($widget['no_of_product'], $categoryIds);
                // break;
            }

            $modifiedProducts = [];
            $userId = null;
            if ($this->request->getSession()->read('Auth.User')) {
                $userId = $this->request->getSession()->read('Auth.User')['id'];
            }

            if(isset($products) && !empty($products)){

                foreach ($products as $product) {
                    $product['reference_name'] = $product['reference_name'] ?? "N/A";
                    $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                    $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                    $product['discount'] = $this->Products->getDiscount($product['id']);
                    $product['availability_status'] = $this->Products->singleProductStockStatus($product['id']);

                    $product['promotion_price'] = $this->Products->getProductPrice($product['id']);
                    $product['product_image'] = '';
                    $image = $this->ProductImages->getDefaultProductImage($product['id']);
                    if ($image) {
                        $product['product_image'] = $this->Media->getCloudFrontURL($image);
                        $modifiedProducts[] = $product;
                    }
                    $product['whishlist'] = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product['id']);
                }

                $response[$widget_type][] = [
                    'widget' => $widget,
                    'products' => $modifiedProducts,
                ];
            }
        }
        $widgets = $response ?? [];

        $featured_cats = $this->Categories->featuredCategories();
        foreach ($featured_cats as &$featured_cat) {
            if (!empty($featured_cat['category_icon'])) {
                $featured_cat['category_icon'] = $this->Media->getCloudFrontURL($featured_cat['category_icon']);
            }
        }
        unset($featured_cat);

        // Brand List
        $BrandsList = $this->Brands->find()
            ->where(['status' => 'A'])
            ->order(['name' => 'ASC'])
           // ->limit(2)
            ->toArray();

        foreach ($BrandsList as &$brand) {
            if (!empty($brand['web_banner'])) {
                $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
            }
            if (!empty($brand['brand_logo'])) {
                $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
            }
        }
        unset($brand);

        if(!isset($categoryIds) || empty($categoryIds)){
            $categoryIds = array();
        }

        $categories = $this->Categories->parentCategories();
        $categoryIds = array_column($categoryIds, 'category_id');

        $products2 = $this->Products->getTopSellingProducts(5, $categoryIds, 'best_selling', $customer_id);

        foreach ($products2 as $product) {
            $product['promotion_price'] = $this->Products->getProductPrice($product['id']);
            $product['product_image'] = '';
            $image = $this->ProductImages->getDefaultProductImage($product['id']);
            if ($image) {
                $product['product_image'] = $this->Media->getCloudFrontURL($image);
                $modifiedProducts2[] = $product;
            }
        }

        $this->set(compact('testimonials','userId', 'banner_ads_middle_side_right', 'banner_ads_middle', 'banner_ads_middle_sidebar_left', 'banner_ads_middle_above_footer', 'getFaqsList', 'settings', 'BrandsList', 'categories', 'banners', 'topdeals', 'widgets', 'featured_cats'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('index');
    }

    public function product($product = null)
    {
        if (!$product) {
            return $this->redirect(['controller' => 'Website', 'action' => 'home']);
        }
        $query = $this->Products->find();
        if (is_numeric($product)) {
            $query->where(['id' => $product]);
        } else {
            $query->where(['url_key' => $product]);
        }
        $productData = $query->first();
        if ($productData) {
            $product = $productData->id;
        }
        $userId = null;
        $customer_id = null;
        if ($this->request->getSession()->read('Auth.User')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
        }
        $this->identity = $this->request->getSession()->read('Auth.User');
        if (!$this->identity) {
            $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
            $myReview = null;
        } else {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id','Users.first_name','Users.last_name','Users.email','country_code','mobile_no']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $this->identity->id])
                ->first();
                $customer_id = $users->customer->id;

                $myReview = $this->Products->getMyReview($product, $customer_id);

        }
       
        RecentlyViewedHelper::storeRecentlyViewed($product, $this->request->getSession()->read('Auth.User'));
        $sortBy = $this->request->getQuery('sort-by') ? $this->request->getQuery('sort-by') : 'newest';
        $product_detail = $this->Products->productView($product);

        if (!$product_detail) {
            return $this->redirect(['controller' => 'Website', 'action' => 'home']);
        }
        $product_detail->rating = $this->Reviews->getAverageRating($product);

        $product_detail->total_review = $this->Reviews->getTotalReviews($product);
        $product_detail->discount = $this->Products->getDiscount($product);
        $product_detail->availability_status = $this->Products->singleProductStockStatus($product);
        $product_detail->product_image = '';
        $image = $this->ProductImages->getDefaultProductImage($product);
        if ($image) {
            $product_detail->product_image = $this->Media->getCloudFrontURL($image);
        }

        if (!empty($product_detail->product_images) && (is_array($product_detail->product_images) || is_object($product_detail->product_images))) {
            foreach ($product_detail->product_images as &$image) {
                if (!empty($image['image'])) {
                    $image['image'] = $this->Media->getCloudFrontURL($image['image']);
                }
                if (!empty($image['video'])) {
                    $image['video'] = $this->Media->getCloudFrontURL($image['video']);
                }
            }
            unset($image);
        }


        if (!empty($product_detail->product_variants) && (is_array($product_detail->product_variants) || is_object($product_detail->product_variants))) {
            foreach ($product_detail->product_variants as &$variants) {
                if (!empty($variants->product_variant_images) && (is_array($variants->product_variant_images) || is_object($variants->product_variant_images))) {

                    foreach ($variants->product_variant_images as &$image) {
                        if (!empty($image['image'])) {
                            $image['image'] = $this->Media->getCloudFrontURL($image['image']);
                        }
                        if (!empty($image['video'])) {
                            $image['video'] = $this->Media->getCloudFrontURL($image['video']);
                        }
                    }
                }
            }
            unset($variants);
         }

        $formattedCategories = [];
        if (!empty($product_detail->product_categories)) {
            foreach ($product_detail->product_categories as $productCategory) {
                $formattedCategories[] = [
                    'id' => $productCategory->id,
                    'url_key' => $productCategory->category->url_key,
                    'product_id' => $productCategory->product_id,
                    'category_id' => $productCategory->category_id,
                    'level' => $productCategory->level,
                    'category_name' => $productCategory->category->name, // Using the category name from the joined category
                ];
            }
        }


        $product_detail->product_categories = $formattedCategories;
        $product_detail->whishlist = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product);
        $similar_products = $this->Products->getSimilarProducts($product);

        foreach ($similar_products as $key => $similar_product) {
            $similar_products[$key]['product_image'] = '';
            $image = $this->ProductImages->getDefaultProductImage($similar_product['id']);
            if ($image) {
                $similar_products[$key]['product_image'] = $this->Media->getCloudFrontURL($image);
            }
            $similar_products[$key]['rating'] = $this->Reviews->getAverageRating($similar_product['id']);
        }
        $rating_reviews = $this->review($product, $sortBy, 1);
        $banner_ads = $this->BannerAds->getBannerAds('website', 'Banner', 'Product Page');
        foreach ($banner_ads as &$banner_ad) {
            if (!empty($banner_ad['web_image'])) {
                $banner_ad['web_image'] = $this->Media->getCloudFrontURL($banner_ad['web_image']);
            }
        }
        unset($banner_ad);
        $cities = $this->Cities->listCity();
        $offers = $this->Offers->homeOffers('mobile');
        foreach ($offers as &$offer) {
            if (!empty($offer['mobile_image'])) {
                $offer['mobile_image'] = $this->Media->getCloudFrontURL($offer['mobile_image']);
            }
        }
        $productData = $this->Products->find()
            ->contain([
                'ProductAttributes' => function ($q) {
                    return $q->where(['ProductAttributes.status' => 'A'])
                        ->contain(['Attributes', 'AttributeValues']);
                },
                'Suppliers'
            ])
            ->where(['Products.id' => $product])
            ->first();
 
        $product_detail->credit_partners = null;
        if($product_detail->avl_on_credit == 1){
            $product_detail->credit_partners = $this->ProductPaymentSettings->getCreditPartners($product);
        }
        $uniqueAttributes = [];
        if ($productData) {

            foreach ($productData->product_attributes as $productAttribute) {
                $pa_id = $productAttribute->id;
                $attributeId = $productAttribute->attribute->id;
                $attributeName = $productAttribute->attribute->name;
                $attributeValue = $productAttribute->attribute_value->value;
                $attributeValueId = $productAttribute->attribute_value->id;
                if (!isset($uniqueAttributes[$attributeName])) {
                    $uniqueAttributes[$attributeName] = [];
                }
                $exists = false;
                foreach ($uniqueAttributes[$attributeName] as $existingValue) {
                    if ($existingValue['id'] === $attributeValueId) {
                        $exists = true;
                        break;
                    }
                }

                if (!$exists) {
                    $uniqueAttributes[$attributeName][] = [
                        'id' => $pa_id,
                        // 'id' => $attributeValueId,
                        'value' => $attributeValue
                    ];
                }
            }
        }

        $supplierRenference = __("N/A");
        if(!empty($productData['reference_name']) && isset($productData['reference_name'])) {
            $supplierRenference = $productData['reference_name'];
        }

        $productCategoryName = '';
        if (!empty($product_detail->product_categories)) {
            $productCategoryName = $product_detail->product_categories[0]['category_name'] ?? '';
        }

        $productImages = [];
        if (!empty($product_detail->product_images) && is_array($product_detail->product_images)) {
            foreach ($product_detail->product_images as $img) {
                if (!empty($img['image'])) {
                    $productImages[] = $img['image'];
                }
            }
        }
        $productImages = implode(",", $productImages);
        // $productImages now contains a comma-separated list of image URLs
//        print_r(json_encode($productImages)); die;

//        print_r($product_detail); die;
        $siteUrl = Configure::read('Settings.SITE_URL');
        $currency = Configure::read('Settings.Currency.format.currency_symbol');

        $this->set(compact('currency','siteUrl','productImages','productCategoryName','myReview','supplierRenference','uniqueAttributes', 'userId', 'offers', 'product', 'product_detail', 'rating_reviews', 'similar_products', 'banner_ads', 'cities'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('product');
    }

    /**
     * Check stock status for a product with variant and attribute
     *
     * @return \Cake\Http\Response
     */
    public function checkStockStatus()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $productId = $this->request->getData('product_id');
        $variantId = $this->request->getData('variant_id');
        $attributeId = $this->request->getData('attribute_id');

        try {
            // Call the singleProductStockStatus function to get the stock status
            $stockStatus = $this->Products->singleProductStockStatus($productId, $variantId, $attributeId);

            $response = [
                'status' => 'success',
                'stock_status' => $stockStatus,
                'debug_info' => [
                    'product_id' => $productId,
                    'variant_id' => $variantId,
                    'attribute_id' => $attributeId
                ]
            ];
        } catch (\Exception $e) {
            // Log any exceptions
            $this->log('Error in checkStockStatus: ' . $e->getMessage(), 'error');

            $response = [
                'status' => 'error',
                'message' => 'An error occurred while checking stock status',
                'debug_info' => [
                    'error' => $e->getMessage(),
                    'product_id' => $productId,
                    'variant_id' => $variantId,
                    'attribute_id' => $attributeId
                ]
            ];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function review($id, $sortBy = 'newest', $page = 1, $limit = 2)
    {
        $rating_reviews = $this->Reviews->webProductReviews($id, $sortBy);
        // Apply pagination
        $this->paginate = [
            'limit' => $limit, // Number of reviews per page
            'order' => ['Reviews.created' => 'DESC'],
        ];

        try {
            // Paginate the query
            $reviews = $this->CustomPaginator->paginate($rating_reviews, [
                'limit' => $limit,
                'page' => $page,
            ]);

            if (!empty($reviews['items'])) {
                foreach ($reviews['items'] as &$rating_review) {
                    if ($rating_review['Customers']['profile_photo']) {
                        $rating_review['Customers']['profile_photo'] = $this->Media->getCloudFrontURL($rating_review['Customers']['profile_photo']);
                    }
                }
                unset($rating_review);
            }
            // Prepare the result
            $result = [
                'status' => __('success'),
                'code' => 200,
                'data' => $reviews,
            ];

            // Return JSON if AJAX request
            if ($this->request->is('ajax')) {
                $this->response = $this->response->withType('application/json')
                    ->withStringBody(json_encode($result));

                return $this->response;
            }

            // For non-AJAX requests, return array
            return $result;
        } catch (NotFoundException $e) {
            // Handle invalid page number gracefully
            $result = [
                'status' => __('error'),
                'code' => 404,
                'message' => __('No reviews found.'),
                'data' => [],
            ];

            if ($this->request->is('ajax')) {
                $this->response = $this->response->withType('application/json')
                    ->withStringBody(json_encode($result));

                return $this->response;
            }

            return $result;
        }
    }

    public function contactUs()
    {
        if (!$this->request->getSession()->read('Auth.User')) {
            $users = null;
            $orders = null;
        } else {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id','Users.first_name','Users.last_name','Users.email','country_code','mobile_no']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $this->identity->id])
                ->first(); 
            $orders = $this->Orders->getMyOrder($users->customer->id ?? null);
        }
       
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $ordernumber = $data['order_id'] ?? null;

            if($data['order_id'] && !empty($data['order_id']) && !$this->request->getSession()->read('Auth.User')) {
                $findOrderID = $this->Orders->find()
                    ->where(['Orders.order_number' => $data['order_id']])
                    ->select(['id'])
                    ->first();
                $ordernumber = $findOrderID->id ?? null;
                $data['order_id'] = $ordernumber ?? null;
            }
            elseif($data['order_id'] && !empty($data['order_id']) && $this->request->getSession()->read('Auth.User')) {
                $data['order_id'] = $ordernumber ?? null;
            }
            else{
                $data['order_id'] = null;
            }

            $timestamp = FrozenTime::now()->format('YmdHis');
            $randomNumber = mt_rand(1000, 9999);

            $data['ticketID'] = "$timestamp-$randomNumber";
            $data['customer_id'] = $users->customer->id ?? null;

            $adminUser = $this->Users->find()
                ->where(['role_id' => 1, 'status' => 'A'])
                ->first();

            $data['created_by'] = $users->id ?? ($adminUser->id ?? null);

            $supportTickets = TableRegistry::getTableLocator()->get('SupportTickets');
            $supportTicket = $supportTickets->newEntity($data);

            if ($supportTickets->save($supportTicket)) {

                // $title = 'Test Notification Dhiren';
                // $body  = 'Testing App Notification Dhiren';
                // $customData = []; // Optional

                // $response = $this->Global->sendNotification(
                //     [$deviceToken],
                //     $title,
                //     $body,
                //     $customData
                // );

                $this->Flash->websiteSuccess(__('Your message has been sent successfully.'));

                return $this->redirect(['action' => 'contactUs']);
            }

            $this->Flash->websiteError(__('Unable to save your request. Please try again.'));

            return $this->redirect(['action' => 'contactUs']);
        }
        $supportCategories = $this->supportCategories->find('all', [
            'contain' => ['SupportSubcategories']
        ])->order(['SupportCategories.name' => 'ASC']);
            //dd($users->email);
        $this->viewBuilder()->setTemplatePath('Home');
        $this->set(compact('supportCategories','users','orders'));
        $this->render('contact_us');
    }

    public function logout()
    {
        $this->request->getSession()->delete('Auth.User');

        $this->Flash->toast(__('You have been logged out successfully!'), [
            'element' => 'toast',
            'params' => ['type' => 'success']
        ]);
        return $this->redirect('/website/home/');
    }
    public function deletelogout()
    {
        $this->request->getSession()->delete('Auth.User');

        return $this->redirect('/customer/login/');
    }

    public function callApi($apiUrl)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ]);
        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function search()
    {
        $this->autoRender = false;
        $this->response = $this->response->withType('application/json');

        $query = $this->request->getQuery('q');
        $products = $this->Products->searchWebAPI($query);

        $finalProducts = [];
        foreach ($products as $product) {
            $product['rating'] = $this->Reviews->getAverageRating($product['id']);
            $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
            $product['discount'] = $this->Products->getDiscount($product['id']);
            $product['slug_url'] = $product['url_key'];  // Include slug_url in response

            $formattedCategories = [];
            if (!empty($product->product_categories)) {
                foreach ($product->product_categories as $productCategory) {
                    $formattedCategories[] = [
                        'id' => $productCategory->id,
                        'product_id' => $productCategory->product_id,
                        'category_id' => $productCategory->category_id,
                        'level' => $productCategory->level,
                        'category_name' => $productCategory->category->name
                    ];
                }
            }

            $product['product_categories'] = $formattedCategories;


            if($product['product_image'] == null){
                $image = $this->ProductImages->getDefaultProductImage($product['id']);
                if ($image) {
                    $product['product_image'] = $this->Media->getCloudFrontURL($image);
                }
            }else{
                $product['product_image'] = $this->Media->getCloudFrontURL($product['product_image']);
            }


            // Add the main product to the final list
            $finalProducts[] = $product;

            // Check for variants and add them as separate products
            // if (!empty($product['variants'])) {
            //     foreach ($product['variants'] as $variant) {
            //         $variantProduct = $product; // Clone the main product
            //         $variantProduct['variant_name'] = $variant['variant_name'];
            //         $variantProduct['id'] = $variant['id']; // Use variant ID
            //         $variantProduct['slug_url'] = $variant['url_key']; // Use variant slug
            //         $variantProduct['product_image'] = '';
            //         $variantImage = $this->ProductImages->getDefaultProductImage($variant['id']);
            //         if ($variantImage) {
            //             $variantProduct['product_image'] = $this->Media->getCloudFrontURL($variantImage);
            //         }
            //         $finalProducts[] = $variantProduct;
            //     }
            // }
        }

        return $this->response->withStringBody(json_encode(['products' => $finalProducts]));
    }

    public function cmsPage($slug = null)
    {

        if (empty($slug)) {
            $path = $this->request->getPath();
            if (strpos($path, '/cms/') === 0) {
                $slug = substr($path, 5); // Remove '/cms/' from the beginning
            }
        }

        if (empty($slug)) {
            throw new NotFoundException(__('Page not found'));
        }

        $page = $this->ContentPages->find()
        ->where([
            'ContentPages.status' => 'A',
            'ContentPages.published' => 'Yes',
            'OR' => [
                'ContentPages.url_key' => $slug,
                'ContentPages.title' => $slug
            ]
        ])
        ->first();

        if (empty($page)) {
            throw new NotFoundException(__('Page not found'));
        }

        // Set meta tags for SEO
        if (!empty($page->seo_title)) {
            $this->set('meta_title', $page->seo_title);
        }
        if (!empty($page->meta_description)) {
            $this->set('meta_description', $page->meta_description);
        }
        if (!empty($page->seo_keyword)) {
            $this->set('meta_keywords', $page->seo_keyword);
        }

        $this->set(compact('page'));
        $this->viewBuilder()
            ->setTemplatePath('Website')
            ->setTemplate('cms_page');
    }

    /**
     * Redis Cache Example
     * This method demonstrates how to use Redis cache in CakePHP
     *
     * @return \Cake\Http\Response|null
     */

/*
    public function redisExample()
    {
        // 1. Basic Cache Read/Write Example
        $cacheKey = 'simple_test_key';
        $newValue = 'This is a test value generated at ' . date('Y-m-d H:i:s');

        // Write to cache
        Cache::write($cacheKey, $newValue, 'default');

        // Read from cache
        $cachedValue = Cache::read($cacheKey, 'default');

        // Debug Cache Configuration
        $cacheConfig = Cache::getConfig('default');
        $engine = Cache::pool('default');
        $engineClass = get_class($engine);

        $cacheInfo = [
            'cached_value' => $cachedValue,
            'engine_class' => $engineClass,
            'config' => $cacheConfig,
        ];

        // Test Direct Redis Connection
        try {
            // Check if Predis is installed
            if (!class_exists('\Predis\Client')) {
                // Try to install Predis using Composer
                $cacheInfo['direct_redis'] = [
                    'status' => 'error',
                    'message' => 'Predis client not available. Please run "composer require predis/predis" to install it.',
                ];
            } else {
                // Try to connect to Redis directly using Predis
                $redis = new \Predis\Client([
                    'scheme' => 'tcp',
                    'host'   => '**************',
                    'port'   => 6379,
                ]);

                // Test connection by setting a value
                $redis->set('direct_test_key', 'This redis cache value store from website controller by axit');
                $directValue = $redis->get('direct_test_key');

                $cacheInfo['direct_redis'] = [
                    'status' => 'connected',
                    'test_key' => $directValue,
                ];

                // List all keys in Redis
                $allKeys = $redis->keys('*');
                $cacheInfo['all_redis_keys'] = $allKeys;
            }
        } catch (\Exception $e) {
            $cacheInfo['direct_redis'] = [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }

        $this->set('cacheInfo', $cacheInfo);
        $this->viewBuilder()->setTemplatePath('Website');
        $this->render('redis_example');
    }
*/
    public function creditPayment($productId = null)
    {
        $identity = $this->request->getSession()->read('Auth.User');


        $variantId = $this->request->getQuery('variant') ?? null; // Fetch the 'variant' query parameter from the URL

        if (!$identity) {
            $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
        } else {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id','Users.first_name','Users.last_name','Users.email','country_code','mobile_no']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();
        }

        // Redirect if productId is empty
        if (empty($productId) OR empty($this->identity)) {
            return $this->redirect(['controller' => 'Website', 'action' => 'Home']); // Change as needed
        }
        $information = $this->ProductPaymentSettings->getCreditPartners($productId);

        if (empty($information)) {

            $this->Flash->toast(__('Credit partners not found for this product. Please try again later.'), [
                'element' => 'toast',
                'params' => ['type' => 'warning']
            ]);
            return $this->redirect($this->referer(['controller' => 'Website', 'action' => 'product', $productId]));
        }

        if(isset($information[0]['product']['product_price'])){
            $information[0]['product']['promotion_price'] = $this->Products->getProductPrice($productId);
        }
      // dd($information);

        $this->set(compact('information','users','productId','variantId'));
        $this->viewBuilder()
            ->setTemplatePath('Home')
            ->setTemplate('paybycredit');
    }

    public function shopList()
    {
        $showrooms = $this->Showrooms->showRoomListWithImages();
     
        foreach ($showrooms as &$showroom) {
            foreach ($showroom['showroom_images'] as &$image) {
                if (!empty($image['image'])) {
                    $image['image'] = $this->Media->getCloudFrontURL($image['image']);
                }
            }
            unset($image);
        }
        unset($showroom);
        
        $this->set(compact('showrooms'));
        $this->viewBuilder()
            ->setTemplatePath('Website')
            ->setTemplate('shops');
    }


      /* Credit Payments */
      public function addCreditApplicationsWeb()
      {
          if (!$this->request->is('post')) {
              return $this->response->withType('application/json')->withStatus(400)
                          ->withStringBody(json_encode([
                  'status' => __('error'),
                  'code' => 400,
                  'message' => __('Method not allowed')
              ]));
          }

          $data = $this->request->getData();

          $crossVerifiedFuction = $this->WebsiteFunction->checkPaybyCreditAmount($data);

        if($crossVerifiedFuction['status'] == false){
            return $this->response->withType('application/json')->withStatus(400)
            ->withStringBody(json_encode([
                'status' => __('error'),
                'code' => 400,
                'dat'=>$crossVerifiedFuction,
                'message' => __('Someting went wrong!'),
            ]));
        }
          // $guestToken = $this->request->getSession()->read('cartId') ?? null; // 7d1c54ba-05d8-4a5b-bad4-ed4381486858

          $this->identity = $this->request->getSession()->read('Auth.User');

            if (!$this->identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
            } else {
                $users = $this->Users->find()
                    ->contain([
                        'Customers' => function ($q) {
                            return $q->select(['id']); // Select only the Customer.id field
                        }
                    ])
                    ->select(['Users.id','Users.first_name','Users.last_name','Users.email','country_code','mobile_no']) // Select the necessary fields from Users
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $this->identity->id])
                    ->first();
            }


          $data['cart_id'] = $this->identity->id;

          // Required fields for CreditApplications
          $creditApplicationFields = ['registration_id', 'name', 'email', 'country_code', 'phone_number'];

          // Required fields for CartCreditPayments
          $cartCreditPaymentFields = [
              'cart_id',
              'product_id',
              'credit_payment_terms_id',
              'emi_interest_percentage',
              'emi_interest_amount',
              'total_emi_amount',
              'status'
          ];

          // Validate CreditApplications fields
          foreach ($creditApplicationFields as $field) {
              if (empty($data[$field])) {
                   return $this->response->withType('application/json')->withStatus(400)
                      ->withStringBody(json_encode([
                      'status' => __('error'),
                      'code' => 400,
                      'message' => __("Field {0} is required", [ucwords(str_replace('_', ' ', $field))])
                   ]));
              }
          }

          // Validate CartCreditPayments fields
          foreach ($cartCreditPaymentFields as $field) {
              if (empty($data[$field])) {

                   return $this->response->withType('application/json')->withStatus(200)
                      ->withStringBody(json_encode([
                      'status' => __('error'),
                      'code' => 200,
                      'message' => __("Field {0} is required", [ucwords(str_replace('_', ' ', $field))])
                  ]));
              }
          }

          // Check if registration_id already exists
          $existingApplication = $this->CreditApplications->find()
              ->where(['registration_id' => $data['registration_id']])
              ->first();

          if ($existingApplication) {

               return $this->response->withType('application/json')->withStatus(400)->withStringBody(json_encode([
                  'status' => __('error'),
                  'code' => 400,
                  'message' => __('Registration ID already exists')
              ]));
          }

          // Get Customer ID or use provided one
         // $customerId = $identity ? $identity->get('_matchingData')['Customers']['id'] : null;
          $customer_id = $users->customer->id;
          // Save Credit Application
          $application = $this->CreditApplications->newEntity([
              'customer_id' => $customer_id,
              'registration_id' => $data['registration_id'],
              'name' => $data['name'],
              'email' => $data['email'],
              'country_code' => $data['country_code'],
              'phone_number' => $data['phone_number']
          ]);

          if (!$this->CreditApplications->save($application)) {

               return $this->response->withType('application/json')->withStatus(400)
                      ->withStringBody(json_encode([
                  'status' => __('error'),
                  'code' => 400,
                  'message' => __('Failed to create Credit Application')
              ]));
          }
          $cartId = $this->Carts->getCartItems($customer_id);
          // Save Cart Credit Payment
          $cartCreditPayment = $this->CartCreditPayments->newEntity([
              'cart_id' => $cartId->id,
              'customer_id' => $customer_id,
              'product_id' => $data['product_id'],
              'product_variant_id' => $data['product_variant_id'] ?? null,
              'credit_application_id' => $application->id,
              'credit_payment_terms_id' => $data['credit_payment_terms_id'],
              'emi_interest_percentage' => $data['emi_interest_percentage'],
              'emi_interest_amount' => $data['emi_interest_amount'],
              'total_emi_amount' => $data['total_emi_amount'],
              'status' => $data['status']
          ]);
          if (!$this->CartCreditPayments->save($cartCreditPayment)) {
            $errors = $cartCreditPayment->getErrors(); // Get validation errors
            \Cake\Log\Log::error('Cart Credit Payment save failed: ' . json_encode($errors)); // Log errors for debugging
            return $this->response->withType('application/json')->withStatus(400)
                    ->withStringBody(json_encode([
                'status' => __('error'),
                'code' => 400,
                'message' => __('Failed to save Cart Credit Payment'),
                'errors' => $errors // Include errors in the response
            ]));
        }

        //   if (!$this->CartCreditPayments->save($cartCreditPayment)) {

        //        return $this->response->withType('application/json')->withStatus(400)
        //               ->withStringBody(json_encode([
        //           'status' => __('error'),
        //           'code' => 400,
        //           'message' => __('Failed to save Cart Credit Payment')
        //       ]));
        //   }


          return $this->response->withType('application/json')->withStatus(200)
                      ->withStringBody(json_encode([
              'status' => __('success'),
              'code' => 200,
              'message' => __('Credit Application and Cart Credit Payment saved successfully'),
              'data' => [
                  'credit_application_id' => $application->id,
                  'cart_credit_payment_id' => $cartCreditPayment->id
              ]
          ]));


      }

      /* Submit Credit Application for Checkout */
      public function submitCreditApplication()
      {
          if (!$this->request->is('post')) {
              return $this->response->withType('application/json')->withStatus(400)
                          ->withStringBody(json_encode([
                  'status' => 'error',
                  'code' => 400,
                  'message' => __('Method not allowed')
              ]));
          }

          $data = $this->request->getData();

          // Check if user is logged in
        //   $identity = $this->request->getAttribute('identity');
        //   if (!$identity) {
        //       return $this->response->withType('application/json')->withStatus(401)
        //                   ->withStringBody(json_encode([
        //           'status' => 'error',
        //           'code' => 401,
        //           'message' => __('Please login to submit credit application')
        //       ]));
        //   }

        //   $users = $this->Users->get($identity->id, ['contain' => ['Customers']]);
        //   $customer_id = $users->customer->id;
          



        $this->identity = $this->request->getSession()->read('Auth.User');
        if (!$this->identity) {
            $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
            $myReview = null;
        } else {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id','Users.first_name','Users.last_name','Users.email','country_code','mobile_no']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $this->identity->id])
                ->first();
                $customer_id = $users->customer->id;
        }


          
          // Required fields for CreditApplications
          $creditApplicationFields = ['registration_id', 'name', 'email', 'country_code', 'phone_number'];

          // Validate CreditApplications fields
          foreach ($creditApplicationFields as $field) {
              if (empty($data[$field])) {
                   return $this->response->withType('application/json')->withStatus(400)
                      ->withStringBody(json_encode([
                      'status' => 'error',
                      'code' => 400,
                      'message' => __("Field {0} is required", [ucwords(str_replace('_', ' ', $field))])
                   ]));
              }
          }

          // Check if registration_id already exists
          $existingApplication = $this->CreditApplications->find()
              ->where(['registration_id' => $data['registration_id']])
              ->first();

          if ($existingApplication) {
               return $this->response->withType('application/json')->withStatus(400)->withStringBody(json_encode([
                  'status' => 'error',
                  'code' => 400,
                  'message' => __('Registration ID already exists')
              ]));
          }

          // Save Credit Application
          $application = $this->CreditApplications->newEntity([
              'customer_id' => $customer_id,
              'registration_id' => $data['registration_id'],
              'name' => $data['name'],
              'email' => $data['email'],
              'country_code' => $data['country_code'],
              'phone_number' => $data['phone_number'],
              'status' => 'pending'
          ]);

          if (!$this->CreditApplications->save($application)) {
               return $this->response->withType('application/json')->withStatus(400)
                      ->withStringBody(json_encode([
                  'status' => 'error',
                  'code' => 400,
                  'message' => __('Failed to create Credit Application')
              ]));
          }

          return $this->response->withType('application/json')->withStatus(200)
                      ->withStringBody(json_encode([
              'status' => 'success',
              'code' => 200,
              'message' => __('Credit Application submitted successfully'),
              'credit_application_id' => $application->id
          ]));
      }

      public function addReview($productId = null)
      {
          $this->response = $this->response->withType('application/json');

          if (!$this->request->is('post')) {
              $result = ['status' => __('error'), 'message' => __('Method Not Allowed')];
              return $this->response->withStringBody(json_encode($result));
          }


          $data = $this->request->getData();
          $identity = $this->request->getSession()->read('Auth.User');

          if (!$identity) {
              $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
              return $this->response->withStringBody(json_encode($result));
          }

          $users = $this->Users->find()
              ->contain([
                  'Customers' => function ($q) {
                      return $q->select(['id']); // Select only the Customer.id field
                  }
              ])
              ->select(['Users.id']) // Select the necessary fields from Users
              ->where(['Users.status' => 'A'])
              ->where(['Users.id' => $identity->id])
              ->first();

          $customer_id = $users->customer->id;



          if ($data['rating'] < 5 && empty($data['review'])) {
              $result = ['status' => __('error'), 'message' => __('A review comment is required when the rating is less than 5.')];
              return $this->response->withStringBody(json_encode($result));
          }

          $reviewData = [
              'customer_id' => $customer_id,
              'product_id' => $productId,
              'rating' => $data['rating'],
              'comment' => $data['review'] ?? null
          ];

          $reviewId = $this->Reviews->isReviewExistsOrAdd($reviewData);

          if ($reviewId['status']==true) {
              $result = ['status' => __('success'), 'message' => $reviewId['message']];
          } else {
              $result = ['status' => __('error'), 'message' => $reviewId['message']];
          }
          return $this->response->withType('application/json')->withStatus(200)
          ->withStringBody(json_encode($result));
      }

      /**
       * Update an existing review
       *
       * @param int|null $reviewId The ID of the review to update
       * @return \Cake\Http\Response
       */
      public function updateReview($reviewId = null)
      {
          $this->response = $this->response->withType('application/json');

          if (!$this->request->is('post')) {
              $result = ['status' => false, 'message' => __('Method Not Allowed')];
              return $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
          }

          if (empty($reviewId)) {
              $result = ['status' => false, 'message' => __('Review ID is required')];
              return $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
          }

          $data = $this->request->getData();
          $identity = $this->request->getSession()->read('Auth.User');

          if (!$identity) {
              $result = ['status' => false, 'message' => __('User is not authenticated')];
              return $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
          }

          // Get the customer ID
          $users = $this->Users->find()
              ->contain([
                  'Customers' => function ($q) {
                      return $q->select(['id']);
                  }
              ])
              ->select(['Users.id'])
              ->where(['Users.status' => 'A'])
              ->where(['Users.id' => $identity->id])
              ->first();

          $customer_id = $users->customer->id;

          // Find the review to update
          $review = $this->Reviews->find()
              ->where([
                  'id' => $reviewId,
                  'customer_id' => $customer_id
              ])
              ->first();

          if (!$review) {
              $result = ['status' => false, 'message' => __('Review not found or you do not have permission to edit it')];
              return $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
          }

          // Validate the data
          if ($data['rating'] < 5 && empty($data['review'])) {
              $result = ['status' => false, 'message' => __('A review comment is required when the rating is less than 5.')];
              return $this->response->withType('application/json')->withStatus(200)
              ->withStringBody(json_encode($result));
          }

          // Update the review
          $review->rating = $data['rating'];
          $review->comment = $data['review'] ?? $review->comment;
          $review->modified = new \Cake\I18n\FrozenTime(); // Update the modified timestamp

          if ($this->Reviews->save($review)) {
              $result = ['status' => true, 'message' => __('Review updated successfully')];
          } else {
              $result = ['status' => false, 'message' => __('Failed to update review')];
          }

          return $this->response->withType('application/json')->withStatus(200)
          ->withStringBody(json_encode($result));
      }


    public function deals()
    {
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('deals');
    }

    /**
     * Validate credit eligibility for cart items
     */
    public function validateCreditEligibility()
    {
        if (!$this->request->is('post')) {
            return $this->response->withType('application/json')->withStatus(400)
                        ->withStringBody(json_encode([
                'status' => 'error',
                'code' => 400,
                'message' => __('Method not allowed')
            ]));
        }

        $data = $this->request->getData();
        $productIds = $data['product_ids'] ?? [];

        if (empty($productIds) || !is_array($productIds)) {
            return $this->response->withType('application/json')->withStatus(400)
                        ->withStringBody(json_encode([
                'status' => 'error',
                'code' => 400,
                'message' => __('No product IDs provided')
            ]));
        }

        // Load Products table
        $productsTable = $this->fetchTable('Products');

        // Check if all products have avl_on_credit = 1
        $products = $productsTable->find()
            ->select(['id', 'name', 'avl_on_credit'])
            ->where(['id IN' => $productIds])
            ->all();

        $notAvailableProducts = [];
        foreach ($products as $product) {
            if ((int)$product->avl_on_credit !== 1) {
                $notAvailableProducts[] = $product->name;
            }
        }

        if (!empty($notAvailableProducts)) {
            $message = __('The following items are not available for credit payment: {0}. Please remove them from your cart to proceed with credit payment.',
                implode(', ', $notAvailableProducts));

            return $this->response->withType('application/json')
                        ->withStringBody(json_encode([
                'status' => 'error',
                'message' => $message,
                'not_available_products' => $notAvailableProducts
            ]));
        }

        return $this->response->withType('application/json')
                    ->withStringBody(json_encode([
            'status' => 'success',
            'message' => __('All items are available for credit payment')
        ]));
    }


}
