<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Warehouse $warehouse
 * @var \Cake\Collection\CollectionInterface|string[] $users
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/intlTelInput/css/intlTelInput.min.css') ?>">
    <script src="https://maps.googleapis.com/maps/api/js?key=<?= h($mapApiKey) ?>&libraries=places"></script>
    <style>
        input,
        select,
        textarea {
            width: 300px;
            padding: 5px;
            margin-bottom: 10px;
        }

        .tel {
            width: 100%;
        }

        .iti {
            width: 100%;
        }

        .iti .iti-flag {
            width: 20px;
            height: 14px;
            margin-right: 5px;
        }

        .iti .iti-country {
            display: flex;
            align-items: center;
        }

        .iti .iti-country .iti-flag {
            margin-right: 5px;

            .iti .iti-country .iti-dial-code {
                font-size: 16px;
                margin-left: 5px;
            }

            .iti {
                width: 100%;
                box-sizing: border-box;
            }

            .iti__input {
                width: 100%;
                box-sizing: border-box;
            }

            .tel {
                width: 100% !important;
                box-sizing: border-box;
            }

        }

        .currency-block {
            width: 14% !important;
        }

        .is-invalid-select {
            border-color: #dc3545 !important;
            padding-right: calc(1.5em + .75rem);
            background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
            background-repeat: no-repeat;
            background-position: right calc(.375em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem);
        }
        #map {

            height: 400px;
            width: 100%;
        }
    </style>
</head>
<?php $this->end(); ?>

<body>
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                    <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Warehouses', 'action' => 'index']) ?>">
                    <?= __('Warehouses') ?>
                </a>
            </li>
            <li class="breadcrumb-item active">
                <?= __('Add') ?>
            </li>
        </ul>
        <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
            <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
        </a>
    </div>


    <div class="section-body">
        <div class="container-fluid">
            <div class="card">
                <?php echo $this->Form->create($warehouse, ['id' => 'addWarehousesForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
                <h6 class="m-b-20" style="color: #004958">
                    <?= __('Add Warehouse') ?>
                </h6>
                <div class="form-group row">
                    <label for="warehouse-name" class="col-sm-2 col-form-label fw-bold"><?= __('Warehouse Name') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('name', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'warehouse-name',
                            'placeholder' => __('Enter Warehouse Name'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- <div class="form-group row">
                    <label for="warehouse-email" class="col-sm-2 col-form-label fw-bold">< ?= __('Email') ?>
                    </label>
                    <div class="col-sm-5">
                        < ?php echo $this->Form->control('email', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'warehouse-email',
                            'placeholder' => __('Enter Warehouse Email'),
                            'label' => false,
                        ]); ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div> -->

                <div class="form-group row">
                    <label for="house-area" class="col-sm-2 col-form-label fw-bold"><?= __('Warehouse No/Area') ?><sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('warehouse_no_area', [
                            'type' => 'textarea',
                            'class' => 'form-control',
                            'id' => 'house-area',
                            'placeholder' => __('Enter House No/Area'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- <div class="form-group row">
                    <label for="warehouse-address" class="col-sm-2 col-form-label fw-bold">< ?= __('Address') ?><sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        < ?php echo $this->Form->control('address_line1', [
                            'type' => 'textarea',
                            'class' => 'form-control',
                            'id' => 'warehouse-address',
                            'placeholder' => __('Enter Warehouse Address'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div> -->

                <div class="form-group row">
                    <label for="warehouse-address" class="col-sm-2 col-form-label fw-bold"><?= __('Address') ?> <sup class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5 main-field">
                        <input type="hidden" id="latitude" name="latitude">
                        <input type="hidden" id="longitude" name="longitude">
                        <?php echo $this->Form->control('address_line1', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'warehouse-address',
                            'placeholder' => __('Enter Warehouse Address'),
                            'label' => false,
                            'readonly' => true
                        ]); ?>
                        <div id="map" class="mt-2"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="city" class="col-sm-2 col-form-label fw-bold"><?= __('City') ?><sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('city_id', [
                            'type' => 'select',
                            'id' => 'city',
                            'options' => $cities,
                            'class' => 'form-control select2',
                            'label' => false,
                            'empty' => __('Select a City'),
                            'required' => true
                        ]) ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <div class="form-group row municipality">
                    <label for="municiplaity" class="col-sm-2 col-form-label fw-bold"><?= __('Municipality') ?><sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('municipality_id', [
                            'type' => 'select',
                            'id' => 'municiplaity',
                            'options' => $municipalities,
                            'class' => 'form-control select2',
                            'label' => false,
                            'empty' => __('Select a Municipality'),
                            'required' => true
                        ]) ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- <div class="form-group row">
                    <label for="warehouse-phone" class="col-sm-2 col-form-label fw-bold">< ?= __('Phone No') ?><sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <div class="input-group">
                            < ?php echo $this->Form->control('warehouse_phone_no', [
                                'type' => 'tel',
                                'class' => 'form-control tel',
                                'id' => 'warehouse-phone',
                                'label' => false,
                                'placeholder' => __('Enter Warehouse Phone'),
                                'required' => true,
                                'maxlength' => '20'
                            ]); ?>
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                    <input type="hidden" id="country-code-warehouse" name="warehouse_country_code" value="">
                </div> -->

                <div class="form-group row">
                    <label for="manager-name" class="col-sm-2 col-form-label fw-bold"><?= __('Manager Name') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('manager_id', [
                            'type' => 'select',
                            'id' => 'manager-name',
                            'options' => $users,
                            'class' => 'form-control select2',
                            'label' => false,
                            'empty' => __('Select a Manager'),
                            'required' => true
                        ]) ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="manager-email" class="col-sm-2 col-form-label fw-bold"><?= __('Manager Email') ?>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('manager_email', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'manager-email',
                            'placeholder' => __('Enter Manager Email'),
                            'label' => false,
                            'readonly' => 'readonly'
                        ]); ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="manager-phone" class="col-sm-2 col-form-label fw-bold"><?= __('Manager Phone') ?></label>
                    <div class="col-sm-5">
                        <div class="input-group">
                            <?php echo $this->Form->control('manager_phone', [
                                'type' => 'tel',
                                'class' => 'form-control tel',
                                'id' => 'manager-phone',
                                'label' => false,
                                'placeholder' => __('Enter Manager Phone'),
                                'readonly' => 'readonly',
                                'maxlength' => '20'
                            ]); ?>
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>

                    <input type="hidden" id="country-code-manager" name="manager_country_code" value="">
                </div>

                <div class="form-group row">
                    <label for="assistant-name" class="col-sm-2 col-form-label fw-bold"><?= __('Warehouse Assistant') ?>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('assistant_id', [
                            'type' => 'select',
                            'id' => 'assistant-name',
                            'options' => $assistant,
                            'class' => 'form-control select2',
                            'label' => false,
                            'empty' => __('Select a Warehouse Assistant')
                        ]) ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="capacity" class="col-sm-2 col-form-label fw-bold"><?= __('Capacity in Tonnes') ?>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('capacity', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'capacity',
                            'placeholder' => __('Enter Capacity in Tonnes'),
                            'label' => false
                        ]); ?>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-sm-10 offset-sm-2">
                        <button type="submit" id="btnSubmit" class="btn"><?= __('Save') ?></button>
                        <button type="reset" class="btn"><?= __('Reset') ?></button>
                    </div>
                </div>
                </form>
            </div>
        </div>
    </div>
</body>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/image.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/intlTelInput/js/intlTelInput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"></script>

<script>

    let map;
    let marker;
    let geocoder;

    function initMap() {
        // Initialize map at a default location
        const defaultLocation = { lat: 5.359952, lng: -4.008256 }; // Abidjan, Ivory Coast

        // Initialize map
        map = new google.maps.Map(document.getElementById("map"), {
            center: defaultLocation,
            zoom: 14,
        });

        // Initialize geocoder
        geocoder = new google.maps.Geocoder();

        // Add a draggable marker
        marker = new google.maps.Marker({
            position: defaultLocation,
            map: map,
            draggable: true,
        });

        // Fetch address on marker drag
        marker.addListener("dragend", () => {
            const position = marker.getPosition();
            updateLatLngInputs(position.lat(), position.lng());
            fetchAddress(position.lat(), position.lng());
        });

        // Set default values in hidden fields
        document.getElementById("latitude").value = defaultLocation.lat;
        document.getElementById("longitude").value = defaultLocation.lng;

        // Fetch address on initial load
        fetchAddress(defaultLocation.lat, defaultLocation.lng);
    }

    function updateLatLngInputs(lat, lng) {
        document.getElementById("latitude").value = lat;
        document.getElementById("longitude").value = lng;
    }

    function fetchAddress(lat, lng) {
        const latlng = { lat: parseFloat(lat), lng: parseFloat(lng) };

        geocoder.geocode({ location: latlng }, (results, status) => {
            if (status === "OK") {
                if (results[0]) {
                    // Display the address in the input field
                    document.getElementById("warehouse-address").value = results[0].formatted_address;
                    console.log("Address:", results[0].formatted_address);
                } else {
                    console.log("No results found");
                    document.getElementById("address").value = "No address found";
                }
            } else {
                console.log("Geocoder failed due to: " + status);
                document.getElementById("address").value = "Error fetching address";
            }
        });
    }

    // Initialize the map when the page loads
    window.onload = initMap;

    $(document).ready(function() {
        $('.select2').select2({
            minimumResultsForSearch: 0
        });

        // var input = document.querySelector("#warehouse-phone");
        // var iti = window.intlTelInput(input, {
        //     initialCountry: "ci",
        //     separateDialCode: true,
        //     preferredCountries: ["ci", "us", "gb"],
        //     utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
        // });

        // function updateCountryCode() {

        //     $('#country-code-warehouse').val(iti.getSelectedCountryData().dialCode);
        //     // var countryData = iti.getSelectedCountryData();
        //     // var countryCode = countryData.dialCode;
        //     // var countryCodeText = `+${countryCode} `;
        //     // input.placeholder = countryCodeText;
        //     // input.value = countryCodeText;
        //     // document.getElementById("country-code-warehouse").value = countryCode;
        // }

        // input.addEventListener("countrychange", updateCountryCode);
        // updateCountryCode();

        var mn_ph_input = document.querySelector("#manager-phone");
        var mn_ph_iti = window.intlTelInput(mn_ph_input, {
            initialCountry: "ci",
            separateDialCode: true,
            preferredCountries: ["ci", "us", "gb"],
            utilsScript: "<?= $this->Url->webroot('bundles/intlTelInput/js/utils.js') ?>"
        });

        function updateCountryCodeManager() {
            $('#country-code-manager').val(mn_ph_iti.getSelectedCountryData().dialCode);
            // var countryData = mn_ph_iti.getSelectedCountryData();
            // var countryCode = countryData.dialCode;
            // var countryCodeText = `+${countryCode} `;
            // mn_ph_input.placeholder = countryCodeText;
            // mn_ph_input.value = countryCodeText;
            // document.getElementById("country-code-manager").value = countryCode;
        }

        mn_ph_input.addEventListener("countrychange", updateCountryCodeManager);
        updateCountryCodeManager();

        var abidjanCityId = <?= $ABIDJAN_CITY_ID ?>;

        if ($('#city').val() == abidjanCityId) {
            $('.municipality').show();
        } else {
            $('.municipality').hide();
        }

        $('#city').change(function() {
            if ($(this).val() == abidjanCityId) {
                $('.municipality').show();
            } else {
                $('.municipality').hide();
            }
        });

        function validateForm() {
            let isValid = true;

            $('#addWarehousesForm').find('input[required]:visible, textarea[required]:visible, select[required]:visible').each(function() {
                let value = $(this).val().trim();
                let isSelect2 = $(this).hasClass('select2');

                if (value === '') {
                    if (!$(this).is('input[type="tel"]')) {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                        }
                    } else if ($(this).is('input[type="tel"]')) {
                        $(this).addClass('is-invalid-select');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                    }
                } else {
                    if ($(this).is('input[type="tel"]')) {
                        if ($(this).val().length <= 8) {
                            $(this).addClass('is-invalid-select');
                            let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                            let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                            feedback.text('<?= __("Please enter valid") ?>' + fieldName.toLowerCase() + '.').show();
                            isValid = false;
                        } else {
                            $(this).removeClass('is-invalid-select');
                            let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                            feedback.hide();
                        }
                    } else {
                        $(this).removeClass('is-invalid-select');
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                        }
                    }

                }
            });
            if (isValid) {
                var emailField = $('#warehouse-email').val();
                if (emailField) {
                    var emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailPattern.test(emailField)) {
                        isValid = false;
                        $('#warehouse-email').addClass('is-invalid');
                        let feedback = $('#warehouse-email').closest('.form-group').find('.invalid-feedback');
                        feedback.text('<?= __("Please enter a valid email address.") ?>').show();
                    } else {
                        $('#email').removeClass('is-invalid');
                        let feedback = $('#warehouse-email').closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                    }
                }
            }
            return isValid;
        }

        $('#btnSubmit').click(function(event) {
            event.preventDefault();
            if (!validateForm()) {
                return;
            }
            var form = $('#addWarehousesForm')[0];
            form.action = "<?= $this->Url->build(['controller' => 'Warehouses', 'action' => 'add']) ?>";
            $('#btnSubmit').attr('disabled', true);
            form.submit();
        });


        $('#manager-name').change(function() {
            var managerId = $(this).val();
            $('#manager-email').val('');
            mn_ph_input.value = "";
            mn_ph_iti.setCountry("ci");

            if (managerId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Users', 'action' => 'getManagerDetails']) ?>/' + managerId,
                    type: 'GET',
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#manager-email').val(response.managerDetails.email);
                            const existingPhoneNumber = response.managerDetails.phone;
                            const existingCountryCode = response.managerDetails.country_code;

                            const countryData = window.intlTelInput.getCountryData();

                            const foundCountry = countryData.find(country => country.dialCode === existingCountryCode);

                            if (foundCountry) {
                                mn_ph_iti.setCountry(foundCountry.iso2);
                                updateCountryCodeManager();
                                mn_ph_input.value = mn_ph_input.value + existingPhoneNumber;
                                document.getElementById("country-code-manager").value = existingCountryCode;
                            } else {
                                mn_ph_iti.setCountry("ci");
                                updateCountryCodeManager();
                                mn_ph_input.value = existingPhoneNumber;
                            }
                        } else {
                            swal('Error', response.message, 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        swal('Error', 'There was an error fetching manager details.', 'error');
                    }
                });
            } else {
                $('#manager-details').hide();
            }
        });


    });
</script>
<?php $this->end(); ?>