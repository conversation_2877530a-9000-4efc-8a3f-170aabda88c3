<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Database\Expression\QueryExpression;
use Cake\ORM\Locator\TableLocator;
use Cake\I18n\FrozenTime;
use Cake\Datasource\EntityInterface;

/**
 * Products Model
 *
 * @property \App\Model\Table\BrandsTable&\Cake\ORM\Association\BelongsTo $Brands
 * @property \App\Model\Table\SuppliersTable&\Cake\ORM\Association\BelongsTo $Suppliers
 * @property \App\Model\Table\BannerAdsTable&\Cake\ORM\Association\HasMany $BannerAds
 * @property \App\Model\Table\CartItemsTable&\Cake\ORM\Association\HasMany $CartItems
 * @property \App\Model\Table\InventoriesTable&\Cake\ORM\Association\HasMany $Inventories
 * @property \App\Model\Table\OrderItemsTable&\Cake\ORM\Association\HasMany $OrderItems
 * @property \App\Model\Table\ProductAttributesTable&\Cake\ORM\Association\HasMany $ProductAttributes
 * @property \App\Model\Table\ProductCategoriesTable&\Cake\ORM\Association\HasMany $ProductCategories
 * @property \App\Model\Table\ProductImagesTable&\Cake\ORM\Association\HasMany $ProductImages
 * @property \App\Model\Table\ProductVariantsTable&\Cake\ORM\Association\HasMany $ProductVariants
 * @property \App\Model\Table\ReturnItemsTable&\Cake\ORM\Association\HasMany $ReturnItems
 * @property \App\Model\Table\ReviewsTable&\Cake\ORM\Association\HasMany $Reviews
 * @property \App\Model\Table\ShowroomStocksTable&\Cake\ORM\Association\HasMany $ShowroomStocks
 * @property \App\Model\Table\SupplierPurchaseOrderItemsTable&\Cake\ORM\Association\HasMany $SupplierPurchaseOrderItems
 * @property \App\Model\Table\SupplierStocksTable&\Cake\ORM\Association\HasMany $SupplierStocks
 * @property \App\Model\Table\WarehouseStocksTable&\Cake\ORM\Association\HasMany $WarehouseStocks
 * @property \App\Model\Table\WishlistsTable&\Cake\ORM\Association\HasMany $Wishlists
 *
 * @method \App\Model\Entity\Product newEmptyEntity()
 * @method \App\Model\Entity\Product newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Product> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Product get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Product findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Product patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Product> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Product|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Product saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Product>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Product>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Product>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Product> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Product>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Product>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Product>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Product> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ProductsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('products');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Brands', [
            'foreignKey' => 'brand_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Suppliers', [
            'foreignKey' => 'supplier_id',
            // 'joinType' => 'INNER',
        ]);
        $this->hasMany('SupplierProducts', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('BannerAds', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('CartItems', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('Inventories', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('OrderItems', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('ProductAttributes', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('ProductCategories', [
            'foreignKey' => 'product_id',
        ]);

        $this->hasMany('ProductImages', [
            'foreignKey' => 'product_id',
        ]);

        $this->hasMany('ProductVariants', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('ReturnItems', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('Reviews', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('ProductStocks', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('PurchaseOrderProducts', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('StockRequestItems', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('SupplierStocks', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('WarehouseStocks', [
            'foreignKey' => 'product_id',
        ]);
        $this->hasMany('Wishlists', [
            'foreignKey' => 'product_id',
        ]);

        $this->hasMany('ProductShowroomPrices', [
            'foreignKey' => 'product_id',
        ]);

        $this->hasMany('RelatedProducts', [
            'className' => 'RelatedProducts', // Reference to the RelatedProducts table
            'foreignKey' => 'product_id', // This matches the 'product_id' in related_products table
            'joinType' => 'INNER',
        ]);

        // Optionally, if you want to retrieve the related products through the related_id
        $this->hasMany('RelatedProductsForThisProduct', [
            'className' => 'RelatedProducts', // Reference to the RelatedProducts table
            'foreignKey' => 'related_id', // This allows you to get products related to this product
            'joinType' => 'INNER',
        ]);

        $this->hasMany('ProductDeals', [
            'foreignKey' => 'product_id',
        ]);

        $this->belongsTo('Merchants', [
            'foreignKey' => 'merchant_id',
            'joinType' => 'LEFT' // Since merchant_id can be null
        ]);

    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        // $validator
        //     ->scalar('url_key')
        //     ->maxLength('url_key', 255)
        //     ->requirePresence('url_key', 'create')
        //     ->notEmptyString('url_key');

        $validator
            ->nonNegativeInteger('brand_id')
            ->notEmptyString('brand_id');

        $validator
            ->nonNegativeInteger('supplier_id')
            ->allowEmptyString('supplier_id');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('product_reference')
            ->maxLength('product_reference', 255, 'Product reference cannot exceed 255 characters.')
            ->allowEmptyString('product_reference');

        $validator
            ->scalar('reference_name')
            ->maxLength('reference_name', 255)
            ->requirePresence('reference_name', 'create')
            ->notEmptyString('reference_name');

        $validator
            ->scalar('description')
            ->requirePresence('description', 'create')
            ->notEmptyString('description');

        $validator
            ->scalar('details')
            ->allowEmptyString('details');

        // $validator
        //     ->scalar('product_image')
        //     ->maxLength('product_image', 255)
        //     ->requirePresence('product_image', 'create')
        //     ->notEmptyFile('product_image');

        $validator
            ->scalar('product_preference')
            ->allowEmptyString('product_preference');

        $validator
            ->scalar('product_size')
            ->requirePresence('product_size', 'create')
            ->notEmptyString('product_size');

        $validator
            ->decimal('product_weight')
            ->allowEmptyString('product_weight');

        $validator
            ->scalar('product_model')
            ->maxLength('product_model', 255)
            ->allowEmptyString('product_model');

        $validator
            ->scalar('product_tags')
            ->maxLength('product_tags', 255)
            ->allowEmptyString('product_tags');

        $validator
            ->scalar('sku')
            ->requirePresence('sku', 'create')
            ->notEmptyString('sku', 'Please enter the SKU ID.')
            ->maxLength('sku', 100, 'SKU ID cannot exceed 100 characters.')
            ->add('sku', 'uniqueActiveSKU', [
                'rule' => function ($value, $context) {
                    // if (!isset($context['data']['status']) || $context['data']['status'] === 'D') {
                    //     return true; // Skip validation if status is 'D'
                    // }

                    $productsTable = \Cake\ORM\TableRegistry::getTableLocator()->get('Products');
                    $query = $productsTable->find()
                        ->where(['sku' => $value, 'status !=' => 'D']);

                    if (!empty($context['data']['id'])) {
                        // Exclude the current record in case of update
                        $query->where(['id !=' => $context['data']['id']]);
                    }

                    return !$query->count();
                },
                'message' => 'This SKU ID is already in use. Please enter a unique SKU ID.'
            ]);


        $validator
            ->scalar('barcode')
            ->maxLength('barcode', 255)
            ->allowEmptyString('barcode');

        $validator
            ->scalar('qrcode')
            ->maxLength('qrcode', 255)
            ->allowEmptyString('qrcode');

        $validator
            ->scalar('scanned_barcode')
            ->maxLength('scanned_barcode', 255)
            ->allowEmptyString('scanned_barcode');

        // $validator
        //     ->integer('quantity')
        //     ->requirePresence('quantity', 'create')
        //     ->notEmptyString('quantity');

        $validator
            ->decimal('purchase_price')
            ->allowEmptyString('purchase_price');

        $validator
            ->decimal('product_price')
            ->allowEmptyString('product_price');

        $validator
            ->decimal('sales_price')
            ->allowEmptyString('sales_price');

        $validator
            ->decimal('promotion_price')
            ->allowEmptyString('promotion_price');

        $validator
            ->date('promotion_start_date')
            ->allowEmptyDate('promotion_start_date');

        $validator
            ->date('promotion_end_date')
            ->allowEmptyDate('promotion_end_date');

        $validator
            ->integer('max_buy_limit')
            ->allowEmptyString('max_buy_limit');

        $validator
            ->notEmptyString('COD_in_city');

        $validator
            ->notEmptyString('COD_out_city');

        $validator
            ->notEmptyString('avl_on_credit');

        $validator
            ->decimal('warranty')
            ->allowEmptyString('warranty');

        $validator
            ->boolean('return_allow')
            ->allowEmptyString('return_allow');

        $validator
            ->integer('return_time_period')
            ->allowEmptyString('return_time_period');

        $validator
            ->scalar('meta_title')
            ->maxLength('meta_title', 255)
            ->allowEmptyString('meta_title');

        $validator
            ->scalar('meta_keyword')
            ->maxLength('meta_keyword', 255)
            ->allowEmptyString('meta_keyword');

        $validator
            ->scalar('meta_description')
            ->allowEmptyString('meta_description');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['brand_id'], 'Brands'), ['errorField' => 'brand_id']);
        $rules->add($rules->existsIn(['supplier_id'], 'Suppliers'), ['errorField' => 'supplier_id']);

        return $rules;
    }

    public function getMyReview($productId, $customerId)
    {
        // Get the review for the given product and customer
        $orderDelivered = $this->OrderItems->find()
            ->contain(['Orders'])
            ->where([
                'OrderItems.product_id' => $productId,
                'Orders.customer_id' => $customerId,
                'Orders.status IN' => ['Delivered', 'Pending Cancellation'] // status when the order is completed
            ])
            ->count();

        if ($orderDelivered > 0) {
            $review = $this->Reviews->find()
                ->where(['product_id' => $productId, 'customer_id' => $customerId])
                ->first();

            if ($review) {
                $review->userReview = true; //(time() - $review->created->getTimestamp()) < (0 * 0 * 0 * 0); // Can edit within 7 days
                return $review;
            } else {
                return ['canAddReview' => true]; // Indicate that the customer can add a review
            }
        }
        return ['canAddReview' => false]; 
        // return null; // No review if the order is not delivered or completed
    }

    //S
    public function productList($category, $sort, $brand, $price, $price_discount, $rating, $page, $limit, $attribute_filter, $filtercategory, $product_brand, $merchant_id, $customer_id = null)
    {

        // Some changes done by M
        $page = (int)$page;
        $limit = (int)$limit;

        if ($sort == 'low-to-high') {
            $order = ['Products.promotion_price' => 'ASC'];
        } else if ($sort == 'high-to-low') {
            $order = ['Products.promotion_price' => 'DESC'];
        } else if ($sort == 'new-arrival') {
            $order = ['Products.created' => 'DESC'];
        } else if ($sort == 'relevance') {
            $order = ['Reviews.rating' => 'DESC'];
        } else if ($sort == 'discount') {
            $order = ['((Products.sales_price - Products.promotion_price)*100/Products.sales_price)' => 'DESC'];
        } else if ($sort == 'popularity') {
            $order = ['Reviews.rating' => 'DESC'];
        } else {
            $order = ['Reviews.rating' => 'DESC'];
        }


        $conditions = [];

        $conditions[] = [
            'Products.status' => 'A', 'Products.sales_price !=' => 0,
        ];

        if ($category != "0") {
            $conditions[] = [
                'ProductCategories.category_id IN' => $category,
            ];
        }

        if ($product_brand != "0") {
            $conditions[] = [
                'Products.brand_id IN' => $product_brand,
            ];
        }

        if ($merchant_id != "0") {
            $conditions[] = [
                'Products.merchant_id' => $merchant_id,
                'Products.approval_status' => 'Approved',
            ];
        }

        if (!empty($filtercategory)) {
            $filtercategory_arr = explode("--", trim($filtercategory));
            $conditions[] = [
                'ProductCategories2.category_id IN' => $filtercategory_arr,
            ];
        }

        // Conditional filters based on user input

        if (!empty($brand)) {
            $brand_arr = explode("--", trim($brand));
            $conditions[] = ['Products.brand_id IN' => $brand_arr];
        }

        if (!empty($price)) {
            $priceRange = explode("--", trim($price));
            $conditions[] = ['Products.promotion_price >=' => $priceRange[0], 'Products.promotion_price <=' => $priceRange[1]];
        }

        if (!empty($price_discount)) {
            $conditions[] = ['((Products.sales_price - Products.promotion_price)*100/Products.sales_price)>=' . trim($price_discount)];
        }

        // Dynamic Attribute Filters
        /*if (!empty($attribute_filter)) {

            foreach ($attribute_filter as $key => $value) {
                // Assuming dynamic attributes are stored in ProductAttributes or a similar table
                $conditions[] = [
                    'CategoryAttributes.attribute_name' => $key,
                    'ProductAttributes.attribute_value' => $value
                ];
            }
        }*/

        $products = $this->find('all');
        $products->select($this);
        $query = $this->find();

        $caseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.offer_price
        ELSE Products.promotion_price
    END'
        );

        $StartDatecaseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.start_date
        ELSE Products.promotion_start_date
    END'
        );

        $EndDatecaseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.end_date
        ELSE Products.promotion_end_date
    END'
        );

        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
                WHEN ProductDeals.status = "A"
                     AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
                THEN 1
                ELSE 0
            END'
        );

        // Favorite product expression
        $isProductFavoriteExpr = $products->newExpr()->add(
            !empty($customer_id)
            ? "CASE WHEN wishlist_products.id IS NOT NULL THEN 1 ELSE 0 END"
            : "0"
        );

        $products->select([
            'Merchants.id',
            'Merchants.company_name',
            'Merchants.description',
            /*'Merchants.merchant_logo',*/
            'Users.id',
            'Users.first_name',
            'Users.last_name',
            'Brands.name',
            // 'Suppliers.name',
            'Reviews.rating',
            'promotion_price' => $caseExpression,
            'promotion_start_date' => $StartDatecaseExpression,
            'promotion_end_date' => $EndDatecaseExpression,
            'IsDealActive' => $isDealActiveExpression,
            'is_product_favorite' => $isProductFavoriteExpr
        ])
            ->contain([
                'ProductCategories' => [
                    'Categories'
                ],
                'ProductVariants' => function ($q) use ($customer_id) {

                    $join = [];
                    if (!empty($customer_id)) {
                        $join['wishlist_variants'] = [
                            'table' => 'wishlists',
                            'type' => 'LEFT',
                            'conditions' => [
                                'wishlist_variants.product_variant_id = ProductVariants.id',
                                'wishlist_variants.product_id = ProductVariants.product_id',
                                'wishlist_variants.customer_id' => $customer_id
                            ]
                        ];
                    }

                    return $q->select([
                        'ProductVariants.id',
                        'ProductVariants.product_id',
                        'ProductVariants.variant_name',
                        'ProductVariants.reference_name',
                        'ProductVariants.sku',
                        'ProductVariants.variant_size',
                        'ProductVariants.variant_weight',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sales_price',
                        'ProductVariants.promotion_price',
                        'ProductVariants.quantity',
                        'ProductVariants.variant_description',
                        'ProductVariants.status',
                        'product_variant_discount' => $q->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)'),
                        'is_variant_favorite' => $q->newExpr()->add(
                            !empty($customer_id)
                            ? "CASE WHEN wishlist_variants.id IS NOT NULL THEN 1 ELSE 0 END"
                            : "0"
                        )
                    ])
                    ->join($join)
                    ->where(['ProductVariants.status' => 'A']);
                },
                'ProductVariants.ProductVariantImages' => function ($q) {
                    return $q->where(['ProductVariantImages.status' => 'A']);
                },
                'ProductDeals',
                'Merchants' => ['Users'], // Only include merchant info if merchant_id is present
            ])
            ->join([
                'Brands' => [
                    'table' => 'brands',
                    'type' => 'INNER',
                    'conditions' => 'Products.brand_id = Brands.id'
                ],
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => 'ProductCategories.product_id = Products.id '
                ],
                'ProductCategories2' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => 'ProductCategories2.product_id = Products.id and ProductCategories2.level IN (1,2)'
                ],
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => 'Reviews.product_id = Products.id'
                ],
                'ProductDeals' => [
                    'table' => 'product_deals',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductDeals.product_id = Products.id',
                        'ProductDeals.status = "A"',
                        'CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date'
                    ]
                ]
                // 'Suppliers' => [
                //     'table' => 'suppliers',
                //     'type' => 'LEFT',
                //     'conditions' => 'Products.supplier_id = Suppliers.id'
                // ]
            ]);

            // Add wishlist join if customer_id exists
            if (!empty($customer_id)) {
                $products->join([
                    'wishlist_products' => [
                        'table' => 'wishlists',
                        'type' => 'LEFT',
                        'conditions' => [
                            'wishlist_products.product_id = Products.id',
                            'wishlist_products.customer_id' => $customer_id,
                            'wishlist_products.product_variant_id IS NULL'
                        ]
                    ]
                ]);
            }

            $products->where($conditions)
            ->order($order)
            ->group(['Products.id']); // Group by product ID to calculate the average correctly

        if (!empty($rating)) {
            $products->having(function (QueryExpression $exp) use ($rating) {
                return $exp->gte('AVG(Reviews.rating)', $rating); // Filter products with average rating >= rating
            });
        }

        $products->page($page, $limit);

        $result = $products->toArray();

        if (!$result) {
            return null;
        }

        return $result;
    }

    /**
     * Get total count of products for pagination
     */
    public function getTotalProductCount($category, $sort, $brand, $price, $price_discount, $rating, $attribute_filter, $filtercategory, $product_brand, $merchant_id, $customer_id = null)
    {
        $conditions = [];

        $conditions[] = [
            'Products.status' => 'A', 'Products.sales_price !=' => 0,
        ];

        if ($category != "0") {
            $conditions[] = [
                'ProductCategories.category_id IN' => $category,
            ];
        }

        if ($product_brand != "0") {
            $conditions[] = [
                'Products.brand_id IN' => $product_brand,
            ];
        }

        if ($merchant_id != "0") {
            $conditions[] = [
                'Products.merchant_id' => $merchant_id,
                'Products.approval_status' => 'Approved',
            ];
        }

        if (!empty($filtercategory)) {
            $filtercategory_arr = explode("--", trim($filtercategory));
            $conditions[] = [
                'ProductCategories2.category_id IN' => $filtercategory_arr,
            ];
        }

        // Conditional filters based on user input
        if (!empty($brand)) {
            $brand_arr = explode("--", trim($brand));
            $conditions[] = ['Products.brand_id IN' => $brand_arr];
        }

        if (!empty($price)) {
            $priceRange = explode("--", trim($price));
            $conditions[] = ['Products.promotion_price >=' => $priceRange[0], 'Products.promotion_price <=' => $priceRange[1]];
        }

        if (!empty($price_discount)) {
            $conditions[] = ['((Products.sales_price - Products.promotion_price)*100/Products.sales_price)>=' . trim($price_discount)];
        }

        $query = $this->find()
            ->select([
                'Products.id',
                'avg_rating' => $this->find()->func()->avg('Reviews.rating')
            ])
            ->join([
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => 'ProductCategories.product_id = Products.id '
                ],
                'ProductCategories2' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => 'ProductCategories2.product_id = Products.id and ProductCategories2.level IN (1,2)'
                ],
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => 'Reviews.product_id = Products.id'
                ]
            ])
            ->where($conditions)
            ->group(['Products.id']);

        if (!empty($rating)) {
            $query->having(function (QueryExpression $exp) use ($rating) {
                return $exp->gte('avg_rating', $rating);
            });
        }

        return $query->count();
    }

    public function getTopDealProducts($limit = 5)
    {
        
        if($limit == 0 || $limit == null){
            $limit = 5;
        }

        $today = FrozenTime::now();

        $query = $this->find();
        $query->select($this)
            ->select([
                'rating' => $this->find()->func()->avg('Reviews.rating'),
                'total_reviews' => $this->find()->func()->count('Reviews.id'),
                'discount' => $this->find()->newExpr()->add('ROUND((Products.product_price - Products.promotion_price) * 100 / Products.product_price, 2)'),
                'availability_status' => $this->find()->newExpr()->add('IF(SUM(ProductVariants.quantity) > 0, "In Stock", "Out of Stock")')
            ])
            ->join([
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => 'ProductVariants.product_id = Products.id'
                ],
            ])
            ->where([
                'Products.status' => 'A', 
                'Products.approval_status' => 'Approved', 
                'Products.product_preference' => 'Deal',
                'OR' => [
                    'Products.promotion_start_date <=' => $today,
                    'Products.promotion_start_date IS' => null
                ],
                'OR' => [
                    'Products.promotion_end_date >=' => $today,
                    'Products.promotion_end_date IS' => null
                ],
            ]);
        
            if (!empty($categoryIds)) {
                $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                    return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
                });
            }

            $query
                ->group(['Products.id'])
                ->order([
                    '(Products.product_price - Products.promotion_price) * 100 / Products.product_price' => 'DESC',
                    'rating' => 'DESC'
                ])
                ->limit($limit)
                ->toArray();

            return $query;
    }

    //M
    public function getSimilarProducts($productId)
    {
        $relatedProducts = [];
        if ($productId) {
            $relatedProductIds = [];
            $relatedProducts = $this->RelatedProducts->find()
                ->select(['related_id'])
                ->where(['status' => 'A', 'product_id' => $productId]);

            foreach ($relatedProducts as $relatedProduct) {
                $relatedProductIds[] = $relatedProduct->related_id;
            }

            // if ($productId) {
            //     $relatedProductIds[] = $productId;
            // }

            $query = $this->find();

            $caseExpression = $query->newExpr()->add(
                'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.offer_price
        ELSE Products.promotion_price
    END'
            );

            $StartDatecaseExpression = $query->newExpr()->add(
                'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.start_date
        ELSE Products.promotion_start_date
    END'
            );

            $EndDatecaseExpression = $query->newExpr()->add(
                'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.end_date
        ELSE Products.promotion_end_date
    END'
            );

            $isDealActiveExpression = $query->newExpr()->add(
                'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN 1
        ELSE 0
    END'
            );
            if ($relatedProductIds) {
                $relatedProducts = $this->find()
                    ->select([
                        'Products.id',
                        'Products.name',
                        'Brands.name',
                        'Reviews.rating',
                        'promotion_price' => $caseExpression,
                        'promotion_start_date' => $StartDatecaseExpression,
                        'promotion_end_date' => $EndDatecaseExpression,
                        'IsDealActive' => $isDealActiveExpression,
                    ])
                    ->contain([
                        'ProductCategories' => [
                            'Categories'
                        ],
                        'ProductDeals'
                    ])
                    ->join([
                        'Brands' => [
                            'table' => 'brands',
                            'type' => 'INNER',
                            'conditions' => 'Products.brand_id = Brands.id'
                        ],
                        'ProductCategories' => [
                            'table' => 'product_categories',
                            'type' => 'LEFT',
                            'conditions' => 'ProductCategories.product_id = Products.id'
                        ],
                        'ProductCategories2' => [
                            'table' => 'product_categories',
                            'type' => 'LEFT',
                            'conditions' => 'ProductCategories2.product_id = Products.id AND ProductCategories2.level IN (1,2)'
                        ],
                        'Reviews' => [
                            'table' => 'reviews',
                            'type' => 'LEFT',
                            'conditions' => 'Reviews.product_id = Products.id'
                        ],
                        'ProductDeals' => [
                            'table' => 'product_deals',
                            'type' => 'LEFT',
                            'conditions' => 'ProductDeals.product_id = Products.id'
                        ]
                    ])
                    ->where(['Products.id IN' => $relatedProductIds])
                    ->where(['Products.status' => 'A'])
                    ->group(['Products.id'])
                    ->toArray();
            }
        }

        return $relatedProducts;
    }

    //M Not In Use
    public function similarProduct($productId)
    {

        $tableLocator = new TableLocator();
        $productCategoriesTable = $tableLocator->get('ProductCategories');
        $categoriesTable = $tableLocator->get('Categories');

        $productCategoryIds = $productCategoriesTable->find('list', [
            'keyField' => 'category_id',
            'valueField' => 'category_id'
        ])
            ->where(['product_id' => $productId])
            ->toArray();

        if (empty($productCategoryIds)) {
            return [];
        }

        $lastLevelCategory = $categoriesTable->find('all')
            ->where([
                'id IN' => $productCategoryIds,
                'id NOT IN' => $categoriesTable->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                    ->where(['parent_id IN' => $productCategoryIds])
            ])
            ->first();


        if (empty($lastLevelCategory)) {
            return [];
        }

        $similarProducts = $this->find('all')
            ->matching('ProductCategories', function ($q) use ($lastLevelCategory) {
                return $q->where(['ProductCategories.category_id' => $lastLevelCategory->id]);
            })
            ->where(['Products.id !=' => $productId])
            ->limit(10)
            ->toArray();

        return !empty($similarProducts) ? $similarProducts : [];
    }

    //M
    public function productView($productId)
    {

        $conditions = [
            'Products.status' => 'A',
            'Products.id' => $productId,
            'Products.sales_price !=' => 0,
        ];

        $products = $this->find();
        $products->select($this);
        $query = $this->find();
        $caseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.offer_price
        ELSE Products.promotion_price
    END'
        );

        $StartDatecaseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.start_date
        ELSE Products.promotion_start_date
    END'
        );

        $EndDatecaseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.end_date
        ELSE Products.promotion_end_date
    END'
        );

        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
                WHEN ProductDeals.status = "A"
                     AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
                THEN 1
                ELSE 0
            END'
        );
        $products->select([
            'Merchants.id',
            'Merchants.company_name',
            'Merchants.description',
            /*'Merchants.merchant_logo',*/
            'Users.id',
            'Users.first_name',
            'Users.last_name',
            'Brands.name',
            // 'Suppliers.name',
            'promotion_price' => $caseExpression,
            'promotion_start_date' => $StartDatecaseExpression,
            'promotion_end_date' => $EndDatecaseExpression,
            'IsDealActive' => $isDealActiveExpression,
            'product_discount' => $products->newExpr()->add('Products.sales_price - Products.promotion_price'),
        ])
            ->contain([
                'ProductImages' => function ($q) {
                    return $q->where(['ProductImages.status' => 'A']);
                },
                'ProductAttributes' => function ($q) {
                    return $q->where(['ProductAttributes.status' => 'A'])
                        ->contain(['Attributes', 'AttributeValues']);
                },
                'ProductVariants' => function ($q) {
                    return $q->select([
                        'ProductVariants.id',
                        'ProductVariants.product_id',
                        'ProductVariants.variant_name',
                        'ProductVariants.reference_name',
                        'ProductVariants.sku',
                        'ProductVariants.variant_size',
                        'ProductVariants.variant_weight',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sales_price',
                        'ProductVariants.promotion_price',
                        'ProductVariants.quantity',
                        'ProductVariants.variant_description',
                        'ProductVariants.status',
                        'product_variant_discount' => $q->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)')
                    ])->where(['ProductVariants.status' => 'A']);
                },
                'ProductVariants.ProductVariantImages' => function ($q) {
                    return $q->where(['ProductVariantImages.status' => 'A']);
                },
                'ProductCategories' => [
                    'Categories'
                ],
                'ProductDeals',
                'Merchants' => ['Users'], // Only include merchant info if merchant_id is present
            ])
            ->join([
                'Brands' => [
                    'table' => 'brands',
                    'type' => 'INNER',
                    'conditions' => 'Products.brand_id = Brands.id'
                ],
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductAttributes' => [
                    'table' => 'product_attributes',
                    'type' => 'LEFT',
                    'conditions' => ['ProductAttributes.product_id = Products.id', 'ProductAttributes.status' => 'A']
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => ['ProductVariants.product_id = Products.id', 'ProductVariants.status' => 'A']
                ],
                'ProductDeals' => [
                    'table' => 'product_deals',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductDeals.product_id = Products.id',
                        'ProductDeals.status = "A"',
                        'CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date'
                    ]
                ]
                // 'Suppliers' => [
                //     'table' => 'suppliers',
                //     'type' => 'LEFT',
                //     'conditions' => 'Products.supplier_id = Suppliers.id'
                // ]
            ])
            ->where($conditions)
            ->group(['Products.id']);

        $result = $products->first();

        if (!$result) {
            return null;
        }

        return $result;
    }

    public function getProductPrice($productId)
    {
        //        $productPrice = $this->Products->getProductPrice($productId);
        //        echo 'Product Price: ' . ($productPrice ?? 'N/A');

        $query = $this->find();

        // CASE expression to determine the correct price
        $priceExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
        END'
        );

        // Query to fetch price based on conditions
        $product = $this->find()
            ->select([
                'price' => $priceExpression,
            ])
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                [
                    'ProductDeals.product_id = Products.id',
                    'ProductDeals.status' => 'A',
                    'CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date'
                ]
            )
            ->where([
                'Products.id' => $productId,
                'Products.status' => 'A'
            ])
            ->first();

        return $product ? (float) $product->price : null;
    }

    public function getDiscount($productId)
    {
        $product = $this->find()
            ->select([
                'discount' => 'ROUND((sales_price - promotion_price) * 100 / sales_price, 2)'
            ])
            ->where(['id' => $productId])
            ->first();

        return $product ? (float)$product->discount : 0.0; // Return 0.0 if no product found
    }

    public function getDiscountProduct($productId, $variant = null)
    {
        if ($variant) {
            // Check in product_variants table if variant is provided
            $productVariant = $this->ProductVariants->find()
                ->select([
                    'discount' => 'ROUND((sales_price - promotion_price) * 100 / sales_price, 2)'
                ])
                ->where(['product_id' => $productId, 'id' => $variant])
                ->first();

            return $productVariant ? (float)$productVariant->discount : 0.0;
        } else {
            // Check in products table if no variant is provided
            $product = $this->find()
                ->select([
                    'discount' => 'ROUND((sales_price - promotion_price) * 100 / sales_price, 2)'
                ])
                ->where(['id' => $productId])
                ->first();

            return $product ? (float)$product->discount : 0.0;
        }
    }

    public function getAvailabilityStatus($productId)
    {
        $product = $this->find()
            ->select(['availability_status' => 'IF((SELECT SUM(quantity) FROM product_stocks WHERE product_id = Products.id) > 0, "In Stock", "Out of Stock")'])
            ->where(['id' => $productId])
            ->first();

        return $product ? $product->availability_status : 'Out of Stock'; // Default to 'Out of Stock' if no product found
    }

    public function singleProductStockStatus($productId = null, $variantId = null, $ProductAttributeId = null)
    {
        if ($productId === null) {
            return 'Out of Stock';
        }

        $stockConditions = ['product_id' => $productId];
        if ($variantId !== null) {
            $stockConditions['product_variant_id'] = $variantId;
        }
        if ($ProductAttributeId !== null) {
            $stockConditions['product_attribute_id'] = $ProductAttributeId;
        }

        $stockSum = $this->getAssociation('ProductStocks')
            ->find()
            ->select(['total' => 'SUM(quantity)'])
            ->where($stockConditions)
            ->enableHydration(false)
            ->first();

        $total = $stockSum && isset($stockSum['total']) ? (int)$stockSum['total'] : 0;

        return $total > 0 ? 'In Stock' : 'Out of Stock';
    }

    //S
    // public function productView($productId) {

    //     $conditions = [];

    //     // Base conditions that always apply
    //     $conditions[] = [
    //         'Products.status' => 'A',
    //         'Products.id' => $productId,
    //         'Reviews.status'=>'A'
    //     ];

    //     $products = $this->find();
    //     $products->select($this)
    //         ->select([
    //             'Brands.name',
    //             'rating' => $products->func()->avg('Reviews.rating'),
    //             'total_reviews' => $products->func()->count('Reviews.id'),
    //             'discount' => $products->newExpr()->add('ROUND((Products.product_price - Products.promotion_price)*100/Products.product_price,2)')
    //         ])
    //         ->contain([ 'ProductImages','ProductCategories' ]) // Contain any necessary associations
    //         ->join([
    //             'Brands' => [
    //             'table' => 'brands',
    //             'type' => 'INNER',
    //             'conditions' => 'Products.brand_id = Brands.id'
    //             ],
    //             'Reviews' => [
    //             'table' => 'reviews',
    //             'type' => 'LEFT',
    //             'conditions' => 'Reviews.product_id = Products.id'
    //             ]
    //         ])
    //         ->where($conditions)
    //         ->group(['Products.id']) // Group by product ID to calculate the average correctly
    //         ->first();

    //     echo "<pre>"; print_r($products->toArray()); die;
    //     return $products;

    // }

    //S
    // public function similarProduct($productId) {

    //     $tableLocator = new TableLocator();
    //     $productCategoriesTable = $tableLocator->get('ProductCategories');
    //     $categoriesTable = $tableLocator->get('Categories');

    //     // Get all categories associated with the product
    //     $productCategoryIds = $productCategoriesTable->find('list', [
    //             'keyField' => 'category_id',
    //             'valueField' => 'category_id'
    //         ])
    //         ->where(['product_id' => $productId])
    //         ->toArray();

    //     // Find the last-level category among them
    //     $lastLevelCategory = $categoriesTable->find('all')
    //         ->where([
    //             'id IN' => $productCategoryIds,
    //             'id NOT IN' => $categoriesTable->find('list', ['keyField' => 'id', 'valueField' => 'id'])
    //                 ->where(['parent_id IN' => $productCategoryIds]) // Exclude categories that are parents (not leaf)
    //         ])
    //         ->first();

    //     $similarProducts = $this->find('all')
    //         ->matching('ProductCategories', function ($q) use ($lastLevelCategory) {
    //             return $q->where(['ProductCategories.category_id' => $lastLevelCategory->id]);
    //         })
    //         ->where(['Products.id !=' => $productId]) // Exclude the current product
    //         ->limit(10) // Optionally limit the number of results
    //         ->toArray();

    //     return $similarProducts;
    // }

    //S
    public function getProductsSuggestions($query)
    {
        return $this->find('all')
            ->select(['id', 'name', 'type' => "'product'"])
            ->where(['name LIKE' => '%' . $query . '%'])
            ->limit(10)
            ->toArray();
    }

    public function search($query, $customer_id = null)
    {
        $query = trim(strtolower($query));
        $queryParts = explode(' ', $query);

        $conditions = [
            'OR' => [
                'LOWER(Products.name) LIKE' => "%$query%",
                'LOWER(Products.sku) LIKE' => "%$query%",
                'LOWER(Products.reference_name) LIKE' => "%$query%",
                'LOWER(Products.product_tags) LIKE' => "%$query%",
                'LOWER(ProductVariants.variant_name) LIKE' => "%$query%",
                'LOWER(ProductVariants.sku) LIKE' => "%$query%"
            ]
        ];

        foreach ($queryParts as $part) {
            $conditions['OR'] = array_merge($conditions['OR'], [
                ['LOWER(Products.name) LIKE' => "%$part%"],
                ['LOWER(Products.sku) LIKE' => "%$part%"],
                ['LOWER(Products.reference_name) LIKE' => "%$part%"],
                ['LOWER(Products.product_tags) LIKE' => "%$part%"],
                ['LOWER(ProductVariants.variant_name) LIKE' => "%$part%"],
                ['LOWER(ProductVariants.sku) LIKE' => "%$part%"]
            ]);
        }

        $productsQuery = $this->find();

        // Favorite flags using CASE expressions
        $isProductFavoriteExpr = $productsQuery->newExpr()->add(
            !empty($customer_id)
                ? "CASE WHEN wishlist_products.id IS NOT NULL THEN 1 ELSE 0 END"
                : "0"
        );

        $productsQuery
            ->select($this)
            ->select([
                'is_product_favorite' => $isProductFavoriteExpr
            ])
            ->contain([
                'Brands',
                'ProductVariants' => function ($q) use ($customer_id) {

                    $join = [];
                    if (!empty($customer_id)) {
                        $join['wishlist_variants'] = [
                            'table' => 'wishlists',
                            'type' => 'LEFT',
                            'conditions' => [
                                'wishlist_variants.product_variant_id = ProductVariants.id',
                                'wishlist_variants.product_id = ProductVariants.product_id',
                                'wishlist_variants.customer_id' => $customer_id
                            ]
                        ];
                    }

                    return $q->select(['id', 'product_id', 'variant_name', 'sku',
                        'is_variant_favorite' => $q->newExpr()->add(
                                !empty($customer_id)
                                    ? "CASE WHEN wishlist_variants.id IS NOT NULL THEN 1 ELSE 0 END"
                                    : "0"
                            )
                        ])
                        ->join($join)
                        ->where(['ProductVariants.status' => 'A'])
                        ->contain([
                            'ProductVariantImages' => function ($q) {
                                return $q->select(['variant_id', 'image', 'image_default'])
                                    ->order(['image_default' => 'DESC', 'id' => 'ASC']);
                            }
                        ]);
                }
            ])
            ->leftJoin(
                ['ProductVariants' => 'product_variants'],
                ['ProductVariants.product_id = Products.id']
            )
            ->join([
                // Product-level wishlist join
                'wishlist_products' => [
                    'table' => 'wishlists',
                    'type' => 'LEFT',
                    'conditions' => !empty($customer_id)
                        ? [
                            'wishlist_products.product_id = Products.id',
                            'wishlist_products.product_variant_id IS' => null,
                            'wishlist_products.customer_id' => $customer_id
                        ]
                        : '1=0'
                ]
            ])
            ->where([
                'Products.status' => 'A',
                $conditions
            ])
            ->order([
                'CASE
                    WHEN LOWER(Products.name) = :query THEN 1
                    WHEN LOWER(Products.name) LIKE :query_full THEN 2
                    WHEN LOWER(Products.name) LIKE :query_partial THEN 3
                    ELSE 4
                 END' => 'ASC',
                'Products.id' => 'ASC'
            ])
            ->bind(':query', $query, 'string')
            ->bind(':query_full', "%$query%", 'string')
            ->bind(':query_partial', "%{$queryParts[0]}%", 'string')
            ->distinct(['Products.id']);
            
        $products = $productsQuery->toArray();

        $finalProducts = [];    

        // Flatten the product_variants array for each product
        foreach ($products as $product) {
            
            $finalProducts[] = $product;

            // Handle variants
            if (!empty($product->product_variants)) {
                foreach ($product->product_variants as $variant) {
                    $variantProduct = clone $product;
                    $variantProduct->name = $variant->variant_name;
                    $variantProduct->product_variant_id = $variant->id;
                    $variantProduct->is_variant_favorite = $variant->is_variant_favorite ?? 0;

                    if (!empty($variant->product_variant_images)) {
                        $variantProduct->product_image = $variant->product_variant_images[0]->image;
                    }
                    unset($variantProduct->product_variants);

                    $finalProducts[] = $variantProduct;
                }
            }
        }

        return $finalProducts;
    }


    //S
    // public function search($query)
    // {

    //     // $products = $this->find()
    //     //     ->contain([
    //     //         'Brands',
    //     //     ])
    //     //     ->leftJoinWith('ProductCategories.Categories') // Join categories through ProductCategories
    //     //     ->where([
    //     //         'OR' => [
    //     //             'Products.name LIKE' => '%' . $query . '%', // Search in product names
    //     //             'Brands.name LIKE' => '%' . $query . '%', // Search in brand names
    //     //             'Categories.name LIKE' => '%' . $query . '%', // Search in category names
    //     //         ]
    //     //     ])
    //     //     ->distinct(['Products.id']) // Ensure unique results
    //     //     ->toArray();

    //     $queryReceived = $query;
    //     $queryNormalized = '';
    //     $queryParts = [];
    //     $lastThree = '';

    //     if (isset($query) && $query !== '') {
    //         $queryNormalized = strtolower(str_replace(' ', '', $queryReceived));
    //         $queryParts = explode(' ', strtolower($queryReceived));
    //         if (count($queryParts) > 2) {

    //             $middleIndex = (int) ceil(count($queryParts) / 2); 
    //             $firstPart = implode(' ', array_slice($queryParts, 0, $middleIndex));
    //             $secondPart = implode(' ', array_slice($queryParts, $middleIndex));

    //             $queryParts = [$firstPart, $secondPart];
    //         } 
    //         $length = strlen($queryNormalized);

    //         $takeLength = (int) ceil($length / 2);
    //         $lastThree = substr($queryNormalized, -$takeLength);
    //         // $lastThree = substr($queryNormalized, -3);
    //     }

    //     $productsByName = $this->find()
    //         ->contain(['Brands'])
    //         ->where([
    //             'Products.status' => 'A',
    //             'OR' => array_merge(
    //                 [
    //                     'LOWER(REPLACE(Products.name, " ", "")) LIKE' => '%' . $queryNormalized . '%',
    //                     'LOWER(REPLACE(Products.sku, " ", "")) LIKE' => '%' . $queryNormalized . '%',
    //                     'LOWER(REPLACE(Products.reference_name, " ", "")) LIKE' => '%' . $queryNormalized . '%',
    //                     'LOWER(REPLACE(Products.product_tags, " ", "")) LIKE' => '%' . $queryNormalized . '%',
    //                 ],
    //                 array_map(
    //                     fn($part) => [
    //                         'LOWER(Products.name) LIKE' => '%' . $part . '%',
    //                         'LOWER(Products.sku) LIKE' => '%' . $part . '%',
    //                         'LOWER(Products.reference_name) LIKE' => '%' . $part . '%',
    //                         'LOWER(Products.product_tags) LIKE' => '%' . $part . '%',
    //                     ],
    //                     $queryParts
    //                 ),
    //                 $lastThree ? [
    //                     ['LOWER(Products.name) LIKE' => '%' . $lastThree . '%'],
    //                     ['LOWER(Products.sku) LIKE' => '%' . $lastThree . '%'],
    //                     ['LOWER(Products.reference_name) LIKE' => '%' . $lastThree . '%'],
    //                     ['LOWER(Products.product_tags) LIKE' => '%' . $lastThree . '%'],
    //                 ] : []
    //             )
    //         ])
    //         ->distinct(['Products.id'])
    //         ->toArray();

    //     $productsByVariant = $this->find()
    //         ->contain(['ProductVariants'])
    //         ->matching('ProductVariants', function ($q) {
    //             return $q->where(['ProductVariants.status' => 'A']);
    //         })
    //         ->where([
    //             'Products.status' => 'A',
    //             'OR' => array_merge(
    //                 [
    //                     'LOWER(REPLACE(ProductVariants.variant_name, " ", "")) LIKE' => '%' . $queryNormalized . '%',
    //                     'LOWER(REPLACE(ProductVariants.sku, " ", "")) LIKE' => '%' . $queryNormalized . '%',
    //                     'LOWER(REPLACE(ProductVariants.reference_name, " ", "")) LIKE' => '%' . $queryNormalized . '%',
    //                 ],
    //                 array_map(
    //                     fn($part) => [
    //                         'LOWER(ProductVariants.variant_name) LIKE' => '%' . $part . '%',
    //                         'LOWER(ProductVariants.sku) LIKE' => '%' . $part . '%',
    //                         'LOWER(ProductVariants.reference_name) LIKE' => '%' . $part . '%',
    //                     ],
    //                     $queryParts
    //                 ),
    //                 $lastThree ? [
    //                     ['LOWER(ProductVariants.variant_name) LIKE' => '%' . $lastThree . '%'],
    //                     ['LOWER(ProductVariants.sku) LIKE' => '%' . $lastThree . '%'],
    //                     ['LOWER(ProductVariants.reference_name) LIKE' => '%' . $lastThree . '%'],
    //                 ] : []
    //             )
    //         ])
    //         ->distinct(['Products.id'])
    //         ->toArray();

    //     $productsByCategory = $this->find()
    //         ->contain(['Brands'])
    //         ->leftJoinWith('ProductCategories.Categories')
    //         ->where([
    //             'Products.status' => 'A',
    //             'Categories.status' => 'A',
    //             'OR' => array_merge(
    //                 ['LOWER(REPLACE(Categories.name, " ", "")) LIKE' => '%' . $queryNormalized . '%'],
    //                 array_map(
    //                     fn($part) => ['LOWER(Categories.name) LIKE' => '%' . $part . '%'],
    //                     $queryParts
    //                 ),
    //                 $lastThree ? [['LOWER(Categories.name) LIKE' => '%' . $lastThree . '%']] : []
    //             )
    //         ])
    //         ->distinct(['Products.id'])
    //         ->toArray();

    //     $productsByBrand = $this->find()
    //         ->contain(['Brands'])
    //         ->where([
    //             'Products.status' => 'A',
    //             'Brands.status' => 'A',
    //             'OR' => array_merge(
    //                 ['LOWER(REPLACE(Brands.name, " ", "")) LIKE' => '%' . $queryNormalized . '%'],
    //                 array_map(
    //                     fn($part) => ['LOWER(Brands.name) LIKE' => '%' . $part . '%'],
    //                     $queryParts
    //                 ),
    //                 $lastThree ? [['LOWER(Brands.name) LIKE' => '%' . $lastThree . '%']] : []
    //             )
    //         ])
    //         ->distinct(['Products.id'])
    //         ->toArray();


    //     $products = array_unique(
    //         array_merge($productsByName, $productsByCategory, $productsByBrand, $productsByVariant),
    //         SORT_REGULAR
    //     );

    //     return $products;
    // }
    // Ax
    public function searchWebAPI($query)
    {
        $query = trim(strtolower($query));
        $queryParts = explode(' ', $query);

        $conditions = [
            'OR' => [
                // Product conditions
                'LOWER(Products.name) LIKE' => "%$query%",
                'LOWER(Products.sku) LIKE' => "%$query%",
                'LOWER(Products.reference_name) LIKE' => "%$query%",
                'LOWER(Products.product_tags) LIKE' => "%$query%",
                // Product variant conditions
                'LOWER(ProductVariants.variant_name) LIKE' => "%$query%",
                'LOWER(ProductVariants.sku) LIKE' => "%$query%",
                // Category conditions
                'LOWER(Categories.name) LIKE' => "%$query%"
            ]
        ];

        foreach ($queryParts as $part) {
            $conditions['OR'] = array_merge($conditions['OR'], [
                ['LOWER(Products.name) LIKE' => "%$part%"],
                ['LOWER(Products.sku) LIKE' => "%$part%"],
                ['LOWER(Products.reference_name) LIKE' => "%$part%"],
                ['LOWER(Products.product_tags) LIKE' => "%$part%"],
                ['LOWER(ProductVariants.variant_name) LIKE' => "%$part%"],
                ['LOWER(ProductVariants.sku) LIKE' => "%$part%"],
                ['LOWER(Categories.name) LIKE' => "%$part%"]
            ]);
        }

        $products = $this->find()
            ->contain([
                'Brands',
                'ProductVariants' => function ($q) {
                    return $q->select(['id', 'product_id', 'variant_name', 'sku'])
                        ->where(['ProductVariants.status' => 'A'])
                        ->contain([
                            'ProductVariantImages' => function ($q) {
                                return $q->select([
                                    'variant_id',
                                    'image',
                                    'image_default'
                                ])
                                    ->order(['image_default' => 'DESC', 'id' => 'ASC']);
                                //  ->limit(1);
                            }
                        ]);
                }
            ])
            ->leftJoin(
                ['ProductVariants' => 'product_variants'],
                ['ProductVariants.product_id = Products.id']
            )
            ->leftJoin(
                ['ProductCategories' => 'product_categories'],
                ['ProductCategories.product_id = Products.id']
            )
            ->leftJoin(
                ['Categories' => 'categories'],
                ['Categories.id = ProductCategories.category_id', 'Categories.status' => 'A']
            )
            ->where([
                'Products.status' => 'A',
                $conditions
            ])
            ->order([
                'CASE
                    WHEN LOWER(Products.name) = :query THEN 1
                    WHEN LOWER(Products.name) LIKE :query_full THEN 2
                    WHEN LOWER(Products.name) LIKE :query_partial THEN 3
                    ELSE 4
                 END' => 'ASC',
                'Products.id' => 'ASC'
            ])
            ->bind(':query', $query, 'string')
            ->bind(':query_full', "%$query%", 'string')
            ->bind(':query_partial', "%{$queryParts[0]}%", 'string')
            ->distinct(['Products.id'])
            ->toArray();

        $finalProducts = [];
        // Flatten the product_variants array for each product
        foreach ($products as $product) {
            $finalProducts[] = $product; // Keep the main product

            // If product has variants, append each as a separate entry
            if (!empty($product->product_variants)) {
                foreach ($product->product_variants as $variant) {
                    $variantProduct = clone $product; // Copy the main product
                    $variantProduct->name = $variant->variant_name; // Set variant name as product name

                    // Set the first product_variant_image if available
                    if (!empty($variant->product_variant_images)) {
                        $variantProduct->product_image = $variant->product_variant_images[0]->image;
                    }

                    unset($variantProduct->product_variants); // Remove the product_variants array
                    $finalProducts[] = $variantProduct;
                }
            }
        }

        return $finalProducts;
    }




    //M needs to clarify
    public function getDealOfTheDayProducts($limit = null, $categoryIds = [], $product_preference = null, $customer_id = null)
    {
        $today = FrozenTime::now();

        $query = $this->find();
        $query->select($this)
            ->select([
                'reference_name',
                'hours_left' => $this->find()->newExpr()->add('IF(Products.promotion_end_date IS NOT NULL, GREATEST(TIMESTAMPDIFF(HOUR, \'' . $today->format('Y-m-d H:i:s') . '\', DATE_ADD(Products.promotion_end_date, INTERVAL 1 DAY) - INTERVAL 1 SECOND), 0), NULL)'),
                // 'Reviews.id',
                // 'Reviews.rating',
                // 'Reviews.comment',
                // 'Reviews.customer_id',
                'average_rating' => $this->find()->func()->round([
                $this->find()->func()->avg('Reviews.rating'),1]),
                // Product Favorite flag
                'is_product_favorite' => $this->find()->newExpr()->add(
                    !empty($customer_id)
                        ? "CASE WHEN wishlist_products.id IS NOT NULL THEN 1 ELSE 0 END"
                        : "0"
                )
            ])
            ->contain([
                  'ProductVariants' => function ($q1) use ($customer_id) {
                    
                    $join = [];
                    if (!empty($customer_id)) {
                        $join['wishlist_variants'] = [
                            'table' => 'wishlists',
                            'type' => 'LEFT',
                            'conditions' => [
                                'wishlist_variants.product_variant_id = ProductVariants.id',
                                'wishlist_variants.product_id = ProductVariants.product_id',
                                'wishlist_variants.customer_id' => $customer_id
                            ]
                        ];
                    }
                    return $q1->select([
                        'ProductVariants.id',
                        'ProductVariants.product_id',
                        'ProductVariants.variant_name',
                        'ProductVariants.reference_name',
                        'ProductVariants.sku',
                        'ProductVariants.variant_size',
                        'ProductVariants.variant_weight',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sales_price',
                        'ProductVariants.promotion_price',
                        'ProductVariants.quantity',
                        'ProductVariants.variant_description',
                        'ProductVariants.status',
                        'product_variant_discount' => $q1->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)'),
                        //Variant Favorite flag
                        'is_variant_favorite' => $q1->newExpr()->add(
                            !empty($customer_id)
                                ? "CASE WHEN wishlist_variants.id IS NOT NULL THEN 1 ELSE 0 END"
                                : "0"
                        )
                    ])
                    ->join($join)
                    ->where(['ProductVariants.status' => 'A']);
                },
                'ProductVariants.ProductVariantImages' => function ($q1) {
                    return $q1->where(['ProductVariantImages.status' => 'A']);
                },
            ])
            ->join([
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => 'ProductVariants.product_id = Products.id'
                ],
                'ProductDeals' => [
                    'table' => 'product_deals',
                    'type' => 'LEFT',
                    'conditions' => 'ProductDeals.product_id = Products.id'
                ],
                //Product-level wishlist join
                'wishlist_products' => [
                    'table' => 'wishlists',
                    'type' => 'LEFT',
                    'conditions' => !empty($customer_id)
                        ? [
                            'wishlist_products.product_id = Products.id',
                            'wishlist_products.product_variant_id IS' => null,
                            'wishlist_products.customer_id' => $customer_id
                        ]
                        : '1=0' // If no customer_id, disable join
                ]
            ])
            ->where([
                'Products.status' => 'A',
                'Products.approval_status' => 'Approved',
                'Products.product_preference' => 'Deal',
            ]);
        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }
        if ($limit !== null) {
            $query->limit($limit);
        }

        $query
            ->group(['Products.id'])
            ->order(['(Products.product_price - Products.promotion_price) * 100 / Products.product_price' => 'DESC']);
            
        
        return $query->toArray();
    }

    //M
    public function getTopSellingProducts($limit = null, $categoryIds = [], $product_preference = null, $customer_id = null)
    {
        $query = $this->find();

        $caseExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
         END'
        );

        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN 1
            ELSE 0
         END'
        );

        //Product Favorite flag
        $isProductFavoriteExpr = $query->newExpr()->add(
            !empty($customer_id)
                ? "CASE WHEN wishlist_products.id IS NOT NULL THEN 1 ELSE 0 END"
                : "0"
        );

        $query->select($this)
            ->select([
                'total_sales' => $this->find()->func()->count('OrderItems.id'),
                'promotion_price' => $caseExpression,
                'IsDealActive' => $isDealActiveExpression,
                'is_product_favorite' => $isProductFavoriteExpr
            ])
            ->contain([
                  'ProductVariants' => function ($q1) use ($customer_id) {

                    $join = [];
                    if (!empty($customer_id)) {
                        $join['wishlist_variants'] = [
                            'table' => 'wishlists',
                            'type' => 'LEFT',
                            'conditions' => [
                                'wishlist_variants.product_variant_id = ProductVariants.id',
                                'wishlist_variants.product_id = ProductVariants.product_id',
                                'wishlist_variants.customer_id' => $customer_id
                            ]
                        ];
                    }

                    return $q1->select([
                        'ProductVariants.id',
                        'ProductVariants.product_id',
                        'ProductVariants.variant_name',
                        'ProductVariants.reference_name',
                        'ProductVariants.sku',
                        'ProductVariants.variant_size',
                        'ProductVariants.variant_weight',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sales_price',
                        'ProductVariants.promotion_price',
                        'ProductVariants.quantity',
                        'ProductVariants.variant_description',
                        'ProductVariants.status',
                        'product_variant_discount' => $q1->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)'),
                         //Variant Favorite flag
                        'is_variant_favorite' => $q1->newExpr()->add(
                            !empty($customer_id)
                                ? "CASE WHEN wishlist_variants.id IS NOT NULL THEN 1 ELSE 0 END"
                                : "0"
                        )
                    ])
                    ->join($join)
                    ->where(['ProductVariants.status' => 'A']);
                },
                'ProductVariants.ProductVariantImages' => function ($q1) {
                    return $q1->where(['ProductVariantImages.status' => 'A']);
                },
            ])
            ->join([
                'OrderItems' => [
                    'table' => 'order_items',
                    'type' => 'INNER',
                    'conditions' => 'OrderItems.product_id = Products.id',
                    'OrderItems.status NOT IN' => ['Cancelled', 'Returned'],
                ],
                // Product-level wishlist join
                'wishlist_products' => [
                    'table' => 'wishlists',
                    'type' => 'LEFT',
                    'conditions' => !empty($customer_id)
                        ? [
                            'wishlist_products.product_id = Products.id',
                            'wishlist_products.product_variant_id IS' => null,
                            'wishlist_products.customer_id' => $customer_id
                        ]
                        : '1=0' // Disable if no customer_id
                ]
            ])
            ->leftJoinWith('Reviews', function ($q) {
                return $q->where(['Reviews.status' => 'A']);
            })
            ->leftJoinWith('ProductVariants')
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                [
                    'ProductDeals.product_id = Products.id'
                ]
            )
            ->where([
                'Products.status' => 'A',
                'Products.approval_status' => 'Approved'
            ]);

        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        $query->group(['Products.id'])
            ->order(['total_sales' => 'DESC'])
            ->toArray();

        return $query;
    }

    //M
    public function getNewArrivalProducts($limit = null, $categoryIds = [], $product_preference = null, $customer_id = null)
    {
        $products = $this->find();
        $products->select($this);
        $query = $this->find();

        // Define CASE expressions for promotion price and deal active flag
        $caseExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
         END'
        );

        $StartDatecaseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.start_date
        ELSE Products.promotion_start_date
    END'
        );

        $EndDatecaseExpression = $query->newExpr()->add(
            'CASE
        WHEN ProductDeals.status = "A"
             AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
        THEN ProductDeals.end_date
        ELSE Products.promotion_end_date
    END'
        );

        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN 1
            ELSE 0
         END'
        );

        // Product-level favorite
        $isProductFavoriteExpr = $products->newExpr()->add(
            !empty($customer_id)
                ? "CASE WHEN wishlist_products.id IS NOT NULL THEN 1 ELSE 0 END"
                : "0"
        );

        $query->select($this)
            ->select([
                'promotion_price' => $caseExpression,
                'promotion_start_date' => $StartDatecaseExpression,
                'promotion_end_date' => $EndDatecaseExpression,
                'IsDealActive' => $isDealActiveExpression,
                'product_discount' => $products->newExpr()->add('Products.sales_price - Products.promotion_price'),
                'is_product_favorite' => $isProductFavoriteExpr
            ])
            ->contain([
                  'ProductVariants' => function ($q1) use ($customer_id) {
                    
                    $join = [];
                    if (!empty($customer_id)) {
                        $join['wishlist_variants'] = [
                            'table' => 'wishlists',
                            'type' => 'LEFT',
                            'conditions' => [
                                'wishlist_variants.product_variant_id = ProductVariants.id',
                                'wishlist_variants.product_id = ProductVariants.product_id',
                                'wishlist_variants.customer_id' => $customer_id
                            ]
                        ];
                    }

                    return $q1->select([
                        'ProductVariants.id',
                        'ProductVariants.product_id',
                        'ProductVariants.variant_name',
                        'ProductVariants.reference_name',
                        'ProductVariants.sku',
                        'ProductVariants.variant_size',
                        'ProductVariants.variant_weight',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sales_price',
                        'ProductVariants.promotion_price',
                        'ProductVariants.quantity',
                        'ProductVariants.variant_description',
                        'ProductVariants.status',
                        'product_variant_discount' => $q1->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)'),
                        'is_variant_favorite' => $q1->newExpr()->add(
                        !empty($customer_id)
                                ? "CASE WHEN wishlist_variants.id IS NOT NULL THEN 1 ELSE 0 END"
                                : "0"
                        )

                    ])
                    ->join($join)
                    ->where(['ProductVariants.status' => 'A']);
                },
                'ProductVariants.ProductVariantImages' => function ($q1) {
                    return $q1->where(['ProductVariantImages.status' => 'A']);
                },
            ])
            ->join([
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => 'ProductVariants.product_id = Products.id'
                ],
                // Wishlist join for main product
                'wishlist_products' => [
                    'table' => 'wishlists',
                    'type' => 'LEFT',
                    'conditions' => !empty($customer_id)
                        ? [
                            'wishlist_products.product_id = Products.id',
                            'wishlist_products.product_variant_id IS' => null,
                            'wishlist_products.customer_id' => $customer_id
                        ]
                        : '1=0'
                ]
            ])
            // Left join with product_deals to allow promotion override logic
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                ['ProductDeals.product_id = Products.id']
            )
            ->where([
                'Products.status' => 'A',
                'Products.approval_status' => 'Approved',
                'Products.created >=' => (new \DateTime())->modify('-365 days')->format('Y-m-d H:i:s')
            ]);

        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        $query->group(['Products.id'])
            ->order(['Products.created' => 'DESC']);

        $result = $query->toArray();

        if (!$result) {
            return null;
        }

        return $result;
    }


    //M
    public function getFeaturedProducts($limit = null, $categoryIds = [], $product_preference = null, $customer_id = null)
    {
        $query = $this->find();

        // Define CASE expressions for promotion price and deal active flag
        $caseExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
         END'
        );
        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN 1
            ELSE 0
         END'
        );

        // Product favorite expression
        $isProductFavoriteExpr = $query->newExpr()->add(
            !empty($customer_id)
                ? "CASE WHEN wishlist_products.id IS NOT NULL THEN 1 ELSE 0 END"
                : "0"
        );

        $query->select($this)
            ->select([
                'promotion_price' => $caseExpression,
                'IsDealActive' => $isDealActiveExpression,
                'is_product_favorite' => $isProductFavoriteExpr
            ])
            ->contain([
                  'ProductVariants' => function ($q1) use ($customer_id) {

                    $join = [];
                    if (!empty($customer_id)) {
                        $join['wishlist_variants'] = [
                            'table' => 'wishlists',
                            'type' => 'LEFT',
                            'conditions' => [
                                'wishlist_variants.product_variant_id = ProductVariants.id',
                                'wishlist_variants.product_id = ProductVariants.product_id',
                                'wishlist_variants.customer_id' => $customer_id
                            ]
                        ];
                    }

                    return $q1->select([
                        'ProductVariants.id',
                        'ProductVariants.product_id',
                        'ProductVariants.variant_name',
                        'ProductVariants.reference_name',
                        'ProductVariants.sku',
                        'ProductVariants.variant_size',
                        'ProductVariants.variant_weight',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sales_price',
                        'ProductVariants.promotion_price',
                        'ProductVariants.quantity',
                        'ProductVariants.variant_description',
                        'ProductVariants.status',
                        'product_variant_discount' => $q1->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)'),
                        // Variant favorite flag
                        'is_variant_favorite' => $q1->newExpr()->add(
                            !empty($customer_id)
                                ? "CASE WHEN wishlist_variants.id IS NOT NULL THEN 1 ELSE 0 END"
                                : "0"
                        )
                    ])
                    ->join($join)
                    ->where(['ProductVariants.status' => 'A']);
                },
                'ProductVariants.ProductVariantImages' => function ($q1) {
                    return $q1->where(['ProductVariantImages.status' => 'A']);
                },
            ])
            ->join([
                'Reviews' => [
                    'table' => 'reviews',
                    'type' => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductVariants' => [
                    'table' => 'product_variants',
                    'type' => 'LEFT',
                    'conditions' => 'ProductVariants.product_id = Products.id'
                ],
                // Product-level wishlist join
                'wishlist_products' => [
                    'table' => 'wishlists',
                    'type' => 'LEFT',
                    'conditions' => !empty($customer_id)
                        ? [
                            'wishlist_products.product_id = Products.id',
                            'wishlist_products.product_variant_id IS' => null,
                            'wishlist_products.customer_id' => $customer_id
                        ]
                        : '1=0'
                ]
            ])
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                ['ProductDeals.product_id = Products.id']
            )
            ->where([
                'Products.status' => 'A',
                'Products.approval_status' => 'Approved',
                'Products.product_preference' => 'Featured'
            ]);

        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        $query->group(['Products.id']);

        return $query->toArray();
    }

    //Get custom widget products
    public function getCustomWidgetProducts($limit = null, $categoryIds = [], $product_preference = null)
    {
        //query to get custom widget products based on category IDs
        $query = $this->find();
        
        if ($limit !== null) {
            $query->limit($limit);
        }

        if (empty($categoryIds)) {
            // Return nothing if no category IDs given
            return $query->where(['1 = 0']);
        }

        return $query
            ->matching('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            })
            ->group(['Products.id']);
            // ->having(['COUNT(DISTINCT ProductCategories.category_id) =' => count($categoryIds)]);
    
        //End get custom widget products based on category IDs

    }
    //M
    public function getSpecialOffersProducts($limit = null, $categoryIds = [], $product_preference = null)
    {
        $query = $this->find();

        // Define CASE expressions for promotion price and deal active flag.
        $caseExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN ProductDeals.offer_price
            ELSE Products.promotion_price
         END'
        );
        $isDealActiveExpression = $query->newExpr()->add(
            'CASE
            WHEN ProductDeals.status = "A"
                 AND CURRENT_DATE BETWEEN ProductDeals.start_date AND ProductDeals.end_date
            THEN 1
            ELSE 0
         END'
        );

        $query->select($this)
            ->select([
                'promotion_price' => $caseExpression,
                'IsDealActive'    => $isDealActiveExpression,
            ])
            ->join([
                'Reviews' => [
                    'table'      => 'reviews',
                    'type'       => 'LEFT',
                    'conditions' => [
                        'Reviews.product_id = Products.id',
                        'Reviews.status' => 'A'
                    ]
                ],
                'ProductVariants' => [
                    'table'      => 'product_variants',
                    'type'       => 'LEFT',
                    'conditions' => 'ProductVariants.product_id = Products.id'
                ]
            ])
            ->leftJoin(
                ['ProductDeals' => 'product_deals'],
                ['ProductDeals.product_id = Products.id']
            )
            ->where([
                'Products.status'          => 'A',
                'Products.approval_status' => 'Approved',
                // Special offer condition: discount should be at least 20%
                'ROUND((Products.product_price - Products.promotion_price) * 100 / Products.product_price, 2) >=' => 20
            ]);

        if (!empty($categoryIds)) {
            $query->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
            });
        }

        if ($limit !== null) {
            $query->limit($limit);
        }

        $query->group(['Products.id']);

        return $query->toArray();
    }

    //S
    public function productAutocomplete($queryStr)
    {
        return $this->find('all')
            ->select(['id', 'name'])
            ->where([
                'OR' => [
                    'name LIKE' => '%' . $queryStr . '%', // Search in product names
                    'sku LIKE' => '%' . $queryStr . '%', // Search in sku
                ]
            ])
            ->toArray();
    }

    //S
    public function searchProduct($flag, $queryStr)
    {
        if ($flag == 'ID') {
            $products = $this->find()
                ->select(['id', 'name', 'url_key', 'description', 'sku', 'product_weight', 'product_size', 'product_price', 'sales_price', 'promotion_price', 'COD_in_city', 'COD_out_city'])
                ->contain([
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ],
                    'ProductVariants'
                ])
                ->where(['Products.id' => $queryStr])
                ->toArray();
        } else {

            $products = $this->find()
                ->select(['id', 'name', 'url_key', 'description', 'sku', 'product_weight', 'product_size', 'product_price', 'sales_price', 'promotion_price'])
                ->contain([
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ],
                    'ProductVariants'
                ])
                ->where([
                    'OR' => [
                        'Products.name LIKE' => '%' . $queryStr . '%', // Search in product names
                        'Products.sku LIKE' => '%' . $queryStr . '%', // Search in sku
                    ]
                ])
                ->distinct(['Products.id']) // Ensure unique results
                ->toArray();
        }

        return $products;
    }

    //S
    public function getProductDetail($code)
    {
        $res = $this->find()
            ->select(['id', 'name', 'url_key', 'description', 'sku', 'product_weight', 'product_size', 'product_price', 'sales_price', 'promotion_price', 'COD_in_city', 'COD_out_city'])
            ->contain([
                'ProductAttributes' => [
                    'Attributes',
                    'AttributeValues'
                ],
                'ProductVariants'
            ])
            ->where(['sku' => $code])
            ->first();

        if ($res) {
            return $res->toArray();
        } else {
            return false;
        }
    }

    public function deleteProduct(EntityInterface $product, array $options = []): bool
    {

        if ($this->delete($product)) {
            return true;
        }
        return false;
    }

    // write query with get product image from product image table and if image is not found then get from product variant image table
    public function getProductThumbnailImage($productId, $variantId = null)
    {
        if ($variantId) {
            $productVariantImage = $this->ProductVariants->ProductVariantImages->find()
                ->select(['image'])
                ->where(['variant_id' => $variantId, 'status' => 'A'])
                ->order(['image_default' => 'DESC'])
                ->first();

            if ($productVariantImage) {
                return $productVariantImage->image;
            }
        }

        $productImage = $this->ProductImages->find()
            ->select(['image'])
            ->where(['product_id' => $productId, 'status' => 'A'])
            ->order(['image_default' => 'DESC'])
            ->first();

        if ($productImage) {
            return $productImage->image;
        }

        return null;
    }

    //Get Product by ID
    public function getProductById($id)
    {
        return $this->get($id); // Fetches one record by primary key (id)
    }

    //S
    public function getZohoProductId($product_id) {
        
        $zohoId = $this->find()
        ->select(['zoho_productId'])
        ->where(['id' => $product_id])
        ->first();
        return $zohoId->zoho_productId;
    }
}
