body {
    background-color: #fff9f2;
    margin: 0;
}

.follow {
    width: 65px;
    height: 20px;
    font-family: sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #ffffff;
    margin-right: 12px;
    opacity: 0;
}

.social-icon {
    color: #ffffff;
    width: 16px;
    height: 16px;
    margin-right: 12px;
    opacity: 0;
}

.dropdown-container {
    display: flex;
    border-left: 1px solid rgba(255, 255, 255, 0.16);
    margin-left: 15px;
    padding-left: 15px;
    opacity: 0;
}

select {
    color: white;
    background-color: #004958;
    outline: none;
    border: none;
}

#languageSelect {
    margin-right: 15px;
    margin-left: 12px;
    outline: none;
    border: none;
}

#shopbtn {
    background-color: transparent !important;
    color: black;
    width: 60px;
    height: 23px;
    left: 85px;
    opacity: 0;
    font-family: Ubuntu;
    font-size: 16px;
    font-weight: 400;
    line-height: 16px;
    outline: none;
    border: none;
}

#openModal {
    display: block;
}
/* style for home page */
.home .banner-container{
    display: block;
    margin-top: 22px;
    padding: 0 15px;
}
.home .banner-container .category-container{
    display: none;
}
.home .banner-container .slider-container .slider-wrapper .slider img{
    width: 100%;
    border-radius: 20px;
}
.home .banner-container .aside{
    display: block;
    width: 100%;
    background-color: #fff;
}
.home .banner-container .aside .aside-top .aside-image-content{
    display: flex;
    justify-content: center;
}
.home .banner-container .aside .aside-top .aside-image-content .orders{
    display: block;
    width: 48%;
    text-decoration: none;
    color: #000;
    font-size: 14px;
    text-align: center;
}
.home .banner-container .aside .aside-top .aside-image-content .orders:last-child{
    display: block;
    width: 50%;
}
.home .banner-container .aside .aside-top .aside-image-content .orders .aside-image-icons{
    width: 24px;
    height: 24px;
}
.home .banner-container .aside .aside-top .aside-image-content .orders .aside-contents {
    text-decoration: none;
    font-size: 14px;
    color: #000;
    font-weight: 400;
}
.home .banner-container .aside .aside-top .aside-image-content .orders .aside-contents .text-highlight {
    font-size: 14px;
    font-weight: 600;
    color: #EF8013;
}
.home .banner-container .aside .aside-bottom{
    display: none;
}
/* Deals of the day */
.mobile-deals-day{
    display: block;
}
.deals-day{
    display: none;
}
.mobile-deals-day .deal-timer-container{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}
.mobile-deals-day .deal-timer-container .browse-all-product{
    display: none;
}
.mobile-deals-day .prnt-pt-ad{
    display: flex;
    padding-left: 15px;
}
.mobile-deals-day .prnt-pt-ad .img-wrapper{
    width: 109px;
    margin-top: 15px;
    margin-bottom: 15px;
    margin-right: 0px;
}
.mobile-deals-day .prnt-pt-ad .img-wrapper img{
    width: 100%;
    height: 100%;
    border-radius: 20px;
}
.mobile-deals-day .prnt-pt-ad .deal-of-day-cards{
    width: 80%;
}
.mobile-deals-day .prnt-pt-ad .deal-of-day-cards .deal-first{
    display: flex;
    width: 100%;
}
/* Footer banner section */
.footer-banner-wrapper{
    /* padding-left: 15px; */
    display: flex;
    align-items: center;
    padding: 0 13px;
}
.footer-banner-wrapper .ad-img {
    width: 50%;
    padding: 0 2px;
}
/* Question section */
.ques-section{
    display: none;
}

@media screen and (min-width:992px) {
    /* popup style */
    #modal{
        z-index: 9999;
        position: absolute;
        top: 219px;
        right: 270px;
        display: none;
        border: 1px solid #ef8013;
        border-radius: 10px;
        background-color: #fff;
        padding: 10px;
    }
    #modal:before {
        content: '';
        border: 1px solid #ef8013;
        border-left: 1px solid transparent;
        border-bottom: 1px solid transparent;
        width: 10px;
        height: 10px;
        position: absolute;
        top: -6px;
        z-index: 9999;
        display: inline-block;
        transform: rotate(-45deg);
        left: 24px;
    }
    #modal .modal-content .pop-up-item-details {
        color: #000;
        text-decoration: none;
        font-size: 15px;
        font-weight: 500;
        margin-top: 10px;
        margin-bottom: 0px;
    }
    #modal .close-btn{
        position: absolute;
        top: 5px;
        right: 10px;
    }
    #cart{
        z-index: 9999;
        position: absolute;
        top: 219px;
        right: 210px;
        display: none;
        border: 1px solid #ef8013;
        border-radius: 10px;
        background-color: #fff;
        padding: 10px;
    }
    #cart:before {
        content: '';
        border: 1px solid #ef8013;
        border-left: 1px solid transparent;
        border-bottom: 1px solid transparent;
        width: 10px;
        height: 10px;
        position: absolute;
        top: -6px;
        z-index: 9999;
        display: inline-block;
        transform: rotate(-45deg);
        left: 24px;
    }
    #cart .close-btn{
        position: absolute;
        top: 5px;
        right: 10px;
    }
    #cart .modal-content .pop-up-item-details {
        color: #000;
        text-decoration: none;
        font-size: 15px;
        font-weight: 500;
        margin-top: 0px;
        margin-bottom: 0px;
    }
    #cart .modal-content > a{
        color: #ee902c;
        text-decoration: none;
        font-size: 15px;
        font-weight: 500;
        margin-top: 0px;
        margin-bottom: 10px;
        display: block;
    }
    #cart .modal-content .wish-list-more > a{
        color: #ee902c;
        text-decoration: none;
        font-size: 15px;
        font-weight: 500;
        margin-top: 5px;
        margin-bottom: 0px;
        display: block;
    }
    /* Home screen style */
    .home .banner-container {
        display: flex;
        justify-content: center;
        margin-top: 22px;
    }
    .home .banner-container .category-container{
        width: 20%;
        display: block;
    }
    .home .banner-container .category-container ul{
        list-style-type: none;
        background-color: #fff;
        border-radius: 20px;
        border: 1px solid #F9DAB9;
        padding-top: 31px;
        padding-bottom: 17px;
        padding-left: 12px;
        padding-right: 9px;
        margin-top: 0;
        height: 526px;
        overflow: hidden;
    }
    .home .banner-container .category-container ul li{
        padding: 13px 10px;
        position: relative;
    }
    .home .banner-container .category-container ul li:hover{
        background-color: #F9DAB9;
        border-radius: 8px;
    }
    .home .banner-container .category-container ul li a{
        font-size: 16px;
        font-weight: 400;
        color: #505050;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .home .banner-container .category-container ul li a > span{
        color: #D47712;
        position: absolute;
        right: -6px;
    }
    .home .banner-container .category-container ul li:hover a{
        color: #1C1C1C;
        font-weight: 600;
    }
    .home .banner-container .slider-container{
        width: 59%;
        border-radius: 20px;
        margin-left: 10px;
        margin-right: 16px;
    }
    .home .banner-container .slider-container .slider-wrapper .slider{
        width: 100% !important;
    }
    .home .banner-container .slider-container .slider-wrapper .slider img{
        width: 100%;
        height: 520px;
        border-radius: 20px;
    }
    .home .banner-container .aside {
        width: 21%;
        height: 520px;
        background-color: #ee902c;
        border-radius: 20px;
        padding: 6px;
    }

    .home .banner-container .aside .aside-top {
        width: 100%;
        height: auto;
        border-radius: 20px;
        background-color: #ffffff;
        margin-left: 0px;
        margin-top: 0px;
        margin-bottom: 9px;
        padding: 27px 10px 29px;
    }
    .home .banner-container .aside .aside-top .aside-image-content {
        display: block;
    }
    .home .banner-container .aside .aside-top .aside-image-content .orders {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: #000;
        margin-bottom: 18px;
        width: 100%;
        text-align: left;
    }
    .home .banner-container .aside .aside-top .aside-image-content .orders:last-child {
        display: flex;
        width: 100%;
    }
    .home .banner-container .aside .aside-top .aside-image-content .orders:last-child {
        margin-bottom: 0px;
    }
    .home .banner-container .aside .aside-top .aside-image-content .orders .aside-image-icons {
        width: 26px;
        height: 26px;
    }
    .home .banner-container .aside .aside-top .aside-image-content .orders .aside-contents {
        width: 87%;
        margin-left: 11px;
    }
    .home .banner-container .aside .aside-top .aside-image-content .orders .aside-contents {
        text-decoration: none;
        font-size: 14px;
        color: #000;
        font-weight: 400;
    }
    .home .banner-container .aside .aside-top .aside-image-content .orders .aside-contents .text-highlight {
        font-size: 16px;
        font-weight: 600;
        color: #EF8013;
    }
    .home .banner-container .aside .aside-top .aside-image-container a {
        display: block;
    }
    
    .home .banner-container .aside .aside-bottom{
        display: block;
        width: 100%;
        height: 321px;
        margin-left: 0px;
        margin-right: 0px;
        margin-bottom: 0px;
    }
    .home .banner-container .aside .aside-bottom .img-wrapper{
        overflow: hidden;
        border-radius: 15px;
        height: 100%;
    }
    .home .banner-container .aside .aside-bottom .aside-bottom-img {
        width: 100%;
        height: 100%;
        border-radius: 20px;
        margin-left: 0px;
        object-fit: cover;
        /* transform: scale(1.33); */
    }
    /* Deals of the day */
    .mobile-deals-day{
        display: none;
    }
    .deals-day{
        display: block;
    }
    .deals-day .deal-timer-container{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
    }
    .deals-day .prnt-pt-ad{
        display: flex;
    }
    .deals-day .prnt-pt-ad .img-wrapper{
        width: 224px;
        margin-top: 15px;
        margin-bottom: 15px;
        margin-right: 15px;
    }
    .deals-day .prnt-pt-ad .img-wrapper img{
        width: 100%;
        height: 100%;
        border-radius: 20px;
    }
    .deals-day .prnt-pt-ad .deal-of-day-cards{
        width: 78%;
    }
    .deals-day .prnt-pt-ad .deal-of-day-cards .deal-first{
        display: flex;
        width: 100%;
    }
    /* Middle Banner Section */
    #middleBannerCarousel{
        padding: 0;
    }
    #middleBannerCarousel .carousel{
        padding-right: 0;
    }
    #middleBannerCarousel .carousel-inner-class{
        padding-left: 0;
    }
    #middleBannerCarousel .carousel-inner-class .landscape-images{
        margin-right: 0;
    }
    /* Foorer Banner Section */
    .footer-banner-wrapper{
        display: flex;
        align-items: center;
        margin-top: 20px;
        padding: 0px 0px 15px;
    }
    .footer-banner-wrapper .ad-img{
        width: 50%;
        padding: 0 15px;
    }
    .footer-banner-wrapper .ad-img img{
        width: 100%;
        height: 261px;
        object-fit: cover;
        border-radius: 20px;
    }
    /* Question section */
    .ques-section{
        display: block;
        padding-top: 20px;
    }
    .ques-section .ques-other-parent{
        display: flex;
        align-items: flex-start;
    }
    .ques-section .ques-other-parent .ques{
        width: 55%;
        margin-right: 101px;
    }
    .ques-section .ques-other-parent .ques .faq-item{
        border-top: 1px solid #000;
    }
    .ques-section .ques-other-parent .ques .faq-item:last-child{
        border-bottom: 1px solid #000;
    }
    .ques-section .ques-other-parent .ques .faq-item .bab-ques-q{
        position: relative;
        font-size: 18px;
        font-weight: 600;
        line-height: 30px;
    }
    .ques-section .ques-other-parent .ques .faq-item .bab-ques-q .plus{
        position: absolute;
        right: 0;
        top: 6px;
        font-weight: 400;
        display: flex;
        border: 1px solid #EE902C;
        border-radius: 50%;
        width: 15px;
        height: 15px;
        align-items: center;
        justify-content: center;
        background-color: #EE902C;
        color: #fff;
        cursor: pointer;
    }
    .ques-section .ques-other-parent .ques .faq-item .bab-ques-q .plus .symbol{
        position: relative;
        top: 1px;
    }
    .ques-section .ques-other-parent .ques .faq-item.active .bab-ques-q .plus .symbol{
        position: relative;
        top: -1px;
    }
    .ques-section .ques-other-parent .other{
        width: 45%;
    }
    .ques-section .ques-other-parent .other .bab-ques-w{
        font-size: 25px;
        font-weight: 500;
        line-height: 32px;
        margin: 0 0 13px;
    }
    .ques-section .ques-other-parent .other .bab-ques-b {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        color: rgba(0, 0, 0, .6);
        margin: 0;
    }
    .ques-section .ques-other-parent .other .bab-ques-b > a {
        display: block;
        margin-top: 5px;
        text-decoration: none;
        color: rgba(0,0,0,.6);
    }
}
@media screen and (min-width:1200px) {
    .home .banner-container .category-container{
        width: 20%;
    }
    .home .banner-container .slider-container{
        width: 62%;
        border-radius: 20px;
        margin-left: 10px;
        margin-right: 16px;
    }
    .home .banner-container .aside {
        width: 17%;
        height: 520px;
        background-color: #ee902c;
        border-radius: 20px;
        padding: 6px;
    }
}
@media screen and (min-width:1400px) {
    .home .banner-container .category-container{
        width: 18%;
    }
    .home .banner-container .slider-container{
        width: 68%;
        border-radius: 20px;
        margin-left: 10px;
        margin-right: 16px;
    }
    .home .banner-container .aside {
        width: 15%;
        height: 520px;
        background-color: #ee902c;
        border-radius: 20px;
        padding: 6px;
    }
}
