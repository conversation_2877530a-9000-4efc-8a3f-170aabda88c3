<?php $this->end(); ?>
<link rel="stylesheet" href="/css/styles.css"/>
 <link rel="stylesheet" href="/css/card.css"/>

<?php $this->start('add_js'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <!-- <script>
        $(document).ready(function(){
            $(".owl-carousel").owlCarousel({
                loop: true,              // Loop through the slides
                margin: 20,              // Spacing between slides
                nav: true,               // Show next/prev buttons
                dots: false,                  // Show pagination dots
                autoplay: true,          // Autoplay the carousel
                autoplayTimeout: 3000,   // Autoplay interval in ms
                autoplayHoverPause: true, // Pause autoplay on hover
                responsive: {            // Responsive settings
                    0: {                 // For screens less than 600px wide
                        items: 1         // Show 1 item
                    },
                    600: {               // For screens 600px and up
                        items: 3         // Show 3 items
                    },
                    1000: {              // For screens 1000px and up
                        items: 5         // Show 5 items
                    }
                }
            });
        });
    </script> -->
<?php $this->end(); ?>

<home class="home">
    <div class="container">
        <div class="banner-container">
            <div class="category-container">
                <ul>
                    <?php foreach ($categories as $cate): ?>
                        <li>
                            <a href="<?= '/product-list/' . $cate['url_key'] ?>" class="category-links"><?= h($cate['name']) ?><span>&#12297;</span></a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="slider-container">
                <!-- <img class="slider" src="./assets/ad-big-scrn.png" /> -->
                <div class="slider-wrapper">
                    <div class="slider">
                        <?php foreach ($banners as $banner): ?>
                            <?php if(isset($banner['url_link']) && !empty($banner['url_link'])): ?>
                        <a target="_blank" href="<?= h($banner['url_link']) ?>">
                            <img src="<?= h($banner['web_banner']) ?>" alt="<?= h($banner['title'] ?? 'Banner Image') ?>"></a>
                        <?php else: ?>
                                <img src="<?= h($banner['web_banner']) ?>" alt="<?= h($banner['title'] ?? 'Banner Image') ?>">
                        <?php endif; ?>

                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <div class="aside">
                <div class="aside-top">
                    <div class="aside-image-content">
                        <a href="tel: +25 21 007 007" class="orders">
                            <img src="<?= $this->Url->webroot('assets/cart.png') ?>" class="aside-image-icons"/>
                            <div class="aside-contents">
                                <div class="call">Call To Order</div>
                                <div class="text-highlight"> 25 21 007 007</div>
                            </div>
                        </a>
                        <a href="/become-seller" class="orders">
                            <img src="<?= $this->Url->webroot('assets/shop.png') ?>" class="aside-image-icons"/>
                            <div class="aside-contents">
                                    Sell On <span class="text-highlight">BABIKEN</span>
                            </div>
                        </a>
                        <a href="/shops" class="orders">
                            <img src="<?= $this->Url->webroot('assets/deal.png') ?>" class="aside-image-icons"/>
                            <div class="aside-contents">Our Shops</div>
                        </a>
                    </div>
                    <div class="aside-text-container">
                        
                        
                        
                    </div>
                </div>
                <?php if (isset($banner_ads_middle_side_right) && !empty($banner_ads_middle_side_right)): ?>
                    <div class="aside-bottom">
                        <?php foreach ($banner_ads_middle_side_right as $val): ?>
                            <?php if ($val['ad_type'] == 'Sidebar Right'): ?>
                                <div class="img-wrapper">
                                    <img class="aside-bottom-img" src="<?= $val['web_image'] ?>"/>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</home>
<div class="container">
    <?php if (count($topdeals) > 0): ?>
        <div class="carousel-container">
            <p class="carousel-head"><?= __('Top Deal Items') ?></p>
            <div class="carousel-controls">
                <button id="topDealCarousel-left" class="carousel-button left-btn">🡠</button>
                <button id="topDealCarousel-right" class="carousel-button right-btn">🡢</button>
            </div>
            <div class="carousel-wrapper"  id="topDealCarousel">
                <div class="parent-carousel">
                    <div class="carousel">
                        <div class="carousel-inner-class">
                            <?php foreach ($topdeals as $val): ?>
                                <div class="card">
                                    <div class="card-body">
                                        <?php if (!empty($val['web_image'])): ?>
                                            <div class="card-image">
                                                <img src="<?= $val['web_image'] ?>" class="top-deal-image"/>
                                            </div>
                                        <?php else: ?>
                                            <div class="card-image">
                                                <img src="<?= $this->Url->webroot('assets/no-image.png') ?>" class="top-deal-image"
                                                alt="No Image Available"/>
                                            </div>
                                        <?php endif; ?>
                                        <div class="card-name">
                                            <div class="top-deal-heading"><?= h($val['offer_name']) ?></div>
                                            <!--                            <div class="top-deal-subheading">UP TO -->
                                            <?php //= h($val['discount']) ?><!-- OFF</div>-->
                                            <div class="top-deal-subheading"><?= h($val['offer_description']) ?></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<div class="container">
    <?php if (isset($widgets['best_selling']) && count($widgets['best_selling']) > 0): ?>
        <div class="carousel-container">
            <p class="carousel-head">
                <?php if (isset($widgets['best_selling'][0]['widget']['title']) && !empty($widgets['best_selling'][0]['widget']['title'])): ?>
                    <?= trim($widgets['best_selling'][0]['widget']['title']); ?>
                <?php else: ?>
                    <?= __('Top Selling Items') ?>
                <?php endif; ?>
            </p>
            <div class="carousel-controls">
                <button id="topSellingCarousel-left" class="carousel-button left-btn">🡠</button>
                <button id="topSellingCarousel-right"  class="carousel-button right-btn">🡢</button>
            </div>
            <div class="carousel-wrapper"  id="topSellingCarousel">
                <div class="parent-carousel clone-parent-carousel">
                    <div class="carousel">
                        <div class="carousel-inner-class">
                            <?php if (isset($widgets['best_selling'][0]['products'])): ?>
                                <?php foreach ($widgets['best_selling'][0]['products'] as $val): ?>
                                    <div class="card">
                                        <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val['url_key']]) ?>" class="card-anchor"></a>
                                        <div class="card-wishlist">
                                            <?php if ($val->whishlist): ?>
                                                <div class="remove-to-wishlist-btn"
                                                    data-product-id="<?= $val->id ?>"><span
                                                        class="wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                                </div>
                                            <?php else: ?>
                                                <div class="add-to-wishlist-btn"
                                                    data-product-id="<?= $val->id ?>">
                                                    <span class="wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-body">
                                            <div class="card-image">
                                                <?php if (!empty($val['product_image'])): ?>
                                                    <img src="<?= $val['product_image'] ?>" class="product-img" alt="Product Image" />
                                                <?php else: ?>
                                                    <img src="<?= $this->Url->webroot('assets/no-image.png') ?>" class="product-img"
                                                    alt="Product Image"/>
                                                <?php endif; ?>
                                            </div>
                                            <?php
                                            $rating = $val['rating'];
                                            $fullStars = floor($rating);
                                            $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                            $emptyStars = 5 - $fullStars - $halfStar;
                                            $starsHtml = str_repeat('★', $fullStars); // Full stars
                                            if ($halfStar) {
                                                $starsHtml .= '★☆'; // Half star
                                            }
                                            $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                            ?>

                                            <div class="card-rating">
                                                <?= $starsHtml ?> <span
                                                    class="card-star"><?= number_format($rating, 1) ?></span>
                                            </div>

                                            <div class="card-name">
                                                <span class="product-name"><?= $val['name'] ?> </span>
                                            </div>
                                            <div class="card-reference">
                                                <span class="reference-title"><?= __("Reference: ") ?></span> <span class="referenece-name"><?= $val['reference_name'] ?? "" ?></span>
                                            </div>
                                            <div class="card-price">
                                                <span>
                                                    <?= $this->Price->setPriceFormat($val['promotion_price']) ?>
                                                    <div class="card-offer">
                                                        <span class="strike-price"> <?= $this->Price->setPriceFormat($val['sales_price']) ?></span>
                                                        <span class="price-offer"><span class="ax-sale-off"><?= $val['discount'] ?></span>% 0ff</span>
                                                    </div>
                                                </span>
                                                <button class="card-add-to-cart-button add-to-cart" data-product-id="<?= $val['id'] ?>">
                                                    <span class="card-cart-icon"><i class="fas fa-cart-arrow-down"></i></span>
                                                    <span class="card-add-to-cart">ADD TO CART</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>                    
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<!-- New Arrival slider  -->
<div class="container">
    <div class="carousel-container">
        <?php if (isset($widgets['new_arrival'][0]['products'])): ?>
            <p class="carousel-head">
                <?php if (isset($widgets['new_arrival'][0]['widget']['title']) && !empty($widgets['new_arrival'][0]['widget']['title'])): ?>
                    <?= trim($widgets['new_arrival'][0]['widget']['title']); ?>
                <?php else: ?>
                    <?= __("New Arrivals") ?>
                <?php endif; ?>
            </p>
            <div class="carousel-controls">
                <button id="newArrivalCarousel-left"  class="carousel-button left-btn">🡠</button>
                <button id="newArrivalCarousel-right"  class="carousel-button right-btn">🡢</button>
            </div>
            <div class="carousel-wrapper"  id="newArrivalCarousel">
                <div class="parent-carousel">
                    <div class="carousel">
                        <div class="carousel-inner-class">
                            <?php foreach ($widgets['new_arrival'][0]['products'] as $val): ?>
                                <div class="card">
                                    <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val->url_key]) ?>" class="card-anchor"></a>
                                    <div class="card-wishlist">
                                        <?php if ($val->whishlist): ?>
                                            <div class="remove-to-wishlist-btn"
                                                data-product-id="<?= $val->id ?>"><span
                                                    class="wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                            </div>
                                        <?php else: ?>
                                            <div class="add-to-wishlist-btn"
                                                data-product-id="<?= $val->id ?>"><span
                                                    class="wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body">
                                        <div class="card-image">
                                            <?php if (!empty($val['product_image'])): ?>
                                                <img src="<?= $val['product_image'] ?>" class="product-img" alt="Product Image"/>
                                            <?php else: ?>
                                                <img src="<?= $this->Url->webroot('assets/no-image.png') ?>" class="product-img" alt="Product Image"/>
                                            <?php endif; ?>
                                        </div>
                                        <?php
                                        $rating = $val['rating'];
                                        $fullStars = floor($rating);
                                        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                        $emptyStars = 5 - $fullStars - $halfStar;
                                        $starsHtml = str_repeat('★', $fullStars); // Full stars
                                        if ($halfStar) {
                                            $starsHtml .= '★☆'; // Half star
                                        }
                                        $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                        ?>
                                        <div class="card-rating">
                                            <?= $starsHtml ?> <span
                                                class="card-star"><?= number_format($rating, 1) ?></span>
                                        </div>
                                        <div class="card-name">
                                            <span class="product-name"><?= $val['name'] ?><span>
                                        </div>
                                        <div class="card-reference">
                                            <span class="reference-title"><?= __("Reference: ") ?></span> <span class="referenece-name"><?= $val['reference_name'] ?? "" ?></span>
                                        </div>
                                        <div class="card-price">
                                            <span>
                                                <?= $this->Price->setPriceFormat($val['promotion_price']) ?>
                                                <div class="card-offer">
                                                    <span class="strike-price"><?= $this->Price->setPriceFormat($val['sales_price']) ?></span>
                                                    <!-- <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $val['discount'] ?></span>% 0ff</span> -->
                                                    <span class="price-offer"><?= __("NEW") ?></span>
                                                </div>
                                            </span>
                                            <button class="card-add-to-cart-button add-to-cart"
                                                    data-product-id="<?= $val['id'] ?>">
                                                <span class="card-cart-icon"><i
                                                        class="fas fa-cart-arrow-down"></i></span>
                                                <span class="card-add-to-cart">ADD TO CART</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- Middle Banner slider -->
<!-- <div class="container"> -->
    <div class="carousel-container">
        <?php if (isset($banner_ads_middle) && !empty($banner_ads_middle)): ?>
            <div class="carousel-wrapper"  id="middleBannerCarousel">
                <div class="parent-carousel">
                    <div class="carousel">
                        <div class="carousel-inner-class">
                            <?php foreach ($banner_ads_middle as $val): ?>
                                <?php if ($val['ad_type'] == 'Middle'): ?>
                                    <div class="landscape-images">
                                        <img class="landscape-images-img" src="<?= $val['web_image'] ?>"/>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                        <button class="prev" onclick="plusSlides(-1, 'middleBannerCarousel')">🡠</button>
                        <button class="next" onclick="plusSlides(1, 'middleBannerCarousel')">🡢</button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
<!-- </div> -->
<!-- Deals of the day section -->
<div class="container">
    <div class="carousel-container deals-day">
        <?php   if (isset($widgets['deal']) && count($widgets['deal']) > 0): ?>
            <p class="carousel-head"><?php echo $widgets['deal'][0]['widget']['title']; ?></p>
            <div class="deal-timer-container">
                <p class="deal-of-day-timer">Deals end in: <span id="timer"></span>
                    <span>
                        <?php
                            // Current date and time
                            $currentDate = new DateTime();

                            // Replace this with your dynamic end date from the array
                            $endDate = new DateTime(date('Y-m-d') . ' 23:59:59'); // DateTime($widgets['deal'][0]['widget']['end_date']);

                            // Check if the deal is still active
                            if ($endDate > $currentDate) {
                            // Calculate the remaining time in seconds
                            $remainingSeconds = $endDate->getTimestamp() - $currentDate->getTimestamp();
                        ?>

                            <script>
                                // JavaScript Countdown Timer
                                let remainingTime = <?php echo $remainingSeconds; ?>;

                                function updateTimer() {
                                    if (remainingTime <= 0) {
                                        document.getElementById('timer').innerText = "Deal has ended.";
                                        return;
                                    }

                                    // Calculate hours, minutes, and seconds
                                    const hours = Math.floor(remainingTime / 3600);
                                    const minutes = Math.floor((remainingTime % 3600) / 60);
                                    const seconds = remainingTime % 60;

                                    // Display the time in HH:MM:SS format
                                    document.getElementById('timer').innerText =
                                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                                    // Decrement the remaining time
                                    remainingTime--;
                                }

                                // Initialize the timer
                                updateTimer();
                                setInterval(updateTimer, 1000);
                            </script>
                        <?php
                        } else {
                            // If the deal has already ended
                            echo "<p>". __('The deal has ended.') . "</p>";
                        }
                        ?>
                    </span>
                </p>
                <a class="browse-all-product" href="/deals">
                    <?= __('Browse All Product') ?>  <span class="cat-aero">🡢</span>
                </a>
            </div>
            <div class="prnt-pt-ad">
                <div class="img-wrapper">
                    <?php if (isset($banner_ads_middle_sidebar_left) && !empty($banner_ads_middle_sidebar_left)): ?>
                        <?php foreach ($banner_ads_middle_sidebar_left as $val): ?>
                            <?php if ($val['ad_type'] == 'Sidebar Left'): ?>
                                <img src="<?= $val['web_image'] ?>" class="pt-ad"/>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php
                // print_r($widgets['deal'][0]['widget']['products']); die;
                // Split the products array into two parts for display in two columns
                $firstColumn = array_slice($widgets['deal'][0]['products'], 0, 4);
                $secondColumn = array_slice($widgets['deal'][0]['products'], 4, 4);
                ?>

                <div class="deal-of-day-cards">
                    <!-- First Column -->
                    <div class="deal-first">
                        <?php foreach ($firstColumn as $product): ?>
                            <div class="card">
                                <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $product->url_key]) ?>"></a>
                                <div class="card-wishlist">
                                    <?php if ($product->whishlist): ?>
                                        <div class="remove-to-wishlist-btn"data-product-id="<?= $product->id ?>">
                                            <span class="wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                        </div>
                                    <?php else: ?>
                                        <div class="add-to-wishlist-btn" data-product-id="<?= $product->id ?>">
                                            <span class="wishlist-heart"><img src="/assets/heart-nobackground.png" class="wishlist"></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body">
                                    <div class="card-image">
                                        <img src="<?= $product['product_image'] ?>" class="snd-crsl-img-1"/>
                                        <div class="hr-container">
                                            <div class="hr" style="width: 50%;background: #FF3B3B;height: 100%;"></div>
                                        </div>
                                    </div>
                                    <?php
                                    $rating = $product['rating'];
                                    $fullStars = floor($rating);
                                    $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                    $emptyStars = 5 - $fullStars - $halfStar;
                                    $starsHtml = str_repeat('★', $fullStars); // Full stars
                                    if ($halfStar) {
                                        $starsHtml .= '★☆'; // Half star
                                    }
                                    $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                    ?>
                                    <div class="card-rating">
                                        <?= $starsHtml ?> 
                                        <span class="card-star"><?= number_format($rating, 1) ?></span>
                                    </div>
                                    <div class="card-name">
                                        <span class="product-name"><?= $product['name'] ?></span>
                                    </div>
                                    <div class="card-reference">
                                        <span class="reference-title">Reference:</span> <span class="reference-name"><?= $product['reference_name'] ?? "" ?></span>
                                    </div>
                                    <!-- <div class="custom-card-subname-1"><?= $product['reference_name'] ?? "" ?></div> -->
                                    
                                    <div class="card-price">
                                        <span>
                                            <?= $this->Price->setPriceFormat($product['promotion_price']) ?>
                                            <div class="card-offer">
                                                <span class="strike-price"><?= $this->Price->setPriceFormat($product['sales_price']) ?></span>
                                                <span class="price-offer"><span class="ax-sale-off"><?= $product['discount'] ?></span>% 0ff</span>
                                            </div>
                                        </span>
                                        <button class="card-add-to-cart-button  add-to-cart"
                                                data-product-id="<?= $product->id ?>">
                                            <span class="card-cart-icon"><i class="fas fa-cart-arrow-down"></i></span>
                                            <span class="card-add-to-cart">ADD TO CART</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <!-- Second Column -->
                    <div class="deal-second">
                        <?php foreach ($secondColumn as $product): ?>
                            <div class="card">
                                <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $product['url_key']]) ?>"></a>
                                <div class="card-wishlist">
                                    <?php if ($product->whishlist): ?>
                                        <div class="remove-to-wishlist-btn" data-product-id="<?= $product->id ?>">
                                            <span class="p-v-p-item-description-add-to-wishlist-heart"><img src="/assets/heart-background.png" class="wishlist"> </span>
                                        </div>
                                    <?php else: ?>
                                        <div class="add-to-wishlist-btn" data-product-id="<?= $product->id ?>">
                                            <span class="p-v-p-item-description-add-to-wishlist-heart"><img src="/assets/heart-nobackground.png" class="wishlist">  </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body">
                                    <div class="card-image">
                                        <img src="<?= $product['product_image'] ?>" style="height: 220px;" class="snd-crsl-img-1"/>
                                        <div class="hr-container">
                                            <div class="hr" style="width: 50%;background: #FF3B3B;height: 100%;"></div>
                                        </div>
                                    </div>
                                    <?php
                                    $rating = $product['rating'];
                                    $fullStars = floor($rating);
                                    $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                    $emptyStars = 5 - $fullStars - $halfStar;
                                    $starsHtml = str_repeat('★', $fullStars); // Full stars
                                    if ($halfStar) {
                                        $starsHtml .= '★☆'; // Half star
                                    }
                                    $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                    ?>
                                    <div class="card-rating">
                                        <?= $starsHtml ?> <span
                                            class="card-star"><?= number_format($rating, 1) ?></span>
                                    </div>
                                    <div class="card-name">
                                        <span class="product-name"><?= $product['name'] ?></span>
                                        <!-- <div class="custom-card-subname">Reference: </div> -->
                                    </div>
                                    <div class="card-reference">
                                        <span class="reference-title">Reference:</span> <span class="reference-name"><?= $product['reference_name'] ?? "" ?></span>
                                    </div>
                                    <div class="card-price">
                                        <span>
                                            <?= $this->Price->setPriceFormat($product['promotion_price']) ?>
                                            <div class="card-offer">
                                                <span class="strike-price"><?= $this->Price->setPriceFormat($product['sales_price']) ?></span>
                                                <span class="price-offer"><span class="ax-sale-off"><?= $product['discount'] ?></span>% 0ff</span>
                                            </div>
                                        </span>
                                        <button class="card-add-to-cart-button  add-to-cart" data-product-id="<?= $product['id'] ?>">
                                            <span class="card-cart-icon"><i class="fas fa-cart-arrow-down"></i></span>
                                            <span class="card-add-to-cart">ADD TO CART</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <div class="carousel-container mobile-deals-day">
        <?php   if (isset($widgets['deal']) && count($widgets['deal']) > 0): ?>
            <p class="carousel-head"><?php echo $widgets['deal'][0]['widget']['title']; ?></p>
            <div class="deal-timer-container">
                <p class="deal-of-day-timer">Deals end in: <span id="timer"></span>
                    <span>
                        <?php
                            // Current date and time
                            $currentDate = new DateTime();

                            // Replace this with your dynamic end date from the array
                            $endDate = new DateTime(date('Y-m-d') . ' 23:59:59'); // DateTime($widgets['deal'][0]['widget']['end_date']);

                            // Check if the deal is still active
                            if ($endDate > $currentDate) {
                            // Calculate the remaining time in seconds
                            $remainingSeconds = $endDate->getTimestamp() - $currentDate->getTimestamp();
                        ?>

                            <script>
                                // JavaScript Countdown Timer
                                let remainingTime = <?php echo $remainingSeconds; ?>;

                                function updateTimer() {
                                    if (remainingTime <= 0) {
                                        document.getElementById('timer').innerText = "Deal has ended.";
                                        return;
                                    }

                                    // Calculate hours, minutes, and seconds
                                    const hours = Math.floor(remainingTime / 3600);
                                    const minutes = Math.floor((remainingTime % 3600) / 60);
                                    const seconds = remainingTime % 60;

                                    // Display the time in HH:MM:SS format
                                    document.getElementById('timer').innerText =
                                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                                    // Decrement the remaining time
                                    remainingTime--;
                                }

                                // Initialize the timer
                                updateTimer();
                                setInterval(updateTimer, 1000);
                            </script>
                        <?php
                        } else {
                            // If the deal has already ended
                            echo "<p>". __('The deal has ended.') . "</p>";
                        }
                        ?>
                    </span>
                </p>
                <a class="browse-all-product" href="/deals">
                    <?= __('Browse All Product') ?>  <span class="cat-aero">🡢</span>
                </a>
            </div>
            <div class="carousel-controls">
                <button id="dealDayCarousel-left"  class="carousel-button left-btn">🡠</button>
                <button id="dealDayCarousel-right"  class="carousel-button right-btn">🡢</button>
            </div>
            <div class="prnt-pt-ad">
                <div class="img-wrapper">
                    <?php if (isset($banner_ads_middle_sidebar_left) && !empty($banner_ads_middle_sidebar_left)): ?>
                        <?php foreach ($banner_ads_middle_sidebar_left as $val): ?>
                            <?php if ($val['ad_type'] == 'Sidebar Left'): ?>
                                <img src="<?= $val['web_image'] ?>" class="pt-ad"/>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <div class="carousel-wrapper"  id="dealDayCarousel">
                    <div class="parent-carousel">
                        <div class="carousel">
                            <div class="carousel-inner-class">
                                <?php
                                // print_r($widgets['deal'][0]['widget']['products']); die;
                                // Split the products array into two parts for display in two columns
                                $firstColumn = array_slice($widgets['deal'][0]['products'], 0, 4);
                                $secondColumn = array_slice($widgets['deal'][0]['products'], 4, 4);
                                ?>
                                <!-- First Column -->
                                <!-- <div class="deal-of-day-carts"> -->
                                <?php foreach ($firstColumn as $product): ?>
                                    <div class="card">
                                        <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $product->url_key]) ?>"></a>
                                        <div class="card-wishlist">
                                            <?php if ($product->whishlist): ?>
                                                <div class="remove-to-wishlist-btn"data-product-id="<?= $product->id ?>">
                                                    <span class="wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                                </div>
                                            <?php else: ?>
                                                <div class="add-to-wishlist-btn" data-product-id="<?= $product->id ?>">
                                                    <span class="wishlist-heart"><img src="/assets/heart-nobackground.png" class="wishlist"></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-body">
                                            <div class="card-image">
                                                <img src="<?= $product['product_image'] ?>" class="snd-crsl-img-1"/>
                                                <div class="hr-container">
                                                    <div class="hr" style="width: 50%;background: #FF3B3B;height: 100%;"></div>
                                                </div>
                                            </div>
                                            <?php
                                            $rating = $product['rating'];
                                            $fullStars = floor($rating);
                                            $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                            $emptyStars = 5 - $fullStars - $halfStar;
                                            $starsHtml = str_repeat('★', $fullStars); // Full stars
                                            if ($halfStar) {
                                                $starsHtml .= '★☆'; // Half star
                                            }
                                            $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                            ?>
                                            <div class="card-rating">
                                                <?= $starsHtml ?> 
                                                <span class="card-star"><?= number_format($rating, 1) ?></span>
                                            </div>
                                            <div class="card-name">
                                                <span class="product-name"><?= $product['name'] ?></span>
                                            </div>
                                            <div class="card-reference">
                                                <span class="reference-title">Reference:</span> <span class="reference-name"><?= $product['reference_name'] ?? "" ?></span>
                                            </div>
                                            <!-- <div class="custom-card-subname-1"><?= $product['reference_name'] ?? "" ?></div> -->
                                            
                                            <div class="card-price">
                                                <span>
                                                    <?= $this->Price->setPriceFormat($product['promotion_price']) ?>
                                                    <div class="card-offer">
                                                        <span class="strike-price"><?= $this->Price->setPriceFormat($product['sales_price']) ?></span>
                                                        <span class="price-offer"><span class="ax-sale-off"><?= $product['discount'] ?></span>% 0ff</span>
                                                    </div>
                                                </span>
                                                <button class="card-add-to-cart-button  add-to-cart"
                                                        data-product-id="<?= $product->id ?>">
                                                    <span class="card-cart-icon"><i class="fas fa-cart-arrow-down"></i></span>
                                                    <span class="card-add-to-cart">ADD TO CART</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                <!-- </div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- Featured Categories Slier -->
<div class="container">
    <div class="carousel-container">
        <?php if (isset($widgets['featured']) && is_array($widgets['featured']) && count($widgets['featured']) > 0): ?>
            <p class="carousel-head">
                <?php if (isset($widgets['featured'][0]['widget']['title']) && !empty($widgets['featured'][0]['widget']['title'])): ?>
                    <?= trim($widgets['featured'][0]['widget']['title']); ?>
                <?php else: ?>
                    <?= __('Featured Categories') ?>
                <?php endif; ?>
            </p>

            <div class="carousel-controls">
                <button id="featureCategoryCarousel-left" class="carousel-button left-btn">🡠</button>
                <button id="featureCategoryCarousel-right" class="carousel-button right-btn">🡢</button>
            </div>
            <div class="carousel-wrapper" id="featureCategoryCarousel">
                <div class="parent-carousel clone-custom-parent-carousel">
                    <div class="carousel">
                        <div class="carousel-inner-class">
                            <?php if (isset($widgets['featured'][0]['products'])): ?>
                                <?php foreach ($widgets['featured'][0]['products'] as $val): ?>
                                    <div class="card">
                                        <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val['url_key']]) ?>" class="card-anchor"></a>
                                        <div class="card-wishlist">
                                            <?php if ($val->whishlist): ?>
                                                <div class="remove-to-wishlist-btn"
                                                    data-product-id="<?= $val->id ?>"><span
                                                        class="wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                                </div>
                                            <?php else: ?>
                                                <div class="add-to-wishlist-btn"
                                                    data-product-id="<?= $val->id ?>"><span
                                                        class="wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                                                </div>
                                            <?php endif; ?>

                                        </div>
                                        <div class="card-body">
                                            <div class="card-image">
                                                <?php if (!empty($val['product_image'])): ?>
                                                        <img src="<?= $val['product_image'] ?>" class="snd-crsl-img" alt="Product Image"/>
                                                <?php else: ?>
                                                        <img src="<?= $this->Url->webroot('assets/no-image.png') ?>" class="snd-crsl-img" alt="Product Image"/>
                                                <?php endif; ?>
                                            </div>
                                            <?php
                                            $rating = $val['rating'];
                                            $fullStars = floor($rating);
                                            $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                            $emptyStars = 5 - $fullStars - $halfStar;
                                            $starsHtml = str_repeat('★', $fullStars); // Full stars
                                            if ($halfStar) {
                                                $starsHtml .= '★☆'; // Half star
                                            }
                                            $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                            ?>
                                            <div class="card-rating">
                                                <?= $starsHtml ?> 
                                                <span class="card-star"><?= number_format($rating, 1) ?></span>
                                            </div>
                                            <div class="card-name">
                                                <span class="product-name"><?= $val['name'] ?></span>
                                            </div>
                                            <div class="card-reference">
                                                <span class="reference-title"><?= __("Reference: ") ?></span> <span class="reference-name"><?= $val['reference_name'] ?? "" ?></span>
                                            </div>
                                                <!-- <div class="custom-card-subname"><?= $val['reference_name'] ?? "" ?></div> -->
                                            <div class="card-price">
                                                <span>
                                                    <?= $this->Price->setPriceFormat($val['promotion_price']) ?>
                                                    <div class="card-offer">
                                                        <span class="strike-price"><?= $this->Price->setPriceFormat($val['sales_price']) ?></span>
                                                        <span class="price-offer"><span class="ax-sale-off"><?= $val['discount'] ?></span>% 0ff</span>
                                                    </div>
                                                </span>
                                                <button class="card-add-to-cart-button add-to-cart"
                                                    data-product-id="<?= $val['id'] ?>">
                                                <span class="card-cart-icon"><i
                                                        class="fas fa-cart-arrow-down"></i></span>
                                                <span class="card-add-to-cart">ADD TO CART</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- Custom Widget -->
<div class="container">
    <div class="carousel-container">
        <?php if(isset($widgets['Custom'])): ?>
            <?php foreach ($widgets['Custom'] as $customwidget): ?>
                <p class="carousel-head"><?= trim(string: $customwidget['widget']['title']); ?></p>
                <div class="carousel-controls">
                    <button id="customWidgetCarousel-left" class="carousel-button left-btn">🡠</button>
                    <button id="customWidgetCarousel-right" class="carousel-button right-btn">🡢</button>
                </div>
                <div class="carousel-wrapper" id="customWidgetCarousel">
                    <div class="parent-carousel clone-custom-parent-carousel">
                        <div class="carousel">
                            <div class="carousel-inner-class">
                                <?php if (!empty($customwidget['products'])): ?>
                                    <?php foreach ($customwidget['products'] as $val): ?>
                                        <div class="card">
                                            <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val['url_key']]) ?>" class="card-anchor"></a>
                                            <div class="card-wishlist">
                                                <?php if ($val->whishlist): ?>
                                                    <div class="remove-to-wishlist-btn" data-product-id="<?= $val->id ?>">
                                                        <span class="wishlist-heart">
                                                            <img src="/assets/heart-background.png" class="wishlist">
                                                        </span>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="add-to-wishlist-btn" data-product-id="<?= $val->id ?>">
                                                        <span class="wishlist-heart">
                                                            <img src="/assets/heart-nobackground.png" class="wishlist">
                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="card-body">
                                                <div class="card-image">
                                                    <?php if (!empty($val['product_image'])): ?>
                                                            <img src="<?= $val['product_image'] ?>" class="snd-crsl-img" alt="Product Image"/>
                                                    <?php else: ?>
                                                            <img src="<?= $this->Url->webroot('assets/no-image.png') ?>" class="snd-crsl-img" alt="Product Image"/>
                                                    <?php endif; ?>
                                                </div>
                                                <?php
                                                    $rating = $val['rating'];
                                                    $fullStars = floor($rating);
                                                    $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                                    $emptyStars = 5 - $fullStars - $halfStar;
                                                    $starsHtml = str_repeat('★', $fullStars);
                                                    if ($halfStar) {
                                                        $starsHtml .= '★☆';
                                                    }
                                                    $starsHtml .= str_repeat('☆', $emptyStars);
                                                ?>

                                                <div class="card-rating">
                                                    <?= $starsHtml ?> 
                                                    <span class="card-star"><?= number_format($rating, 1) ?></span>
                                                </div>

                                                <div class="card-name">
                                                    <span class="product-name"><?= $val['name'] ?></span>
                                                </div>
                                                <div class="card-reference">
                                                    <span class="reference-title"><?= __("Reference: ") ?></span> <span class="reference-name"><?= $val['reference_name'] ?? "" ?></span>
                                                </div>

                                                <div class="card-price">
                                                    <span>
                                                        <?= $this->Price->setPriceFormat($val['promotion_price']) ?>
                                                        <div class="card-offer">
                                                            <span class="strike-price"><?= $this->Price->setPriceFormat($val['sales_price']) ?></span>
                                                            <span class="price-offer">
                                                                <span class="ax-sale-off"><?= $val['discount'] ?></span>% off
                                                            </span>
                                                        </div>
                                                    </span>
                                                    <button class="card-add-to-cart-button add-to-cart" data-product-id="<?= $val['id'] ?>">
                                                        <span class="card-cart-icon"><i class="fas fa-cart-arrow-down"></i></span>
                                                        <span class="card-add-to-cart">ADD TO CART</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div> 
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>
<!-- Our brands -->
<div class="container">
    <div class="carousel-container">
        <?php if (isset($BrandsList) && !empty($BrandsList)): ?>
            <p class="carousel-head">Our Brands</p>

            <div class="carousel-controls">
                <button id="brandCarousel-left" class="carousel-button left-btn">🡠</button>
                <button id="brandCarousel-right" class="carousel-button right-btn">🡢</button>
            </div>
            <div class="carousel-wrapper" id="brandCarousel">
                <div class="parent-carousel clone-custom-parent-carousel">
                    <div class="carousel">
                        <div class="carousel-inner-class brand-carousel">
                            <?php foreach ($BrandsList as $val): ?>
                                <a href="<?= '/website/product-brand-list/' . $val['url_key'] ?>" class="brand-list">
                                    <img src="<?= !empty($val['brand_logo']) ? $val['brand_logo'] : $this->Url->webroot('assets/no-image.png') ?>" class="brands" />
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- Recently viewed products -->
<div class="container">
    <div class="carousel-container">
        <?php if (isset($userId) && $userId !== null): ?>
            
            <p class="carousel-head">Recently Viewed Products</p>
            <div class="carousel-controls">
                <button id="recentlyViewedCarousel-left" class="carousel-button left-btn-s">🡠</button>
                <button id="recentlyViewedCarousel-right" class="carousel-button right-btn-s">🡢</button>
            </div>
            <div class="carousel-wrapper" id="recentlyViewedCarousel">
                <div class="parent-carousel clone-custom-parent-carousel">
                    <div class="carousel">
                        <div class="carousel-inner-class">
                            <?= $this->RecentViewProduct->renderSlider($userId) ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- Footer Banner -->
 <div class="container">
    <div class="footer-banner-wrapper">
        <?php if (isset($banner_ads_middle_above_footer) && !empty($banner_ads_middle_above_footer)): ?>
            <div class="ad-img">
                <?php foreach ($banner_ads_middle_above_footer as $val): ?>
                    <?php if ($val['ad_type'] == 'Above Footer'): ?>
                        <img src="<?= $val['web_image'] ?>" class="ad-img-1"/>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- Client says -->
<div class="container">
    <div class="carousel-container">
        <p class="carousel-head"><?= __("What Our Client Say About Us") ?></p>

        <div class="carousel-controls">
            <button id="clientFeedbackCarousel-left" class="carousel-button left-btn-s" onclick="slideLeftMyCarousel()">🡠</button>
            <button id="clientFeedbackCarousel-right" class="carousel-button right-btn-s" onclick="moveRightMyCarousel()">🡢</button>
        </div>
        <div class="carousel-wrapper" id="clientFeedbackCarousel">
            <div class="parent-carousel">
                <div class="carousel">
                    <div class="carousel-inner-class">
                        <?php foreach ($testimonials as $val): ?>
                            <div class="card">
                                <div class="cards-inner-text">
                                    <img src="<?= $this->Url->webroot('/assets/profile-icon.png') ?>" class="dp-img"/>
                                    <p class="dp-name"><?= $val['name'] ?>
                                        <span class="star-rating">
                                            <span class="star">&#9733;</span>
                                            <span class="rating">
                                                <?= strlen($val['rating']) > 30 ? substr($val['rating'], 0, 30) : $val['rating'] ?>
                                            </span>
                                        </span>
                                    </p>
                                    <p class="comment clone-comment">  <?= strlen($val['comment']) > 200 ? substr($val['comment'], 0, 200) : $val['comment'] ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Question Section -->
<div class="container">
    <div class="ques-section">
        <div class="ques-other-parent">
            <div class="ques">
                <?php foreach ($getFaqsList as $val): ?>
                    <div class="faq-item">
                        <p class="bab-ques-q"><?= $val['title'] ?> <span class="plus"><span class="symbol">+</span></span></p>
                        <div class="faq-content">
                            <p><?= $val['content'] ?></p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="other">
                <p class="bab-ques-w">How we can help you?</p>
                <p class="bab-ques-b">If you have any questions or queries, direct connect with us!
                    <a href="tel:<?= h($this->SiteSettings->getSettings()->customer_support_no) ?>">
                        <i class="fas fa-mobile-alt"></i> <?= h($this->SiteSettings->getSettings()->customer_support_no) ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
