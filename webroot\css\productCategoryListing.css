.productCategoryListingC{
    margin-bottom: 20px;
    background-color: rgba(249, 218, 185, .6);
    padding: 6px 15px;
}
.productCategoryListingC .productCategoryListing{
    display: flex;
    align-items: center;
}
.productCategoryListingC .productCategoryListing img{
    width: 20px;
}
.productCategoryListingC .productCategoryListing .productCategoryListing-home-span a{
    margin: 0 15px;
    font-size: 14px;
    font-weight: 500;
    color: #5f6c72;
    text-decoration: none;
}
.productCategoryListingC .productCategoryListing .Electronic-devices{
    margin-left: 15px;
    font-size: 14px;
    color: #8E4F0C;
    font-weight: 500; 
}
.prod-listing-body .productCategoryListing-aside-vertical{
    display: none;
}
.prod-listing-body .prod-listing-body-splitter .productCategoryListing-search-sort {
    display: none;
}
.prod-listing-body .prod-listing-body-splitter .productCategoryListing-active-filters{
    display: flex;
    align-items: center;
}
.prod-listing-body .prod-listing-body-splitter .productCategoryListing-active-filters .abacus-span{
    display: block;
    border: 1px solid #F7CEA1;
    border-radius: 10px;
    padding: 10px 10px 6px;
}
.prod-listing-body .prod-listing-body-splitter .productCategoryListing-active-filters .abacus-span > img{
    width: 40px;
}
.prod-listing-body .prod-listing-body-splitter .productCategoryListing-active-filters span.productCategoryListing-home-span {
    width: 36%;
    margin-left: 15px;
}
.prod-listing-body .prod-listing-body-splitter .productCategoryListing-custom-card-container2 {
    display: block;
    margin-top: 10px;
    margin-bottom: 15px;
    width: 100%;
    position: relative;
}
@media screen and (min-width:992px) {
    /* Breadcrumb style */
    .productCategoryListingC {
        margin-bottom: 20px;
        background-color: rgba(249, 218, 185, .6);
    }
    .productCategoryListingC .productCategoryListing {
        display: flex;
        align-items: center;
        padding:26px 0px;
    }
    .productCategoryListingC .productCategoryListing .productCategoryListing-home-icn {
        width: 18px;
        height: 18px;
    }
    .productCategoryListingC .productCategoryListing .productCategoryListing-home-span {
        padding: 0px 5px;
        color: #5f6c72;
    }
    .productCategoryListingC .productCategoryListing .productCategoryListing-home-span > a {
        font-size: 14px;
        font-weight: 500;
        color: #5f6c72;
        text-decoration: none;
    }
    .productCategoryListingC .productCategoryListing .Electronic-devices {
        font-size: 14px;
        color: #8E4F0C;
        font-weight: 500;
    }
    /* card list page */
    .prod-listing-body {
        display: flex;
    }

    .prod-listing-body .productCategoryListing-aside-vertical {
        width: 29%;
        display: block;
    }
    .prod-listing-body .productCategoryListing-aside-div {
        box-shadow: 0px 0px 34.9px 0px #f9dab9;
        background-color: white;
        border-radius: 10px;
        margin-left: 0px;
        height: max-content;
        padding: 30px 15px;
    }    
    .prod-listing-body .productCategoryListing-aside-div-header {
        margin-left: 0cap;
        margin-top: 24px;
        margin-bottom: 6px;
        font-size: 18px;
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item {
        position: relative;
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item label {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 5px;
        border-radius: 5px;
        transition: all 0.3s;
        gap: 10px;
        font-size: 18px;
        width: 100%;
        position: relative;
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item label:before {
        content: '';
        width: 17px;
        height: 17px;
        border-radius: 50%;
        margin-left: 0;
        box-sizing: border-box;
        margin-right: 3px;
        font-size: 17px;
        border: 1px solid gray;
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item label span.product-name{
        display: inline-block;
        padding-right: 5px;
        font-size: 16px;
        font-weight: 400;
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item label .toggle-icon {
        font-size: 14px;
        color: black;
        transition: transform 0.3s;
        position: absolute;
        right: 2px;
        top: 8px;
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item input[type="checkbox"]:checked + label .toggle-icon {
        transform: rotate(180deg); /* Flip the icon */
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item .subcategory {
        margin-top: 5px;
        display: none;
        font-size: 14px;
        list-style: none;
        padding: 0;
        padding-left: 30px;
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item .subcategory .subcategory-item {
        margin: 5px 0;
        padding: 5px 10px;
        cursor: pointer;
    }
    .prod-listing-body .productCategoryListing-aside-div .category-section .category-item input[type="checkbox"]:checked ~ .subcategory {
        display: block;
    }
    .prod-listing-body .productCategoryListing-aside-div #productCategoryListing-hr-radio {
        width: 100%;
        margin-top: 20px;
        margin-left: 0px;
    }
    .prod-listing-body .productCategoryListing-aside-div .range_container {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin: 26px 0;
    }
    .prod-listing-body .productCategoryListing-aside-div .range_container .form_control_container {
        margin-right: 10px;
        width: 50%;
    }
    .prod-listing-body .productCategoryListing-aside-div .range_container .form_control_container .form_control_container__time{
        font-size: 16px;
        color: #77878F;
        font-weight: 500;
    }
    .prod-listing-body .productCategoryListing-aside-div .range_container .form_control_container input.form_control_container__time__input {
        border-radius: 3px;
        pointer-events: none;
        box-sizing: border-box;
        width: 100%;
        height: 40px;
        border: 1px solid #E4E7E9;
        padding: 0 12px;
    }
    .prod-listing-body .productCategoryListing-aside-div .productCategoryListing-brands-check-div {
        margin-left: 0;
    }
    .prod-listing-body .productCategoryListing-aside-div .productCategoryListing-brands-check-div .productCategoryListing-brands-check-label label {
        font-size: 16px;
        font-weight: 400;
        color: #475156;
    }
    .prod-listing-body .productCategoryListing-add a > .productCategoryListing-add-img {
        box-shadow: 0px 0px 34.9px 0px #f9dab9;
        width: 100%;
        border-radius: 10px;
        margin-left: 0px;
        max-height: 590px;
        margin-top: 20px;
    }
    .prod-listing-body .prod-listing-body-splitter {
        margin-left: 15px;
        width: 70%;
        padding-right: 5px;
    }
    .prod-listing-body .prod-listing-body-splitter .productCategoryListing-search-sort {
        display: flex;
        width: 100%;
        justify-content: space-between;
        margin-bottom: 15px;
    }
    .prod-listing-body .prod-listing-body-splitter .productCategoryListing-active-filters {
        width: 100%;
        padding: 12px 24px;
        border-radius: 10px;
        background: #f9dab999;
        font-weight: 500;
        display: flex;
    }
    .prod-listing-body .prod-listing-body-splitter .productCategoryListing-search-sort input.productCategoryListing-search {
        border: 1px solid #ff901e;
        width: 200px;
        font-size: 18px;
        height: 44px;
        border-radius: 10px;
        margin-top: -1px;
        outline: 0;
    }
    .prod-listing-body .prod-listing-body-splitter .productCategoryListing-search-sort .product-sort-option .productCategoryListing-sort {
        width: 180px;
        border-radius: 8px;
        padding: 8px 16px;
        border: 1px solid #f9dab9;
        background-color: white;
        color: grey;
        font-size: 18px;
        height: 44px;
    }
    .prod-listing-body .prod-listing-body-splitter .productCategoryListing-active-filters span.productCategoryListing-home-span {
        width: 15%;
    } 
    .prod-listing-body .prod-listing-body-splitter .productCategoryListing-active-filters .abacus-span{
        display: none;
    }
}

.p-v-p-item-description-image-description {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
        Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
        sans-serifs;
}

.p-v-p-item-description-image-description-avail-offer-field {
    background-color: transparent !important;
    display: flex;
}

.clonep-v-p-item-description-image-description-avail-offer-field-d {
    height: 40px !important;
}

.clone-p-v-p-item-description-image-description-avail-offer-field-q {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #ff8037;
    margin: 0px 10px 0px 0px;
}

.clone-p-v-p-item-description-image-description-avail-offer-field-w {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #8b8b8b;
    margin: 0px 10px 0px 0px;
}

.clone-p-v-p-item-description-image-description-avail-offer-field-e {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #8204ff;
    margin: 0px 10px 0px 0px;
}

.clone-p-v-p-item-description-image-description-avail-offer-field-r {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #133568;
    margin: 0px 10px 0px 0px;
}

.width {
    height: 25px !important;
    width: 55px !important;
    border-radius: 10px !important;
    font-weight: 500 !important;
    font-size: medium !important;
    color: white;
    text-align: center;
}

#clone-prod-view-page-my-parent-carousel-s-container {
    display: block !important;
}

.clone-p-v-p-r-r {
    width: 1145px !important;
    overflow: hidden !important;
    margin-left: 63px !important;
    padding-left: 30px !important;
}
.prod-view-page-my-parent-carousel-s {
    /* margin-left: 79px !important; */
    margin-left: 35px !important;
}

.login-button {
    color: black;
}
.productCategoryListing-active-filters .active-filter-container{
    width: 89%;
    flex-wrap: wrap;
    display: flex;
}





.products_category {
    width: 100%;
    box-shadow: 0px 0px 34.9px 0px #f9dab9;
    background-color: white;
    border-radius: 10px;
    height: max-content;
    position: sticky;
    top: 10px;
}
.productFilters_section {
    display: flex;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
        Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
        sans-serifs;
    max-width: 1190px;
    margin: 0 auto;
    justify-content: space-between;
    margin-bottom: 60px;
}



.productCategoryListing-aside-div-header {
    margin-left: 30px;
    width: 280px;
    margin-bottom:6px ;
    font-size: 18px;
}

.productFilters_section .prod-listing-body-splitter {
    width: 100%;
}

.productFilters_section .productCategoryListing-active-filters {
    width: 100%;
    padding: unset;
}

.selected_filter {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.selected_filter .results_found {
    margin: 0px;
    color: #8e4f0c;
    font-weight: 500;
}
.selected_filter .results_found span {
    color: #5f6c72;
    font-size: 14px;
    font-weight: 400;
}

#productCategoryListing-search-icon {
    position: relative;
    right: 35px;
    width: 18px;
    top: 5px;
}

.productCategoryListing-search {
    width: 145px;
    border-radius: 8px;
    padding: 12px 16px;
    border: 1px solid #f9dab9;
}

.productCategoryListing-sort {
    /* width: 109px; */
    width: 180px;
    border-radius: 8px;
    padding: 12px 16px;
    border: 1px solid #f9dab9;
    background-color: white;
    color: grey;
    font-size: 18px;
    height: 100%;
}



.productCategoryListing-active-filter-tag {
    border-radius: 15px;
    background-color: white;
    font-weight: 600;
    padding: 3px 15px;
    margin-right: 15px;
    margin-bottom: 5px;
}

.productCategoryListing-active-filter-tag-x {
    color: gray;
    padding-left: 8px;
    font-weight: 400;
    cursor: pointer;
}

.custom-box {
    background-color: #06c270 !important;
}

.clone-heart-icon {
    font-size: 35px;
    z-index: 9999;
    position: absolute;
    color: red;
    margin: 5px 0px 0px 170px;
}

.productCategoryListing-custom-card-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    margin-bottom: 15px;
    width: 835px;
    /* justify-content: space-between; */
    justify-content: flex-start;
    position: relative;
}

.custom-card-div {
    margin-bottom: 20px;
}

.productCategoryListing-aside-div-form {
    display: flex;
    flex-direction: column;
}

.productCategoryListing-aside-radio-container {
    padding: 6px;
    margin-left: 15px;
}

.productCategoryListing-rating-stars-div {
    margin-left: 30px;
    margin-top: -20px;
    cursor: pointer;
}

.productCategoryListing-rating-star {
    font-size: 40px;
}

.star-one {
    color: #06c270;
}

.star-two {
    color: #fdf3e8;
}

#productCategoryListing-radio-btn {
    accent-color: #ee902c;
}

.max-min-div {
    display: flex;
    justify-content: space-around;
    width: 300px;
    margin-left: 21px;
}

.min-price-input {
    border-radius: 8px;
    padding: 12px 16px;
    border: 1px solid #f9dab9;
    width: 100px;
}

.max-price-input {
    border-radius: 8px;
    padding: 12px 16px;
    border: 1px solid #f9dab9;
    width: 100px;
}

.productCategoryListing-brands-check-double {
    display: flex;
}

.productCategoryListing-brands-check-label {
    padding: 5px;
    /* width: 150px; */
    width: 161px;
}

.productCategoryListing-brands-check {
    accent-color: #ee902c;
}

/* For 1020px */
/* @media (max-width: 1024px) {
    .productCategoryListing-aside-div {
        margin-left: 170px;
        width: 270px;
    }

    .max-min-div {
        width: 191px;
    }

    .min-price-input,
    .max-price-input {
        width: 50px;
    }

    #productCategoryListing-hr-radio {
        width: 210px;
    }

    .productCategoryListing-add-img {
        width: 269px;
        margin-left: 170px;
    }
    .productCategoryListing-active-filters {
        width: 665px;
        padding: 12px 24px;
        border-radius: 10px;
        background: #f9dab999;
        font-weight: 500;
    }

    .productCategoryListing-custom-card-container {
        width: 705px;
    }

    .productCategoryListing-aside-vertical {
        margin-left: -167px;
    }

    .productCategoryListing {
        margin-left: 35px;
    }

    .custom-card,
    .custom-card-div,
    .custom-card-class,
    .custom-card-recent {
        height: 385px;
    }
    .custom-card,
    .custom-card-div,
    .custom-card-class,
    .custom-card-recent {
        width: 204px;
    }
    .abacus-span,
    .abacus {
        display: none;
    }
}

/* For 768px */
/*@media (max-width: 768px) {
    .productCategoryListing-aside-vertical {
        display: none;
    }
    .prod-listing-body{
        width: 100%;
        justify-content: flex-start;
        font-size: 14px;
    }
    .prod-listing-body-splitter{
        width: 100%;
        margin-left: 15px;
        margin-right: 15px;
    }
    .productCategoryListing-active-filters {
        font-size: 30px;
        display: flex;
        align-items: center;
        width: 100%;
        padding: 12px 0px;
        /* font-size: 20px; */
       /* font-size: 15px;
    }
    .productCategoryListing-active-filters .active-filter-container {
        width: 89%;
        flex-wrap: wrap;
        display: flex;
        align-items: center;
        padding-left: 15px;
    }
    .productCategoryListing-custom-card-container2 {
        width: 100%;
    }
    .productCategoryListingC {
        width: 100%;
    }
    .product-sort-option {
        height: 35px;
        display: flex;
        align-items: center;
    }
    .productCategoryListing-sort {
        /* width: 109px; */
      /*  width: 160px;
        padding: 5px 8px;
        font-size: 14px;
        height: 45px;
    }
    .productCategoryListing-search-sort {
        display: flex;
        width: 100%;
        justify-content: space-between;
        margin-bottom: 15px;
    }
    .productCategoryListing-search-sort > div:first-child{
        width: 41%;
    }
    .productCategoryListing-search-sort > div input.productCategoryListing-search {
        width: 100%;
        font-size: 14px;
        height: 45px;
        box-sizing: border-box;
    }
    .p-v-p-item-description-image-description-title-container {
        width: 242px;
        display: flex;
        flex-direction: column;
        margin-left: -1px;
    }

    .p-v-p-item-description-image-container {
        width: 376px;
        height: 397px;
    }

    /* .prod-listing-body {
        width: 940px;
    } */

    /* .prod-listing-body-splitter {
        margin-left: -158px;
        width: 707px;
    } */

    /* .productCategoryListing-active-filters {
        width: 880px;
    } */
   /* .productCategoryListing-active-filters span.productCategoryListing-home-span {
        width: 30%;
    }
    .productCategoryListing-custom-card-container {
        width: 930px;
    }

    .abacus-span,
    .abacus {
        display: none;
    }
}
@media (max-width: 509px) {
    .productCategoryListing-custom-card-container2 {
        justify-content: center;
        width: 100%;
    }
    .productCategoryListing-custom-card-container2 .custom-card-div{
        width: 100%;
        height: 437px;
    }
    .snd-crsl-img {
        width: 100%;
        height: 200px;
    }
    .productCategoryListing-active-filters span.productCategoryListing-home-span {
        margin-left: 15px;
    }
}
/* For 435px */
/*@media (max-width: 426px) {
    
    .productCategoryListing-search-sort {
        display: none;
    }

    .productCategoryListingC {
        margin-top: 145px;
    }

    .productCategoryListingC {
        width: 100%px;
    }

    .productCategoryListing {
        font-size: 24px;
        margin-left: 90px;
    }

    .productCategoryListing-home-icn {
        height: 20px;
        width: 20px;
    }

    .productCategoryListing-active-filters {
        font-size: 15px;
        display: block;
        padding-left: 10px;
    }

    .custom-card,
    .custom-card-div,
    .custom-card-class,
    .custom-card-recent {
        width: 430px;
        height: 550px;
    }

    .custom-card-subname {
        margin-top: 17px;
        font-size: 16px;
    }

    .strikethrough-text {
        position: relative;
        /* top: -37px; */
      /*  top: 0px;
        font-size: 16px;
    }

    .custom-card-add-to-cart-button {
        position: relative;
        /* left: 180px; */
      /*  left: 0;
        /* font-size: 38px;
        height: 55px;
        width: 75px; */
      /*  font-size: 15px;
        height: 36px;
        width: 136px;
        justify-content: space-between;
    }
    .abacus-span,
    .abacus {
        display: block;
        width: 50px;
        height: auto;
    }
    .abacus-span {
        background-color: white;
        border: 1px solid orange;
        border-radius: 10px;
        padding: 15px;
        margin-right: 15px;
        display: none;
    }
    .snd-crsl-img {
        width: 100%;
        height: 200px;
    }
    .custom-box {
        margin-bottom: 0px;
        margin-left: 12px;
        position: relative !important;
        top: 14px;
    }
} */
.product-filter {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}
.product-filter label {
    margin-right: 10px;
    font-weight: 600;
    font-size: 18px;
}
.product-filter .productCategoryListing-search {
    width: 270px;
}
/* For 320px */
/* @media (max-width: 321px) {
    .bread-crump-c {
        position: relative;
        left: -115px;
    }
} */
