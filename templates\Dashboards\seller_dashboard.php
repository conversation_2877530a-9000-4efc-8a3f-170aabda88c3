<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
<style>
    .pending_actions {
        min-height: max-content;
    }
    .content {
        max-height: 250px;
        overflow-y: scroll;
    }
    .low-stock-alert {
        animation: blink 1s infinite;
        color: #dc3545;
    }
    @keyframes blink {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>
<?php $this->end(); ?>

<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Seller Dashboard') ?></h4>
            </li>
        </ul>
        <div>
            <!-- <a href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'orderReport']) ?>" class="btn btn-primary">
                <i class="fas fa-download"></i> <?= __('Download Reports') ?>
            </a> -->
        </div>
    </div>

    <div class="section-body1">
        <div class="container-fluid">
            <?= $this->Flash->render() ?>
        </div>
    </div>

    <!-- Time Period Selector -->
    <div class="row mt-5 pe-3 date_picker">
        <div class="col-sm-3 m-r-50">
        </div>
        <div class="col-sm-7 text-end">
            <div class="row align-items-center mb-2">
                <div class="col-sm-5">
                    <select id="date-period" class="form-select m-l-100" onchange="handleChange(this)">
                        <option value="last_24_hours"><?= __('Last 24 Hours') ?></option>
                        <option value="last_7_days" selected><?= __('Last 7 Days') ?></option>
                        <option value="last_30_days"><?= __('Last 30 Days') ?></option>
                        <option value="current_month"><?= __('Current Month') ?></option>
                        <option value="custom"><?= __('Custom') ?></option>
                    </select>
                </div>
            </div>
            <div class="row align-items-center mb-2 d-none" id="datesappear">
                <form id="dateRangeForm" class="d-flex">
                    <div class="col-sm-3">
                        <label for="from-date" class="col-form-label fw-400 m-r-10"><?= __('From Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="from-date" name="from-date" />
                    </div>
                    <div class="col-sm-3">
                        <label for="to-date" class="col-form-label fw-400 m-r-10"><?= __('To Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="to-date" name="to-date" />
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-primary btn-sm" type="submit"><?= __('Submit') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="section-body mt-4">
        <div class="container-fluid">
            <!-- Sales Overview Cards -->
            <div class="row">
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style1 dashboard_box">
                        <div class="card-statistic-3 bg1">
                            <div class="card-icon card-icon-large"><i class="fa fa-shopping-cart"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Total Orders') ?></h4>
                                <span id="totalOrders"><?= h($orderStats['total_orders'] ?? 0) ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" role="progressbar" data-width="100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <?= __('Today: ') ?><?= h($ordersToday ?? 0) ?></span>
                                    <span class="text-nowrap"><?= __('This Week: ') ?><?= h($ordersThisWeek ?? 0) ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style2 dashboard_box">
                        <div class="card-statistic-3 bg2">
                            <div class="card-icon card-icon-large"><i class="fa fa-money-bill-alt"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Net Revenue') ?></h4>
                                <span id="netRevenue"><?php $revenue = $orderStats['total_revenue'] ?? 0; echo h(number_format((float)$revenue, 0, '', $thousandSeparator) . ' ' . h($currencySymbol)); ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" role="progressbar" data-width="100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <?= __('Avg Order: ') ?><?php $avgOrder = $orderStats['avg_order_value'] ?? 0; echo h(number_format((float)$avgOrder, 0, '', $thousandSeparator) . ' ' . h($currencySymbol)); ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style3 dashboard_box">
                        <div class="card-statistic-3 bg3">
                            <div class="card-icon card-icon-large"><i class="fa fa-box"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Pending Orders') ?></h4>
                                <span id="pendingOrders"><?= h($orderStats['pending_orders'] ?? 0) ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" role="progressbar" data-width="100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <?= __('Delivered: ') ?><?= h($orderStats['delivered_orders'] ?? 0) ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style4 dashboard_box">
                        <div class="card-statistic-3 bg4">
                            <div class="card-icon card-icon-large"><i class="fa fa-exclamation-triangle low-stock-alert"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Low Stock Alert') ?></h4>
                                <span id="lowStockCount"><?= h(count($lowStockProducts ?? [])) ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" role="progressbar" data-width="100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <?= __('Products') ?></span>
                                    <span class="text-nowrap"><?= __('Need attention') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue and Settlement Overview -->
            <div class="row mt-4">
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style1 dashboard_box">
                        <div class="card-statistic-3 bg1">
                            <div class="card-icon card-icon-large"><i class="fa fa-clock"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Pending Settlements') ?></h4>
                                <span id="pendingSettlements"><?php $pending = $settlementStats['pending_settlements'] ?? 0; echo h(number_format((float)$pending, 0, '', $thousandSeparator)); ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" role="progressbar" data-width="100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <?= __('Awaiting') ?></span>
                                    <span class="text-nowrap"><?= __('Processing') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style2 dashboard_box">
                        <div class="card-statistic-3 bg2">
                            <div class="card-icon card-icon-large"><i class="fa fa-check-circle"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Completed Settlements') ?></h4>
                                <span id="completedSettlements"><?php $completed = $settlementStats['completed_settlements'] ?? 0; echo h(number_format((float)$completed, 0, '', $thousandSeparator)); ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" role="progressbar" data-width="100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <?= __('Successfully') ?></span>
                                    <span class="text-nowrap"><?= __('Processed') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style3 dashboard_box">
                        <div class="card-statistic-3 bg3">
                            <div class="card-icon card-icon-large"><i class="fa fa-wallet"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Net Payout') ?></h4>
                                <span id="netPayout"><?php $payout = $settlementStats['total_payout'] ?? 0; echo h(number_format((float)$payout, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol); ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" role="progressbar" data-width="100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <?= __('Total') ?></span>
                                    <span class="text-nowrap"><?= __('Received') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style4 dashboard_box">
                        <div class="card-statistic-3 bg4">
                            <div class="card-icon card-icon-large"><i class="fa fa-chart-line"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Gross Sales') ?></h4>
                                <span id="grossSales"><?php $gross = $settlementStats['total_amount'] ?? 0; echo h(number_format((float)$gross, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol); ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" role="progressbar" data-width="100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <?= __('To Settle') ?></span>
                                    <span class="text-nowrap"><?= __('Pending') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mt-4">
                <div class="col-md-12 col-lg-6 col-xl-6">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Revenue Trends') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="revenue_chart"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-6">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Settlement Trends') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="settlement_chart"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Performance and Recent Orders -->
            <div class="row mt-4">
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Top 5 Selling Products') ?></h4>
                        </div>
                        <div class="card-body top_products" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border" style="position: relative; max-height: 320px; overflow-y: scroll;">
                                <?php if (!empty($topSellingProducts)): ?>
                                    <?php foreach ($topSellingProducts as $product): ?>
                                        <li class="product-list">
                                            <?php
                                            $imagePath = !empty($product->product_images) ? $product->product_images[0]->image : 'default-product-image.png';
                                            ?>
                                            <img class="msr-3 rounded" width="50" src="<?= $imagePath ?>" alt="product">
                                            <div class="set-flex">
                                                <div class="float-end">
                                                    <div class="font-weight-600 text-muted text-small"><?= h($product->total_units_sold ?? 0) ?> Sales</div>
                                                </div>
                                                <div class="fw-bold font-15"><?= h($product->name) ?></div>
                                                <div class="mt-1">
                                                    <div class="budget-price">
                                                        <div class="budget-price-square" style="width: <?= min(100, ($product->total_sales_amount ?? 0) / 1000) ?>%;"></div>
                                                        <div class="budget-price-label"><?php $salesAmount = $product->total_sales_amount ?? 0; echo h(number_format((float)$salesAmount, 0, '', $thousandSeparator) . ' ' . h($currencySymbol)); ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="product-list">
                                        <div class="text-center text-muted"><?= __('No products sold yet') ?></div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Recent Orders') ?></h4>
                        </div>
                        <div class="card-body product_categories" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border" style="position: relative; max-height: 320px; overflow-y: scroll;">
                                <?php if (!empty($recentOrders)): ?>
                                    <?php foreach ($recentOrders as $order): ?>
                                        <li class="product-list">
                                            <div class="set-flex">
                                                <div class="fw-bold font-15">#<?= h($order->order_number) ?></div>
                                                <div class="mt-1">
                                                    <div class="budget-price">
                                                        <div class="budget-price-square bg-<?= $order->status === 'delivered' ? 'success' : ($order->status === 'pending' ? 'warning' : 'info') ?>" style="width: 100%;"></div>
                                                        <div class="budget-price-label"><?= h(ucfirst($order->status)) ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-muted text-small">
                                                <?= h($order->created->format('M d, Y H:i')) ?> - 
                                                <?= h(number_format((float)$order->merchant_total, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="product-list">
                                        <div class="text-center text-muted"><?= __('No orders yet') ?></div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Low Performing Products and Low Stock Alerts -->
            <div class="row mt-4">
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Low Performing Products') ?></h4>
                        </div>
                        <div class="card-body product_categories" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border" style="position: relative; max-height: 260px; overflow-y: scroll;">
                                <?php if (!empty($lowPerformingProducts)): ?>
                                    <?php foreach ($lowPerformingProducts as $product): ?>
                                        <li class="product-list">
                                            <div class="set-flex">
                                                <div class="fw-bold font-15"><?= h($product->name) ?></div>
                                                <div class="mt-1">
                                                    <div class="budget-price">
                                                        <div class="budget-price-square bg-warning" style="width: 30%;"></div>
                                                        <div class="budget-price-label"><?= __('Low Sales') ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="product-list">
                                        <div class="text-center text-muted"><?= __('All products performing well') ?></div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Low Stock Alerts') ?></h4>
                        </div>
                        <div class="card-body product_categories" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border" style="position: relative; max-height: 260px; overflow-y: scroll;">
                                <?php if (!empty($lowStockProducts)): ?>
                                    <?php foreach ($lowStockProducts as $product): ?>
                                        <li class="product-list">
                                            <div class="set-flex">
                                                <div class="fw-bold font-15"><?= h($product->name) ?></div>
                                                <div class="mt-1">
                                                    <div class="budget-price">
                                                        <div class="budget-price-square bg-danger" style="width: 100%;"></div>
                                                        <div class="budget-price-label"><?= __('Low Stock') ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="product-list">
                                        <div class="text-center text-muted"><?= __('All products well stocked') ?></div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settlement Details -->
            <div class="row m-t-10">
                <div class="col-12 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Pending Settlements') ?></h4>
                        </div>
                        <div class="card-body" style="max-height:260px; overflow-y:auto">
                            <ul class="list-unstyled list-unstyled-border">
                                <?php if (!empty($pendingSettlements)): ?>
                                    <?php foreach ($pendingSettlements as $st): ?>
                                        <li class="product-list d-flex justify-content-between">
                                            <div>#<?= h($st->id) ?> — <?= h($st->payment_method ?? 'N/A') ?></div>
                                            <div><?= h(number_format((float)$st->final_payout, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="product-list">
                                        <div class="text-center text-muted"><?= __('No pending settlements') ?></div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4><?= __('Settled Recently') ?></h4>
                        </div>
                        <div class="card-body" style="max-height:260px; overflow-y:auto">
                            <ul class="list-unstyled list-unstyled-border">
                                <?php if (!empty($completedSettlements)): ?>
                                    <?php foreach ($completedSettlements as $st): ?>
                                        <li class="product-list d-flex justify-content-between">
                                            <div>#<?= h($st->id) ?> — <?= h($st->payment_method ?? 'N/A') ?></div>
                                            <div><?= h(number_format((float)$st->final_payout, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></div>
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="product-list">
                                        <div class="text-center text-muted"><?= __('No completed settlements') ?></div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script>
    $(function () {
        chartSettlements();
        chartRevenue();
        handleDatePeriodChange();
    });

    function handleChange(select) {
        if (select.value === 'custom') {
            $('#datesappear').removeClass('d-none');
        } else {
            $('#datesappear').addClass('d-none');
            // Here you would typically reload the dashboard with new date range
            // For now, we'll just show a message
            console.log('Selected period:', select.value);
        }
    }

    function handleDatePeriodChange() {
        $('#date-period').on('change', function() {
            const period = $(this).val();
            if (period === 'custom') {
                $('#datesappear').removeClass('d-none');
            } else {
                $('#datesappear').addClass('d-none');
                // Reload dashboard data for selected period
                reloadDashboardData(period);
            }
        });
    }

    function reloadDashboardData(period) {
        // This function would make an AJAX call to reload dashboard data
        // based on the selected time period
        console.log('Reloading dashboard data for period:', period);
    }

    function chartSettlements() {
        var options = {
            chart: { height: 300, type: "bar" },
            series: [{ name: "Payout (k)", data: <?= json_encode($settlementAmounts); ?> }],
            colors: ["#0d839b"],
            xaxis: { categories: <?= json_encode($settlementMonths); ?> },
            yaxis: { title: { text: '<?= __('Payout (thousands)') ?>' } },
            dataLabels: { enabled: false }
        };
        new ApexCharts(document.querySelector("#settlement_chart"), options).render();
    }

    function chartRevenue() {
        var options = {
            chart: { height: 300, type: "line" },
            series: [{ name: "Revenue (k)", data: <?= json_encode($revenueData ?? []); ?> }],
            colors: ["#28a745"],
            xaxis: { categories: <?= json_encode($revenueMonths ?? []); ?> },
            yaxis: { title: { text: '<?= __('Revenue (thousands)') ?>' } },
            dataLabels: { enabled: false },
            stroke: { curve: 'smooth' }
        };
        new ApexCharts(document.querySelector("#revenue_chart"), options).render();
    }
</script>
<?php $this->end(); ?>
