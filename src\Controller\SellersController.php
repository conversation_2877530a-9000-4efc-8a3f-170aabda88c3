<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use App\Utility\DatabaseUtility;
use Cake\Datasource\ConnectionManager;
use Cake\Utility\Security;

/**
 * Sellers Controller
 *
 * @property \App\Model\Table\MerchantsTable $Merchants
 */
class SellersController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Flash');
        $this->loadComponent('Global');
        $this->Merchants = $this->fetchTable('Merchants');
        $this->Users = $this->fetchTable('Users');
        $this->Roles = $this->fetchTable('Roles');
        $this->States = $this->fetchTable('States');
        $this->Cities = $this->fetchTable('Cities');
        $this->Categories = $this->fetchTable('Categories');
    }

    public function index()
    {
        $query = $this->Merchants->find();
        $query->where(['status' => 'A']);
        $merchants = $this->paginate($query);
        $categoryList = $this->Merchants->getCategories();
        foreach ($merchants as $merchant) {
            if (isset($categoryList[$merchant->business_type])) {
                $merchant->business_type = $categoryList[$merchant->business_type];
                $merchant->phone_number = "{$merchant->country_code}-{$merchant->phone_number}";
            }
        }
        $categories = $this->Merchants->getCategories();
        $this->set(compact('merchants', 'categories'));
    }

    public function ajaxIndex()
    {
        $this->request->allowMethod(['get']);
        $this->viewBuilder()->setLayout('ajax');
        $query = $this->Merchants->find();
        $query->where(['Merchants.status' => 'A']);
  
        $search = $this->request->getQuery('search');
        $filters = $this->request->getQuery();

        // Join Users table to allow searching by user email
        $query->contain(['Users']);

        $orConditions = [
            'Merchants.company_name LIKE' => "%$search%",
            'Merchants.merchant_identifier LIKE' => "%$search%",
            'Users.email LIKE' => "%$search%",
        ];
        $query->where(['OR' => $orConditions]);

        DatabaseUtility::valueFilter($query, 'Merchants', 'approval_status', $filters, 'status');
        DatabaseUtility::valueFilter($query, 'Merchants', 'business_type', $filters, 'category');
        DatabaseUtility::likeFilter($query, 'Merchants', 'address_line1', $filters, 'location');
        DatabaseUtility::likeFilter($query, 'Merchants', 'address_line2', $filters, 'location');
        DatabaseUtility::likeFilter($query, 'Merchants', 'country', $filters, 'location');
        DatabaseUtility::dateEquals($query, 'Merchants', 'created', $filters, 'date');

        $merchants = $this->paginate($query);
        $categoryList = $this->Merchants->getCategories();
        foreach ($merchants as $merchant) {
            if (isset($categoryList[$merchant->business_type])) {
                $merchant->business_type = $categoryList[$merchant->business_type];
                $merchant->phone_number = $merchant->country_code . $merchant->phone_number;
            }
        }
        $this->set(compact('merchants'));
        $this->render('ajax_index');
    }

    public function uploadDocument($merchantId)
    {
        $this->request->allowMethod(['post']);
        $merchant = $this->Merchants->find()->where(['id' => $merchantId, 'status' => 'A'])->firstOrFail();
        $MerchantDocuments = $this->fetchTable('MerchantDocuments');

        $data = $this->request->getData();
        $file = $data['document_file'] ?? null;
        $documentType = $data['document_type'] ?? '';
        $notes = $data['notes'] ?? null;

        if (empty($documentType)) {
            $this->Flash->error(__('Please select a document type.'));
            return $this->redirect(['action' => 'view', $merchantId]);
        }

        $result = $MerchantDocuments->saveDocument($merchantId, $documentType, $file, $notes);
        
        if ($result === true) {
            $this->Flash->success(__('Document uploaded successfully.'));
        } else {
            $this->Flash->error(__($result));
        }
        
        return $this->redirect(['action' => 'view', $merchantId]);
    }

    public function add()
    {
        $merchant = $this->Merchants->newEmptyEntity();
        
        $categories = $this->Merchants->getCategories();
        $states = $this->States->find('list', ['keyField' => 'id', 'valueField' => 'state_name'])->toArray();
        $cities = $this->Cities->find('list', ['keyField' => 'id', 'valueField' => 'city_name'])->toArray();
        $businessCategories = $this->Categories->find('list', ['keyField' => 'id', 'valueField' => 'name'])->toArray();

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            
            // Handle file uploads
            $merchantLogo = $this->request->getData('merchant_logo');
            $banner = $this->request->getData('banner');
            
            // Validate required file uploads
            if (!$merchantLogo || $merchantLogo->getError() !== UPLOAD_ERR_OK) {
                $this->Flash->error(__('Please upload a merchant logo.'));
                $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                return;
            }
            
            if (!$banner || $banner->getError() !== UPLOAD_ERR_OK) {
                $this->Flash->error(__('Please upload a banner image.'));
                $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                return;
            }
            
            // Validate merchant logo resolution and size
            $logoValidationErrors = $this->validateImageResolution($merchantLogo, 'logo');
            if (!empty($logoValidationErrors)) {
                $this->Flash->error(__('Logo validation failed: ' . implode('; ', $logoValidationErrors)));
                $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                return;
            }
            
            // Upload merchant logo
            $logoFileName = $this->uploadFile($merchantLogo, 'merchant_logos');
            if (!$logoFileName) {
                $this->Flash->error(__('Failed to upload merchant logo. Please try again.'));
                $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                return;
            }
            $data['merchant_logo'] = $logoFileName;
            
            // Validate banner resolution and size
            $bannerValidationErrors = $this->validateImageResolution($banner, 'banner');
            if (!empty($bannerValidationErrors)) {
                $this->Flash->error(__('Banner validation failed: ' . implode('; ', $bannerValidationErrors)));
                $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                return;
            }
            
            // Upload banner
            $bannerFileName = $this->uploadFile($banner, 'merchant_banners');
            if (!$bannerFileName) {
                $this->Flash->error(__('Failed to upload banner. Please try again.'));
                $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                return;
            }
            $data['banner'] = $bannerFileName;
            
            // Validate seller documents if any are uploaded
            if (!empty($data['seller_document'])) {
                $documentValidationErrors = $this->validateSellerDocuments($data['seller_document']);
                if (!empty($documentValidationErrors)) {
                    $this->Flash->error(__('Document validation failed: ' . implode('; ', $documentValidationErrors)));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                    return;
                }
            }
            
            // Handle phone number and country code
            if (!empty($data['mobile_full'])) {
                $fullPhoneNumber = trim($data['mobile_full']);
                $data['phone_number'] = $fullPhoneNumber;
                
                // Extract country code from the full phone number
                if (preg_match('/^\+(\d{1,4})(.+)$/', $fullPhoneNumber, $matches)) {
                    $data['country_code'] = '+' . $matches[1];
                    $data['phone_number'] = $matches[2];
                } else {
                    $data['country_code'] = '+225';
                    $data['phone_number'] = $fullPhoneNumber;
                }
            } else {
                $data['country_code'] = '+225';
            }
            
            // Set default values
            $data['approval_status'] = 'Pending';
            $data['verification_status'] = 'Not Started';
            $data['document_submitted'] = 0;
            $data['confirmation_email_sent'] = 0;
            $data['status'] = 'A';
            
            // Handle category_ids - convert array to comma-separated string
            if (!empty($data['category_ids']) && is_array($data['category_ids']) && count($data['category_ids']) > 0) {
                $data['category_ids'] = (string) implode(',', $data['category_ids']);
            } else {
                $data['category_ids'] = null;
            }
                        
            // Check if merchant with same email already exists
            if (!empty($data['email'])) {
                $existingMerchant = $this->Merchants->find()
                    ->contain(['Users'])
                    ->where(['Users.email' => $data['email']])
                    ->first();
                
                if ($existingMerchant) {
                    $this->Flash->error(__('A merchant is already available with the same email address.'));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                    return;
                }
            }
            
            // Generate password for merchant (outside transaction)
            $plainPassword = Security::randomString(10);
            $password = $plainPassword;
                        
            // Create user account and merchant in a transaction
            $connection = $this->Merchants->getConnection();
            $connection->begin();
            
            try {
                // Check if user with email already exists
                $existingUser = null;
                $isNewUser = false;
                
                if (!empty($data['email'])) {
                    $existingUser = $this->Users->find()->where(['email' => $data['email']])->first();
                    $role = $this->Roles->find()->where(['slug' => 'seller'])->first();
                    if (!$role) {
                        $this->Flash->error(__('Role not found. Please contact administrator.'));
                        $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                        return;
                    }
                    if ($existingUser) {
                        // User exists, use their ID
                        $data['user_id'] = $existingUser->id;
                        $user = $existingUser;
                    } else {
                        // Create new user account
                        $isNewUser = true;
                        $user = $this->Users->newEmptyEntity();
                        $userData = [
                            'first_name' => $data['company_name'],
                            'last_name' => null,
                            'email' => $data['email'],
                            'country_code' => $data['country_code'],
                            'mobile_no' => str_replace(' ', '', $data['mobile_full'] ?? $data['phone_number']), 
                            'user_type' => 'Merchant',
                            'password' => $password,
                            'role_id' => $role->id,
                            'status' => 'A'
                        ];
                        
                        $user = $this->Users->patchEntity($user, $userData);
                        
                        if ($this->Users->save($user)) {
                            $data['user_id'] = $user->id;
                        } else {
                            $this->Flash->error(__('Failed to create user account. Please try again.'));
                            $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                            return;
                        }
                    }
                } else {
                    $this->Flash->error(__('Email is required to create merchant account.'));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                    return;
                }
                
                // Ensure user_id is set
                if (empty($data['user_id'])) {
                    $this->Flash->error(__('User ID is required. Please try again.'));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                    return;
                }
                                
                $merchant = $this->Merchants->patchEntity($merchant, $data);
                
                if ($this->Merchants->save($merchant)) {
                    // Handle KYC documents if uploaded
                    if (!empty($data['seller_document'])) {
                        $MerchantDocuments = $this->fetchTable('MerchantDocuments');
                        $MerchantDocuments->saveMultipleDocuments($merchant->id, $data['seller_document']);
                    }
                    
                    $connection->commit();
                    
                    // Send appropriate email based on whether user is new or existing
                    $to = trim($data['email']);
                    
                    if ($isNewUser) {
                        // New user - send welcome email with credentials
                        $subject = "Welcome to Babiken - Merchant Account Created";
                        $template = "welcome_seller";
                        
                        $viewVars = array(
                            'username' => $data['company_name'], 
                            'email' => $data['email'], 
                            'password' => $plainPassword,
                            'business_name' => $data['company_name'],
                            'merchant_code' => $merchant->merchant_code,
                            'existing_user' => false
                        );
                        
                        $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                        if ($send_email) {
                            $this->Flash->success(__('The merchant has been saved successfully and a welcome email with login credentials has been sent.'));
                        } else {
                            $this->Flash->success(__('The merchant has been saved successfully, but there was an issue sending the welcome email.'));
                        }
                    } else {
                        // Existing user - send merchant account notification
                        $subject = "New Merchant Account Created - Babiken";
                        $template = "welcome_seller";
                        
                        $viewVars = array(
                            'username' => $data['company_name'], 
                            'email' => $data['email'], 
                            'business_name' => $data['company_name'],
                            'merchant_code' => $merchant->merchant_code,
                            'existing_user' => true
                        );
                        
                        $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                        if ($send_email) {
                            $this->Flash->success(__('The merchant has been saved successfully and a notification email has been sent to the existing user.'));
                        } else {
                            $this->Flash->success(__('The merchant has been saved successfully, but there was an issue sending the notification email.'));
                        }
                    }
                    
                    return $this->redirect(['action' => 'index']);
                } else {
                    $this->Flash->error(__($e->getMessage()));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                    return;
                }
            } catch (\Exception $e) {
                $connection->rollback();
                $this->Flash->error(__($e->getMessage()));
                $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
                return;
            }
        }
                
        $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories'));
    }

    private function uploadFile($file, $folder)
    {
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!in_array($file->getClientMediaType(), $allowedTypes)) {
            return null;
        }
        
        // Validate file size (5MB max)
        $maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if ($file->getSize() > $maxSize) {
            return null;
        }
        
        $uploadFolder = 'uploads/' . $folder . '/';
        $targetdir = WWW_ROOT . $uploadFolder;
        
        if (!is_dir($targetdir)) {
            mkdir($targetdir, 0755, true);
        }
        
        $fileName = $file->getClientFilename();
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
        $targetfile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $fileExt;
        
        $fileTmpName = $file->getStream()->getMetadata('uri');
        
        if (move_uploaded_file($fileTmpName, $targetdir . $targetfile)) {
            return $uploadFolder . $targetfile;
        }
        
        return null;
    }

    public function edit($id = null)
    {
        $merchant = $this->Merchants->get($id, [
            'contain' => ['Users', 'States', 'Cities', 'Categories', 'MerchantDocuments'],
        ]);
        
        // Decode category_ids for form display
        if (!empty($merchant->category_ids)) {
            // Convert comma-separated string to array
            $merchant->category_ids = explode(',', $merchant->category_ids);
            // Convert string IDs to integers
            $merchant->category_ids = array_map('intval', $merchant->category_ids);
        } else {
            $merchant->category_ids = [];
        }
        
        // Check if merchant is approved and active - if so, restrict editing
        $isApprovedAndActive = ($merchant->approval_status === 'Approved' && $merchant->status === 'A');
        
        $categories = $this->Merchants->getCategories();
        $states = $this->States->find('list', ['keyField' => 'id', 'valueField' => 'state_name'])->toArray();
        $cities = $this->Cities->find('list', ['keyField' => 'id', 'valueField' => 'city_name'])->toArray();
        $businessCategories = $this->Categories->find('list', ['keyField' => 'id', 'valueField' => 'name'])->toArray();
        
        $documentTypes = [
            'cerificate' => 'Certificate',
            'licence' => 'License',
            'others' => 'Others'
        ];

        if ($this->request->is(['patch', 'post', 'put'])) {
            // If merchant is approved and active, prevent editing
            // if ($isApprovedAndActive) {
            //     $this->Flash->error(__('Cannot edit merchant information. This merchant is approved and active.'));
            //     return $this->redirect(['action' => 'view', $id]);
            // }
            
            $data = $this->request->getData();
            
            // Handle category_ids - convert array to comma-separated string
            if (!empty($data['category_ids']) && is_array($data['category_ids']) && count($data['category_ids']) > 0) {
                $data['category_ids'] = (string) implode(',', $data['category_ids']);
            } else {
                $data['category_ids'] = null;
            }
            // dd($data);
            // Handle file uploads
            $merchantLogo = $this->request->getData('merchant_logo');
            $banner = $this->request->getData('banner');
            
            // Remove file objects from data to prevent validation errors
            unset($data['merchant_logo']);
            unset($data['banner']);
            
            // Only process file uploads if files are actually uploaded
            if ($merchantLogo && is_object($merchantLogo) && $merchantLogo->getError() === UPLOAD_ERR_OK) {
                // Validate merchant logo resolution and size
                $logoValidationErrors = $this->validateImageResolution($merchantLogo, 'logo');
                if (!empty($logoValidationErrors)) {
                    $this->Flash->error(__('Logo validation failed: ' . implode('; ', $logoValidationErrors)));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories', 'documentTypes', 'isApprovedAndActive'));
                    return;
                }
                
                $logoFileName = $this->uploadFile($merchantLogo, 'merchant_logos');
                if (!$logoFileName) {
                    $this->Flash->error(__('Failed to upload merchant logo. Please try again.'));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories', 'documentTypes', 'isApprovedAndActive'));
                    return;
                }
                $data['merchant_logo'] = $logoFileName;
            }
            
            if ($banner && is_object($banner) && $banner->getError() === UPLOAD_ERR_OK) {
                // Validate banner resolution and size
                $bannerValidationErrors = $this->validateImageResolution($banner, 'banner');
                if (!empty($bannerValidationErrors)) {
                    $this->Flash->error(__('Banner validation failed: ' . implode('; ', $bannerValidationErrors)));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories', 'documentTypes', 'isApprovedAndActive'));
                    return;
                }
                
                $bannerFileName = $this->uploadFile($banner, 'merchant_banners');
                if (!$bannerFileName) {
                    $this->Flash->error(__('Failed to upload banner. Please try again.'));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories', 'documentTypes', 'isApprovedAndActive'));
                    return;
                }
                $data['banner'] = $bannerFileName;
            }
            
            // Remove email from data to prevent changes
            unset($data['email']);
            
            // Handle phone number and country code for edit
            if (!empty($data['mobile_full'])) {
                $fullPhoneNumber = trim($data['mobile_full']);
                
                // Extract country code from the full phone number
                if (preg_match('/^\+(\d{1,4})(.+)$/', $fullPhoneNumber, $matches)) {
                    $data['country_code'] = '+' . $matches[1];
                    $data['phone_number'] = $matches[2];
                } else {
                    $data['phone_number'] = $fullPhoneNumber;
                }
            }
            
            // Validate seller documents if any are uploaded
            if (!empty($data['seller_document'])) {
                $documentValidationErrors = $this->validateSellerDocuments($data['seller_document']);
                if (!empty($documentValidationErrors)) {
                    $this->Flash->error(__('Document validation failed: ' . implode('; ', $documentValidationErrors)));
                    $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories', 'documentTypes', 'isApprovedAndActive'));
                    return;
                }
            }
            
            // Handle KYC documents if uploaded
            if (!empty($data['seller_document'])) {
                $MerchantDocuments = $this->fetchTable('MerchantDocuments');
                $MerchantDocuments->saveMultipleDocuments($merchant->id, $data['seller_document']);
            }
            
            $merchant = $this->Merchants->patchEntity($merchant, $data);
            
            if ($this->Merchants->save($merchant)) {
                $this->Flash->success(__('The merchant has been updated successfully.'));
                return $this->redirect(['action' => 'view', $id]);
            } else {
                $this->Flash->error(__('The merchant could not be updated. Please, try again.'));
            }
        }
        
        $this->set(compact('merchant', 'categories', 'states', 'cities', 'businessCategories', 'documentTypes', 'isApprovedAndActive'));
    }

    /**
     * Generate Google Maps URL for merchant address
     *
     * @param \App\Model\Entity\Merchant $merchant
     * @return string
     */
    private function generateMapUrl($merchant)
    {
        $addressParts = [];
        
        // Add address line 1
        if (!empty($merchant->address_line1)) {
            $addressParts[] = $merchant->address_line1;
        }
        
        // Add address line 2
        if (!empty($merchant->address_line2)) {
            $addressParts[] = $merchant->address_line2;
        }
        
        // Add city
        if (!empty($merchant->city) && !empty($merchant->city->city_name)) {
            $addressParts[] = $merchant->city->city_name;
        }
        
        // Add state
        if (!empty($merchant->state) && !empty($merchant->state->state_name)) {
            $addressParts[] = $merchant->state->state_name;
        }
        
        // Add zipcode
        if (!empty($merchant->zipcode)) {
            $addressParts[] = $merchant->zipcode;
        }
        
        // Add country
        if (!empty($merchant->country)) {
            $addressParts[] = $merchant->country;
        }
        
        // If no address parts are available, use company name as fallback
        if (empty($addressParts)) {
            $addressParts[] = $merchant->company_name;
        }
        
        // Combine all address parts
        $fullAddress = implode(', ', $addressParts);
        
        // URL encode the address
        $encodedAddress = urlencode($fullAddress);
        
        // Generate Google Maps URL (primary choice)
        return "https://www.google.com/maps/search/?api=1&query=" . $encodedAddress;
    }

    public function view($id = null)
    {
        $merchant = $this->Merchants->get($id, [
            'contain' => ['Users', 'States', 'Cities', 'Categories', 'MerchantDocuments'],
        ]);
        
        $documentTypes = [
            'cerificate' => 'Certificate',
            'licence' => 'License',
            'others' => 'Others'
        ];
        
        // Fetch merchant orders
        $MerchantOrders = $this->fetchTable('MerchantOrders');
        $merchantOrders = $MerchantOrders->getMerchantOrders($id, 10);
        $orderStats = $MerchantOrders->getMerchantOrderStats($id);
        
        // Fetch merchant ratings
        $MerchantRatings = $this->fetchTable('MerchantRatings');
        $merchantRatings = $MerchantRatings->getMerchantRatings($id, 10);
        $ratingStats = $MerchantRatings->getMerchantRatingStats($id);
        
        // Fetch merchant products
        $Products = $this->fetchTable('Products');
        $merchantProducts = $Products->find()
            ->where(['merchant_id' => $id, 'status' => 'A'])
            ->contain(['ProductImages', 'ProductCategories.Categories'])
            ->order(['created' => 'DESC'])
            ->limit(10)
            ->toArray();
        
        // Fetch merchant settlements for earnings data
        $MerchantSettlements = $this->fetchTable('MerchantSettlements');
        $merchantSettlements = $MerchantSettlements->getMerchantSettlements($id, 10);
        
        // Get business categories for display
        $businessCategories = $this->Categories->find('list', ['keyField' => 'id', 'valueField' => 'name'])->toArray();
        
        // Generate map URL for merchant address
        $mapUrl = $this->generateMapUrl($merchant);
        
        $this->set(compact('merchant', 'documentTypes', 'merchantOrders', 'orderStats', 'merchantRatings', 'ratingStats', 'merchantProducts', 'merchantSettlements', 'businessCategories', 'mapUrl'));
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The seller could not be deleted. Please, try again.'];

        try {
            $seller = $this->Merchants->get($id);
            $record = $this->Users->get($seller->user_id);
            
            $record->status = 'D';
            $seller->status = 'D';

            if ($this->Merchants->save($seller) && $this->Users->save($record)) {
                $response = ['success' => true, 'message' => 'The seller has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function changeStatus($id = null)
    {
        $this->request->allowMethod(['post']);
        $merchant = $this->Merchants->get($id);
        
        $newStatus = $this->request->getData('status');
        $merchant->approval_status = $newStatus;
        
        // Handle Info Requested status
        if ($newStatus === 'Info Requested') {
            $adminNote = $this->request->getData('info_note');
            if (!empty($adminNote)) {
                $merchant->admin_note = $adminNote;
            }
        }
        
        if ($newStatus === 'Approved') {
            $merchant->approval_date = date('Y-m-d');
        }
        
        if ($this->Merchants->save($merchant)) {
            $this->Flash->success(__('Merchant status updated successfully.'));
        } else {
            $this->Flash->error(__('Failed to update merchant status.'));
        }
        
        return $this->redirect(['action' => 'view', $id]);
    }

    public function verifyDocument()
    {
        $this->request->allowMethod(['post']);
        $documentId = $this->request->getData('document_id');
        $MerchantDocuments = $this->fetchTable('MerchantDocuments');
        
        $document = $MerchantDocuments->get($documentId);
        $document->status = 'A';
        
        if ($MerchantDocuments->save($document)) {
            $this->Flash->success(__('Document verified successfully.'));
        } else {
            $this->Flash->error(__('Failed to verify document.'));
        }
        
        return $this->redirect(['action' => 'view', $document->merchant_id]);
    }

    public function rejectDocument()
    {
        $this->request->allowMethod(['post']);
        $documentId = $this->request->getData('document_id');
        $MerchantDocuments = $this->fetchTable('MerchantDocuments');
        
        $document = $MerchantDocuments->get($documentId);
        $document->status = 'D';
        
        if ($MerchantDocuments->save($document)) {
            $this->Flash->success(__('Document rejected successfully.'));
        } else {
            $this->Flash->error(__('Failed to reject document.'));
        }
        
        return $this->redirect(['action' => 'view', $document->merchant_id]);
    }

    /**
     * Validate seller documents before processing
     *
     * @param array $documentsData Array with document_type as key and file object as value
     * @return array Array of validation error messages
     */
    private function validateSellerDocuments($documentsData)
    {
        $errors = [];
        
        // Allowed file extensions
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];
        
        // Allowed MIME types
        $allowedMimeTypes = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        // Maximum file size (10MB)
        $maxFileSize = 10 * 1024 * 1024;
        
        foreach ($documentsData as $documentType => $file) {
            // Skip if no file uploaded for this document type
            if (!$file || $file->getError() === UPLOAD_ERR_NO_FILE) {
                continue;
            }
            
            // Check for upload errors
            if ($file->getError() !== UPLOAD_ERR_OK) {
                $errorMessage = ucfirst($documentType) . ' upload error: ';
                switch ($file->getError()) {
                    case UPLOAD_ERR_INI_SIZE:
                        $errorMessage .= 'File exceeds PHP upload_max_filesize limit';
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        $errorMessage .= 'File exceeds HTML form MAX_FILE_SIZE limit';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $errorMessage .= 'File was only partially uploaded';
                        break;
                    case UPLOAD_ERR_NO_TMP_DIR:
                        $errorMessage .= 'Missing temporary folder';
                        break;
                    case UPLOAD_ERR_CANT_WRITE:
                        $errorMessage .= 'Failed to write file to disk';
                        break;
                    case UPLOAD_ERR_EXTENSION:
                        $errorMessage .= 'A PHP extension stopped the file upload';
                        break;
                    default:
                        $errorMessage .= 'Unknown upload error (code: ' . $file->getError() . ')';
                }
                $errors[] = $errorMessage;
                continue;
            }
            
            // Check file size
            if ($file->getSize() > $maxFileSize) {
                $errors[] = ucfirst($documentType) . ' file size (' . round($file->getSize() / 1024 / 1024, 2) . 'MB) exceeds maximum allowed size (10MB)';
                continue;
            }
            
            // Get file extension
            $fileName = $file->getClientFilename();
            $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            
            // Check file extension
            if (!in_array($fileExtension, $allowedExtensions)) {
                $errors[] = ucfirst($documentType) . ' file type (.' . $fileExtension . ') is not allowed. Allowed types: ' . implode(', ', $allowedExtensions);
                continue;
            }
            
            // Check MIME type
            $fileMimeType = $file->getClientMediaType();
            if (!in_array($fileMimeType, $allowedMimeTypes)) {
                $errors[] = ucfirst($documentType) . ' file MIME type (' . $fileMimeType . ') is not allowed. Allowed types: ' . implode(', ', $allowedMimeTypes);
                continue;
            }
            
            // Additional security check: verify file content matches extension
            if (!$this->validateFileContent($file, $fileExtension)) {
                $errors[] = ucfirst($documentType) . ' file content does not match the file extension (.' . $fileExtension . ')';
            }
        }
        
        return $errors;
    }
    
    /**
     * Validate file content matches the extension
     *
     * @param \Laminas\Diactoros\UploadedFile $file
     * @param string $extension
     * @return bool
     */
    private function validateFileContent($file, $extension)
    {
        $filePath = $file->getStream()->getMetadata('uri');
        
        // Read first few bytes to check file signature
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            return false;
        }
        
        $header = fread($handle, 8);
        fclose($handle);
        
        // Check file signatures
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                return (substr($header, 0, 2) === "\xFF\xD8");
            case 'png':
                return (substr($header, 0, 8) === "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A");
            case 'gif':
                return (substr($header, 0, 6) === "GIF87a" || substr($header, 0, 6) === "GIF89a");
            case 'pdf':
                return (substr($header, 0, 4) === "%PDF");
            case 'doc':
                return (substr($header, 0, 8) === "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1");
            case 'docx':
                // DOCX files are ZIP archives, check for ZIP signature
                return (substr($header, 0, 2) === "PK");
            default:
                return true; // For unknown extensions, assume valid
        }
    }
    
    /**
     * Validate image resolution and size for logo and banner
     *
     * @param \Laminas\Diactoros\UploadedFile $file
     * @param string $imageType 'logo' or 'banner'
     * @return array Array of validation error messages
     */
    private function validateImageResolution($file, $imageType)
    {
        $errors = [];
        
        // Maximum file size (5MB)
        $maxFileSize = 5 * 1024 * 1024;
        
        // Check file size
        if ($file->getSize() > $maxFileSize) {
            $errors[] = ucfirst($imageType) . ' file size (' . round($file->getSize() / 1024 / 1024, 2) . 'MB) exceeds maximum allowed size (5MB)';
            return $errors; // Return early if size is too large
        }
        
        // Get file path
        $filePath = $file->getStream()->getMetadata('uri');
        
        // Get image dimensions
        $imageInfo = getimagesize($filePath);
        if ($imageInfo === false) {
            $errors[] = ucfirst($imageType) . ' is not a valid image file';
            return $errors;
        }
        
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        
        // Allowed resolutions
        $allowedResolutions = [
            ['width' => 240, 'height' => 180],
            ['width' => 320, 'height' => 250]
        ];
        
        // Check if dimensions match allowed resolutions
        $isValidResolution = false;
        foreach ($allowedResolutions as $resolution) {
            if ($width === $resolution['width'] && $height === $resolution['height']) {
                $isValidResolution = true;
                break;
            }
        }
        
        if (!$isValidResolution) {
            $allowedResolutionsText = [];
            foreach ($allowedResolutions as $resolution) {
                $allowedResolutionsText[] = $resolution['width'] . 'x' . $resolution['height'];
            }
            $errors[] = ucfirst($imageType) . ' dimensions (' . $width . 'x' . $height . ') are not allowed. Allowed resolutions: ' . implode(' or ', $allowedResolutionsText);
        }
        
        return $errors;
    }
} 