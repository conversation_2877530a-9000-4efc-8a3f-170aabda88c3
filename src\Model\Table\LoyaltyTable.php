<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Core\Configure;
use App\Model\Table\Log;
use Cake\ORM\TableRegistry;

/**
 * Loyalty Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 *
 * @method \App\Model\Entity\Loyalty newEmptyEntity()
 * @method \App\Model\Entity\Loyalty newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Loyalty> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Loyalty get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Loyalty findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Loyalty patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Loyalty> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Loyalty|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Loyalty saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Loyalty>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Loyalty>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Loyalty>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Loyalty> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Loyalty>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Loyalty>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Loyalty>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Loyalty> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class LoyaltyTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('loyalty');
        $this->setDisplayField('loyalty_category');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->scalar('loyalty_category')
            ->maxLength('loyalty_category', 255)
            ->requirePresence('loyalty_category', 'create')
            ->notEmptyString('loyalty_category');

        $validator
            ->decimal('spent_amount')
            ->requirePresence('spent_amount', 'create')
            ->notEmptyString('spent_amount');

        $validator
            ->decimal('points')
            ->requirePresence('points', 'create')
            ->notEmptyString('points');

        $validator
            ->dateTime('validity')
            ->allowEmptyDateTime('validity');

        $validator
            ->scalar('status')
            ->requirePresence('status', 'create')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);

        return $rules;
    }

    /**
     * Calculate loyalty points (legacy method - now uses getLoyaltyPoints)
     * 
     * @param int $customerId Customer ID
     * @return array Loyalty points data
     * @deprecated Use getLoyaltyPoints() instead
     */
    public function calculateLoyaltyPoints($customerId)
    {
        // Use the new method for consistency
        return $this->getLoyaltyPoints($customerId);
    }

    public function customerLoyaltyInsert($data)
    {
        $entity = $this->newEmptyEntity();
        $record = $this->patchEntity($entity, $data);
    
        if ($this->save($record)) {
            return $record; // return saved entity
        }
        return false;
    }

    /**
     * Earn loyalty points with expiration extension logic
     * 
     * @param int $customerId Customer ID
     * @param float $earnedPoints Points to be earned
     * @param string $loyaltyCategory Loyalty category
     * @return bool|array Success status or error details
     */
    public function earnLoyaltyPoints($customerId, $earnedPoints, $loyaltyCategory = 'standard')
    {
        try {
            // Find existing loyalty record
            $existingLoyalty = $this->find()
                ->where(['customer_id' => $customerId, 'status' => 'A'])
                ->order(['created' => 'DESC'])
                ->first();

            $currentDate = new \DateTime();
            $newExpirationDate = $currentDate->modify('+3 months');

            if ($existingLoyalty) {
                // Check if existing points are still valid
                $existingExpirationDate = null;
                if (!empty($existingLoyalty->validity)) {
                    $existingExpirationDate = new \DateTime($existingLoyalty->validity->i18nFormat('yyyy-MM-dd HH:mm:ss'));
                }

                // If existing points are still valid, extend their expiration
                if ($existingExpirationDate && $existingExpirationDate > $currentDate) {
                    // Extend expiration date to match new points expiration
                    $existingLoyalty->validity = $newExpirationDate;
                } else {
                    // Existing points have expired, set new expiration
                    $existingLoyalty->validity = $newExpirationDate;
                }

                // Add new points to existing balance
                $existingLoyalty->points += $earnedPoints;
                
                // Update loyalty category if different
                if (!empty($loyaltyCategory) && $existingLoyalty->loyalty_category !== $loyaltyCategory) {
                    $existingLoyalty->loyalty_category = $loyaltyCategory;
                }

                if ($this->save($existingLoyalty)) {
                    return true;
                }
            } else {
                // Create new loyalty record
                $newLoyalty = $this->newEmptyEntity();
                $newLoyalty->customer_id = $customerId;
                $newLoyalty->points = $earnedPoints;
                $newLoyalty->loyalty_category = $loyaltyCategory;
                $newLoyalty->spent_amount = '0.00';
                $newLoyalty->validity = $newExpirationDate;
                $newLoyalty->status = 'A';

                if ($this->save($newLoyalty)) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            // Log error for debugging (commented out)
            // $this->log('Error in earnLoyaltyPoints: ' . $e->getMessage(), 'error');
            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get loyalty points with proper expiration handling
     * 
     * @param int $customerId Customer ID
     * @return array Loyalty points data
     */
    public function getLoyaltyPoints($customerId)
    {
        $loyalty = $this->find()
            ->where(['customer_id' => $customerId, 'status' => 'A'])
            ->order(['created' => 'DESC'])
            ->first();

        if (!$loyalty) {
            return [
                'status' => 'success',
                'data' => [
                    'loyalty_category' => null,
                    'points' => '0.00',
                    'points_converted' => '0.00',
                    'validity' => null,
                    'is_expired' => false,
                ],
                'message' => __('No loyalty points found')
            ];
        }

        $currentDate = new \DateTime();
        $validityDate = null;
        $isExpired = false;

        if (!empty($loyalty->validity)) {
            $validityDate = new \DateTime($loyalty->validity->i18nFormat('yyyy-MM-dd HH:mm:ss'));
            $isExpired = $validityDate < $currentDate;
        }

        if ($isExpired) {
            return [
                'status' => 'error',
                'data' => [
                    'loyalty_category' => $loyalty->loyalty_category,
                    'points' => number_format(floatval($loyalty->points), 2),
                    'points_converted' => '0.00',
                    'validity' => $loyalty->validity,
                    'validity_end_date' => $validityDate,
                    'is_expired' => true,
                ],
                'message' => __('Loyalty points have expired')
            ];
        }

        $loyaltyCategory = $loyalty->loyalty_category;
        $loyaltyPoints = floatval($loyalty->points);
        $loyaltyPointsConverted = 0.00;

        // Calculate converted points based on customer group
        $CustomerGroupMappings = TableRegistry::getTableLocator()->get('CustomerGroupMappings');
        $customerGroupsQuery = $CustomerGroupMappings
            ->find()
            ->select(['customer_group_id'])
            ->where(['customer_id' => $customerId, 'status' => 'A'])
            ->all();

        $customerGroupIds = [];
        foreach ($customerGroupsQuery as $row) {
            $customerGroupIds[] = $row->customer_group_id;
        }

        if (!empty($customerGroupIds)) {
            $LoyaltySettings = TableRegistry::getTableLocator()->get('LoyaltySettings');
            $loyalty_setting = $LoyaltySettings
                ->find()
                ->where([
                    'customer_group_id IN' => $customerGroupIds,
                    'status' => 'A'
                ])
                ->order(['redeem_point_value' => 'DESC'])
                ->first();

            if ($loyalty_setting) {
                $loyalty_redeem_point_value = $loyalty_setting->redeem_point_value;
                $loyalty_redeem_points = $loyalty_setting->redeem_points;

                $final_redeem_points = $loyaltyPoints / $loyalty_redeem_points;
                $loyaltyPointsConverted = $final_redeem_points * $loyalty_redeem_point_value;
            }
        }

        return [
            'status' => 'success',
            'data' => [
                'loyalty_category' => $loyaltyCategory,
                'points' => number_format($loyaltyPoints, 2),
                'points_converted' => $loyaltyPointsConverted,
                'validity' => $loyalty->validity,
                'validity_end_date' => $validityDate,
                'is_expired' => false,
            ],
            'message' => __('Loyalty points successfully calculated')
        ];
    }
    
    /**
     * Reduce loyalty points for a cancelled or returned order
     * 
     * @param int $customerId Customer ID
     * @param float $orderAmount Order amount (subtotal) for which loyalty points were earned
     * @param string $reason Reason for reduction (cancellation/return)
     * @return array|bool Success status or error details
     */
    public function reduceLoyaltyPointsForOrder($customerId, $orderAmount, $reason = 'Order cancellation/return')
    {
        try {
            // Find the customer's active loyalty record
            $loyalty = $this->find()
                ->where(['customer_id' => $customerId, 'status' => 'A'])
                ->order(['created' => 'DESC'])
                ->first();

            if (!$loyalty) {
                return ['error' => true, 'message' => 'No active loyalty record found for customer'];
            }

            // Calculate how many points were earned for this order amount
            $CustomerGroupMappings = TableRegistry::getTableLocator()->get('CustomerGroupMappings');
            $customerGroupsQuery = $CustomerGroupMappings
                ->find()
                ->select(['customer_group_id'])
                ->where(['customer_id' => $customerId, 'status' => 'A'])
                ->all();

            $customerGroupIds = [];
            foreach ($customerGroupsQuery as $row) {
                $customerGroupIds[] = $row->customer_group_id;
            }

            if (empty($customerGroupIds)) {
                return ['error' => true, 'message' => 'Customer not assigned to any customer group'];
            }

            $LoyaltySettings = TableRegistry::getTableLocator()->get('LoyaltySettings');
            $loyalty_setting = $LoyaltySettings
                ->find()
                ->where([
                    'customer_group_id IN' => $customerGroupIds,
                    'status' => 'A'
                ])
                ->order(['earning_threshold_amount' => 'DESC'])
                ->first();

            if (!$loyalty_setting) {
                return ['error' => true, 'message' => 'No loyalty settings configured for customer group'];
            }

            // Calculate points that were earned for this order
            $earning_threshold_amount = $loyalty_setting->earning_threshold_amount;
            $points_per_threshold = $loyalty_setting->earning_points;
            
            $pointsEarnedForOrder = 0;
            if ($earning_threshold_amount > 0) {
                $pointsEarnedForOrder = floor($orderAmount / $earning_threshold_amount) * $points_per_threshold;
            }

            if ($pointsEarnedForOrder <= 0) {
                return ['error' => true, 'message' => 'No loyalty points were earned for this order amount'];
            }

            // Check if customer has enough points to reduce
            if ($loyalty->points < $pointsEarnedForOrder) {
                return ['error' => true, 'message' => 'Insufficient loyalty points to reduce'];
            }

            // Reduce the points
            $loyalty->points = max(0, $loyalty->points - $pointsEarnedForOrder);
            
            // If points become 0, update status to inactive
            if ($loyalty->points <= 0) {
                $loyalty->status = 'I';
            }

            // Save the updated loyalty record
            if ($this->save($loyalty)) {
                return [
                    'success' => true,
                    'points_reduced' => $pointsEarnedForOrder,
                    'remaining_points' => $loyalty->points,
                    'message' => "Successfully reduced {$pointsEarnedForOrder} loyalty points for {$reason}"
                ];
            } else {
                return ['error' => true, 'message' => 'Failed to save loyalty record'];
            }

        } catch (\Exception $e) {
            return ['error' => true, 'message' => 'Error reducing loyalty points: ' . $e->getMessage()];
        }
    }
    

}
