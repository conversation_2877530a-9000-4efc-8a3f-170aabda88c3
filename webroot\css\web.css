*, ::after, ::before{
    box-sizing: border-box;
}
html, body{
    margin: 0;
    padding: 0;
}
body{
    font-family: Ubuntu, sans-serif;
}
.no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
}
header{
    height: 279px;
}
header .main-header {
    width: 100%;
    height: 50px;
    background-color: #004958;
}
header .sub-header {
    height: 73px;
    width: 100%;
    background-color: #0d839b;
    text-align: center;
    padding: 10px 0;
    overflow: hidden;
}
header .sub-header .scrolling-text {
    font-size: 22px;
    font-weight: 400;
    line-height: 16.8px;
    height: 23px;
    color: #ffffff;
    display: inline-block;
    white-space: nowrap;
    animation: scroll 25s linear infinite;
}

@keyframes scroll {
    0% {
        transform: translateX(150%);
    }
    100% {
        transform: translateX(-150%);
    }
}
.container{
    width: 100%;
    margin: 0 auto;
    padding-left: 0px;
    padding-right: 0px;
}
.main-header .contents {
    display: flex;
    padding-top: 17px;
    justify-content: flex-start;
    padding-left: 30px;
}
.main-header .contents .welcome {
    font-size: 14px;
    font-weight: 700;
    line-height: 16.8px;
    color: #ffffff;
}
header .navbar.fixed-top {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 11 !important;
    box-shadow: 6px 12px 13px -12px #aaa;
}
.navbar{
    background-color: #fff;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 50px;
    padding: 0px 18px;
    display: inline-block;
    width: 100%;
    position: relative;
}
.navbar-row{
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
}
.navbar hr.break{
    display: none;
}
.navbar .home-text{
    display: none;
}
.navbar .category-wrapper{
    margin-right: 15px;
}
.navbar .category-wrapper .v-dots{
    color: #000;
    cursor: pointer;
    position: relative;
    bottom: 5px;
}
.navbar .category-wrapper .category-dropdown-container{
    width: 70%;
    display: block;
    position: fixed;
    top: 0px;
    background-color: #fff;
    left: 0;
    height: 100vh;
    z-index: 11;
    overflow-y: scroll;
    border-right: 2px solid #F9DAB9;
}
.navbar .category-wrapper .category-dropdown-container .mobile-menu-header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 49px 15px 25px 15px;
    border: 1px solid #F9DAB9;
    border-bottom-right-radius: 50px;
    margin-bottom: 22px;
}
.navbar .category-wrapper .category-dropdown-container .mobile-menu-header .close-menu .back-icon{
    width: 30px;
}
.navbar .category-wrapper .category-dropdown-container ul.category-dropdown{
    margin: 0 30px;
    list-style-type: none;
    padding-left: 0;
    margin-top: 0;
}
.navbar .category-wrapper .category-dropdown-container ul.category-dropdown li{
    margin-bottom: 5px;
    padding: 10px;
}
.navbar .category-wrapper .category-dropdown-container ul.category-dropdown li:hover{
    font-weight: 500;
    background-color: #f9dab9;
    border-radius: 4px;
}
.navbar .category-wrapper .category-dropdown-container ul.category-dropdown li a{
    font-size: 16px;
    font-weight: 400;
    color: #505050;
    text-decoration: none;
    letter-spacing: 0.8px;
}
.navbar .category-wrapper .category-dropdown-container ul.category-dropdown li:hover a{
    font-weight: 600;
}
.navbar .logo{
    margin-right: 8px;
}
.navbar .logo .nav-icon {
    width: 120px;
    height: auto;
    margin-right: 0px;
}
.navbar .menu-logo .nav-icon {
    width: 120px;
    height: auto;
    margin-left: 20px;
    margin-right: 0px;
}
.navbar .search-container-agn {
    width: 100%;
    margin-right: 10px;
    height: 46px;
    border-radius: 23px;
    border: 1px solid #f9dab9;
    box-shadow: 0px 0px 6.1px 0px #f9dab9;
    position: absolute;
    top: 77px;
    left: 0;
    margin-top: 10px;
    margin-bottom: 10px;
}
.navbar .search-container-agn .search-container input.search-input {
    margin-top: 0px;
    margin-left: 0px;
    height: 46px;
    width: 100%;
    color: #000;
    font-size: 13px;
    border: none;
    outline: 0;
    border-radius: 23px !important;
    padding-left: 10px;
    padding-right: 40px;
}
.navbar .search-container-agn .search-container input.search-input::placeholder {
    color: #d9d9d9;
    font-size: 15px;
}
.navbar .search-container-agn .search-container img#fa-search{
    margin-top: 0;
    position: absolute;
    margin-right: 0;
    width: 24px;
    right: 6px;
    top: 15px;
}
.navbar .shop-container{
    display: flex;
    align-items: center;
    justify-content: center;
}
.navbar .shop-container .whishlist {
    position: relative;
    margin-right: 8px;
    width: 43px;
    height: 43px;
    border: 1px solid #f7cea1;
    border-radius: 50%;
}
.navbar .shop-container .whishlist img#heart {
    height: auto;
    width: 21px;
    color: black;
    margin-left: 23%;
    margin-top: 27%;
}
.navbar .shop-container .whishlist .cart-superscript {
    position: absolute;
    top: -1px;
    right: -4px;
    font-size: 10px;
    color: white;
    background: #f77f00;
    border-radius: 50%;
    padding: 3px 6px;
}
.navbar .shop-container .cart {
    margin-right: 8px;
    width: 43px;
    height: 43px;
    border: 1px solid #f7cea1;
    border-radius: 50%;
}
.navbar .shop-container .cart img.bag-icon {
    height: auto;
    width: 21px;
    margin-top: 22%;
    margin-left: 20%;
}
.navbar .shop-container .cart a.icon {
    text-decoration: none;
}
.navbar .shop-container .cart .superscript {
    position: relative;
    top: -33px;
    right: -32px;
    font-size: 10px;
    color: white;
    background: #f77f00;
    border-radius: 50%;
    padding: 3px 6px;
    border: none;
    outline: none;
}
.navbar .shop-container .profile{
    width: 43px;
    height: 43px;
    border: 1px solid #f7cea1;
    border-radius: 50%;
}
.navbar .shop-container .profile img#profile-icon {
    height: auto;
    width: 21px;
    color: black;
    margin-left: 26%;
    margin-top: 27%;
}
.navbar .shop-container .profile .login-button{
    display: none;
}
/* Footer style */
.footer {
    height: auto;
    background-color: #0d839b;
    width: 100%;
    margin-top: 40px;
}
.footer .footer-header-containers .footer-headers {
    width: 100%;
    text-align: center;
}
.footer .footer-header-containers .footer-headers .footer-icon {
    width: 195px;
    height: auto;
    margin-top: 15px;
}
.footer .footer-header-containers .footer-headers .image-below {
    margin-left: 8px;
}
.footer .footer-header-containers .footer-headers .image-below .cutomer {
    display: none;
}
.footer .footer-header-containers .footer-headers .image-below .number {
    display: none;
}
.footer .footer-header-containers .footer-headers .image-below .nothing {
    display: none;
}
.footer .footer-header-containers .footer-headers .image-below .email {
    display: none;
}
.footer .footer-header-containers .footer-headers .image-below .footer-icons-container .social-icon-footer-div {
    background-color: #fff;
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-top: 15px;
    margin-bottom: 15px;
    margin-right: 10px;
    border-radius: 5px;
}
.footer .footer-header-containers .footer-headers .image-below .footer-icons-container .social-icon-footer-div .social-icon-footer {
    text-decoration: none;
}
.footer .footer-header-containers .footer-headers .image-below .footer-icons-container .social-icon-footer-div .social-icon-footer #footer-icon {
    color: #000;
    padding: 4px 4px;
}
.footer .for-mobile-res{
    display: block;
}
.footer .for-mobile-res .c-name-foo{
    display: block;
    text-align: center;
    font-size: 13px;
    line-height: 20px;
    font-weight: 500;
    color: #fff;
    letter-spacing: 1px;
}
.footer .for-mobile-res .c-num-foo{
    display: block;
    text-align: center;
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
    color: #fff;
    letter-spacing: 1px;
}
.footer .footer-header-containers .clone-footer-headers{
    display: none;
}
.footer .other-footer-text{
    text-align: center;
}
.footer .other-footer-text .other-footer-icon {
    margin-top: 0;
    margin-right: 0px;
    display: block;
    margin: 0 auto 15px;
}
.footer .other-footer-text .other-footer-icon .Download-The-App {
    text-align: center;
    font-size: 18px;
    line-height: 24px;
    font-weight: 700;
    color: #F6F6F6;
    margin-top: 15px;
    margin-left: 0;
    letter-spacing: 1px;
}
.footer .other-footer-text .other-footer-icon .other-footer-icon-footer {
    width: 97px;
}
.footer .other-footer-text .parent-Payment-Methods-img{
    display: none;
}
.footer .footer-br{
    display: block;
}
.footer .copyright{
    font-size: 13px;
    font-weight: 600;
    line-height: 24px;
    color: #fff;
    text-align: center;
    padding-bottom: 12px;
    letter-spacing: 1.1px;
}
.footer .mobile-footer-bottom{
    display: block;
    background-color: #404040;
    padding: 14px 0 13px;
    margin-bottom: 12px;
}
.footer .mobile-footer-bottom .bottom-link{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 10px;
}
.footer .mobile-footer-bottom .bottom-link a{
    font-size: 10px;
    font-weight: 500;
    color: #F6F6F6;
    text-decoration: none;
}
.footer .mobile-footer-bottom .bottom-link a:active{
    color: #F6F6F6;
}

@media screen and (min-width: 576px) {
    header .navbar.fixed-top {
        position: fixed;
        width: 510px;
        top: 0;
        z-index: 11 !important;
        box-shadow: 6px 12px 13px -12px #aaa;
    }
    .container{
        max-width: 540px;
        margin: 0 auto;
        padding-left: 15px;
        padding-right: 15px;
    }
    .main-header .contents {
        display: flex;
        padding-top: 17px;
        justify-content: flex-start;
    }
    .main-header .contents .welcome {
        font-size: 14px;
        font-weight: 700;
        line-height: 16.8px;
        color: #ffffff;
    }
    .navbar{
        background-color: #fff;
        border-bottom-left-radius: 50px;
        border-bottom-right-radius: 50px;
        padding: 0px 30px;
        display: inline-block;
        width: 100%;
        position: relative;
    }
    .navbar-row{
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    .navbar hr.break{
        display: none;
    }
    .navbar .category-wrapper{
        margin-right: 22px;
    }
    .navbar .category-wrapper .v-dots{
        color: #000;
        cursor: pointer;
        position: relative;
        bottom: 5px;
    }
    .navbar .logo{
        margin-right: 49px;
    }
    .navbar .logo .nav-icon {
        width: 150px;
        height: auto;
        margin-right: 0px;
    }
    .navbar .search-container-agn {
        width: 100%;
        margin-right: 10px;
        height: 46px;
        top: 95px;
        border-radius: 23px;
        border: 1px solid #f9dab9;
        box-shadow: 0px 0px 6.1px 0px #f9dab9;
    }
    .navbar .shop-container{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .navbar .shop-container .whishlist {
        position: relative;
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .whishlist .cart-superscript {
        position: absolute;
        top: -1px;
        right: -4px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 3px 6px;
    }
    .navbar .shop-container .cart {
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .cart .superscript {
        position: relative;
        top: -33px;
        right: -32px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 3px 6px;
        border: none;
        outline: none;
    }
    .navbar .shop-container .profile{
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
}

@media screen and (min-width: 768px) {
    header{
        height: auto;
    }
    header .navbar.fixed-top {
        position: fixed;
        width: 690px;
        top: 0;
        z-index: 11 !important;
        box-shadow: 6px 12px 13px -12px #aaa;
    }
    .container{
        max-width: 720px;
        margin: 0 auto;
        padding-left: 15px;
        padding-right: 15px;
    }
    .main-header .contents {
        display: flex;
        padding-top: 17px;
        justify-content: flex-start;
        padding-left: 0px;
    }
    .main-header .contents .welcome {
        font-size: 14px;
        font-weight: 700;
        line-height: 16.8px;
        color: #ffffff;
    }
    .navbar{
        background-color: #fff;
        border-bottom-left-radius: 50px;
        border-bottom-right-radius: 50px;
        padding-top: 19px;
        padding-bottom: 20px;
        display: inline-block;
        width: 100%;
    }
    .navbar-row{
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    .navbar hr.break{
        display: none;
    }
    .navbar .logo{
        margin-right: 0px;
    }
    .navbar a img.nav-icon{
        width: 150px;
        height: auto;
        margin-right: 0px;
    }
    .navbar .home-text{
        display: none;
    }
    .navbar .search-container-agn {
        width: 230px;
        margin-right: 10px;
        height: 46px;
        border-radius: 23px;
        border: 1px solid #f9dab9;
        box-shadow: 0px 0px 6.1px 0px #f9dab9;
        position: relative;
        top: 0px;
        margin-top: 0px;
        margin-bottom: 0px;
    }
    .navbar .shop-container{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .navbar .shop-container .whishlist {
        position: relative;
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .whishlist .cart-superscript {
        position: absolute;
        top: -1px;
        right: -4px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 3px 6px;
    }
    .navbar .shop-container .cart {
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .cart .superscript {
        position: relative;
        top: -33px;
        right: -32px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 3px 6px;
        border: none;
        outline: none;
    }
    .navbar .shop-container .profile{
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
}

@media screen and (min-width: 992px) {
    header .navbar.fixed-top {
        position: fixed;
        width: 960px;
        top: 0;
        z-index: 11 !important;
        box-shadow: 6px 12px 13px -12px #aaa;
    }
    .container{
        max-width: 960px;
        margin: 0 auto;
        padding-left: 15px;
        padding-right: 15px;
    }
    .main-header .contents {
        display: flex;
        padding-top: 17px;
        justify-content: flex-start;
    }
    .main-header .contents .welcome {
        font-size: 14px;
        font-weight: 700;
        line-height: 16.8px;
        color: #ffffff;
    }
    .navbar{
        background-color: #fff;
        border-bottom-left-radius: 50px;
        border-bottom-right-radius: 50px;
        padding-top: 19px;
        padding-bottom: 20px;
        display: inline-block;
        width: 100%;
    }
    .navbar-row{
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    .navbar .category-wrapper .category-dropdown-container {
        background: #fff;
        list-style-type: none;
        padding: 10px 5px 0px 5px;
        width: 205px;
        position: absolute;
        left: 0;
        border-radius: 5px;
        margin-top: 0;
        top: 75px;
        border: 1px solid #fbb973;
        box-shadow: 6px 12px 13px -12px #aaa;
        height: auto;
        z-index: 11;
        overflow-y: scroll;
    }
    .navbar .category-wrapper .category-dropdown-container .mobile-menu-header{
        display: none;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown{
        list-style-type: none;
        padding-left: 0;
        margin: 0;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown li{
        padding-left: 10px;
        margin-bottom: 10px;
        padding-top: 5px;
        padding-bottom: 5px;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown li:hover{
        font-weight: 500;
        background-color: #f9dab9;
        border-radius: 4px;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown li a{
        font-size: 16px;
        font-weight: 400;
        color: #000;
        text-decoration: none;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown li:hover a{
        font-weight: 600;
    }
    .navbar hr.break{
        display: none;
    }
    .navbar .logo{
        margin-right: 0px;
    }
    .navbar a img.nav-icon{
        width: 150px;
        height: auto;
        margin-right: 0px;
    }
    .navbar .home-text{
        display: block;
    }
    .navbar .search-container-agn {
        width: 280px;
        margin-right: 10px;
        height: 46px;
        border-radius: 23px;
        border: 1px solid #f9dab9;
        box-shadow: 0px 0px 6.1px 0px #f9dab9;
    }
    .navbar .shop-container{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .navbar .shop-container .whishlist {
        position: relative;
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .whishlist img#heart {
        height: auto;
        width: 21px;
        color: black;
        margin-left: 27%;
        margin-top: 29%;
    }
    .navbar .shop-container .whishlist .cart-superscript {
        position: absolute;
        top: -1px;
        right: -4px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 3px 6px;
    }
    .navbar .shop-container .cart {
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .cart .superscript {
        position: relative;
        top: -33px;
        right: -32px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 3px 6px;
        border: none;
        outline: none;
    }
    .navbar .shop-container .profile{
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    /* Footer style */
    .footer {
        height: 640px;
        background-color: #0d839b;
        width: 100%;
        margin-top: 40px;
    }
    .footer .for-mobile-res{
        display: none;
    }
    .footer .footer-header-containers {
        font-size: 28px;
        font-weight: 700;
        line-height: 32px;
        color: #f6f6f6;
        display: flex;
        justify-content: flex-start;
        text-align: center;
    }
    .footer .footer-header-containers .footer-headers {
        margin: 70px 43px 0px 0px;
        text-align: center;
        margin-bottom: 0px !important;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .footer .footer-header-containers .footer-headers .image-below {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        text-align: left;
        color: #fff;
        height: max-content;
        width: 180px;
        margin-left: 0;
        margin-right: 67px;
        margin-top: 15px;
        position: relative;
        right: 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .footer .footer-header-containers .footer-headers .image-below .cutomer {
        margin-top: 5px;
        margin-bottom: 0;
    }
    .footer .footer-header-containers .footer-headers .image-below .number {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        font-size: 18px;
        font-weight: 500;
        line-height: 24px;
        margin-top: 0;
        margin-bottom: 0;
        width: max-content;
    }
    .footer .footer-header-containers .footer-headers .image-below .nothing {
        margin-bottom: 0;
        margin-top: 0;
    }
    .footer .footer-header-containers .footer-headers .image-below .email {
        margin-top: 0;
        margin-bottom: 24px;
    }
    .footer .footer-header-containers .footer-headers .image-below .footer-icons-container {
        flex-wrap: nowrap;
        display: flex;
    }
    .footer .footer-header-containers .footer-headers .image-below .footer-icons-container  .social-icon-footer-div{
        background-color: #F6F6F6;
        display: inline-block;
        width: 37px;
        height: 37px;
        padding: 6px;
        margin-top: 0px;
        margin-bottom: 0px;
        margin-right: 10px;
        border-radius: 8px;
    }
    .footer .footer-header-containers .footer-headers .footer-icons-container .social-icon-footer #footer-icon {
        font-size: 20px;
    }
    .footer .footer-header-containers .footer-headers .image-below .footer-icons-container .social-icon-footer > i#footer-icon.fa-facebook-f {
        padding-left: 6px;
    }
    .footer .footer-header-containers .footer-headers .need-help {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        text-align: left;
        color: #fff;
        width: 180px;
        position: relative;
        right: 0;
        margin-right: 0px;
        margin-top: 0;
    }
    .footer .footer-header-containers .footer-headers .need-help .need-help-below {
        font-size: 18px;
        font-weight: 500;
        line-height: 30px;
    }
    .footer .footer-header-containers .footer-headers .need-help .need-help-below a{
        color: #ffffff;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    .footer .footer-header-containers .footer-headers .need-help .need-help-below a:hover{
        color: #ffffff;
        text-decoration: none;
    }
    .footer .other-footer-text {
        display: flex;
        justify-content: flex-start;
        margin-top: 0px;
        margin-bottom: 36px;
    }
    .footer .other-footer-text .other-footer-icon {
        display: block;
        margin-right: 180px !important;
        margin-left: 0;
    }
    .footer .other-footer-text .other-footer-icon .Download-The-App {
        font-size: 22px;
        line-height: 30px;
        color: white;
        margin-left: 0;
        margin-right: 0;
        font-weight: 600;
        margin-top: 60px;
        margin-bottom: 10px;
    }
    .footer .other-footer-text .other-footer-icon .other-footer-icon-footer {
        height: auto;
        margin-right: 10px;
        padding: 3px;
        width: 153px;
    }
    .footer .other-footer-text .parent-Payment-Methods-img {
        display: block;
    }
    .footer .other-footer-text .parent-Payment-Methods-img .Payment-Methods {
        font-size: 22px;
        line-height: 30px;
        color: white;
        font-weight: 600;
        margin-top: 60px;
        margin-bottom: 10px;
    }
    .footer .other-footer-text .parent-Payment-Methods-img .Payment-Methods-img {
        width: 312px;
        height: auto;
        border-radius: 9px;
        position: relative;
        top: 0;
        left: 0;
        padding: 5px;
        text-align: center;
    }
    .footer .other-footer-text .parent-Payment-Methods-img .Payment-Methods-img .alpha {
        margin-right: 10px;
        padding: 1px;
        height: 59px;
    }
    .footer .footer-br {
        width: 100%;
        margin-top: -2px;
        color: #bebcbd;
    }
    .footer .copyright {
        font-size: 13px;
        color: white;
        text-align: center;
        margin-top: 27px;
        margin-bottom: 0;
        letter-spacing: 1.2px;
    }
    .footer .mobile-footer-bottom{
        display: none;
    }
}

@media screen and (min-width: 1200px) {
    header .navbar.fixed-top {
        position: fixed;
        width: 1109px;
        top: 0;
        z-index: 11 !important;
        box-shadow: 6px 12px 13px -12px #aaa;
    }
    .container{
        max-width: 1140px;
        margin: 0 auto;
        padding-left: 15px;
        padding-right: 15px;
    }
    .main-header .contents {
        display: flex;
        padding-top: 17px;
        justify-content: flex-start;
    }
    .main-header .contents .welcome {
        font-size: 14px;
        font-weight: 700;
        line-height: 16.8px;
        color: #ffffff;
    }
    .navbar{
        background-color: #fff;
        border-bottom-left-radius: 50px;
        border-bottom-right-radius: 50px;
        padding-top: 19px;
        padding-bottom: 20px;
        display: inline-block;
        width: 100%;
    }
    .navbar-row{
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    .navbar .category-wrapper .category-dropdown-container {
        background: #fff;
        list-style-type: none;
        padding: 10px 5px 0px 5px;
        width: 265px;
        position: absolute;
        left: 0;
        border-radius: 5px;
        margin-top: 0;
        top: 75px;
        border: 1px solid #fbb973;
        box-shadow: 6px 12px 13px -12px #aaa;
        height: auto;
        z-index: 11;
        overflow-y: scroll;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown{
        list-style-type: none;
        padding-left: 0;
        margin-top: 0;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown li{
        padding-left: 10px;
        margin-bottom: 10px;
        padding-top: 5px;
        padding-bottom: 5px;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown li:hover{
        font-weight: 500;
        background-color: #f9dab9;
        border-radius: 4px;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown li a{
        font-size: 16px;
        font-weight: 400;
        color: #000;
        text-decoration: none;
    }
    .navbar .category-wrapper .category-dropdown-container ul.category-dropdown li:hover a{
        font-weight: 600;
    }
    .navbar hr.break{
        display: block;
        margin-left: 15px;
        margin-right: 15px;
        border-right: 1px solid #f7d1a7;
        height: 60px;
    }
    .navbar .logo{
        margin-right: 0px;
    }
    .navbar a img.nav-icon{
        width: 150px;
        height: auto;
        margin-right: 0px;
    }
    .navbar .home-text{
        display: block;
    }
    .navbar .home-text a{
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        color: #000;
    }
    .navbar .search-container-agn {
        width: 377px;
        margin-right: 0px;
        height: 46px;
        border-radius: 23px;
        border: 1px solid #f9dab9;
        box-shadow: 0px 0px 6.1px 0px #f9dab9;
    }
    .navbar .shop-container{
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 30%;
    }
    .navbar .shop-container .whishlist {
        position: relative;
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .whishlist .cart-superscript {
        position: absolute;
        top: -1px;
        right: -4px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 3px 6px;
    }
    .navbar .shop-container .cart {
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .cart .superscript {
        position: relative;
        top: -32px;
        right: -34px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 3px 6px;
        border: none;
        outline: none;
    }
    .navbar .shop-container .profile{
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .navbar .shop-container .profile a{
        color: #000;
        text-decoration: none;
    }
    .navbar .shop-container .profile .icon{
        display: block;
        width: 100%;
        text-decoration: none;
    }
    .navbar .shop-container .profile .icon img#profile-icon{
        width: 25px;
        height: auto;
        color: black;
        margin-left: 10px;
        margin-top: 3px;
    }
    .navbar .shop-container .profile .login-button{
        display: block;
        margin-left: 15px;
        font-size: 14px;
        font-weight: 600;
        line-height: 30px;
        width: max-content;
        height: 36px;
    }
    /* Footer style */
    .footer {
        height: 615px;
        background-color: #0d839b;
        width: 100%;
        margin-top: 40px;
    }
    .footer .footer-header-containers {
        font-size: 28px;
        font-weight: 700;
        line-height: 32px;
        color: #f6f6f6;
        display: flex;
        justify-content: center;
        text-align: center;
    }
    .footer .footer-header-containers .footer-headers {
        margin: 70px 20px 0px 0px;
        text-align: center;
        margin-bottom: 0px !important;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .footer .footer-header-containers .footer-headers .image-below {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        text-align: left;
        color: #fff;
        height: max-content;
        width: 180px;
        margin-right: 67px;
        margin-top: 15px;
        position: relative;
        right: 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .footer .footer-header-containers .footer-headers .need-help {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        text-align: left;
        color: #fff;
        width: 180px;
        position: relative;
        right: 0;
        margin-right: 77px;
        margin-top: 0;
    }
    .footer .footer-header-containers .footer-headers .need-help .need-help-below {
        font-size: 18px;
        font-weight: 500;
        line-height: 30px;
    }
    .footer .footer-header-containers .footer-headers .need-help .need-help-below a{
        color: #ffffff;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    .footer .footer-header-containers .footer-headers .need-help .need-help-below a:hover{
        color: #ffffff;
        text-decoration: none;
    }
    .footer .other-footer-text {
        display: flex;
        justify-content: flex-start;
        margin-top: 0px;
        margin-bottom: 10px;
    }
    .footer .other-footer-text .other-footer-icon {
        display: block;
        margin-right: 220px !important;
        margin-left: 0;
    }
    .footer .other-footer-text .other-footer-icon {
        display: block;
        margin-top: 0;
        margin-right: 0;
    }
    .footer .other-footer-text .other-footer-icon .Download-The-App {
        font-size: 22px;
        line-height: 30px;
        color: white;
        margin-left: 0;
        margin-right: 0;
        font-weight: 600;
        margin-top: 60px;
        margin-bottom: 10px;
    }
    .footer .other-footer-text .other-footer-icon .other-footer-icon-footer {
        height: auto;
        margin-right: 10px;
        padding: 3px;
    }
    .footer .other-footer-text .parent-Payment-Methods-img {
        display: block;
        width: 50%;
        text-align: left;
    }
    .footer .other-footer-text .parent-Payment-Methods-img .Payment-Methods {
        font-size: 22px;
        line-height: 30px;
        color: white;
        font-weight: 600;
        margin-top: 60px;
        margin-bottom: 10px;
    }
    .footer .other-footer-text .parent-Payment-Methods-img .Payment-Methods-img {
        width: 312px;
        height: auto;
        border-radius: 9px;
        position: relative;
        top: 0;
        left: 0;
        padding: 5px;
        text-align: center;
    }
    .footer .other-footer-text .parent-Payment-Methods-img .Payment-Methods-img .alpha {
        margin-right: 10px;
        padding: 1px;
        height: 59px;
    }
    .footer .footer-br {
        width: 100%;
        margin-top: -2px;
        color: #bebcbd;
    }
    .footer .copyright {
        font-size: 13px;
        color: white;
        text-align: center;
        margin-top: 20px;
        margin-bottom: 0;
        letter-spacing: 1.2px;
    }
}

@media screen and (min-width: 1400px) {
    header .navbar.fixed-top {
        position: fixed;
        width: 1290px;
        top: 0;
        z-index: 11 !important;
        box-shadow: 6px 12px 13px -12px #aaa;
        margin: 0 auto;
    }
    .container{
        max-width: 1320px;
        padding-left: 15px;
        padding-right: 15px;
    }
    .main-header .contents {
        display: flex;
        padding-top: 17px;
        justify-content: flex-start;
    }
    .main-header .contents .welcome {
        font-size: 14px;
        font-weight: 700;
        line-height: 16.8px;
        color: #ffffff;
    }
    .navbar{
        background-color: #fff;
        border-bottom-left-radius: 50px;
        border-bottom-right-radius: 50px;
        padding-top: 19px;
        padding-bottom: 20px;
        display: inline-block;
        width: 100%;
    }
    .navbar-row{
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    .navbar hr.break{
        display: block;
    }
    .navbar .logo{
        margin-right: 0px;
    }
    .navbar a img.nav-icon{
        width: 150px;
        height: auto;
        margin-right: 0px;
    }
    .navbar .home-text{
        display: block;
    }
    .navbar .search-container-agn {
        width: 400px;
        margin-right: 0px;
        height: 46px;
        border-radius: 23px;
        border: 1px solid #f9dab9;
        box-shadow: 0px 0px 6.1px 0px #f9dab9;
        margin-left: 70px;
    }
    .navbar .shop-container{
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }
    .navbar .shop-container .whishlist {
        position: relative;
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .whishlist .cart-superscript {
        position: absolute;
        top: -1px;
        right: -4px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 2px 6px;
    }
    .navbar .shop-container .cart {
        margin-right: 15px;
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .navbar .shop-container .cart .superscript {
        position: relative;
        top: -40px;
        right: -34px;
        font-size: 10px;
        color: white;
        background: #f77f00;
        border-radius: 50%;
        padding: 2px 6px;
        border: none;
        outline: none;
    }
    .navbar .shop-container .profile{
        width: 46px;
        height: 46px;
        border: 1px solid #f7cea1;
        border-radius: 50%;
    }
    .footer .other-footer-text .other-footer-icon {
        display: block;
        margin-right: 310px !important;
        margin-left: 0;
    }
}
/* @media screen and (min-width: 1920px) {
    header .navbar.fixed-top {
        position: fixed;
        width: 1320px;
        top: 0;
        z-index: 11 !important;
        box-shadow: 6px 12px 13px -12px #aaa;
        margin: 0 auto;
    }
} */

button#buy-now-button.disabled{
    color: #c2c2c2;
    border-color: #c2c2c2;
}
.color-gb {
    cursor: pointer;
}
span.text-danger {
    color: red;
}
button.add-to-cart-button.disabled {
    background: #c2c2c2;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #000000 !important;
}
.select2-container--default .select2-selection--single {
    border: none;
}
.select2-container--default .select2-selection--single {
    border: none;
    font-size: 12px;
}
.for-mobile-res {
    display: none;
}
/* Loader container */
.search-results {
    position: fixed;

    overflow-y: auto;
    display: none;
    z-index: 100000 !important;
}
.ax-loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8); /* Semi-transparent white */
    z-index: 9999; /* Ensure it appears on top of everything */
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px); /* Blurred background */
    transition: opacity 0.5s ease, visibility 0.5s ease;
    opacity: 1;
    visibility: visible;
}
.ax-loader-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}