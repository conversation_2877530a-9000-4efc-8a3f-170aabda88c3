<?php

declare(strict_types=1);

//namespace App\Controller;
namespace App\Controller\Api\V10;

//use App\Controller\AppController;
use App\Controller\Api\V10\AppController;
use App\Utility\RecentlyViewedHelper;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Core\Configure;
use Cake\Core\Configure\Engine\PhpConfig;
use Cake\Core\Exception\Exception;
use Cake\I18n\Time;

//use Cake\Http\Response;
//use Cake\Http\Exception\BadRequestException;
//use Cake\Http\Exception\NotFoundException;
use Cake\View\JsonView;
use Cake\Utility\Security;
use Cake\Routing\Router;
use Cake\I18n\FrozenTime;
use Cake\Utility\Text;
use \DateTime;
use Dom\Element;


class ApisController extends AppController
{
    // Declare the property
    protected $Cities;
    protected $Categories;
    protected $Users;
    protected $Customers;
    protected $OtpVerifications;
    protected $Banners;
    protected $BannerAds;
    protected $Offers;
    protected $Widgets;
    protected $Products;
    protected $ApiRequestLogs;
    protected $PaymentMethods;
    protected $Carts;
    protected $CartItems;
    protected $WidgetCategoryMappings;
    protected $Orders;
    protected $OrderItems;
    protected $SiteSettings;
    protected $Wishlists;
    protected $Reviews;
    protected $Showrooms;
    protected $Brands;
    protected $CustomerAddresses;
    protected $Loyalty;
    protected $ReviewImages;
    protected $ContentPages;
    protected $CustomerCards;
    protected $Invoices;
    protected $DeliveryCharges;
    protected $Transactions;
    protected $FaqCategories;
    protected $Faqs;
    protected $Wallets;
    protected $OrderCancellationCategories;
    protected $OrderCancellations;
    protected $OrderReturnCategories;
    protected $OrderReturns;
    protected $OrderTrackingHistories;
    protected $ProductImages;
    protected $OrderReturnImages;
    protected $ContactQueryTypes;
    protected $Municipalities;
    protected $ComingSoon;
    protected $Partners;
    protected $ProductVariantImages;
    protected $ProductDeals;
    protected $CartItemAttributes;
    protected $CreditApplications;
    protected $CartCreditPayments;
    protected $Merchants;
    protected $SupportTickets;
    protected $SupportCategories;
    protected $SupportTicketUpdates;
    protected $SupportTicketImages;
    protected $ZohoSettings;
    protected $zohoSettings;
    protected $CustomerGroupMappings;
    protected $LoyaltySettings;

    public function initialize(): void
    {
        parent::initialize();

        $this->Merchants = $this->fetchTable('Merchants');
        $this->Cities = $this->fetchTable('Cities');
        $this->Categories = $this->fetchTable('Categories');
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');
        $this->OtpVerifications = $this->fetchTable('OtpVerifications');
        $this->Banners = $this->fetchTable('Banners');
        $this->BannerAds = $this->fetchTable('BannerAds');
        $this->Offers = $this->fetchTable('Offers');
        $this->Widgets = $this->fetchTable('Widgets');
        $this->Products = $this->fetchTable('Products');
        $this->Reviews = $this->fetchTable('Reviews');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Brands = $this->fetchTable('Brands');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->ApiRequestLogs = $this->fetchTable('ApiRequestLogs');
        $this->PaymentMethods = $this->fetchTable('PaymentMethods');
        $this->Carts = $this->fetchTable('Carts');
        $this->CartItems = $this->fetchTable('CartItems');
        $this->WidgetCategoryMappings = $this->fetchTable('WidgetCategoryMappings');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->Wishlists = $this->fetchTable('Wishlists');
        $this->Loyalty = $this->fetchTable('Loyalty');
        $this->ReviewImages = $this->fetchTable('ReviewImages');
        $this->ContentPages = $this->fetchTable('ContentPages');
        $this->DeliveryCharges = $this->fetchTable('DeliveryCharges');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->CustomerCards = $this->fetchTable('CustomerCards');
        $this->Invoices = $this->fetchTable('Invoices');
        $this->FaqCategories = $this->fetchTable('FaqCategories');
        $this->Faqs = $this->fetchTable('Faqs');
        $this->Wallets = $this->fetchTable('Wallets');
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->OrderTrackingHistories = $this->fetchTable('OrderTrackingHistories');
        $this->ProductImages = $this->fetchTable('ProductImages');
        $this->OrderReturnImages = $this->fetchTable('OrderReturnImages');
        $this->ContactQueryTypes = $this->fetchTable('ContactQueryTypes');
        $this->Municipalities = $this->fetchTable('Municipalities');
        $this->ComingSoon = $this->fetchTable('ComingSoon');
        $this->Partners = $this->fetchTable('Partners');
        $this->ProductVariantImages = $this->fetchTable('ProductVariantImages');
        $this->ProductDeals = $this->fetchTable('ProductDeals');
        $this->CartItemAttributes = $this->fetchTable('CartItemAttributes');
        $this->CreditApplications = $this->fetchTable('CreditApplications');
        $this->CartCreditPayments = $this->fetchTable('CartCreditPayments');
        $this->SupportTickets = $this->fetchTable('SupportTickets');
        $this->SupportCategories = $this->fetchTable('SupportCategories');
        $this->SupportTicketUpdates = $this->fetchTable('SupportTicketUpdates'); 
        $this->SupportTicketImages = $this->fetchTable('SupportTicketImages');
        $this->ZohoSettings = $this->fetchTable('ZohoSettings');
        $this->zohoSettings = $this->fetchTable('zohoSettings');
        $this->CustomerGroupMappings = $this->fetchTable('CustomerGroupMappings');
        $this->LoyaltySettings = $this->fetchTable('LoyaltySettings');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
               
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('Mtn');
        $this->loadComponent('Wave');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('WebsiteFunction');
        $this->loadComponent('Zoho');

        $this->Authentication->addUnauthenticatedActions(['viewCartNew', 'listCity', 'listShowroom', 'listShowroomByCity', 'showroomDetailByCity', 'categories', 'parentcategories', 'subcategories', 'requestOtp', 'verifyOtp', 'resentOtp', 'signup', 'login', 'home', 'productList', 'productView', 'suggestions', 'search', 'logout', 'paymentMethods', 'addCustomerEmail', 'socialSignup', 'socialLogin', 'addCustomerMobile', 'saveMobileNo', 'resetPassword', 'viewAllDealsofTheDay', 'requestResetPassword', 'viewTopSellingProducts', 'viewNewArrivalProducts', 'viewFeaturedProducts', 'viewTopDealProducts', 'viewAllFeaturedCategories', 'orderCancellationCategoriesList', 'orderReturnCategoriesList', 'getBannerAds', 'contactUsQueryTypes', 'brandList', 'addToCart', 'updateCartItem', 'deleteCartItem', 'viewCart', 'viewTermsConditions', 'viewAboutUs', 'updateOrder', 'showroomAddress', 'sellerList', 'sellerProducts', 'listSalespersons']);
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        //$this->response = $this->response->withType('application/json');
    }

    public function viewClasses(): array
    {
        return [JsonView::class];
    }

    //S get seller list
    public function sellerList()
    {

        if ($this->request->is('get')) {

            $data = $this->request->getQuery();

            if($data['page']){
                $page = $data['page'];
            }else{
                $page = 1;
            }
            if($data['limit']){
                $limit = $data['limit'];
            }else{
                $limit = 10;
            }

            $filter_city = 0; //$data['city_id'];

            $search_str = 0; //$data['search_str'];

            $sellers = $this->Merchants->sellerList($filter_city, $search_str, $page, $limit);
            if ($sellers) {
                $result = ['status' => 'success', 'data' => $sellers];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $sellers, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S get seller products with sorting, filtering, and seller categories
    public function sellerProducts($merchant_id)
    {
        if ($this->request->is('get')) {
            // Authenticated customer (for wishlist flags inside product queries)
            $identity = $this->request->getAttribute('identity');
            $customer_id = $identity ? ($identity->get('_matchingData')['Customers']['id'] ?? 0) : 0;

            // Query params (same behavior as productList)
            $sort = $this->request->getQuery('sort', 'popularity');
            $price = $this->request->getQuery('price');
            $filtercategory = $this->request->getQuery('filter_category');
            $brand = $this->request->getQuery('brand');
            $rating = $this->request->getQuery('rating');
            $price_discount = $this->request->getQuery('price_discount');
            $page = (int)$this->request->getQuery('page', 1);
            $limit = (int)$this->request->getQuery('limit', 20);

            // Dynamic attribute filters (exclude known keys)
            $attribute_filter = [];
            if (!empty($this->request->getQuery())) {
                $queryParams = $this->request->getQuery();
                $excludedKeys = ['category', 'sort', 'page', 'limit', 'price', 'brand', 'rating', 'price_discount', 'filter_category'];
                foreach ($queryParams as $key => $value) {
                    if (!in_array($key, $excludedKeys)) {
                        $attribute_filter[$key] = $value;
                    }
                }
            }

            // Products for the seller using existing productList() logic
            $products = $this->Products->productList(0, $sort, $brand, $price, $price_discount, $rating, $page, $limit, $attribute_filter, $filtercategory, 0, $merchant_id, $customer_id);

            if (!empty($products)) {
                foreach ($products as &$product) {
                    $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                    $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                    $product['discount'] = $this->Products->getDiscount($product['id']);

                    $formattedCategories = [];
                    if (!empty($product->product_categories)) {
                        foreach ($product->product_categories as $productCategory) {
                            $formattedCategories[] = [
                                'id' => $productCategory->id,
                                'product_id' => $productCategory->product_id,
                                'category_id' => $productCategory->category_id,
                                'level' => $productCategory->level,
                                'category_name' => $productCategory->category->name
                            ];
                        }
                    }
                    $product->product_categories = $formattedCategories;

                    $product['product_image'] = '';
                    $image = $this->ProductImages->getDefaultProductImage($product['id']);
                    if ($image) {
                        $product['product_image'] = $this->Media->getCloudFrontURL($image);
                    }
                }
                unset($product);
            }

            // Categories and subcategories derived from seller products
            $categoriesTree = [];
            try {
                // Fetch distinct categories linked to this merchant's approved & active products
                $pcRows = $this->Products->ProductCategories->find()
                    ->select([
                        'ProductCategories.category_id',
                        'Categories.id',
                        'Categories.name',
                        'Categories.parent_id'
                    ])
                    ->innerJoinWith('Products', function ($q) use ($merchant_id) {
                        return $q->where([
                            'Products.merchant_id' => $merchant_id,
                            'Products.status' => 'A',
                            'Products.approval_status' => 'Approved'
                        ]);
                    })
                    ->innerJoinWith('Categories', function ($q) {
                        return $q->where(['Categories.status' => 'A']);
                    })
                    ->contain(['Categories'])
                    ->group([
                        'ProductCategories.category_id',
                        'Categories.id',
                        'Categories.name',
                        'Categories.parent_id'
                    ])
                    ->toArray();

                // Index categories by id and by parent
                $catsById = [];
                $childrenByParent = [];
                foreach ($pcRows as $row) {
                    $cat = $row->category ?? null;
                    if (!$cat) { continue; }
                    $catsById[$cat->id] = ['id' => $cat->id, 'name' => $cat->name, 'parent_id' => $cat->parent_id];
                    $parentId = $cat->parent_id ?? 0;
                    if (!isset($childrenByParent[$parentId])) { $childrenByParent[$parentId] = []; }
                    $childrenByParent[$parentId][] = $cat->id;
                }

                // Ensure parent categories are present as nodes if only children were matched
                if (!empty($catsById)) {
                    $parentIds = array_unique(array_filter(array_column($catsById, 'parent_id')));
                    $existingIds = array_keys($catsById);
                    $missingParentIds = array_values(array_diff($parentIds, $existingIds));
                    if (!empty($missingParentIds)) {
                        $parents = $this->Categories->find()
                            ->select(['id', 'name', 'parent_id'])
                            ->where(['id IN' => $missingParentIds, 'status' => 'A'])
                            ->toArray();
                        foreach ($parents as $p) {
                            $catsById[$p->id] = ['id' => $p->id, 'name' => $p->name, 'parent_id' => $p->parent_id];
                            $ppid = $p->parent_id ?? 0;
                            if (!isset($childrenByParent[$ppid])) { $childrenByParent[$ppid] = []; }
                            if (!in_array($p->id, $childrenByParent[$ppid], true)) {
                                $childrenByParent[$ppid][] = $p->id;
                            }
                        }
                    }
                }

                // Build tree for top-level parents (parent_id is null)
                foreach ($catsById as $cid => $cat) {
                    if ($cat['parent_id'] === null) {
                        $children = [];
                        foreach ($childrenByParent[$cid] ?? [] as $childId) {
                            if (isset($catsById[$childId])) {
                                $children[] = [
                                    'id' => $catsById[$childId]['id'],
                                    'name' => $catsById[$childId]['name']
                                ];
                            }
                        }
                        $categoriesTree[] = [
                            'id' => $cat['id'],
                            'name' => $cat['name'],
                            'sub_categories' => $children
                        ];
                    }
                }

                // If no explicit parents captured, still include leaf categories as standalone entries
                if (empty($categoriesTree) && !empty($catsById)) {
                    foreach ($catsById as $cat) {
                        if ($cat['parent_id'] !== null) {
                            $categoriesTree[] = [
                                'id' => $cat['id'],
                                'name' => $cat['name'],
                                'sub_categories' => []
                            ];
                        }
                    }
                }
            } catch (\Throwable $e) {
                $categoriesTree = [];
            }

            $alldata = [
                'categories' => $categoriesTree,
                'products' => $products
            ];

            $result = ['status' => 'success', 'data' => $alldata];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }
    

    // S get all cities detail
    public function listCity()
    {

        if ($this->request->is('get')) {

            $cities = $this->Cities->listCity();
            if ($cities) {
                $result = ['status' => 'success', 'data' => $cities];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $cities, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    // S get all showrooms
    public function listShowroom()
    {

        if ($this->request->is('get')) {

            $showrooms = $this->Showrooms->listShowroom();
            if ($showrooms) {
                $result = ['status' => 'success', 'data' => $showrooms];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $showrooms, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    // S get showloom list based on city id
    public function listShowroomByCity($city_id)
    {

        if ($this->request->is('get')) {

            $showrooms = $this->Showrooms->listShowroomByCity($city_id);
            if ($showrooms) {
                $result = ['status' => 'success', 'data' => $showrooms];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $showrooms, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    // S get showloom detail based on city id
    public function showroomDetailByCity($city_id)
    {

        if ($this->request->is('get')) {

            $showrooms = $this->Showrooms->showroomDetailByCity($city_id);
            if ($showrooms) {
                $result = ['status' => 'success', 'data' => $showrooms];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $showrooms, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S logout
    public function logout()
    {

        if ($this->request->is('get')) {

            $result = $this->Authentication->getResult();
            if ($result->isValid()) {
                $this->Authentication->logout();
                $result = ['status' => 'success', 'message' => __('You are successfully logged out')];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'message' => __('You are already logged out')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S categories // updated by M
    public function categories()
    {

        if ($this->request->is('get')) {

            // $categories = $this->Categories->allCategories();

            $categories = $this->Categories->find('threaded')
                ->select(['id', 'name', 'parent_id', 'url_key', 'category_icon', 'display_order'])
                ->where(['status' => 'A'])
                ->order(['display_order' => 'ASC'])
                ->toArray();

            $categories = $this->processCategories($categories);

            // foreach ($categories as &$category) {
            //     if (!empty($category['category_icon'])) {
            //         $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
            //     }
            // }
            // unset($category);

            // foreach ($categories as &$category) {
            //     if(!empty($category->child_categories)){
            //         foreach ($category->child_categories as &$child) {
            //             if (!empty($child['category_icon'])) {
            //                 $child['category_icon'] = $this->Media->getCloudFrontURL($child['category_icon']);
            //             }
            //         }
            //         unset($child);
            //     }
            // }

            if ($categories) {
                $result = ['status' => 'success', 'data' => $categories];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $categories, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    private function processCategories($categories)
    {
        foreach ($categories as &$category) {
            // Process category icon
            if (!empty($category['category_icon'])) {
                $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
            }

            // Check for child categories and process them recursively
            if (!empty($category['children'])) {
                $category['child_categories'] = $this->processCategories($category['children']);
                unset($category['children']); // Remove the raw 'children' key
            } else {
                $category['child_categories'] = [];
            }
        }

        return $categories;
    }

    //S categories  // Updated by M
    public function parentcategories()
    {

        if ($this->request->is('get')) {

            $categories = $this->Categories->parentCategories();

            foreach ($categories as &$category) {
                if (!empty($category['category_icon'])) {
                    $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
                }
            }
            unset($category);

            if ($categories) {
                $result = ['status' => 'success', 'data' => $categories];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $categories, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S categories  // Updated by M
    public function subcategories($parent_id)
    {

        if ($this->request->is('get')) {

            $categories = $this->Categories->subCategories($parent_id);

            foreach ($categories as &$category) {
                if (!empty($category['category_icon'])) {
                    $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
                }
            }
            unset($category);

            if ($categories) {
                $result = ['status' => 'success', 'data' => $categories];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $categories, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S home   // Updated by M
    public function home()
    {

        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $customer_id = $identity->get('_matchingData')['Customers']['id'];
            } else {
                $customer_id = 0;
            }

            $banners = $this->Banners->homeBanners('mobile');

            foreach ($banners as &$banner) {
                if (!empty($banner['mobile_banner'])) {
                    $banner['mobile_banner'] = $this->Media->getCloudFrontURL($banner['mobile_banner']);
                }
            }
            unset($banner);

            $comingSoon = $this->ComingSoon->homeComingSoon();

            foreach ($comingSoon as $item) {
                if (!empty($item['mob_image'])) {
                    $item['mob_image'] = $this->Media->getCloudFrontURL($item['mob_image']);
                }
            }
            unset($item);

            $categories = $this->Categories->parentCategories();

            foreach ($categories as &$category) {
                if (!empty($category['category_icon'])) {
                    $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
                }
            }
            unset($category);

            $brands = $this->Brands->GetBrands();

            foreach ($brands as &$brand) {
                if (!empty($brand['brand_logo'])) {
                    $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
                }

                if (!empty($brand['web_banner'])) {
                    $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
                }
            }
            unset($brand);

            $widgets = $this->Widgets->homeWidgets('mobile');
            // echo "<pre>"; print_r($widgets); die;
            foreach ($widgets as $widget) {
                $products = [];

                $categoryIds = $this->Widgets->WidgetCategoryMappings->find()
                    ->select(['category_id'])
                    ->where(['widget_id' => $widget['id'], 'status' => 'A'])
                    ->toArray();

                $categoryIds = array_column($categoryIds, 'category_id');
                switch ($widget['widget_type']) {

                    case 'Deals of the Day':
                        $widget_type = 'deal';
                        $products = $this->Products->getDealOfTheDayProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $customer_id);
                    //    echo "<pre>Deals of the day test "; print_r($products); exit;
                    //     foreach ($products as $product) {
                    //         echo $product->name;
                    //     }
                    //     exit;
                    
                    //  echo "<pre> deals of the day :**********"; print_r($products); exit;
                        break;
                    case 'Best Seller':
                        $widget_type = 'best_selling';
                        $products = $this->Products->getTopSellingProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $customer_id);
                        // echo "<pre>"; print_r($products->toArray()); die;
                        break;
                    case 'New Arrivals':
                        $widget_type = 'new_arrival';
                        $products = $this->Products->getNewArrivalProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $customer_id);
                        // echo "<pre>"; print_r($products); die;
                        break;
                    case 'Featured':
                        $widget_type = 'featured';
                        $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $customer_id);
                        break;
                    case 'Custom':
                        $widget_type = 'custom';
                        $products = $this->Products->getCustomWidgetProducts($widget['no_of_product'], $categoryIds, $widget['product_preference']);
                        break;
                        // echo "<pre
                        // case 'Special Offers':
                        // $widget = 'special_offers';
                        // $products = $this->Products->getSpecialOffersProducts($widget['no_of_product'], $categoryIds);
                        // break;
                }
                $modifiedProducts = [];
                foreach ($products as $product) {
                    $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                    $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                    $product['discount'] = $this->Products->getDiscount($product['id']);
                    $product['availability_status'] = $this->Products->getAvailabilityStatus($product['id']);
                    
                    // Add wishlist status for product
                    if ($identity) {
                        $product['is_product_favorite'] = $this->Wishlists->whishListCheckSingle($identity['id'], $product['id']);
                    } else {
                        $product['is_product_favorite'] = false;
                    }

                    $product['product_image'] = '';
                    $image = $this->ProductImages->getDefaultProductImage($product['id']);
                    if ($image) {
                        $product['product_image'] = $this->Media->getCloudFrontURL($image);

                        // For custom widgets and deals of the day, include product variants if available
                        if ($widget_type === 'custom' || $widget_type === 'deal') {
                            $variants = $this->ProductVariants->find()
                                ->select([
                                    'ProductVariants.id',
                                    'ProductVariants.product_id',
                                    'ProductVariants.variant_name',
                                    'ProductVariants.reference_name',
                                    'ProductVariants.sku',
                                    'ProductVariants.variant_size',
                                    'ProductVariants.variant_weight',
                                    'ProductVariants.purchase_price',
                                    'ProductVariants.sales_price',
                                    'ProductVariants.promotion_price',
                                    'ProductVariants.quantity',
                                    'ProductVariants.variant_description',
                                    'ProductVariants.status',
                                    'product_variant_discount' => $this->ProductVariants->find()->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)')
                                ])
                                ->where(['ProductVariants.product_id' => $product['id'], 'ProductVariants.status' => 'A'])
                                ->contain([
                                    'ProductVariantImages' => function ($q) {
                                        return $q->where(['ProductVariantImages.status' => 'A'])
                                                 ->order(['ProductVariantImages.image_default' => 'DESC', 'ProductVariantImages.id' => 'ASC']);
                                    }
                                ])
                                ->toArray();

                            // Map variant images to CloudFront URLs and add wishlist status
                            foreach ($variants as &$variant) {
                                if (!empty($variant->product_variant_images)) {
                                    foreach ($variant->product_variant_images as &$vimg) {
                                        if (!empty($vimg->image)) {
                                            $vimg->image = $this->Media->getCloudFrontURL($vimg->image);
                                        }
                                        if (!empty($vimg->video)) {
                                            $vimg->video = $this->Media->getCloudFrontURL($vimg->video);
                                        }
                                    }
                                    unset($vimg);
                                }
                                
                                // Add wishlist status for variant
                                if ($identity) {
                                    $variant->is_variant_favorite = $this->Wishlists->whishListCheckVariant($identity['id'], $product['id'], $variant->id);
                                } else {
                                    $variant->is_variant_favorite = false;
                                }
                            }
                            unset($variant);

                            $product['product_variants'] = $variants;
                        }

                        $modifiedProducts[] = $product;
                    }
                                       
                }
                $response[$widget_type][] = [
                    'widget' => $widget,
                    'products' => $modifiedProducts
                ];
            }

            $widgets = $response;
            $middle_banner_ads = $this->BannerAds->getBannerAds('mobile', 'Middle', 'Home Page');

            foreach ($middle_banner_ads as &$middle) {
                if (!empty($middle['mobile_image'])) {
                    $middle['mobile_image'] = $this->Media->getCloudFrontURL($middle['mobile_image']);
                }
            }
            unset($middle);

            $above_footer_banner_ads = $this->BannerAds->getBannerAds('mobile', 'Above Footer', 'Home Page');

            foreach ($above_footer_banner_ads as &$above) {
                if (!empty($above['mobile_image'])) {
                    $above['mobile_image'] = $this->Media->getCloudFrontURL($above['mobile_image']);
                }
            }
            unset($above);

            $banner_ads = $this->BannerAds->homeBannerAds('mobile');
            foreach ($banner_ads as &$banner_ad) {
                if (!empty($banner_ad['mobile_image'])) {
                    $banner_ad['mobile_image'] = $this->Media->getCloudFrontURL($banner_ad['mobile_image']);
                }
            }
            unset($banner_ad);

            $featured_cats = $this->Categories->featuredCategories();
            foreach ($featured_cats as &$featured_cat) {
                if (!empty($featured_cat['category_icon'])) {
                    $featured_cat['category_icon'] = $this->Media->getCloudFrontURL($featured_cat['category_icon']);
                }
            }
            unset($featured_cat);

            $offers = $this->Offers->homeOffers('mobile');

            foreach ($offers as &$offer) {
                if (!empty($offer['mobile_image'])) {
                    $offer['mobile_image'] = $this->Media->getCloudFrontURL($offer['mobile_image']);
                }
            }
            unset($offer);

            $topdeals = $this->Offers->getTopDeals($limit = 5, 'mobile')->toArray();
            foreach ($topdeals as $topdeal) {
                if ($topdeal['mobile_image'] != '' || $topdeal['mobile_image'] != null) {
                    $topdeal['mobile_image'] = $this->Media->getCloudFrontURL($topdeal['mobile_image']);
                }
            }
            unset($topdeal);

            $dealsOftheDay = $this->ProductDeals->getTopProdDeals($limit = 5);
                foreach ($dealsOftheDay as $dindex => $deal) {

                    $dealsOftheDay[$dindex]['rating'] = $this->Reviews->getAverageRating($deal['product_id']);
                    $dealsOftheDay[$dindex]['total_review'] = $this->Reviews->getTotalReviews($deal['product_id']);
                    
                    // Add wishlist status for product
                    if ($identity) {
                        $dealsOftheDay[$dindex]['is_product_favorite'] = $this->Wishlists->whishListCheckSingle($identity['id'], $deal['product_id']);
                    } else {
                        $dealsOftheDay[$dindex]['is_product_favorite'] = false;
                    }
                    // Calculate discount percentage
                    $sales_price = isset($deal['sales_price']) ? (float)$deal['sales_price'] : 0.0;
                    $promotion_price = isset($deal['promotion_price']) ? (float)$deal['promotion_price'] : 0.0;
                    $discount = 0.0;
                    if ($sales_price > 0 && $promotion_price > 0 && $sales_price > $promotion_price) {
                        $discount = round((($sales_price - $promotion_price) * 100) / $sales_price, 2);
                    }
                    $dealsOftheDay[$dindex]['discount'] = $discount;
                    if (!empty($deal->product) && !empty($deal->product->product_images)) {
                        foreach ($deal->product->product_images as $image) {
                            if (!empty($image->image)) {
                                $image->image = $this->Media->getCloudFrontURL($image->image);
                            }
                        }
                    }

                    // Add product variants for deals of the day
                    if (!empty($deal['product_id'])) {
                        $variants = $this->ProductVariants->find()
                            ->select([
                                'ProductVariants.id',
                                'ProductVariants.product_id',
                                'ProductVariants.variant_name',
                                'ProductVariants.reference_name',
                                'ProductVariants.sku',
                                'ProductVariants.variant_size',
                                'ProductVariants.variant_weight',
                                'ProductVariants.purchase_price',
                                'ProductVariants.sales_price',
                                'ProductVariants.promotion_price',
                                'ProductVariants.quantity',
                                'ProductVariants.variant_description',
                                'ProductVariants.status',
                                'product_variant_discount' => $this->ProductVariants->find()->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)')
                            ])
                            ->where(['ProductVariants.product_id' => $deal['product_id'], 'ProductVariants.status' => 'A'])
                            ->contain([
                                'ProductVariantImages' => function ($q) {
                                    return $q->where(['ProductVariantImages.status' => 'A'])
                                             ->order(['ProductVariantImages.image_default' => 'DESC', 'ProductVariantImages.id' => 'ASC']);
                                }
                            ])
                            ->toArray();

                        // Map variant images to CloudFront URLs and add wishlist status
                        foreach ($variants as &$variant) {
                            if (!empty($variant->product_variant_images)) {
                                foreach ($variant->product_variant_images as &$vimg) {
                                    if (!empty($vimg->image)) {
                                        $vimg->image = $this->Media->getCloudFrontURL($vimg->image);
                                    }
                                    if (!empty($vimg->video)) {
                                        $vimg->video = $this->Media->getCloudFrontURL($vimg->video);
                                    }
                                }
                                unset($vimg);
                            }
                            
                            // Add wishlist status for variant
                            if ($identity) {
                                $variant->is_variant_favorite = $this->Wishlists->whishListCheckVariant($identity['id'], $deal['product_id'], $variant->id);
                            } else {
                                $variant->is_variant_favorite = false;
                            }
                        }
                        unset($variant);

                        $dealsOftheDay[$dindex]['product_variants'] = $variants;
                    }
                }

            unset($topdeal);

            $BrandsList = $this->Brands->find()->order('RAND()')->limit(5)->toArray();
            foreach ($BrandsList as &$brand) {
                if (!empty($brand['web_banner'])) {
                    $brand['mobile_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
                }
                if (!empty($brand['brand_logo'])) {
                    $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
                }
            }
            unset($brand);

            // Top 20 marketplace sellers
            $marketplaceSellers = $this->Merchants->getTopSellers();
            foreach ($marketplaceSellers as &$seller) {
                if (is_array($seller)) {
                    if (!empty($seller['merchant_logo'])) {
                        $seller['merchant_logo'] = $this->Media->getCloudFrontURL($seller['merchant_logo']);
                    }
                    if (!empty($seller['web_banner'])) {
                        $seller['web_banner'] = $this->Media->getCloudFrontURL($seller['web_banner']);
                    }
                } else {
                    if (!empty($seller->merchant_logo)) {
                        $seller->merchant_logo = $this->Media->getCloudFrontURL($seller->merchant_logo);
                    }
                    if (!empty($seller->web_banner)) {
                        $seller->web_banner = $this->Media->getCloudFrontURL($seller->web_banner);
                    }
                }
            }
            unset($seller);

            $alldata = ['banners' => $banners, 'comingSoon' => $comingSoon, 'categories' => $categories, 'brands' => $brands, 'widgets' => $widgets, 'banner_ads' => $banner_ads, 'featured_cats' => $featured_cats, 'offers' => $offers, 'top_deals' => $topdeals, 'deals_of_the_day' => $dealsOftheDay, 'middle_banner_ads' => $middle_banner_ads, 'above_footer_banner_ads' => $above_footer_banner_ads, 'featured_brands' => $BrandsList, 'marketplace_sellers' => $marketplaceSellers];

            $recently_viewed = [];
            if ($identity && !empty($identity->id)) {
                $recently_viewed = \App\Utility\RecentlyViewedHelper::getRecentViewedProducts($identity->id, 20);
                foreach ($recently_viewed as &$rv) {
                    if (!empty($rv->product_image)) {
                        $rv->product_image = $this->Media->getCloudFrontURL($rv->product_image);
                    }
                    // Map product variant images to CloudFront URLs
                    if (!empty($rv->product) && !empty($rv->product->product_variants)) {
                        foreach ($rv->product->product_variants as &$variant) {
                            if (!empty($variant->product_variant_images)) {
                                foreach ($variant->product_variant_images as &$vimg) {
                                    if (!empty($vimg->image)) {
                                        $vimg->image = $this->Media->getCloudFrontURL($vimg->image);
                                    }
                                    if (!empty($vimg->video)) {
                                        $vimg->video = $this->Media->getCloudFrontURL($vimg->video);
                                    }
                                }
                                unset($vimg);
                            }
                        }
                        unset($variant);
                    }
                }
                unset($rv);
            }
            $alldata['recently_viewed'] = $recently_viewed;

            $result = ['status' => 'success', 'data' => $alldata];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
                $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S recently viewed products
    public function recentlyViewedProducts()
    {
        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
                $limit = (int)$this->request->getQuery('limit', 20);
                $items = RecentlyViewedHelper::getRecentViewedProducts($identity->id, $limit);
                foreach ($items as &$item) {
                    // product image
                    if (!empty($item->product_image)) {
                        $item->product_image = $this->Media->getCloudFrontURL($item->product_image);
                    }
                    // product variant images
                    if (!empty($item->product) && !empty($item->product->product_variants)) {
                        foreach ($item->product->product_variants as &$variant) {
                            if (!empty($variant->product_variant_images)) {
                                foreach ($variant->product_variant_images as &$vimg) {
                                    if (!empty($vimg->image)) {
                                        $vimg->image = $this->Media->getCloudFrontURL($vimg->image);
                                    }
                                    if (!empty($vimg->video)) {
                                        $vimg->video = $this->Media->getCloudFrontURL($vimg->video);
                                    }
                                }
                                unset($vimg);
                            }
                        }
                        unset($variant);
                    }
                }
                unset($item);
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $items,
                    'message' => __('Recently viewed products fetched successfully')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S  GET /api/v1.0/products?category=electronics&sort=price&order=asc&page=1&limit=20
    public function productList($category = 0, $product_brand = 0, $merchant_id = 0)
    {

        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $customer_id = $identity->get('_matchingData')['Customers']['id'];
            } else {
                $customer_id = 0;
            }

            // Fetch query parameters with default values

            $sort = $this->request->getQuery('sort', 'popularity'); // Defaults to 'popularity' if not provided

            //filter
            $price = $this->request->getQuery('price');
            $filtercategory = $this->request->getQuery('filter_category'); // id
            $brand = $this->request->getQuery('brand');
            $rating = $this->request->getQuery('rating');
            $price_discount = $this->request->getQuery('price_discount');

            $page = $this->request->getQuery('page', 1); // Defaults to 1
            $limit = $this->request->getQuery('limit', 20); // Defaults to 20

            // Dynamic Attribute Filters
            $attribute_filter = [];
            if (!empty($this->request->getQuery())) {
                $queryParams = $this->request->getQuery();

                // Exclude fixed filters from dynamic filtering
                $excludedKeys = ['category', 'sort', 'page', 'limit', 'price', 'brand', 'rating', 'price_discount'];

                foreach ($queryParams as $key => $value) {
                    if (!in_array($key, $excludedKeys)) {
                        // Assuming dynamic attributes are stored in ProductAttributes or a similar table
                        $attribute_filter[$key] = $value;
                    }
                }
            }

            $products = $this->Products->productList($category, $sort, $brand, $price, $price_discount, $rating, $page, $limit, $attribute_filter, $filtercategory, $product_brand, $merchant_id, $customer_id);

            if (!empty($products)) {
                foreach ($products as &$product) {

                    $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                    $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                    $product['discount'] = $this->Products->getDiscount($product['id']);

                    $formattedCategories = [];
                    if (!empty($product->product_categories)) {
                        foreach ($product->product_categories as $productCategory) {
                            $formattedCategories[] = [
                                'id' => $productCategory->id,
                                'product_id' => $productCategory->product_id,
                                'category_id' => $productCategory->category_id,
                                'level' => $productCategory->level,
                                'category_name' => $productCategory->category->name // Using the category name from the joined category
                            ];
                        }
                    }

                    $product->product_categories = $formattedCategories;

                    $product['product_image'] = '';
                    $image = $this->ProductImages->getDefaultProductImage($product['id']);
                    if ($image) {
                        $product['product_image'] = $this->Media->getCloudFrontURL($image);
                    }
                }
                unset($product);
            }

            // Get total count for pagination
            $totalProducts = $this->Products->getTotalProductCount($category, $sort, $brand, $price, $price_discount, $rating, $attribute_filter, $filtercategory, $product_brand, $merchant_id, $customer_id);
            
            // Calculate pagination details
            $totalPages = ceil($totalProducts / $limit);
            $pagination = [
                'total_items' => $totalProducts,
                'total_pages' => $totalPages,
                'current_page' => (string)$page,
                'limit' => (string)$limit,
                'has_more' => $page < $totalPages
            ];

            $alldata = [
                'products' => $products,
                'pagination' => $pagination
            ];

            $result = ['status' => 'success', 'data' => $alldata];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S change done by //M changes required for related products
    public function productView()
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');
             if ($identity) {
                $customer_id = $identity->get('_matchingData')['Customers']['id'];
            }

            // Fetch query parameters with default values
            $product = $this->request->getQuery('product'); // id
           
            $review_limit = 10;
            $product_detail = $this->Products->productView($product);
           
            $deeplink = Router::url(
                [
                    'controller' => 'Website',
                    'action' => 'product',
                     $product_detail->url_key,
                    'prefix' => false
                ],
                true // Set to true to generate a full URL including the domain
            );

            $product_link = Router::url([
                'controller' => 'Website',
                'action' => 'product',
                $product_detail->id,
                'prefix' => false
            ], true);
            if (!$product_detail) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Product not found')
                ];
                goto label;
            }

            if($identity && $customer_id) RecentlyViewedHelper::storeRecentlyViewed($product, $identity); 
            
            $product_detail->rating = round($this->Reviews->getAverageRating($product),1);
            $product_detail->total_review = $this->Reviews->getTotalReviews($product);
            $product_detail->discount = $this->Products->getDiscount($product);
            $product_detail->availability_status = $this->Products->getAvailabilityStatus($product);
            $similar_products = $this->Products->getSimilarProducts($product);
            $isFavorite = false;
            if($customer_id){
                $isFavorite = $this->Wishlists->exists([
                    'customer_id' => $customer_id,
                    'product_id' => $product,
                    'product_variant_id IS' => NULL
                ]);  
            }            

            $product_detail->is_favorite = $isFavorite ? true : false;

            $product_detail->product_image = '';
            $image = $this->ProductImages->getDefaultProductImage($product);
            if ($image) {
                $product_detail->product_image = $this->Media->getCloudFrontURL($image);
            }

            if (!empty($product_detail->product_images) && (is_array($product_detail->product_images) || is_object($product_detail->product_images))) {
                foreach ($product_detail->product_images as &$image) {
                    if (!empty($image['image'])) {
                        $image['image'] = $this->Media->getCloudFrontURL($image['image']);
                    }
                    if (!empty($image['video'])) {
                        $image['video'] = $this->Media->getCloudFrontURL($image['video']);
                    }
                }
                unset($image);
            }

            if (!empty($product_detail->product_variants) && is_iterable($product_detail->product_variants)) {
                foreach ($product_detail->product_variants as $variant) {
                    if (!empty($variant->product_variant_images) && is_iterable($variant->product_variant_images)) {
                        foreach ($variant->product_variant_images as &$image) {
                            if (!empty($image['image'])) {
                                $image['image'] = $this->Media->getCloudFrontURL($image['image']);
                            }
                            if (!empty($image['video'])) {
                                $image['video'] = $this->Media->getCloudFrontURL($image['video']);
                            }
                        }
                        unset($image);
                    }

                    $isVariantFavorite = false;
                    if($customer_id){
                        $isVariantFavorite = $this->Wishlists->exists([
                            'customer_id' => $customer_id,
                            'product_id' => $product,
                            'product_variant_id' => $variant->id
                        ]);  
                    }            

                    $variant->is_favorite = $isVariantFavorite ? true : false;
                }
            }

            $breadcrumb = [];
            if (!empty($product_detail->product_categories)) {
                foreach ($product_detail->product_categories as $productCategory) {
                    $categoryBreadcrumb = $this->Categories->getBreadcrumb($productCategory->category_id);
                    $breadcrumb = array_merge($breadcrumb, $categoryBreadcrumb);
                }
                $breadcrumb = array_unique($breadcrumb, SORT_REGULAR);
            }

            // Add product name to the breadcrumb
            $breadcrumb[] = [
                'id' => $product_detail->id,
                'name' => $product_detail->name
            ];

            $formattedCategories = [];
            if (!empty($product_detail->product_categories)) {
                foreach ($product_detail->product_categories as $productCategory) {
                    $formattedCategories[] = [
                        'id' => $productCategory->id,
                        'product_id' => $productCategory->product_id,
                        'category_id' => $productCategory->category_id,
                        'level' => $productCategory->level,
                        'category_name' => $productCategory->category->name // Using the category name from the joined category
                    ];
                }
            }

            $product_detail->product_categories = $formattedCategories;
            $product_detail->product_url = $deeplink;
            $product_detail->product_link = $product_link;

            if (!empty($similar_products) && is_array($similar_products)) {
                foreach ($similar_products as &$similar_product) {

                    $similar_product['product_image'] = '';
                    $image = $this->ProductImages->getDefaultProductImage($similar_product['id']);
                    if ($image) {
                        $similar_product['product_image'] = $this->Media->getCloudFrontURL($image);
                    }
                }
                unset($similar_product);
            }

            $rating_reviews = $this->Reviews->getproductReviews($product, $review_limit);
            //echo "<pre>"; print_r($rating_reviews); exit;

            // Add CloudFront URLs for profile photos and images
            foreach ($rating_reviews as $review) {
                // profile_photo
                if (!empty($review->customer?->profile_photo)) {
                    $review->customer->profile_photo = $this->Media->getCloudFrontURL($review->customer->profile_photo);
                }

                // review_images
                foreach ($review->review_images as $image) {
                    $image->image_url = $this->Media->getCloudFrontURL($image->image_url);
                }
            }

            $alldata = ['breadcrumb' => $breadcrumb, 'product_detail' => $product_detail, 'similar_products' => $similar_products, 'reviews' => $rating_reviews];

            $result = ['status' => 'success', 'data' => $alldata];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //autocomplete
    public function suggestions()
    {

        if ($this->request->is('get')) {

            // Get the search term from the query string
            $query = $this->request->getQuery('q');

            // Fetch suggestions from all relevant tables
            $productSuggestions = $this->Products->getProductsSuggestions($query);
            $brandSuggestions = $this->Brands->getBrandSuggestions($query);
            $categorySuggestions = $this->Categories->getCategorySuggestions($query);

            // Combine results
            $suggestions = array_merge($productSuggestions, $brandSuggestions, $categorySuggestions);

            if (!empty($suggestions)) {
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $suggestions,
                    'message' => __('Results for search product')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'data' => $suggestions,
                    'message' => __('No results found')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //search
    public function search()
    {

        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $customer_id = $identity->get('_matchingData')['Customers']['id'];
                $userId = $identity->id;
            } else {
                $userId = null;
                $customer_id = null;
            }
            $query = $this->request->getQuery('q');
            $products = $this->Products->search($query, $customer_id);
            foreach ($products as &$product) {
                $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                $product['discount'] = $this->Products->getDiscount($product['id']);
                $formattedCategories = [];
                if (!empty($product->product_categories)) {
                    foreach ($product->product_categories as $productCategory) {
                        $formattedCategories[] = [
                            'id' => $productCategory->id,
                            'product_id' => $productCategory->product_id,
                            'category_id' => $productCategory->category_id,
                            'level' => $productCategory->level,
                            'is_favourite' => $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product['id']),
                            'category_name' => $productCategory->category->name // Using the category name from the joined category
                        ];
                    }
                }

                $product->product_categories = $formattedCategories;
                $product['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($product['id']);
                if ($image) {
                    $product['product_image'] = $this->Media->getCloudFrontURL($image);
                }

                // Set is_product_favorite based on wishlist logic for product or variant
                $isProductFavorite = false;
                if ($userId !== null) {
                    // If this is a variant product (has product_variant_id), check for both product_id and variant_id
                    if (!empty($product['product_variant_id'])) {
                        $isProductFavorite = $this->Wishlists->exists([
                            'customer_id' => $customer_id,
                            'product_id' => $product['id'],
                            'product_variant_id' => $product['product_variant_id']
                        ]);
                    } else {
                        // Otherwise, check for product_id only (and variant_id is null)
                        $isProductFavorite = $this->Wishlists->exists([
                            'customer_id' => $customer_id,
                            'product_id' => $product['id'],
                            'product_variant_id IS' => null
                        ]);
                    }
                }
                $product['is_product_favorite'] = $isProductFavorite ? true : false;
                unset($product['product_variants']);
            }
            unset($product);

            $result = ['status' => 'success', 'data' => $products];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S add address // Changed by M
    public function addAddress()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {

                $customer_id = $identity->get('_matchingData')['Customers']['id'];
                $data['customer_id'] = $customer_id;

                // Add country codes for phone numbers if provided
                if (isset($data['country_code1'])) {
                    $data['country_code1'] = $data['country_code1'];
                }
                if (isset($data['country_code2'])) {
                    $data['country_code2'] = $data['country_code2'];
                }

                $add_address = $this->CustomerAddresses->addAddress($data);

                if ($add_address) {

                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => $data,
                        'message' => __('Address successfully added')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                } else {
                    $result = ['status' => 'error', 'message' => __('Please try again!')];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function editAddress($id = null)
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {

                $edit_address = $this->CustomerAddresses->editAddress($id, $data);

                if ($edit_address === false) {
                    $result = ['status' => 'error', 'message' => __('Address not found or could not be updated.')];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => $edit_address,
                        'message' => __('Address successfully updated')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function deleteAddress($id = null)
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {

                $attributes = [
                    'status' => 'D'
                ];

                $delete_result = $this->CustomerAddresses->editAddress($id, $attributes);

                if ($delete_result) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => __('Address successfully deleted')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'error', 'message' => __('Address not found or deletion failed')];
                    $this->response = $this->response->withStatus(200);
                }
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function listAddress()
    {

        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {

                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $addresses = $this->CustomerAddresses->listAddress($customerId);

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $addresses,
                    'message' => __('Address listing successfully')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function paymentMethods()
    {

        if ($this->request->is('get')) {

            $methods = $this->PaymentMethods->listMethods();

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $methods,
                'message' => __('Payment method listing successfully')
            ];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M send OTP to mobile or email as per request parameter pass
    public function requestOtp()
    {

        if ($this->request->is('post')) {
            try {

                $data = $this->request->getData();
                if (isset($data['country_code']) && !empty($data['country_code'])) {
                    // Remove the '+' sign from the 'country_code'
                    $data['country_code'] = trim($data['country_code'], '+');
                }
                $request_attr = [
                    'request' => json_encode($data),
                    'user_id' => '',
                    'api_name' => 'requestOtp',
                    //  'created_at'=>Time::now(),
                    //  'updated_at'=>Time::now()
                ];

                $this->ApiRequestLogs->add_new_log($request_attr);

                $data['user_type'] = 'Customer';

                if (empty($data['country_code']) && empty($data['mobile_no']) && empty($data['email'])) {
                    $result = ['status' => 'error', 'message' => __('Mobile number or email is required')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                if (!empty($data['mobile_no']) && !empty($data['country_code'])) {

                    if (!preg_match('/^[0-9]{1,4}\s?[0-9]{10}$/', $data['country_code'] . $data['mobile_no'])) {
                        $result = ['status' => 'error', 'message' => __('Enter valid mobile number')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }

                    $user_identifier = $data['country_code'] . $data['mobile_no'];
                    $otp_type = 'mobile';

                    $user = $this->Users->find()->where(['mobile_no' => $data['mobile_no'], 'country_code' => $data['country_code'], 'status' => 'A'])->first();
                    // if (!$user) {
                    //     $result = ['status' => 'error', 'message' => __('User is not found or is disabled.')];
                    //     $this->response = $this->response->withStatus(200);
                    //     goto label;
                    // }
                }

                if (!empty($data['email'])) {

                    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                        $result = ['status' => 'error', 'message' => __('Enter valid email address')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }

                    $user_identifier = $data['email'];
                    $otp_type = 'email';

                    $user = $this->Users->find()->where(['email' => $data['email'], 'status' => 'A'])->first();
                    // if (!$user) {
                    //     $result = ['status' => 'error', 'message' => __('User is not found or is disabled.')];
                    //     $this->response = $this->response->withStatus(200);
                    //     goto label;
                    // }
                }

                if (!empty($data['mobile_no']) && !empty($data['country_code']) && $data['new_login'] == true) {
                    $usermobile = $this->Users->checkMobileExist(trim($data['mobile_no']), trim($data['country_code']));
                    if ($usermobile) {
                        $result = ['status' => 'error', 'message' => __('Mobile no already exist')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }
                }
                if (!empty($data['email']) && $data['new_login'] == true) {
                    $userid = $this->Users->checkEmailExist(trim($data['email']));
                    if ($userid) {
                        $result = ['status' => 'error', 'message' => __('Email already exist')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }
                }

                $otp = $this->generateOTP();
                $otp_saved = $this->OtpVerifications->save_otp($user_identifier, $otp, $otp_type);

                if ($otp_saved) {

                    if (!empty($data['mobile_no']) && !empty($data['country_code'])) {

                        $otpResult = $this->Global->sendOtp($data['country_code'] . $data['mobile_no'], $otp);

                        if ($otpResult['success']) {
                            $result = [
                                'status' => 'success',
                                'code' => 200,
                                'data' => [
                                    'otp' => $otp
                                ],
                                'message' => __('OTP has been successfully sent!')
                            ];
                            $this->response = $this->response->withStatus(200);
                        } else {
                            $result = [
                                'status' => 'error',
                                'code' => 200,
                                'message' => _('OTP could not sent, Please try again!')
                            ];
                            $this->response = $this->response->withStatus(200);
                        }
                    } else {
                        $to = trim($data['email']);
                        $subject = "Your OTP Code";
                        $template = "customer_otp";
                        $viewVars = array('fullname' => $user->first_name . ' ' . $user->last_name, 'otp' => $otp);
                        $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                        if ($send_email) {
                            $result = [
                                'status' => 'success',
                                'code' => 200,
                                'data' => [
                                    'otp' => $otp
                                ],
                                'message' => __('OTP has been successfully sent!')
                            ];
                            $this->response = $this->response->withStatus(200);
                        } else {
                            $result = [
                                'status' => 'error',
                                'code' => 200,
                                'message' => _('OTP could not sent, Please try again!')
                            ];
                            $this->response = $this->response->withStatus(200);
                        }
                    }
                } else {
                    $result = ['status' => 'error', 'message' => 'Please try again!'];
                    $this->response = $this->response->withStatus(200);
                }
            } catch (\Exception $e) {
                $result = ['status' => 'error', 'message' => __('Parameters went wrong!')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function verifyOtp()
    {

        if ($this->request->is('post')) {
            try {
                $data = $this->request->getData();
                if (isset($data['country_code']) && !empty($data['country_code'])) {
                    // Remove the '+' sign from the 'country_code'
                    $data['country_code'] = trim($data['country_code'], '+');
                }
                $request_attr = [
                    'request' => json_encode($data),
                    'user_id' => '',
                    'api_name' => 'verifyOtp',
                    //  'created_at' => Time::now(),
                    //  'updated_at' => Time::now()
                ];

                $this->ApiRequestLogs->add_new_log($request_attr);

                if (empty($data['otp'])) {
                    $result = ['status' => 'error', 'message' => _('OTP is required')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }


                if (empty($data['country_code']) && empty($data['mobile_no']) && empty($data['email'])) {
                    $result = ['status' => 'error', 'message' => __('Mobile number or email is required')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                if (!empty($data['mobile_no']) && !empty($data['country_code'])) {

                    if (!preg_match('/^[0-9]{1,4}\s?[0-9]{10}$/', $data['country_code'] . $data['mobile_no'])) {
                        $result = ['status' => 'error', 'message' => __('Enter a valid mobile number with a country code')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }

                    $user_identifier = $data['country_code'] . $data['mobile_no'];
                    $otp_type = 'mobile';
                }


                if (!empty($data['email'])) {

                    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                        $result = ['status' => 'error', 'message' => __('Enter valid email address')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }

                    $user_identifier = $data['email'];
                    $otp_type = 'email';
                }

                $otp = $data['otp'];
                $otp_verified = $this->OtpVerifications->verify_otp($user_identifier, $otp, $otp_type);

                if ($otp_verified == 1) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => [
                            $otp_type => $user_identifier
                        ],
                        'message' => __('OTP verified successfully!')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else if ($otp_verified == 2) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'data' => [
                            $otp_type => $user_identifier
                        ],
                        'message' => __('Your OTP has expired. Please request a new one.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else if ($otp_verified == 3) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'data' => [
                            $otp_type => $user_identifier
                        ],
                        'message' => __('Invalid OTP!')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'data' => [
                            $otp_type => $user_identifier
                        ],
                        'message' => __('Invalid OTP!')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            } catch (\Exception $e) {
                $result = ['status' => 'error', 'message' => __('Parameters went wrong!')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function signup()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();
            if (isset($data['country_code']) && !empty($data['country_code'])) {
                // Remove the '+' sign from the 'country_code'
                $data['country_code'] = trim($data['country_code'], '+');
            }
            $request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'signup',
                //   'created_at'=>Time::now(),
                //    'updated_at'=>Time::now()
            ];

            $this->ApiRequestLogs->add_new_log($request_attr);

            if (empty($data['country_code'])) {
                $result = ['status' => 'error', 'message' => __('Country code is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['mobile_no'])) {
                $result = ['status' => 'error', 'message' => __('Mobile number is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                // Validate mobile number with country code
                $data['mobile_no'] = str_replace(' ', '', $data['mobile_no']);
                if (!preg_match('/^[0-9]{1,4}\s?[0-9]{7,15}$/', $data['country_code'] . $data['mobile_no'])) {
                    $result = ['status' => 'error', 'message' => __('Enter valid mobile number')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }


            $usermobile = $this->Users->checkMobileExist(trim($data['mobile_no']), trim($data['country_code']));
            if ($usermobile) {
                $result = ['status' => 'error', 'message' => __('Mobile no already exist')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['password'])) {
                $result = ['status' => 'error', 'message' => __('Password is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (strlen($data['password']) < 8) {
                $result = ['status' => 'error', 'message' => __('password must contain minimum 8 characters in length')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (!preg_match('/[A-Za-z]/', $data['password']) || !preg_match('/[0-9]/', $data['password'])) {
                $result = ['status' => 'error', 'message' => __('Password must be a mix of alphabets and alphanumeric characters')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $data['user_type'] = 'Customer';
            //users
            $user_id = $this->Users->addUser($data);
            if ($user_id) {
                //emprecords
                $emp_record_id = $this->Customers->addCustomer($user_id, $data);

                if ($emp_record_id) {

                    /* -----Start add customer to zoho crm----- */                                      

                    $customer_id = $emp_record_id;
                    $signedup_from = "Customer App - ".$data['device_type'];
                    $zoho_res = $this->Zoho->createCRMContact($customer_id, $signedup_from);

                    /* -----End add customer to zoho crm----- */

                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => $data,
                        'message' => __('Your account has been successfully created!')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'error', 'message' => _('Please try again!')];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = ['status' => 'error', 'message' => _('Please try again!')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function addCustomerEmail()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();
            if (isset($data['country_code']) && !empty($data['country_code'])) {
                // Remove the '+' sign from the 'country_code'
                $data['country_code'] = trim($data['country_code'], '+');
            }

            $request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'addCustomerEmail',
                'created_at' => Time::now(),
                'updated_at' => Time::now()
            ];

            $this->ApiRequestLogs->add_new_log($request_attr);

            if (empty($data['mobile_no'])) {
                $result = ['status' => 'error', 'message' => __('Mobile number is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                if (empty($data['country_code'])) {
                    $result = ['status' => 'error', 'message' => __('Country code is required')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
                if (!preg_match('/^[0-9]{10}$/', $data['mobile_no'])) {
                    $result = ['status' => 'error', 'message' => __('Enter a valid 10-digit mobile number without a country code')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            if (empty($data['email'])) {
                $result = ['status' => 'error', 'message' => __('Email is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                    $result = ['status' => 'error', 'message' => __('Enter valid email address')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            $userid = $this->Users->checkEmailExist(trim($data['email']));

            $user = $this->Users->find()
                ->where(['mobile_no' => $data['mobile_no'], 'country_code' => $data['country_code'], 'status' => 'A'])
                ->first();

            if ($userid) {
                $result = ['status' => 'error', 'message' => __('Email already exist')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $token = Security::hash(Security::randomBytes(25));
            $verification_link = Router::url([
                'controller' => 'Users',
                'action' => 'verifyEmail',
                $user['id'],
                $token,
                'prefix' => false,
            ], true);

            $to = trim($data['email']);
            $subject = "Email Verification";
            $template = "customer_email_verification";

            $viewVars = ['fullname' => $user->first_name . ' ' . $user->last_name, 'verification_link' => $verification_link];
            $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
            if ($send_email) {

                $data['email_verify_token'] = $token;
                $data['is_email_verified'] = 0;
                $user_id = $this->Users->editUser($data);

                if ($user_id) {
                    $customer = $this->Customers->find()
                        ->where(['user_id' => $user_id])
                        ->first();

                    if ($customer) {
                        $customer_id = $customer->id;
                        
                        // Use new loyalty earning method with expiration extension
                        $loyaltyResult = $this->Loyalty->earnLoyaltyPoints(
                            $customer_id, 
                            20, 
                            'Standard'
                        );
                        
                    }

                    $result = ['status' => 'success', 'message' => 'Your email address has been successfully taken! Check your email to verify.'];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'error', 'message' => 'Please try again!'];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = ['status' => 'error', 'message' => 'Email sending failed!'];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function socialSignup()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();
            $request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'socialSignUp',
                'created_at' => Time::now(),
                'updated_at' => Time::now()
            ];
            $this->ApiRequestLogs->add_new_log($request_attr);

            if (empty($data['email'])) {
                $result = ['status' => 'error', 'message' => __('Email is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                    $result = ['status' => 'error', 'message' => __('Enter valid email address')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            $userid = $this->Users->checkEmailExist(trim($data['email']));
            if ($userid) {
                $result = ['status' => 'error', 'message' => __('Email already exist')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $nameParts = explode(' ', $data['name'], 2);
            if (count($nameParts) === 2) {
                $first_name = $nameParts[0];
                $last_name = $nameParts[1];
            } else {
                $first_name = $data['name'];
                $last_name = '';
            }

            $user_attributes = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $data['email']
            ];

            $customer_attributes = [
                'platform' => $data['platform']
            ];

            if (isset($data['udid']) && isset($data['device_type']) && isset($data['app_version']) && isset($data['model_name'])) {
                $customer_attributes['udid'] = $data['udid'];
                $customer_attributes['device_type'] = $data['device_type'];
                $customer_attributes['app_version'] = $data['app_version'];
                $customer_attributes['model_name'] = $data['model_name'];
            }

            $user_id = $this->Users->add_new_user($user_attributes);

            if ($user_id) {

                $customer_attributes['user_id'] = $user_id;
                $emp_record_id = $this->Customers->add_new_customer($customer_attributes);

                if ($emp_record_id) {

                    /* -----Start add customer to zoho crm----- */                                      

                    $customer_id = $emp_record_id;
                    $signedup_from = "Customer App - ".$data['device_type'];
                    $zoho_res = $this->Zoho->createCRMContact($customer_id, $signedup_from);

                    /* -----End add customer to zoho crm----- */

                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => $data,
                        'message' => __('Your account has been successfully created!')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                } else {
                    $result = ['status' => 'error', 'message' => 'Please try again!'];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            } else {
                $result = ['status' => 'error', 'message' => 'Please try again!'];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function socialLogin()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'socialLogin',
                'created_at' => Time::now(),
                'updated_at' => Time::now()
            ];
            $this->ApiRequestLogs->add_new_log($request_attr);

            $existing_email = $this->Users->get_active_user_by_email($data['email']);

            if (!empty($existing_email)) {

                if ($existing_email['customer']['platform'] != $data['platform']) {
                    $result = ['status' => 'error', 'message' => _('You have signed up using ' . $existing_email['customer']['platform'] . ' platform, please try the same login method.')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                $existing_email->last_login = FrozenTime::now();
                $existing_email->token = Security::hash(Security::randomBytes(32));
                $this->Users->save($existing_email);

                if (isset($data['udid']) && isset($data['device_type']) && isset($data['app_version']) && isset($data['model_name']) && isset($data['fcm_token'])) {
                    $device_attributes = [
                        'udid' => trim($data['udid']),
                        'device_type' => trim($data['device_type']),
                        'app_version' => trim($data['app_version']),
                        'model_name' => trim($data['model_name']),
                        'fcm_token' => trim($data['fcm_token'])
                    ];

                    $this->Users->update_customer_by_id($existing_email['customer']['id'], $device_attributes);
                }

                $convert_cart = false;
                $guestToken = $this->request->getHeaderLine('guest-token');
                if (!empty($guestToken)) {
                    $guestCart = $this->Carts->find()
                        ->where(['guest_token' => $guestToken])
                        ->first();

                    if ($guestCart) {
                        $convert_cart = true;
                    }
                }
                $existing_email->convert_cart = $convert_cart;

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $existing_email,
                    'message' => __('You are logged in successfully!')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $result = ['status' => 'error', 'message' => __('Sorry! User not registered, Please signup first!')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function addCustomerMobile()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'addCustomerMobile',
                'created_at' => Time::now(),
                'updated_at' => Time::now()
            ];

            $this->ApiRequestLogs->add_new_log($request_attr);

            if (empty($data['email'])) {
                $result = ['status' => 'error', 'message' => __('Email is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                    $result = ['status' => 'error', 'message' => __('Enter valid email address')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            if (empty($data['mobile_no'])) {
                $result = ['status' => 'error', 'message' => __('Mobile number is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                if (!preg_match('/^\+[0-9]{1,4}\s?[0-9]{10}$/', $data['mobile_no'])) {
                    $result = ['status' => 'error', 'message' => __('Enter a valid mobile number with a country code')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            $user_identifier = $data['mobile_no'];
            $otp = $this->generateOTP();
            $otp_type = 'mobile';
            $otp_saved = $this->OtpVerifications->save_otp($user_identifier, $otp, $otp_type);

            if ($otp_saved) {
                $otpResult = $this->Global->sendOtp($data['mobile_no'], $otp);

                if ($otpResult['success']) {
                    $user = $this->Users->find()
                        ->where(['mobile_no' => $data['mobile_no']])
                        ->first();
                    if ($user) {
                        $user_id = $user->id;
                        $customer = $this->Customers->find()
                            ->where(['user_id' => $user_id])
                            ->first();

                        if ($customer) {
                            $customer_id = $customer->id;
                            
                            // Use new loyalty earning method with expiration extension
                            $loyaltyResult = $this->Loyalty->earnLoyaltyPoints(
                                $customer_id, 
                                20, 
                                'Standard'
                            );
                            
                        }
                    }
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => [
                            'otp' => $otp
                        ],
                        'message' => __('OTP has been successfully sent!')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => _('OTP could not sent, Please try again!')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = ['status' => 'error', 'message' => 'Please try again!'];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function saveMobileNo()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'saveMobileNo',
                'created_at' => Time::now(),
                'updated_at' => Time::now()
            ];

            $this->ApiRequestLogs->add_new_log($request_attr);

            if (empty($data['email'])) {
                $result = ['status' => 'error', 'message' => __('Email is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                    $result = ['status' => 'error', 'message' => __('Enter valid email address')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            if (empty($data['mobile_no'])) {
                $result = ['status' => 'error', 'message' => __('Mobile number is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                if (!preg_match('/^\+[0-9]{1,4}\s?[0-9]{10}$/', $data['mobile_no'])) {
                    $result = ['status' => 'error', 'message' => __('Enter a valid mobile number with a country code')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            $existing_email = $this->Users->get_active_user_by_email($data['email']);

            if (!empty($existing_email)) {

                $user_attributes = [
                    'mobile_no' => $data['mobile_no']
                ];

                $this->Users->update_user_by_id($existing_email['id'], $user_attributes);
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $data,
                    'message' => __('Your mobile number taken successfully!')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $result = ['status' => 'error', 'message' => __('Sorry! User not registered, Please signup first!')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    public function login()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();
            
            if (isset($data['country_code']) && !empty($data['country_code'])) {
                // Remove the '+' sign from the 'country_code'
                $data['country_code'] = trim($data['country_code'], '+');
            }

            $request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'login',
                //  'created_at' => Time::now(),
                //   'updated_at' => Time::now()
            ];

            $this->ApiRequestLogs->add_new_log($request_attr);

            if ((isset($data['mobile_no']) && $data['mobile_no'] && isset($data['country_code']) && $data['country_code']) || (isset($data['email']) && $data['email'])) {

                $userQuery = $this->Users->find();

                if (isset($data['mobile_no']) && $data['mobile_no'] && isset($data['country_code']) && $data['country_code']) {
                    $userQuery = $userQuery->where(['mobile_no' => $data['mobile_no'], 'country_code' => $data['country_code']]);
                } elseif (isset($data['email']) && $data['email']) {
                    $userQuery = $userQuery->where(['email' => $data['email']]);
                }

                $user = $userQuery->where(['status' => 'A'])->order(['id' => 'DESC'])->first();

                if ($user && password_verify($data['password'], $user->password)) {
                    // Check if email verification is required for email login
                    if (isset($data['email']) && !empty($data['email']) && $user->is_email_verified == 0) {
                        $result = ['status' => 'error', 'message' => __('Please verify your email address before logging in. Check your email for verification link.')];
                        $this->response = $this->response->withStatus(200);
                        return $this->response->withStringBody(json_encode($result));
                    }
                    
                    if ($user['status'] == "A" && $user['user_type'] == 'Customer') {

                        $customer = $this->Customers->find()
                            ->where(['user_id' => $user['id']])
                            ->first();

                        if ($customer && $customer->id) {

                            $user->last_login = FrozenTime::now();
                            $user->token = Security::hash(Security::randomBytes(32));
                            $this->Users->save($user);

                            if (isset($data['udid']) && isset($data['device_type']) && isset($data['app_version']) && isset($data['model_name']) && isset($data['fcm_token'])) {

                                $device_attributes = [
                                    'udid' => trim($data['udid']),
                                    'device_type' => trim($data['device_type']),
                                    'app_version' => trim($data['app_version']),
                                    'model_name' => trim($data['model_name']),
                                    'fcm_token' => trim($data['fcm_token'])
                                ];

                                $this->Customers->update_customer_by_id($customer->id, $device_attributes);
                            }
                        }

                        $convert_cart = false;
                        $guestToken = $this->request->getHeaderLine('guest-token');
                        if (!empty($guestToken)) {
                            $guestCart = $this->Carts->find()
                                ->where(['guest_token' => $guestToken])
                                ->first();

                            if ($guestCart) {
                                $convert_cart = true;
                            }
                        }

                        $customer_data = array(
                            'user_id' => $user['id'],
                            'token' => $user['token'],
                            'customer_id' => $customer->id,
                            'first_name' => $user['first_name'],
                            'last_name' => $user['last_name'],
                            'email' => $user['email'],
                            'mobile_no' => $user['mobile_no'],
                            'country_code' => $user['country_code'],
                            'phone_number' => $customer->phone_number,
                            'profile_photo' => $customer->profile_photo,
                            'date_of_birth' => $customer->date_of_birth,
                            'gender' => $customer->gender,
                            'last_login' => $user['last_login'],
                            'device_type' => $customer->device_type,
                            'fcm_token' => $customer->fcm_token,
                            'convert_cart' => $convert_cart
                        );

                        $result = ['status' => 'success', 'data' => $customer_data, 'message' => __('You are logged in successfully!')];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $this->Authentication->logout();
                        $result = ['status' => 'error', 'message' => _('User is not active or not a customer')];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    if (isset($data['mobile_no']) && $data['mobile_no'] && isset($data['country_code']) && $data['country_code']) {
                        $result = ['status' => 'error', 'message' => _('Mobile Number or Password is incorrect')];
                    } elseif (isset($data['email']) && $data['email']) {
                        $result = ['status' => 'error', 'message' => _('Email or Password is incorrect')];
                    }
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = ['status' => 'error', 'message' => __('Invalid parameters')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function resetPassword()
    {

        if ($this->request->is('post')) {
            try {
                $data = $this->request->getData();
                if (isset($data['country_code']) && !empty($data['country_code'])) {
                    // Remove the '+' sign from the 'country_code'
                    $data['country_code'] = trim($data['country_code'], '+');
                }
                if (((isset($data['mobile_no']) && $data['mobile_no'] && isset($data['country_code']) && $data['country_code']) || (isset($data['email']) && $data['email'])) && isset($data['password'])) {

                    if (isset($data['email'])  && $data['email']) {
                        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                            $result = ['status' => 'error', 'message' => __('Enter valid email address')];
                            $this->response = $this->response->withStatus(200);
                            goto label;
                        }
                    }

                    if (isset($data['mobile_no']) && $data['mobile_no'] && $data['country_code'] && isset($data['country_code'])) {
                        if (!preg_match('/^[0-9]{1,4}\s?[0-9]{10}$/', $data['country_code'] . $data['mobile_no'])) {
                            $result = ['status' => 'error', 'message' => __('Enter a valid mobile number with a country code')];
                            $this->response = $this->response->withStatus(200);
                            goto label;
                        }
                    }

                    $userQuery = $this->Users->find();

                    if (isset($data['mobile_no']) && $data['mobile_no'] && isset($data['country_code']) && $data['country_code']) {
                        $userQuery = $userQuery->where([
                            'mobile_no' => $data['mobile_no'],
                            'country_code' => $data['country_code']
                        ]);
                    } elseif (isset($data['email']) && $data['email']) {
                        $userQuery = $userQuery->where(['email' => $data['email']]);
                    }

                    $user = $userQuery->where(['status' => 'A'])->first();

                    if ($user) {

                        $user_attributes = [
                            'password' => $data['password']
                        ];

                        $this->Users->update_user_by_id($user['id'], $user_attributes);

                        $result = ['status' => 'success', 'message' => __('Password has been reset successfully')];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $result = ['status' => 'error', 'message' => __('User not found with provided mobile number or email')];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = ['status' => 'error', 'message' => __('Invalid parameters')];
                    $this->response = $this->response->withStatus(200);
                }
            } catch (\Exception $e) {
                $result = ['status' => 'error', 'message' => __('Parameters went wrong!')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function updateCart()
    {
        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');
            if ($identity) {

                $customerId = $identity->get('_matchingData')['Customers']['id'];
                $cartData = $this->request->getData('cart');

                if (empty($cartData)) {
                    $result = ['status' => 'error', 'message' => __('Cart data is required')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                $cartCheck = $this->Carts->find()
                    ->where(['customer_id' => $customerId])
                    ->first();

                if (!$cartCheck) {
                    $cart_attributes = [
                        'customer_id' => $customerId
                    ];
                    $cart = $this->Carts->add_new($cart_attributes);
                } else {
                    $cart = $cartCheck->id;
                }

                $connection = $this->CartItems->getConnection();
                $connection->begin();

                try {
                    $this->CartItems->deleteAll(['cart_id' => $cart]);

                    $newCartItems = [];
                    foreach ($cartData as $item) {
                        $newItem = $this->CartItems->newEntity([
                            'cart_id' => $cart,
                            'customer_id' => $customerId,
                            'product_id' => $item['product_id'],
                            'product_variant_id' => $item['product_variant_id'],
                            'quantity' => $item['quantity'],
                            'price' => $item['price'],
                        ]);
                        $newCartItems[] = $newItem;
                    }

                    if ($this->CartItems->saveMany($newCartItems)) {
                        $connection->commit();

                        $updatedCart = $this->CartItems->find()
                            ->where(['cart_id' => $cart])
                            // ->contain(['Products'])
                            ->all();

                        $result = [
                            'status' => 'success',
                            'code' => 200,
                            'data' => $updatedCart,
                            'message' => __('Your cart is updated successfully')
                        ];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $connection->rollback();
                        $result = ['status' => 'error', 'message' => __('Failed to update cart')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }
                } catch (\Exception $e) {
                    $connection->rollback();
                    $result = ['status' => 'error', 'message' => __('Failed to update cart')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            } else {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    public function viewCart()
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $cart = $this->Carts->find()
                    ->where(['customer_id' => $customerId])
                    ->contain(['CartItems' => ['Products']])
                    ->first();

                if (!$cart) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => (object)[],
                        'message' => __('Your cart is empty')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                $cartItems = [];
                foreach ($cart->cart_items as $item) {

                    $product_image = null;
                    $image = $this->ProductImages->getDefaultProductImage($item->product->id);
                    if ($image) {
                        $product_image = $this->Media->getCloudFrontURL($image);
                    }

                    $rating = $this->Reviews->getAverageRating($item->product->id);
                    $total_review = $this->Reviews->getTotalReviews($item->product->id);
                    $discount = $this->Products->getDiscount($item->product->id);
                    $availability_status = $this->Products->getAvailabilityStatus($item->product->id);

                    $cartItems[] = [
                        'product_id' => $item->product_id,
                        'product_variant_id' => $item->product_variant_id,
                        'product_name' => $item->product->name,
                        'product_image' => $product_image,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'total_price' => $item->quantity * $item->product->promotion_price,
                        'rating' => $rating,
                        'total_reviews' => $total_review,
                        'discount' => $discount,
                        'availability_status' => $availability_status,
                    ];
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => [
                        'cart_id' => $cart->id,
                        'total_items' => count($cartItems),
                        'cart_items' => $cartItems,
                        'subtotal' => array_sum(array_column($cartItems, 'total_price')),
                    ]
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function viewOrders()
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $siteSettings = $this->SiteSettings->find()
                    ->select(['product_cancel_in_days', 'product_return_in_days'])
                    ->first();

                $limit = $this->request->getQuery('limit', 20); // Default limit is 20
                $page = $this->request->getQuery('page', 1); // Default page is 1

                $ordersQuery = $this->Orders->find()
                    ->where(['Orders.customer_id' => $customerId])
                    ->contain([
                        'CustomerAddresses' => [
                            'fields' => ['id', 'name', 'address_line1', 'address_line2', 'city_id', 'zipcode'],
                            'Cities' => ['fields' => ['id', 'city_name']]
                        ],
                        'CreditApplications', // Include credit applications
                        'CartCreditPayments', // Include cart credit payment
                        'Transactions',
                    ])
                    ->enableAutoFields(true) // includes all fields from Orders by default
                    ->order(['Orders.created' => 'DESC'])
                    ->group(['Orders.id']);

                // Use the custom paginator for orders
                $paginationResult = $this->CustomPaginator->paginate($ordersQuery, [
                    'limit' => $limit,
                    'page' => $page
                ]);

                $orders = $paginationResult['items'];
                $pagination = $paginationResult['pagination'];

                if (empty($orders)) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => (object)[],
                        'message' => __('No past orders found')
                    ];

                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                $responseData = [];
                foreach ($orders as $order) {
                    $itemsQuery = $this->Orders->OrderItems->find()
                        ->where(['OrderItems.order_id' => $order->id])
                        ->contain([
                            'Products' => ['fields' => ['id', 'name', 'reference_name']],
                            'ProductVariants',
                            'OrderTrackingHistories' => [
                                'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                            ],
                            'ProductAttributes' => ['Attributes', 'AttributeValues']
                        ]);

                    // Get all items for the order (no pagination needed for items within an order)
                    $items = $itemsQuery->all();

                    $orderData = [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'order_date' => $order->order_date,
                        'total_amount' => $order->total_amount,
                        'subtotal_amount' => $order->subtotal_amount,
                        'delivery_charge' => $order->delivery_charge,
                        'discount_amount' => $order->discount_amount,
                        'delivery_mode' => $order->delivery_mode,
                        'delivery_date' => $order->delivery_date,
                        'status' => $order->status,
                        'payment_method' => $order->payment_method,
                        'shipping_method' => $order->shipping_method,
                        'no_of_items' => count($items),
                        'credit_application' => $order->credit_application ? [
                            'id' => $order->credit_application->id,
                            'customer_id' => $order->credit_application->customer_id,
                            'registration_id' => $order->credit_application->registration_id,
                            'name' => $order->credit_application->name,
                            'email' => $order->credit_application->email,
                            'country_code' => $order->credit_application->country_code,
                            'phone_number' => $order->credit_application->phone_number,
                            'status' => $order->credit_application->status,
                            'created' => $order->credit_application->created,
                            'modified' => $order->credit_application->modified
                        ] : null,
                        'cart_credit_payment' => $order->cart_credit_payment ? [
                            'id' => $order->cart_credit_payment->id,
                            'cart_id' => $order->cart_credit_payment->cart_id,
                            'customer_id' => $order->cart_credit_payment->customer_id,
                            'product_id' => $order->cart_credit_payment->product_id,
                            'product_variant_id' => $order->cart_credit_payment->product_variant_id,
                            'credit_application_id' => $order->cart_credit_payment->credit_application_id,
                            'credit_payment_terms_id' => $order->cart_credit_payment->credit_payment_terms_id,
                            'emi_interest_percentage' => $order->cart_credit_payment->emi_interest_percentage,
                            'emi_interest_amount' => $order->cart_credit_payment->emi_interest_amount,
                            'total_emi_amount' => $order->cart_credit_payment->total_emi_amount,
                            'status' => $order->cart_credit_payment->status,
                            'created' => $order->cart_credit_payment->created,
                            'modified' => $order->cart_credit_payment->modified
                        ] : null,
                        'delivery_address' => $order->customer_address ? [
                            'name' => $order->customer_address->name,
                            'address_line_1' => $order->customer_address->address_line1,
                            'address_line_2' => $order->customer_address->address_line2,
                            'city' => $order->customer_address->city->city_name,
                            'zipcode' => $order->customer_address->zipcode
                        ] : null,
                        'items' => []
                    ];

                    // Format items data
                    foreach ($items as $item) {

                        $product_image = null;
                        $image = $this->ProductImages->getDefaultProductImage($item->product->id);
                        if ($image) {
                            $product_image = $this->Media->getCloudFrontURL($image);
                        }

                        $returnEligibilityDate = date('Y-m-d', strtotime("{$item->created} +{$siteSettings->product_return_in_days} days"));

                        $latestTrackingStatus = end($item->order_tracking_histories);
                        $estimatedDeliveryDate = null;

                        if ($latestTrackingStatus) {
                            $estimatedDeliveryDate = $latestTrackingStatus->status == 'Shipped'
                                ? date('Y-m-d', strtotime("{$latestTrackingStatus->updated} +5 days"))
                                : null;
                        }

                        $totalPrice = ($item->total_price > 0) ? $item->total_price : ($item->quantity * $item->price);

                        $isReviewed = $this->Reviews->exists([
                            'customer_id ' => $customerId,
                            'product_id' => $item->product_id
                        ]);

                        $itemData = [
                            'product_id' => $item->product_id,
                            'is_product_reviewed' => $isReviewed,
                            'order_item_id' => $item->id,
                            'product_reference' => $item->product->product_reference,
                            'product_name' => $item->product->name,
                            'product_attribute_id' => $item->product_attribute_id,
                            'product_attribute_name' => $item->product_attribute ? $item->product_attribute->attribute->name : '',
                            'product_attribute_value' => $item->product_attribute ? $item->product_attribute->attribute_value->value : '',
                            'reference_name' => $item->product->reference_name,
                            'product_image' => $product_image,
                            'quantity' => $item->quantity,
                            'price' => $item->price,
                            'total_price' => $totalPrice,
                            'status' => $item->status,
                            'status_updated_at' => $latestTrackingStatus->updated ?? 'N/A',
                            'estimated_delivery_date' => $estimatedDeliveryDate,
                            'return_replace_eligibility_date' => $returnEligibilityDate,
                            'tracking_history' => array_map(function ($tracking) {
                                return [
                                    'status' => $tracking->status,
                                    'comment' => $tracking->comment,
                                    'updated_at' => $tracking->updated,
                                ];
                            }, $item->order_tracking_histories)
                        ];

                        // Add product variant details if variant is purchased
                        if (!empty($item->product_variant_id) && $item->product_variant_id > 0) {
                            $variant = $this->ProductVariants->find()
                                ->select([
                                    'ProductVariants.id',
                                    'ProductVariants.product_id',
                                    'ProductVariants.variant_name',
                                    'ProductVariants.reference_name',
                                    'ProductVariants.sku',
                                    'ProductVariants.variant_size',
                                    'ProductVariants.variant_weight',
                                    'ProductVariants.purchase_price',
                                    'ProductVariants.sales_price',
                                    'ProductVariants.promotion_price',
                                    'ProductVariants.quantity',
                                    'ProductVariants.variant_description',
                                    'ProductVariants.status'
                                ])
                                ->where(['ProductVariants.id' => $item->product_variant_id])
                                ->contain([
                                    'ProductVariantImages' => function ($q) {
                                        return $q->where(['ProductVariantImages.status' => 'A'])
                                                 ->order(['ProductVariantImages.image_default' => 'DESC', 'ProductVariantImages.id' => 'ASC']);
                                    }
                                ])
                                ->first();

                            if ($variant) {
                                // Map variant images to CloudFront URLs
                                if (!empty($variant->product_variant_images)) {
                                    foreach ($variant->product_variant_images as &$vimg) {
                                        if (!empty($vimg->image)) {
                                            $vimg->image = $this->Media->getCloudFrontURL($vimg->image);
                                        }
                                        if (!empty($vimg->video)) {
                                            $vimg->video = $this->Media->getCloudFrontURL($vimg->video);
                                        }
                                    }
                                    unset($vimg);
                                }

                                $itemData['product_variant'] = [
                                    'id' => $variant->id,
                                    'variant_name' => $variant->variant_name,
                                    'reference_name' => $variant->reference_name,
                                    'sku' => $variant->sku,
                                    'variant_size' => $variant->variant_size,
                                    'variant_weight' => $variant->variant_weight,
                                    'purchase_price' => $variant->purchase_price,
                                    'sales_price' => $variant->sales_price,
                                    'promotion_price' => $variant->promotion_price,
                                    'quantity' => $variant->quantity,
                                    'variant_description' => $variant->variant_description,
                                    'status' => $variant->status,
                                    'product_variant_images' => $variant->product_variant_images
                                ];
                            }
                        }

                        $orderData['items'][] = $itemData;
                    }

                    $orderData['transactions'] = $order['transactions'][0] ?? [];

                    // Note: Pagination is now provided at the orders level, not per order

                    $responseData[] = $orderData;
                }

                // Add orders pagination details to the response
                $ordersPagination = [
                    'total_items' => $pagination['total_items'],
                    'total_pages' => $pagination['total_pages'],
                    'current_page' => (string)$pagination['current_page'],
                    'limit' => (string)$pagination['limit'],
                    'has_more' => $pagination['has_more']
                ];

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => [
                        'orders' => $responseData,
                        'pagination' => $ordersPagination
                    ],
                    'message' => __('Order listing successfully')
                ];

                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function viewOrderDetails($orderId)
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
                $customerId = $identity->get('_matchingData')['Customers']['id'];

                // Fetch the order for the given order ID and customer ID
                $order = $this->Orders->find()
                    ->where(['Orders.id' => $orderId, 'Orders.customer_id' => $customerId])
                    ->contain([
                        'CustomerAddresses' => [
                            'fields' => ['id', 'name', 'address_line1', 'address_line2', 'city_id', 'zipcode'],
                            'Cities' => ['fields' => ['id', 'city_name']]
                        ],
                        'CreditApplications', // Include credit applications
                        'CartCreditPayments', // Include cart credit payments
                        'ShipmentOrders',
                        'Transactions',
                        'Showrooms'
                    ])
                    ->first();

                if (!$order) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => (object)[],
                        'message' => __('Order not found')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                // Fetch site settings for cancellation and return periods
                $siteSettings = $this->SiteSettings->find()
                    ->select(['product_cancel_in_days', 'product_return_in_days'])
                    ->first();

                // Prepare order data
                $orderData = [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'order_date' => $order->order_date,
                    'total_amount' => $order->total_amount,
                    'subtotal_amount' => $order->subtotal_amount,
                    'delivery_charge' => $order->delivery_charge,
                    'discount_amount' => $order->discount_amount,
                    'delivery_mode' => $order->delivery_mode,
                    'delivery_date' => $order->delivery_date,
                    'status' => $order->status,
                    'payment_method' => $order->payment_method,
                    'shipping_method' => $order->shipping_method,
                    'credit_application' => $order->credit_application ? [
                        'id' => $order->credit_application->id,
                        'customer_id' => $order->credit_application->customer_id,
                        'registration_id' => $order->credit_application->registration_id,
                        'name' => $order->credit_application->name,
                        'email' => $order->credit_application->email,
                        'country_code' => $order->credit_application->country_code,
                        'phone_number' => $order->credit_application->phone_number,
                        'status' => $order->credit_application->status,
                        'created' => $order->credit_application->created,
                        'modified' => $order->credit_application->modified
                    ] : null,
                    'cart_credit_payment' => $order->cart_credit_payment ? [
                        'id' => $order->cart_credit_payment->id,
                        'cart_id' => $order->cart_credit_payment->cart_id,
                        'customer_id' => $order->cart_credit_payment->customer_id,
                        'product_id' => $order->cart_credit_payment->product_id,
                        'product_variant_id' => $order->cart_credit_payment->product_variant_id,
                        'credit_application_id' => $order->cart_credit_payment->credit_application_id,
                        'credit_payment_terms_id' => $order->cart_credit_payment->credit_payment_terms_id,
                        'emi_interest_percentage' => $order->cart_credit_payment->emi_interest_percentage,
                        'emi_interest_amount' => $order->cart_credit_payment->emi_interest_amount,
                        'total_emi_amount' => $order->cart_credit_payment->total_emi_amount,
                        'status' => $order->cart_credit_payment->status,
                        'created' => $order->cart_credit_payment->created,
                        'modified' => $order->cart_credit_payment->modified
                    ] : null,
                    'delivery_address' => $order->customer_address ? [
                        'name' => $order->customer_address->name,
                        'address_line_1' => $order->customer_address->address_line1,
                        'address_line_2' => $order->customer_address->address_line2,
                        'city' => $order->customer_address->city->city_name,
                        'zipcode' => $order->customer_address->zipcode
                    ] : null,
                    'shipment_orders' => $order->shipment_order ? [
                        'id' => $order->shipment_order->id,
                        'shipment_id' => $order->shipment_order->shipment_id,
                        'order_id' => $order->shipment_order->order_id,
                        'driver_id' => $order->shipment_order->driver_id,
                        'customer_id' => $order->shipment_order->customer_id,
                        'customer_address_id' => $order->shipment_order->customer_address_id,
                        'city_id' => $order->shipment_order->city_id,
                        'zone_id' => $order->shipment_order->zone_id,
                        'municipality_id' => $order->shipment_order->municipality_id,
                        'expected_delivery_date' => $order->shipment_order->expected_delivery_date,
                        'actual_delivery_date' => $order->shipment_order->actual_delivery_date,
                        'delivery_attempts' => $order->shipment_order->delivery_attempts,
                        'is_expedited' => $order->shipment_order->is_expedited,
                        'shipping_cost' => $order->shipment_order->shipping_cost,
                        'order_delivery_status' => $order->shipment_order->order_delivery_status,
                        'delivery_status_date' => $order->shipment_order->delivery_status_date,
                        'cash_collected' => $order->shipment_order->cash_collected,
                        'proof_of_delivery' => $order->shipment_order->proof_of_delivery,
                        'driver_comments' => $order->shipment_order->driver_comments,
                        'special_instructions' => $order->shipment_order->special_instructions,
                    ] : null,
                    'items' => [],
                ];

                // Add order-level tracking history if order is cancelled or pending cancellation
                if (in_array($order->status, ['Cancelled', 'Pending Cancellation'])) {
                    $orderTrackingHistory = $this->OrderTrackingHistories->find()
                        ->select(['id', 'order_id', 'status', 'comment', 'updated'])
                        ->where(['order_id' => $order->id])
                        ->order(['updated' => 'ASC'])
                        ->toArray();

                    $orderData['tracking_history'] = array_map(function ($tracking) {
                        return [
                            'status' => $tracking->status,
                            'comment' => $tracking->comment,
                            'updated_at' => $tracking->updated,
                        ];
                    }, $orderTrackingHistory);
                }

                // First check if invoice file exists locally
                $localInvoicePath = WWW_ROOT . 'files' . DS . 'invoices' . DS . 'Invoice-' . $order->id . '.pdf';
                
                if (file_exists($localInvoicePath)) {
                    // Use local file
                    $orderData['invoice'] = Router::url("/files/invoices/Invoice-" . $order->id . ".pdf", true);
                } else {
                    // File not found locally, get from supervisor API
                    $supervisorApi = new SupervisorApisController($this->request);
                    $invoiceResponse = $supervisorApi->printInvoicePdf($order->id);
                    
                    // Extract PDF URL from response body
                    if ($invoiceResponse && $invoiceResponse->getBody()) {
                        $responseData = json_decode((string)$invoiceResponse->getBody(), true);
                        if ($responseData && isset($responseData['pdf_url'])) {
                            $orderData['invoice'] = $responseData['pdf_url'];
                        } else {
                            $orderData['invoice'] = $this->Media->getCloudFrontURL('babiken_uploads/invoices/sample-invoice.pdf');
                        }
                    } else {
                        $orderData['invoice'] = $this->Media->getCloudFrontURL('babiken_uploads/invoices/sample-invoice.pdf');
                    }
                }

                // Paginate order items
                $limit = $this->request->getQuery('limit') ?? 50; // Default limit
                $page = $this->request->getQuery('page') ?? 1;

                // Fetch order items with pagination
                $itemsQuery = $this->Orders->OrderItems->find()
                    ->where(['OrderItems.order_id' => $order->id])
                    ->contain([
                        'Products' => ['fields' => ['id', 'name', 'reference_name']],
                        'ProductVariants',
                        'OrderTrackingHistories' => [
                            'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                        ],
                        'ProductAttributes' => ['Attributes', 'AttributeValues'],
                        'ShipmentOrderItems'
                    ]);

                // Use the custom paginator for order items
                $paginationResult = $this->CustomPaginator->paginate($itemsQuery, [
                    'limit' => $limit,
                    'page' => $page
                ]);

                $items = $paginationResult['items'];
                $pagination = $paginationResult['pagination'];

                // Format items data
                foreach ($items as $item) {

                    $product_image = null;
                    $image = $this->ProductImages->getDefaultProductImage($item->product->id);
                    if ($image) {
                        $product_image = $this->Media->getCloudFrontURL($image);
                    }

                    $returnEligibilityDate = date('Y-m-d', strtotime("{$item->created} +{$siteSettings->product_return_in_days} days"));

                    $latestTrackingStatus = end($item->order_tracking_histories);
                    $estimatedDeliveryDate = null;

                    if ($latestTrackingStatus) {
                        $estimatedDeliveryDate = $latestTrackingStatus->status == 'Shipped'
                            ? date('Y-m-d', strtotime("{$latestTrackingStatus->updated} +5 days"))
                            : null;
                    }

                    $totalPrice = ($item->total_price > 0) ? $item->total_price : ($item->quantity * $item->price);

                    $isReviewed = $this->Reviews->exists([
                        'customer_id ' => $customerId,
                        'product_id' => $item->product_id
                    ]);

                    $itemData = [
                        'id' => $item->id,
                        'is_product_reviewed' => $isReviewed,
                        'product_id' => $item->product_id,
                        'product_name' => $item->product->name,
                        'product_attribute_id' => $item->product_attribute_id,
                        'product_attribute_name' => $item->product_attribute ? $item->product_attribute->attribute->name : '',
                        'product_attribute_value' => $item->product_attribute ? $item->product_attribute->attribute_value->value : '',
                        'product_reference' => $item->product->product_reference,
                        'reference_name' => $item->product->reference_name,
                        'product_image' => $product_image,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'total_price' => $totalPrice,
                        'status' => $item->status,
                        'latest_status' => $latestTrackingStatus->status ?? 'N/A',
                        'status_updated_at' => $latestTrackingStatus->updated ?? 'N/A',
                        'estimated_delivery_date' => $estimatedDeliveryDate,
                        'return_replace_eligibility_date' => $returnEligibilityDate,
                        'tracking_history' => array_map(function ($tracking) {
                            return [
                                'status' => $tracking->status,
                                'comment' => $tracking->comment,
                                'updated_at' => $tracking->updated,
                            ];
                        }, $item->order_tracking_histories),
                        'shipment_order_items' => array_map(function ($shipment) {
                            return [
                                'shipment_item_id' => $shipment->id,
                                'shipment_order_id' => $shipment->shipment_order_id,
                                'order_item_id' => $shipment->order_item_id,
                                'quantity' => $shipment->quantity,
                                'item_delivery_status' => $shipment->item_delivery_status,
                                'delivery_status_date' => $shipment->delivery_status_date,
                                'driver_comments' => $shipment->driver_comments,
                            ];
                        }, $item->shipment_order_items)
                    ];

                    // Add cancellation information for this order item
                    $cancellationInfo = $this->getOrderItemCancellationInfo($item->id);
                    $itemData['cancellation_info'] = $cancellationInfo;

                    // Add return information for this order item
                    $returnInfo = $this->getOrderItemReturnInfo($item->id);
                    $itemData['return_info'] = $returnInfo;

                    // Add product variant details if variant is purchased
                    if (!empty($item->product_variant_id) && $item->product_variant_id > 0) {
                        $variant = $this->ProductVariants->find()
                            ->select([
                                'ProductVariants.id',
                                'ProductVariants.product_id',
                                'ProductVariants.variant_name',
                                'ProductVariants.reference_name',
                                'ProductVariants.sku',
                                'ProductVariants.variant_size',
                                'ProductVariants.variant_weight',
                                'ProductVariants.purchase_price',
                                'ProductVariants.sales_price',
                                'ProductVariants.promotion_price',
                                'ProductVariants.quantity',
                                'ProductVariants.variant_description',
                                'ProductVariants.status'
                            ])
                            ->where(['ProductVariants.id' => $item->product_variant_id])
                            ->contain([
                                'ProductVariantImages' => function ($q) {
                                    return $q->where(['ProductVariantImages.status' => 'A'])
                                             ->order(['ProductVariantImages.image_default' => 'DESC', 'ProductVariantImages.id' => 'ASC']);
                                }
                            ])
                            ->first();

                        if ($variant) {
                            // Map variant images to CloudFront URLs
                            if (!empty($variant->product_variant_images)) {
                                foreach ($variant->product_variant_images as &$vimg) {
                                    if (!empty($vimg->image)) {
                                        $vimg->image = $this->Media->getCloudFrontURL($vimg->image);
                                    }
                                    if (!empty($vimg->video)) {
                                        $vimg->video = $this->Media->getCloudFrontURL($vimg->video);
                                    }
                                }
                                unset($vimg);
                            }

                            $itemData['product_variant'] = [
                                'id' => $variant->id,
                                'variant_name' => $variant->variant_name,
                                'reference_name' => $variant->reference_name,
                                'sku' => $variant->sku,
                                'variant_size' => $variant->variant_size,
                                'variant_weight' => $variant->variant_weight,
                                'purchase_price' => $variant->purchase_price,
                                'sales_price' => $variant->sales_price,
                                'promotion_price' => $variant->promotion_price,
                                'quantity' => $variant->quantity,
                                'variant_description' => $variant->variant_description,
                                'status' => $variant->status,
                                'product_variant_images' => $variant->product_variant_images
                            ];
                        }
                    }

                    $orderData['items'][] = $itemData;
                }

                $orderData['transactions'] = $order['transactions'][0];
                $orderData['showroom'] = $order['showroom'];

                // Add pagination details for the items
                $orderData['pagination'] = $pagination;

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $orderData,
                    'message' => __('Order details retrieved successfully')
                ];

                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function viewAllDealsofTheDay()
    {
        if ($this->request->is('get')) {
            // $data = $this->Products->getDealOfTheDayProducts();
            $data = $this->ProductDeals->getTopProdDeals();
            foreach ($data as $deal) {
                
                if (!empty($deal->product) && !empty($deal->product->product_images)) {
                    foreach ($deal->product->product_images as $image) {
                        if (!empty($image->image)) {
                            $image->image = $this->Media->getCloudFrontURL($image->image);
                        }
                    }
                }

                // Add product variants for deals of the day
                if (!empty($deal['product_id'])) {
                    $variants = $this->ProductVariants->find()
                        ->select([
                            'ProductVariants.id',
                            'ProductVariants.product_id',
                            'ProductVariants.variant_name',
                            'ProductVariants.reference_name',
                            'ProductVariants.sku',
                            'ProductVariants.variant_size',
                            'ProductVariants.variant_weight',
                            'ProductVariants.purchase_price',
                            'ProductVariants.sales_price',
                            'ProductVariants.promotion_price',
                            'ProductVariants.quantity',
                            'ProductVariants.variant_description',
                            'ProductVariants.status',
                            'product_variant_discount' => $this->ProductVariants->find()->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)')
                        ])
                        ->where(['ProductVariants.product_id' => $deal['product_id'], 'ProductVariants.status' => 'A'])
                        ->contain([
                            'ProductVariantImages' => function ($q) {
                                return $q->where(['ProductVariantImages.status' => 'A'])
                                         ->order(['ProductVariantImages.image_default' => 'DESC', 'ProductVariantImages.id' => 'ASC']);
                            }
                        ])
                        ->toArray();

                    // Map variant images to CloudFront URLs
                    foreach ($variants as &$variant) {
                        if (!empty($variant->product_variant_images)) {
                            foreach ($variant->product_variant_images as &$vimg) {
                                if (!empty($vimg->image)) {
                                    $vimg->image = $this->Media->getCloudFrontURL($vimg->image);
                                }
                                if (!empty($vimg->video)) {
                                    $vimg->video = $this->Media->getCloudFrontURL($vimg->video);
                                }
                            }
                            unset($vimg);
                        }
                    }
                    unset($variant);

                    $deal['product_variants'] = $variants;
                }
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $data,
                'message' => __('Deals of the day listing')
            ];

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function viewTopSellingProducts()
    {
        if ($this->request->is('get')) {

            $widgets = $this->Widgets->homeWidgets('mobile');
            $products = [];
            foreach ($widgets as $widget) {

                if ($widget['widget_type'] == 'Best Seller') {

                    $categoryIds = $this->Widgets->WidgetCategoryMappings->find()
                        ->select(['category_id'])
                        ->where(['widget_id' => $widget['id'], 'status' => 'A'])
                        ->toArray();

                    $categoryIds = array_column($categoryIds, 'category_id');

                    $products = $this->Products->getTopSellingProducts($widget['no_of_product'], $categoryIds, $widget['product_preference']);
                }
            }

            //$products = $this->Products->getTopSellingProducts();

            foreach ($products as $product) {

                $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                $product['discount'] = $this->Products->getDiscount($product['id']);
                $product['availability_status'] = $this->Products->getAvailabilityStatus($product['id']);

                $product['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($product['id']);
                if ($image) {
                    $product['product_image'] = $this->Media->getCloudFrontURL($image);
                }
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $products,
                'message' => __('Top selling product listing')
            ];

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function viewNewArrivalProducts()
    {
        if ($this->request->is('get')) {

            $widgets = $this->Widgets->homeWidgets('mobile');
            $products = [];
            foreach ($widgets as $widget) {

                if ($widget['widget_type'] == 'New Arrivals') {

                    $categoryIds = $this->Widgets->WidgetCategoryMappings->find()
                        ->select(['category_id'])
                        ->where(['widget_id' => $widget['id'], 'status' => 'A'])
                        ->toArray();

                    $categoryIds = array_column($categoryIds, 'category_id');

                    $products = $this->Products->getNewArrivalProducts($widget['no_of_product'], $categoryIds, $widget['product_preference']);
                }
            }

            //$products = $this->Products->getNewArrivalProducts($limit = 10);

            foreach ($products as $product) {

                $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                $product['discount'] = $this->Products->getDiscount($product['id']);
                $product['availability_status'] = $this->Products->getAvailabilityStatus($product['id']);

                $product['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($product['id']);
                if ($image) {
                    $product['product_image'] = $this->Media->getCloudFrontURL($image);
                }
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $products,
                'message' => __('All new arrival product listing')
            ];

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function viewFeaturedProducts()
    {

        if ($this->request->is('get')) {

            $widgets = $this->Widgets->homeWidgets('mobile');
            $products = [];
            foreach ($widgets as $widget) {

                if ($widget['widget_type'] == 'Featured') {

                    $categoryIds = $this->Widgets->WidgetCategoryMappings->find()
                        ->select(['category_id'])
                        ->where(['widget_id' => $widget['id'], 'status' => 'A'])
                        ->toArray();

                    $categoryIds = array_column($categoryIds, 'category_id');

                    $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference']);
                }
            }

            //$products = $this->Products->getFeaturedProducts();

            foreach ($products as $product) {

                $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                $product['discount'] = $this->Products->getDiscount($product['id']);
                $product['availability_status'] = $this->Products->getAvailabilityStatus($product['id']);

                $product['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($product['id']);
                if ($image) {
                    $product['product_image'] = $this->Media->getCloudFrontURL($image);
                }
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $products,
                'message' => __('All featured product listing')
            ];

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function viewTopDealProducts()
    {
        if ($this->request->is('get')) {
            $products = $this->Products->getTopDealProducts();

            foreach ($products as $product) {

                $product['rating'] = $this->Reviews->getAverageRating($product['id']);
                $product['total_review'] = $this->Reviews->getTotalReviews($product['id']);
                $product['discount'] = $this->Products->getDiscount($product['id']);
                $product['availability_status'] = $this->Products->getAvailabilityStatus($product['id']);

                // Add product variants for top deal products
                $variants = $this->ProductVariants->find()
                    ->select([
                        'ProductVariants.id',
                        'ProductVariants.product_id',
                        'ProductVariants.variant_name',
                        'ProductVariants.reference_name',
                        'ProductVariants.sku',
                        'ProductVariants.variant_size',
                        'ProductVariants.variant_weight',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sales_price',
                        'ProductVariants.promotion_price',
                        'ProductVariants.quantity',
                        'ProductVariants.variant_description',
                        'ProductVariants.status',
                        'product_variant_discount' => $this->ProductVariants->find()->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)')
                    ])
                    ->where(['ProductVariants.product_id' => $product['id'], 'ProductVariants.status' => 'A'])
                    ->contain([
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['ProductVariantImages.status' => 'A'])
                                     ->order(['ProductVariantImages.image_default' => 'DESC', 'ProductVariantImages.id' => 'ASC']);
                        }
                    ])
                    ->toArray();

                // Map variant images to CloudFront URLs
                foreach ($variants as &$variant) {
                    if (!empty($variant->product_variant_images)) {
                        foreach ($variant->product_variant_images as &$vimg) {
                            if (!empty($vimg->image)) {
                                $vimg->image = $this->Media->getCloudFrontURL($vimg->image);
                            }
                            if (!empty($vimg->video)) {
                                $vimg->video = $this->Media->getCloudFrontURL($vimg->video);
                            }
                        }
                        unset($vimg);
                    }
                }
                unset($variant);

                $product['product_variants'] = $variants;
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $products,
                'message' => __('All top deal items listing')
            ];

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function viewAllFeaturedCategories()
    {

        if ($this->request->is('get')) {
            $featured_cats = $this->Categories->featuredCategories();
            foreach ($featured_cats as &$featured_cat) {
                if (!empty($featured_cat['category_icon'])) {
                    $featured_cat['category_icon'] = $this->Media->getCloudFrontURL($featured_cat['category_icon']);
                }
            }
            unset($featured_cat);

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $featured_cats,
                'message' => __('All featured category listing')
            ];

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function addWishlist()
    {
        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $customer_id = $identity->get('_matchingData')['Customers']['id'];
                $data = $this->request->getData();
                $product_id = $data['product_id'];
                $variant_id = $data['variant_id'] ?? null;
                $attribute_id = $data['attribute_id'] ?? null;
                $added = $this->Wishlists->addToWishlist( $customer_id, $product_id, $variant_id, $attribute_id);
                if ($added) {
                    $result = [
                        'status' => 'success',
                        'code' => $added['status'],
                        'data' => (object)[],
                        'message' => $added['message']
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                } else {
                    $result = ['status' => 'error', 'message' => __('Please try again!')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function viewWishlist()
    {
        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $customer_id = $identity->get('_matchingData')['Customers']['id'];

                $data = $this->request->getData();

                $wishlists = $this->Wishlists->viewWishlist($customer_id);
                /*$wishlists = $this->Wishlists->find()
                ->contain(['ProductVariants']) // this joins the Variants table
                ->toArray();*/

                if ($wishlists) {

                    foreach ($wishlists as &$wishlist) {
                                       

                        $wishlist['product']['rating'] = $this->Reviews->getAverageRating($wishlist['product_id']);
                        $wishlist['product']['total_review'] = $this->Reviews->getTotalReviews($wishlist['product_id']);
                        $wishlist['product']['discount'] = $this->Products->getDiscount($wishlist['product_id']);

                        $wishlist['product']['product_image'] = '';
                            $image = $this->ProductImages->getDefaultProductImage($wishlist['product_id']);
                        if ($image) {
                            $wishlist['product']['product_image'] = $this->Media->getCloudFrontURL($image);
                        }
                        if($wishlist['product_variant_id'] != null)
                        {
                            $wishlist['purchase_price'] = $wishlist['product_variant']['purchase_price'];
                            $wishlist['sales_price'] = $wishlist['product_variant']['sales_price'];
                            $wishlist['promotion_price'] = $wishlist['product_variant']['promotion_price'];

                        }
                        else{
                            $product = $this->Products->get($wishlist->product_id);
                            $wishlist['purchase_price'] = $product->purchase_price;
                            $wishlist['sales_price'] = $product->sales_price;
                            $wishlist['promotion_price'] = $product->promotion_price;
                        }
                    }
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => $wishlists,
                        'message' => __('View wishlists successfully')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                } else {
                    $result = ['status' => 'success', 'message' => __('Data not found')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function removeWishlist()
    {


        
        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $customer_id = $identity->get('_matchingData')['Customers']['id'];

                $data = $this->request->getData();
                $product_id = $data['product_id'];
                $variant_id = $data['variant_id'] ?? null;
                
                $conditions = [
                    'customer_id' => $customer_id,
                    'product_id' => $product_id,
                ];
                if ($variant_id === null) {
                    $conditions['product_variant_id IS'] = null;
                } else {
                    $conditions['product_variant_id'] = $variant_id;
                }
                $wishlist = $this->Wishlists->find()
                    ->where($conditions)
                    ->first();

                if(!$wishlist) {
                    $result = ['status' => 'error', 'message' => __('Product not found in wishlist')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                $removed = $this->Wishlists->delete($wishlist);
                // $removed = $this->Wishlists->removeFromWishlist($customer_id, $product_id, $variant_id);
                if ($removed) {

                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => (object)[],
                        'message' => __('Removed successfully')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                } else {
                    $result = ['status' => 'error', 'message' => __('Please try again!')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function couponList()
    {
        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $coupons = $this->Offers->find()->where(['status' => 'A'])->applyOptions(['order' => ['offer_name' => 'ASC']])->all();

                foreach ($coupons as $coupon) {
                    if (!empty($coupon->web_image)) {
                        $coupon->web_image = $this->Media->getCloudFrontURL($coupon->web_image);
                    }
                    if (!empty($coupon->mobile_image)) {
                        $coupon->mobile_image = $this->Media->getCloudFrontURL($coupon->mobile_image);
                    }
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $coupons,
                    'message' => __('Coupon listing successfully')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function walletPoints()
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
                $customerId = $identity->get('_matchingData')['Customers']['id'];
                $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);

                if ($loyaltyDetails['status'] === 'error') {
                    $result = ['status' => 'error', 'message' => $loyaltyDetails['message']];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'success', 'data' => $loyaltyDetails['data'], 'message' => $loyaltyDetails['message']];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function applyCoupon()
    {
        if ($this->request->is('post')) {
    
            $identity = $this->request->getAttribute('identity');
            $customerId = null;
            $guestToken = null;
            
            if ($identity) {
                // Authenticated user
                $customerId = $identity->get('_matchingData')['Customers']['id'];
            } else {
                // Guest user - get guest_token from request
                $guestToken = $this->request->getData('guest_token');
                if (empty($guestToken)) {
                    $result = ['status' => 'error', 'message' => __('Guest token is required for guest users')];
                    $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                    goto label;
                }
            }
    
            $couponCode = $this->request->getData('coupon_code');
            $subTotal = $this->request->getData('subTotal');
            $cartId = $this->request->getData('cart_id');
            $showroomId = $this->request->getData('showroom_id');
            
            if (empty($couponCode) || empty($subTotal) || empty($cartId)) {
                $result = ['status' => 'error', 'message' => __('Coupon Code, Sub Total, and Cart ID are required')];
                $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                goto label;
            }
            // Get cart details with products and their categories
            if ($customerId) {
                // Authenticated user - get cart items by customer_id
                $cart = $this->Carts->find()->where(['id' => $cartId, 'customer_id' => $customerId])->first();
                if (!$cart) {
                    $result = ['status' => 'error', 'message' => __('Cart not found')];
                    $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                    goto label;
                }

            } else {
                // Guest user - get cart items by guest_token through Carts table join
                $cart = $this->Carts->find()->where(['id' => $cartId, 'guest_token' => $guestToken])->first();
                if (!$cart) {
                    $result = ['status' => 'error', 'message' => __('Cart not found')];
                    $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                    goto label;
                }
            }
            $cartItems = $this->CartItems->find()
                ->where(['CartItems.cart_id' => $cartId])
                ->contain([
                    'Products' => [
                        'ProductCategories' => [
                            'Categories'
                        ]
                    ],
                    'ProductVariants'
                ])
                ->toArray();
    
            if (empty($cartItems)) {
                $result = ['status' => 'error', 'message' => __('Cart not found or empty')];
                $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                goto label;
            }
    
            // Extract product IDs and calculate cart subtotal
            $productIds = [];
            $cartSubtotal = 0;
            $categoryProducts = [];
            
            foreach ($cartItems as $cartItem) {
                $productIds[] = $cartItem->product_id;
                
                // Check if product variant is present and use variant promotion price, otherwise use main product promotion price
                if (!empty($cartItem->product_variant_id) && !empty($cartItem->product_variant)) {
                    $promotionPrice = $cartItem->product_variant->promotion_price;
                } else {
                    $promotionPrice = $cartItem->product->promotion_price;
                }
                
                $cartSubtotal += $promotionPrice * $cartItem->quantity;
                
                // Group products by category
                if (!empty($cartItem->product->product_categories)) {
                    foreach ($cartItem->product->product_categories as $productCategory) {
                        $categoryId = $productCategory->category_id;
                        if (!isset($categoryProducts[$categoryId])) {
                            $categoryProducts[$categoryId] = [
                                'category_name' => $productCategory->category->name ?? 'Unknown',
                                'products' => [],
                                'subtotal' => 0
                            ];
                        }
                        $categoryProducts[$categoryId]['products'][] = $cartItem->product_id;
                        $categoryProducts[$categoryId]['subtotal'] += $promotionPrice * $cartItem->quantity;
                    }
                }
            }
    
            $orderDate = date('Y-m-d H:i:s');
            $sqlDate = date('Y-m-d');
            $offer = $this->Offers->find()
                ->where([
                    'offer_code' => $couponCode, 
                    'status' => 'A',
                    'redeem_mode IN' => ['Online', 'Both'],
                    'offer_start_date <=' => $orderDate,
                    'AND' => [
                        'offer_end_date IS NOT' => null,
                        'offer_end_date >=' => $orderDate
                    ]
                ])
                ->contain([
                    'OfferShowrooms' => function ($q) {
                        return $q->where(['OfferShowrooms.status' => 'A']);
                    },
                    'OfferCategories' => function ($q) {
                        return $q->where(['OfferCategories.status' => 'A']);
                    },
                    'OfferCustomerGroups' => function ($q) {
                        return $q->where(['OfferCustomerGroups.status' => 'A']);
                    }
                ])
                ->first();
    
            if (!$offer) {
                $result = ['status' => 'error', 'message' => __('Invalid coupon code or it doesnot apply to online orders.')];
                $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                goto label;
            }

            // Validate offer_group restrictions
            if ($offer->offer_group === 'Showroom') {
                // If offer_group is 'Showroom', showroom_id must be provided
                if (empty($showroomId)) {
                    $result = ['status' => 'error', 'message' => __('This coupon is only valid for showroom orders. Please select a showroom.')];
                    $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                    goto label;
                }
            } elseif ($offer->offer_group === 'Global') {
                // If offer_group is 'Global', showroom_id should not be provided (online only)
                if (!empty($showroomId)) {
                    $result = ['status' => 'error', 'message' => __('This coupon is only valid for online orders. Please remove showroom selection.')];
                    $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                    goto label;
                }
            }

            // Validate showroom restrictions
            if (!empty($offer->offer_showrooms)) {
                // If coupon has showroom restrictions, validate against provided showroom
                if (empty($showroomId)) {
                    $result = ['status' => 'error', 'message' => __('This coupon is only valid for specific showrooms. Please select a showroom.')];
                    $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                    goto label;
                }
                
                $validShowroom = false;
                foreach ($offer->offer_showrooms as $offerShowroom) {
                    if ($offerShowroom->showroom_id == $showroomId) {
                        $validShowroom = true;
                        break;
                    }
                }
                
                if (!$validShowroom) {
                    $result = ['status' => 'error', 'message' => __('This coupon is not valid for the selected showroom.')];
                    $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                    goto label;
                }
            }

            // Validate minimum cart value using actual cart subtotal
            if ($cartSubtotal < $offer->min_cart_value) {
                $result = ['status' => 'error', 'message' => __('Minimum cart value of ₹' . $offer->min_cart_value . ' is required. Current cart value: ₹' . number_format($cartSubtotal, 2))];
                $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                goto label;
            }
    
            // Validate products against coupon categories and calculate eligible subtotal
            $validProducts = [];
            $eligibleSubtotal = 0;
            $eligibleCategories = [];
            
            if (!empty($offer->offer_categories)) {
                foreach ($offer->offer_categories as $offerCategory) {
                    $categoryId = $offerCategory->category_id;
                    if (isset($categoryProducts[$categoryId])) {
                        $eligibleCategories[] = [
                            'category_id' => $categoryId,
                            'category_name' => $categoryProducts[$categoryId]['category_name'],
                            'products' => $categoryProducts[$categoryId]['products'],
                            'subtotal' => $categoryProducts[$categoryId]['subtotal']
                        ];
                        $eligibleSubtotal += $categoryProducts[$categoryId]['subtotal'];
                        $validProducts = array_merge($validProducts, $categoryProducts[$categoryId]['products']);
                    }
                }
                
                // Remove duplicates from valid products
                $validProducts = array_unique($validProducts);
                
                if (empty($validProducts)) {
                    $result = ['status' => 'error', 'message' => __('No products in your cart match the coupon categories.')];
                    $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode($result));
                    goto label;
                }
            } else {
                // If no specific categories are set, all products are eligible
                $eligibleSubtotal = $cartSubtotal;
                $validProducts = $productIds;
            }
    
            if ($offer) {
    
                if ($offer->offer_type === 'Flat') {
                    $calculatedDiscount = $offer->discount;
                } elseif ($offer->offer_type === 'Percentage') {
                    // Calculate discount based on eligible subtotal (category-specific products only)
                    $calculatedDiscount = ($eligibleSubtotal * $offer->discount) / 100;
    
                    // Apply max discount cap if exists
                    if (!empty($offer->max_amt_per_disc_value) && $calculatedDiscount > $offer->max_amt_per_disc_value) {
                        $calculatedDiscount = (float)$offer->max_amt_per_disc_value;
                    }
                }
    
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => [
                        'id'          => $offer->id,
                        'offer_code'  => $offer->offer_code,
                        'offer_amount'  => number_format(floatval($calculatedDiscount), 2, '.', ''), // computed discount
                        'discount'  => $offer->discount, // original value                            
                        'offer_type'    => $offer->offer_type,
                        'min_cart_value' => $offer->min_cart_value,
                        'cart_subtotal' => number_format($cartSubtotal, 2, '.', ''),
                        'eligible_subtotal' => number_format($eligibleSubtotal, 2, '.', ''),
                        'eligible_categories' => $eligibleCategories,
                        'cat_products'  => $validProducts,
                        'max_allowed'   => $offer->max_amt_per_disc_value,
                        'is_free_shipping'   => $offer->free_shipping,
                        'mixed_categories' => count($categoryProducts) > 1,
                        'total_categories' => count($categoryProducts)
                    ],
                    'message' => __('Coupon applied successfully!')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Invalid coupon code or conditions not met.')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
    
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function viewProfile()
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $customer = $this->Customers->find()
                    ->contain([
                        'Users',
                        'CustomerAddresses'
                    ])
                    ->where(['Customers.id' => $customerId])
                    ->first();

                if ($customer) {
                    if ($customer['profile_photo'] != null && $customer['profile_photo'] != '') {
                        $customer['profile_photo'] = $this->Media->getCloudFrontURL($customer['profile_photo']);
                    }

                    $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);

                    $currentOrdersCount = $this->Orders->find()
                        ->where([
                            'Orders.customer_id' => $customerId,
                            'Orders.status IN' => ['Pending', 'Processing']
                        ])
                        ->count();

                    $result = [
                        'status' => 'success',
                        'data' => [
                            'customer' => $customer,
                            'user' => $customer->user ? $customer->user->toArray() : null,
                            'loyalty_details' => $loyaltyDetails['data'],
                            'current_orders_count' => $currentOrdersCount
                        ]
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'error', 'message' => __('Customer not found')];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function editProfile()
    {
        $result = [
            'status' => 'error',
            'code' => 200,
            'message' => __('An unexpected error occurred.')
        ];
        $this->response = $this->response->withStatus(200);

        if ($this->request->is(['post', 'put'])) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $userId = $identity['id'];
                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $customer = $this->Customers->find()
                    ->contain(['Users'])
                    ->where(['Customers.id' => $customerId])
                    ->first();

                $data = $this->request->getData();
                if ($customer) {
                    $nameParts = explode(' ', $data['name'], 2);
                    $first_name = $nameParts[0];
                    $last_name = count($nameParts) === 2 ? $nameParts[1] : '';

                    $user_attributes = [
                        'first_name' => $first_name,
                        'last_name' => $last_name,
                        'email' => $data['email'],
                        'mobile_no' => $data['mobile_no'],
                        'country_code' => $data['country_code'] ?? null
                    ];

                    $customer_attributes = [
                        'date_of_birth' => $data['date_of_birth'],
                        'gender' => $data['gender']
                    ];

                    if ($data['email']) {
                        $existingEmail = $this->Users->find()
                            ->where(['email' => $data['email'], 'id !=' => $userId, 'status' => 'A'])
                            ->first();

                        if ($existingEmail) {
                            $result = [
                                'status' => 'error',
                                'code' => 200,
                                'message' => __('This email is already in use by another user.')
                            ];
                            $this->response = $this->response->withStatus(200);
                            goto label;
                        }
                    }

                    // Check if the new mobile number is already used by another user
                    if ($data['mobile_no']) {
                        $existingMobile = $this->Users->find()
                            ->where(['mobile_no' => $data['mobile_no'], 'id !=' => $userId, 'status' => 'A'])
                            ->first();

                        if ($existingMobile) {
                            $result = [
                                'status' => 'error',
                                'code' => 200,
                                'message' => __('This mobile number is already in use by another user.')
                            ];
                            $this->response = $this->response->withStatus(200);
                            goto label;
                        }
                    }

                    if (isset($data['profile_photo']) && $data['profile_photo']->getError() === UPLOAD_ERR_OK) {
                        $profile_photo = $data['profile_photo'];
                        $fileName = trim($profile_photo->getClientFilename());

                        if (!empty($fileName)) {
                            $imageTmpName = $profile_photo->getStream()->getMetadata('uri');
                            $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $folderPath = $uploadFolder;
                            $targetdir = WWW_ROOT . $folderPath;
                            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                            $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                            $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                            if ($uploadResult !== 'Success') {
                                $result = [
                                    'status' => 'error',
                                    'code' => 200,
                                    'message' => __('Profile picture could not be uploaded. Please, try again.')
                                ];
                                $this->response = $this->response->withStatus(200);
                                goto label;
                            } else {
                                $customer_attributes['profile_photo'] = $folderPath . $imageFile;
                            }
                        }
                    } else {
                        $customer_attributes['profile_photo'] = $customer['profile_photo'];
                    }

                    $user = $this->Users->update_user_by_id($userId, $user_attributes);

                    if ($user) {

                        $customer = $this->Customers->update_customer_by_id($customerId, $customer_attributes);

                        $customer['profile_photo'] = $this->Media->getCloudFrontURL($customer['profile_photo']);

                        if ($customer) {
                            $result = [
                                'status' => 'success',
                                'code' => 200,
                                'data' => $customer,
                                'message' => __('Profile updated successfully')
                            ];
                            $this->response = $this->response->withStatus(200);
                        } else {
                            $result = [
                                'status' => 'error',
                                'code' => 200,
                                'message' => __('Failed to update profile'),
                                // 'errors' => $customer->getErrors()
                            ];
                            $this->response = $this->response->withStatus(200);
                        }
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Failed to update user information'),
                            // 'errors' => $user->getErrors()
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Customer not found')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function changePassword()
    {
        if ($this->request->is('post')) {

            $data = $this->request->getData();
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $userId = $identity->get('id');

            if ($data['new_password'] !== $data['confirm_password']) {
                $result = ['status' => 'error', 'message' => __('New password and confirm password do not match')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $changePasswordResult = $this->Users->changeUserPassword($userId, $data['old_password'], $data['new_password']);

            if ($changePasswordResult['status'] === 'success') {
                $result = ['status' => 'success', 'message' => __('Password changed successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'error', 'message' => __('Old password is incorrect.')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    public
    function deleteAccount()
    {
        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {

                // Validate input fields
                if (empty($data['password']) || empty($data['comment'])) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Password and comment are required')
                    ];
                    $this->response = $this->response->withStatus(422);
                    goto label;
                }

                $password = $data['password'];
                $comment = $data['comment'];

                $userId = $identity->get('id');
                $user = $this->Users->get($userId);

                // Verify password
                if (!password_verify($password, $user->password)) {
                    $result = [
                        'status' => 'error',
                        'code' => 401,
                        'message' => __('Incorrect password')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                $attributes = [
                    'status' => 'D',
                    'deletion_comment' => $comment,
                    'deleted_at' => date('Y-m-d H:i:s')
                ];

                $delete_result = $this->Users->update_user_by_id($identity['id'], $attributes);

                if ($delete_result) {

                    $this->WebsiteFunction->userAccountDeleteEmail($user->email, $user->first_name . ' ' . $user->last_name, $comment);

                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => __('Account successfully deleted')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'error', 'message' => __('Address not found or deletion failed')];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function rateProduct($productId = null)
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => 'error', 'code' => 200, 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $customerId = $identity->get('_matchingData')['Customers']['id'];


            if ($data['rating'] < 1 || $data['rating'] > 5) {
                $result = ['status' => 'error', 'code' => 200, 'message' => __('Rating must be between 1 and 5.')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if ($data['rating'] < 5 && empty($data['comment'])) {
                $result = ['status' => 'error', 'code' => 200, 'message' => __('A review comment is required when the rating is less than 5.')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $this->Reviews->getConnection()->begin();

            $reviewData = [
                'customer_id' => $customerId,
                'product_id' => $productId,
                'rating' => $data['rating'],
                'comment' => $data['comment'] ?? null
            ];

            $reviewId = $this->Reviews->add_record($reviewData);

            if ($reviewId) {

                $files = $data['images'] ?? [];
                if (count($files) > 5) {
                    $result = ['status' => 'error', 'code' => 200, 'message' => __('You can upload a maximum of 5 images.')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                $uploadError = false;

                if (!empty($files)) {
                    foreach ($files as $file) {
                        if ($file->getError() === UPLOAD_ERR_OK) {
                            $fileName = trim($file->getClientFilename());

                            if (!empty($fileName)) {
                                $imageTmpName = $file->getStream()->getMetadata('uri');

                                $rand = strtoupper(substr(uniqid(sha1((string)time())), -5));
                                $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                                $filePath = Configure::read('Settings.PRODUCT_REVIEW');
                                $folderPath = $uploadFolder . $filePath;
                                $targetdir = WWW_ROOT . $folderPath;
                                $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                                $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                                $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                                if ($uploadResult === 'Success') {

                                    $imageData = [
                                        'review_id' => $reviewId,
                                        'image_url' => $folderPath . $imageFile
                                    ];

                                    if (!$this->ReviewImages->add_record($imageData)) {
                                        $uploadError = true;
                                    }
                                } else {
                                    $uploadError = true;
                                }
                            }
                        }
                    }
                }

                if ($uploadError) {
                    $this->Reviews->getConnection()->rollback();
                    $result = ['status' => 'error', 'code' => 200, 'message' => __('Failed to save review images.')];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $this->Reviews->getConnection()->commit();
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => __('Thank you for rating this product!'),
                        'data' => ['review_id' => $reviewId]
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $this->Reviews->getConnection()->rollback();
                $result = ['status' => 'error', 'code' => 200, 'message' => __('Failed to save your rating.')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function viewContentPage($identifier = null)
    {
        if ($this->request->is('get')) {
            if (empty($identifier)) {
                $this->response = $this->response->withStatus(200);
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('identifier is required')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $content = $this->ContentPages->find()
                ->where(['content_category_identifier' => $identifier, 'content_category' => 'Website Pages'])
                ->contain(['ContentImages'])
                ->first();

            if (!$content) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Content page not found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                if ($content->image != null || $content->image != '') {
                    $content->image = $this->Media->getCloudFrontURL($content->image);
                }

                if (!empty($content->content_images)) {
                    foreach ($content->content_images as &$image) {
                        $image->image = $this->Media->getCloudFrontURL($image->image);
                    }
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $content
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function viewTermsConditions()
    {
        if ($this->request->is('get')) {

            $content = $this->ContentPages->find()
                ->where(['content_category_identifier' => 'terms-conditions', 'content_category' => 'Website Pages'])
                ->contain(['ContentImages'])
                ->first();

            if (!$content) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Content page not found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                if ($content->image != null || $content->image != '') {
                    $content->image = $this->Media->getCloudFrontURL($content->image);
                }

                if (!empty($content->content_images)) {
                    foreach ($content->content_images as &$image) {
                        $image->image = $this->Media->getCloudFrontURL($image->image);
                    }
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $content
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function viewAboutUs()
    {
        if ($this->request->is('get')) {

            $content = $this->ContentPages->find()
                ->where(['content_category_identifier' => 'about-us', 'content_category' => 'Website Pages', 'status' => 'A'])
                ->contain(['ContentImages'])
                ->first();

            if (!$content) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Content page not found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                if ($content->image != null || $content->image != '') {
                    $content->image = $this->Media->getCloudFrontURL($content->image);
                }

                if (!empty($content->content_images)) {
                    foreach ($content->content_images as &$image) {
                        $image->image = $this->Media->getCloudFrontURL($image->image);
                    }
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $content
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function orderCancellationCategoriesList()
    {
        if ($this->request->is('get')) {

            $categoriesQuery = $this->OrderCancellationCategories->getAllCategories();
            $categoriesArray = $categoriesQuery->toArray();

            if (empty($categoriesArray)) {
                $result = [
                    'status' => 'error',
                    'message' => __('No cancellation categories found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'success',
                    'data' => $categoriesArray
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function orderReturnCategoriesList()
    {
        if ($this->request->is('get')) {

            $categoriesQuery = $this->OrderReturnCategories->getAllCategories();
            $categoriesArray = $categoriesQuery->toArray();

            if (empty($categoriesArray)) {
                $result = [
                    'status' => 'error',
                    'message' => __('No return categories found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'success',
                    'data' => $categoriesArray
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function getDeliveryChargefromShowroom()
    {
        $this->request->allowMethod(['post']);

        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('User is not authenticated')
            ];
            $this->response = $this->response->withStatus(200);
        } else {

            $customerId = $identity->get('_matchingData')['Customers']['id'];

            $showroomId = $this->request->getData('showroom_id');
            $deliveryMode = $this->request->getData('delivery_mode');

            $cart = $this->Carts->find()
                ->where(['customer_id' => $customerId])
                ->contain(['CartItems' => ['Products']])
                ->order(['Carts.created' => 'DESC'])
                ->first();

            $weightQuantityArray = [];
            foreach ($cart->cart_items as $item) {
                if (!empty($item->product->product_weight) && $item->quantity > 0) {
                    $weightQuantityArray[] = [
                        'weight' => $item->product->product_weight,
                        'quantity' => $item->quantity
                    ];
                }
            }

            if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Invalid weight and quantity data.')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $showroom = $this->Showrooms->find()
                    ->select(['city_id'])
                    ->where(['id' => $showroomId])
                    ->first();

                if ($showroom) {
                    $cityId = $showroom->city_id;

                    $deliveryCharges = $this->DeliveryCharges->find()
                        ->where([
                            'city_id' => $cityId,
                            'delivery_mode' => $deliveryMode,
                            'status' => 'A'
                        ])
                        ->order(['weight' => 'ASC'])
                        ->toArray();

                    if (empty($deliveryCharges)) {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('No delivery charges found for the selected criteria.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $total_delivery_charge = 0.00;

                        foreach ($weightQuantityArray as $item) {
                            $weight = $item['weight'];
                            $quantity = $item['quantity'];
                            $applicableCharge = null;

                            foreach ($deliveryCharges as $charge) {
                                if ($weight > $charge->weight) {
                                    $applicableCharge = $charge;
                                } else {
                                    break;
                                }
                            }

                            if ($applicableCharge) {
                                $quotient = $quantity / 2;
                                $remainder = $quantity % 2;
                                $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                                $total_delivery_charge += $calculated_charge;
                            }
                        }

                        $result = [
                            'status' => 'success',
                            'code' => 200,
                            'data' => [
                                'delivery_charge' => $total_delivery_charge
                            ],
                            'message' => __('Delivery charges calculated successfully.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Showroom not found.')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function getDeliveryChargeFromCity()
    {
        $this->request->allowMethod(['post']);

        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('User is not authenticated')
            ];
            $this->response = $this->response->withStatus(200);
        } else {

            $customerId = $identity->get('_matchingData')['Customers']['id'];

            $cityId = $this->request->getData('city_id');
            $deliveryMode = $this->request->getData('delivery_mode');

            $cart = $this->Carts->find()
                ->where(['customer_id' => $customerId])
                ->contain(['CartItems' => ['Products']])
                ->order(['Carts.created' => 'DESC'])
                ->first();

            if (!$cart || empty($cart->cart_items)) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Cart is empty')
                ];
                $this->response = $this->response->withStatus(200);
            } else {


                $weightQuantityArray = [];
                $sizeQuantityArray = [];
                foreach ($cart->cart_items as $item) {
                    if (!empty($item->product->product_weight) && $item->quantity > 0) {
                        $weightQuantityArray[] = [
                            'weight' => $item->product->product_weight,
                            'quantity' => $item->quantity
                        ];
                    }
                    if (!empty($item->product->product_weight) && $item->quantity > 0) {
                        $sizeQuantityArray[] = [
                            'size' => $item->product->product_size,
                            'quantity' => $item->quantity
                        ];
                    }
                }

                if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Invalid weight and quantity data.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = $this->DeliveryCharges->calculateDeliveryCharge($cityId, $deliveryMode, $weightQuantityArray, $sizeQuantityArray);
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => [
                            'delivery_charge' => $result['total_delivery_charge']
                        ],
                        'message' => __('Delivery charges calculated successfully.')
                    ];
                    $this->response = $this->response->withStatus(200);
                    // $deliveryCharges = $this->DeliveryCharges->find()
                    //     ->where([
                    //         'city_id' => $cityId,
                    //         'delivery_mode' => $deliveryMode,
                    //         'status' => 'A'
                    //     ])
                    //     ->order(['weight' => 'ASC'])
                    //     ->toArray();

                    // if (empty($deliveryCharges)) {
                    //     $result = [
                    //         'status' => 'error',
                    //         'code' => 200,
                    //         'message' => __('No delivery charges found for the selected criteria.')
                    //     ];
                    //     $this->response = $this->response->withStatus(200);
                    // } else {
                    //     $total_delivery_charge = 0.00;

                    //     foreach ($weightQuantityArray as $item) {
                    //         $weight = $item['weight'];
                    //         $quantity = $item['quantity'];
                    //         $applicableCharge = null;

                    //         foreach ($deliveryCharges as $charge) {
                    //             if ($weight > $charge->weight) {
                    //                 $applicableCharge = $charge;
                    //             } else {
                    //                 break;
                    //             }
                    //         }

                    //         if ($applicableCharge) {
                    //             $quotient = $quantity / 2;
                    //             $remainder = $quantity % 2;
                    //             $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                    //             $total_delivery_charge += $calculated_charge;
                    //         }
                    //     }

                    //     $result = [
                    //         'status' => 'success',
                    //         'code' => 200,
                    //         'data' => [
                    //             'delivery_charge' => $total_delivery_charge
                    //         ],
                    //         'message' => __('Delivery charges calculated successfully.')
                    //     ];
                    //     $this->response = $this->response->withStatus(200);
                    // }
                }
            }
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //Cancell ordered item befor shipped
    public function orderCancelOld($orderId = null)
    {
        $result = [];
        //$deviceToken= $this->request->getData(name: 'devicetoken');

        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $customerId = $identity->get('_matchingData')['Customers']['id'];
            $custDetail = $this->Customers->get($customerId);
            $deviceToken = $custDetail['fcm_token'];

            $userId = $identity['id'];

            $user = $this->Users->get($userId);

            if (empty($orderId)) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Order ID is required')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            try {
                $order = $this->Orders->get($orderId, [
                    'contain' => ['OrderItems']
                ]);
            } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Order not found')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if ($order->customer_id !== $customerId) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Unauthorized to cancel this order')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            // if ($order->payment_method == 'Credit') {
            //         $result = [
            //         'status' => 'error',
            //         'code' => 200,
            //             'message' => __('The order cannot be canceled as it was made on credit')
            //         ];
            //         $this->response = $this->response->withStatus(200);
            //     goto label;
            // }

            $anyDelivered = false;
            foreach ($order->order_items as $item) {
                if ($item->status === 'Shipped') {
                    $anyDelivered = true;
                    break;
                }

                if ($item->status === 'Delivered') {
                    $anyDelivered = true;
                    break;
                }
            }

            if ($anyDelivered) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Cannot cancel order as one or more items have been shipped/delivered')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $existingCancellation = $this->OrderCancellations->find()
                ->where(['order_id' => $orderId])
                ->first();

            if ($existingCancellation) {
                if ($existingCancellation->status === 'Pending') {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Cancellation has already been initiated')
                    ];
                } elseif ($existingCancellation->status === 'Approved') {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Cancellation has already in progress')
                    ];
                } elseif ($existingCancellation->status === 'Completed') {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('This order has already been cancelled')
                    ];
                }
                $this->response = $this->response->withStatus(200);
                goto label;

                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('This order has already been cancelled')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $data = $this->request->getData();
            
            if($order->payment_method == 'Cash on Delivery') {
                $order->status = 'Cancelled';
                $order_status = 'Cancelled';
                $tracking_status = 'Cancelled';
            } else {
                $order->status = 'Pending Cancellation';
                $order_status = 'Pending';
                $tracking_status = 'Pending Cancellation';
            }

            $cancellationData = [
                'order_id' => $orderId,
                'order_cancellation_category_id' => $data['reason_id'],
                'customer_id' => $customerId,
                'reason' => $data['reason'] ?? null,
                'status' => $order_status,
                'canceled_at' => date('Y-m-d H:i:s')
            ];
            
            if ($this->Orders->save($order)) {
                // Reduce loyalty points for this cancelled order
                $orderSubtotal = (float)$order->subtotal_amount;
                $loyaltyResult = $this->Loyalty->reduceLoyaltyPointsForOrder(
                    $customerId, 
                    $orderSubtotal, 
                    'Order cancellation'
                );
                
                $cancellation = $this->OrderCancellations->add_record($cancellationData);

                if ($cancellation) {
					$itemStatus = ($order->status === 'Cancelled') ? 'Cancelled' : 'Pending Cancellation';
					$this->OrderItems->updateAll(['status' => $itemStatus], ['order_id' => $orderId]);

                    $trackingData = [
                        'order_id' => $orderId,
                        'status' => $tracking_status,
                        'comment' => $data['reason'] ?? null,
                    ];
                    $tracking = $this->OrderTrackingHistories->add_record($trackingData);

                                    // Get tracking history for the order
                $trackingHistory = $this->OrderTrackingHistories->find()
                    ->select(['id', 'order_id', 'status', 'comment', 'updated'])
                    ->where(['order_id' => $orderId])
                    ->order(['updated' => 'ASC'])
                    ->toArray();

                    $formattedTrackingHistory = array_map(function ($tracking) {
                        return [
                            'status' => $tracking->status,
                            'comment' => $tracking->comment,
                            'updated_at' => $tracking->updated,
                        ];
                    }, $trackingHistory);

                    $category = $this->OrderCancellationCategories->find()
                        ->select(['name'])
                        ->where(['id' => $data['reason_id']])
                        ->first();

                    $reasonName = $category ? $category->name : __('No reason provided');

                    $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
                    $to = $adminEmails[0];
                    $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
                    $subject = 'Order Cancellation Request';
                    $template = 'order_cancellation';
                    $viewVars = [
                        'order_number' => $order['order_number'],
                        'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
                        'reason' => $reasonName,
                        'comment' => $data['reason'] ?? 'No comment provided',
                        'canceled_at' => $cancellationData['canceled_at'],
                    ];

                    // $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
                    $sendEmail = $this->WebsiteFunction->orderCancellationRequestEmail($data['order_item_id'], $data['reason_id'], $data['reason'], date('Y-m-d H:i:s'));

                    if ($sendEmail) {
                        $result = [
                            'status' => 'success',
                            'code' => 200,
                            'message' => __('Order cancelled has been initiated successfully, and an email notification has been sent to the admin.'),
                            'data' => [
                                'order_id' => $orderId,
                                'order_number' => $order['order_number'],
                                'status' => $order->status,
                                'tracking_history' => $formattedTrackingHistory
                            ]
                        ];
                        //Order cancell pushnotification.
                        $title = 'Order cancelled - #' . $order['order_number'];
                        $body  = 'Your order has been cancelled.';
                        $customData = [
                            "notification_type" => "order",
                            "id" => (string)$orderId,
                            "category" => "order"
                        ];
                        $response = $this->Global->sendNotification(
                            [$deviceToken],
                            $title,
                            $body,
                            $customData
                        );
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Order cancelled has been initiated successfully, but the email notification could not be sent.'),
                            'data' => [
                                'order_id' => $orderId,
                                'order_number' => $order['order_number'],
                                'status' => $order->status,
                                'tracking_history' => $formattedTrackingHistory
                            ]
                        ];
                        
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to cancel the order')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to cancel the order')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //Return item after delivery
    public function orderItemCancelOld($orderItemId = null)
    {
        if (!$this->request->is('post')) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $identity = $this->request->getAttribute('identity');
        if (!$identity) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('User is not authenticated')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $customerId = $identity->get('_matchingData')['Customers']['id'];
        $custDetail = $this->Customers->get($customerId);
        $deviceToken = $custDetail['fcm_token'];

        if (empty($orderItemId)) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Order item id is required')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }
        $data = $this->request->getData();
        $cancelQuantity = isset($data['cancel_quantity']) ? (int)$data['cancel_quantity'] : null;
        try{
            $orderItem = $this->OrderItems->get($orderItemId, [
                'contain' => ['Products']
            ]);
            if ($cancelQuantity && $cancelQuantity > 0) {
                if ($cancelQuantity > $orderItem->quantity) {
                    $result = [
                        'status' => 'error',
                        'code' => 400,
                        'message' => __('Cancelled quantity cannot be greater than ordered quantity.')
                    ];
                    $this->response = $this->response->withStatus(400);
                    goto label;
                } else if ($cancelQuantity < $orderItem->quantity) {
                    // Split item: reduce original, create new for cancellation
                    $originalQuantity = $orderItem->quantity;
                    $orderItem->quantity = $originalQuantity - $cancelQuantity;
                    $orderItem->total_price = $orderItem->price * $orderItem->quantity;
                    $this->OrderItems->save($orderItem);
                    $newItemData = $orderItem->toArray();
                    unset($newItemData['id'], $newItemData['product']); // Remove id and product association
                    $newItemData['quantity'] = $cancelQuantity;
                    $newItemData['total_price'] = $orderItem->price * $cancelQuantity;
                    $newItemData['status'] = 'Pending Cancellation';
                    $newItem = $this->OrderItems->newEntity($newItemData);
                    $this->OrderItems->save($newItem);
                    $orderItemId = $newItem->id;
                    $orderItem = $newItem;
                }
            }
            // Reduce loyalty points for this order item (full cancellation only)
            if ($cancelQuantity === null || $cancelQuantity == $orderItem->quantity) {
                $orderItemSubtotal = (float)$orderItem->total_price;
                $loyaltyResult = $this->Loyalty->reduceLoyaltyPointsForOrder(
                    $customerId, 
                    $orderItemSubtotal, 
                    'Order item cancellation'
                );
                
            }
        } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Order item not found')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $order = $this->Orders->get($orderItem->order_id);
        if ($order->customer_id !== $customerId) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Unauthorized to cancel this order')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }
        if ($orderItem->status === 'Shipped') {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Cannot cancel item has been shipped')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Check for existing cancellation
        $existingCancellation = $this->OrderCancellations->find()
            ->where(['order_item_id' => $orderItemId])
            ->first();

        if ($existingCancellation) {
            if ($existingCancellation->status === 'Pending') {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Cancellation has already been initiated')
                ];
            } elseif ($existingCancellation->status === 'Approved') {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Cancellation has already in progress')
                ];
            } elseif ($existingCancellation->status === 'Completed') {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('This item has already been cancelled')
                ];
            }
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $userId = $identity['id'];
        $user = $this->Users->get($userId);

        // Process cancellation
        $data = $this->request->getData();
    
        
        $cancellationData = [
            'order_item_id' => $orderItemId,
            'order_cancellation_category_id' => $data['reason_id'],
            'reason' => $data['reason'] ?? null,
            'status' => 'Pending',
            'customer_id' => $customerId,
            'order_id' => $orderItem->order_id,
            'canceled_at' => date('Y-m-d H:i:s')
        ];

        $orderItem->status = 'Pending Cancellation';

        if ($this->OrderItems->save($orderItem)) {
           
            // Update order status based on item statuses
            $this->updateOrderStatusBasedOnItems($order->id);
           
            $cancellation = $this->OrderCancellations->add_record($cancellationData);
           
            if ($cancellation) {
                $trackingData = [
                    'order_item_id' => $orderItemId,
                    'status' => 'Pending Cancellation',
                    'comment' => $data['comment'] ?? null,
                ];
                $this->OrderTrackingHistories->add_record($trackingData);

                // Get tracking history for the order item
                $trackingHistory = $this->OrderTrackingHistories->find()
                    ->select(['id', 'order_id', 'order_item_id', 'status', 'comment', 'updated'])
                    ->where(['order_item_id' => $orderItemId])
                    ->order(['updated' => 'ASC'])
                    ->toArray();

                $formattedTrackingHistory = array_map(function ($tracking) {
                    return [
                        'status' => $tracking->status,
                        'comment' => $tracking->comment,
                        'updated_at' => $tracking->updated,
                    ];
                }, $trackingHistory);

                $category = $this->OrderCancellationCategories->find()
                    ->select(['name'])
                    ->where(['id' => $data['reason_id']])
                    ->first();

                $reasonName = $category ? $category->name : __('No reason provided');

                $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
                $to = $adminEmails[0];
                $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
                $subject = 'Order Cancellation Request';
                $template = 'order_item_cancellation';
                $viewVars = [
                    'order_number' => $order['order_number'],
                    'order_item_product' => $orderItem['product']['name'] ?? '-',
                    'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
                    'reason' => $reasonName,
                    'comment' => $data['reason'] ?? 'No comment provided',
                    'canceled_at' => $cancellationData['canceled_at'],
                ];

                $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);

                if ($sendEmail) {

                    //Order item cancel pushnotification.
                    $title = 'Order Item cancelled - #' . $order['order_number'];
                    $body  = 'Your order item has been cancelled.';
                    $customData = [
                        "notification_type" => "orderItem",
                        "id" => (string)$orderItemId,
                        "category" => "order"
                    ];
                    $response = $this->Global->sendNotification(
                        [$deviceToken],
                        $title,
                        $body,
                        $customData
                    );

                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => __('Order item cancelled has been initiates successfully, and an email notification has been sent to the admin.'),
                        'data' => [
                            'order_item_id' => $orderItemId,
                            'order_id' => $orderItem->order_id,
                            'status' => $orderItem->status,
                            'tracking_history' => $formattedTrackingHistory
                        ]
                    ];
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Order item cancelled has been initiates successfully, but the email notification could not be sent to the admin.'),
                        'data' => [
                            'order_item_id' => $orderItemId,
                            'order_id' => $orderItem->order_id,
                            'status' => $orderItem->status,
                            'tracking_history' => $formattedTrackingHistory
                        ]
                    ];
                }
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to initiate cancellation')
                ];
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Failed to update order item status')
            ];
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function productReturn($orderItemId = null)
    {
        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $customerId = $identity->get('_matchingData')['Customers']['id'];

            $custDetail = $this->Customers->get($customerId);
            $deviceToken = $custDetail['fcm_token'];

            $userId = $identity['id'];

            $user = $this->Users->get($userId);

            if (empty($orderItemId)) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Order item ID is required')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            try {
                $orderItem = $this->OrderItems->get($orderItemId, [
                    'contain' => ['Products']
                ]);
            } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Order item not found')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $order = $this->Orders->get($orderItem['order_id']);
            if ($order->customer_id !== $customerId) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Unauthorized to return this order')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
            $data = $this->request->getData();
            $returnQuantity = isset($data['return_quantity']) ? (int)$data['return_quantity'] : null;
            $returnShowroomId = $data['return_showroom_id'] ?? null; // Get return showroom ID
            if ($returnQuantity && $returnQuantity > $orderItem->quantity) {
                $result = [
                    'status' => 'error',
                    'code' => 400,
                    'message' => __('Return quantity cannot be greater than ordered quantity.')
                ];
                $this->response = $this->response->withStatus(400);
                goto label;
            }
            if ($returnQuantity && $returnQuantity > 0 && $returnQuantity < $orderItem->quantity) {
                $originalQuantity = $orderItem->quantity;
                $orderItem->quantity = $originalQuantity - $returnQuantity;
                $orderItem->total_price = $orderItem->price * $orderItem->quantity;
                $this->OrderItems->save($orderItem);
                $newItemData = $orderItem->toArray();
                unset($newItemData['id'], $newItemData['product']);
                $newItemData['quantity'] = $returnQuantity;
                $newItemData['total_price'] = $orderItem->price * $returnQuantity;
                $newItemData['status'] = 'Pending Return';
                $newItem = $this->OrderItems->newEntity($newItemData);
                $this->OrderItems->save($newItem);
                $orderItemId = $newItem->id;
                $orderItem = $newItem;
            }
            // Reduce loyalty points for this order item (full return only)
            if ($returnQuantity === null || $returnQuantity == $orderItem->quantity) {
                $orderItemSubtotal = (float)$orderItem->total_price;
                $loyaltyResult = $this->Loyalty->reduceLoyaltyPointsForOrder(
                    $customerId, 
                    $orderItemSubtotal, 
                    'Product return'
                );
                
            }
            $returnData = [
                'order_id' => $orderItem['order_id'],
                'customer_id' => $customerId,
                'customer_address_id' => $data['customer_address_id'],
                'order_item_id' => $orderItemId,
                'order_return_category_id' => $data['reason_id'],
                'reason' => $data['reason'] ?? null,
                'status' => 'Pending',
                'requested_at' => date('Y-m-d H:i:s'),
                'return_amount' => (float)$orderItem->total_price, // Fixed: Use actual item total price
                'return_quantity' => $returnQuantity ?? $orderItem->quantity, // Fixed: Capture return quantity
                'requested_by' => $customerId, // Fixed: Add requested_by field
                'request_type' => 'Return', // Fixed: Add request_type field
                'return_to' => !empty($returnShowroomId) ? 'Showroom' : null, // Fixed: Set return location
                'return_to_id' => $returnShowroomId, // Fixed: Set return showroom ID
                'pickup_required' => 'No', // Fixed: Add pickup_required field
                'pickup_charge' => 0 // Fixed: Add pickup_charge field
            ];

            $orderItem->status = 'Pending Return';

            if ($this->OrderItems->save($orderItem)) {

                $returnId = $this->OrderReturns->add_record($returnData);

                if ($returnId) {

                    $files = $data['images'] ?? [];
                    $uploadError = false;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            if ($file->getError() === UPLOAD_ERR_OK) {
                                $fileName = trim($file->getClientFilename());

                                if (!empty($fileName)) {
                                    $imageTmpName = $file->getStream()->getMetadata('uri');

                                    $rand = strtoupper(substr(uniqid(sha1((string)time())), -5));
                                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                                    $filePath = Configure::read('Settings.PRODUCT_RETURN');
                                    $folderPath = $uploadFolder . $filePath;
                                    $targetDir = WWW_ROOT . $folderPath;
                                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                                    $uploadResult = $this->Media->upload($imageTmpName, $targetDir, $imageFile, $folderPath);
                                    if ($uploadResult === 'Success') {
                                        $imageData = [
                                            'order_return_id' => $returnId,
                                            'image_url' => $folderPath . $imageFile
                                        ];

                                        if (!$this->OrderReturnImages->add_record($imageData)) {
                                            $uploadError = true;
                                        }
                                    } else {
                                        $uploadError = true;
                                    }
                                }
                            }
                        }
                    }

                    $trackingData = [
                        'order_item_id' => $orderItemId,
                        'status' => 'Pending Return',
                        'comment' => $data['comment'] ?? null,
                    ];
                    $tracking = $this->OrderTrackingHistories->newEntity($trackingData);
                    $this->OrderTrackingHistories->save($tracking);


                    $category = $this->OrderReturnCategories->find()
                        ->select(['name'])
                        ->where(['id' => $data['reason_id']])
                        ->first();

                    $reasonName = $category ? $category->name : __('No reason provided');

                    $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
                    $to = $adminEmails[0];
                    $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
                    $subject = 'Order Item Return Request';
                    $template = 'order_item_return';
                    $viewVars = [
                        'order_number' => $order['order_number'],
                        'order_item_product' => $orderItem['product']['name'],
                        'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
                        'reason' => $reasonName,
                        'comment' => $data['reason'] ?? 'No comment provided',
                        'requested_at' => $returnData['requested_at'],
                    ];

                    
                    $title = 'Order Return Request - #' . $order['order_number'];
                    $body  = 'Order Item Return Request';
                    $customData = [
                        "notification_type" => "order",
                        "id" => (string)$orderItemId,
                        "category" => "order"
                    ];
                    $response = $this->Global->sendNotification(
                        [$deviceToken],
                        $title,
                        $body,
                        $customData
                    );

                    // $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
                    $sendEmail = $this->WebsiteFunction->orderReturnRequestEmail($orderItemId, $data['order_return_category_id'], $data['reason'], date('Y-m-d H:i:s'));

                    if ($sendEmail) {
                        $result = [
                            'status' => 'success',
                            'code' => 200,
                            'message' => __('Order item return has been initiated successfully, and an email notification has been sent to the admin.')
                        ];
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Order item return has been initiated successfully, but the email notification could not be sent to the admin.')
                        ];
                    }
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to process the return')
                    ];
                }
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to update order item status')
                ];
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
        }
        $this->response = $this->response->withStatus(200);
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //Method to update order

    public function updateOrder()
    {
        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');
            $guestToken = $this->request->getHeader('guest-token')[0] ?? null;

            if (!$identity && !$guestToken) {
                $result = ['status' => 'error', 'message' => __('Customer ID or guest token is required')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $customerId = $identity ? $identity->get('_matchingData')['Customers']['id'] : null;
            $cartConditions = $customerId ? ['Carts.customer_id' => $customerId] : ['Carts.guest_token' => $guestToken];

            $cart = $this->Carts->find()
                ->where($cartConditions)
                ->contain(['CartItems' => ['Products']])
                ->order(['Carts.created' => 'DESC'])
                ->first();

            if (!$cart || empty($cart->cart_items)) {
                $result = ['status' => 'error', 'message' => __('Cart is empty.')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $subTotal = 0;
            $totalItems = 0;
            $discountAmount = 0;

            foreach ($cart->cart_items as $item) {

                if($item->product_variant_id) {
                    $variantDetail = $this->ProductVariants->getVariantById($item->product_variant_id);
                    $subTotal += $variantDetail->promotion_price * $item->quantity;
                    $totalItems += $item->quantity;
                    $discountAmount += ($variantDetail->sales_price - $variantDetail->promotion_price) * $item->quantity;

                } else {
                    $subTotal += $item->product->promotion_price * $item->quantity;
                    $totalItems += $item->quantity;
                    $discountAmount += ($item->product->sales_price - $item->product->promotion_price) * $item->quantity;
                }            
            }

            $deliveryCharges = (float)($this->request->getData('delivery_charge') ?? 0);
            $couponAmount = (float)($this->request->getData('coupon_amount') ?? 0);
            $couponType = $this->request->getData('coupon_type') ?? '';
            $couponMinCart = (float)($this->request->getData('coupon_min_cart_value') ?? 0);
            $showroomCharge = $this->request->getData('showroom_charge') ?: 0;
            $pickupCharge = $this->request->getData('pickup_charge') ?: 0;
            $loyaltyPoints = (float)($this->request->getData('loyalty_points') ?: 0);
            $shippingMethod = $this->request->getData('shipping_method') ?? 'standard';

            if ($couponAmount > 0 && $subTotal < $couponMinCart) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Coupon cart value does not match with order cart, hence removing coupon applied.')
                ]));
            }

            $loyaltyPointsRemaining = 0;
            $loyaltyAmount = 0;

            if ($customerId) {
                $loyaltyRecord = $this->Loyalty->find()
                    ->where(['customer_id' => $customerId, 'status' => 'A'])
                    ->first();
                if ($loyaltyRecord) {
                    $loyaltyPointsRemaining = $loyaltyRecord->points;
                    if ($loyaltyPoints > $loyaltyPointsRemaining) {
                        return $this->response->withType('application/json')->withStringBody(json_encode([
                            'status' => 'error',
                            'message' => __('You cannot redeem more loyalty points than you currently have.')
                        ]));
                    }
                }

                if ($loyaltyPoints > 0) {
                    $loyaltyPoints = min($loyaltyPoints, $loyaltyPointsRemaining);

                    $customerGroupsQuery = $this->CustomerGroupMappings
                        ->find()
                        ->select(['customer_group_id'])
                        ->where(['customer_id' => $customerId, 'status' => 'A'])
                        ->all();
                    $customerGroupIds = collection($customerGroupsQuery)->extract('customer_group_id')->toList();

                    if (!empty($customerGroupIds)) {
                        $loyaltySetting = $this->LoyaltySettings
                            ->find()
                            ->where([
                                'customer_group_id IN' => $customerGroupIds,
                                'status' => 'A'
                            ])
                            ->order(['redeem_point_value' => 'DESC'])
                            ->first();

                        if ($loyaltySetting) {
                            $pointValue = $loyaltySetting->redeem_point_value;
                            $pointChunk = $loyaltySetting->redeem_points;

                            // Fix: Calculate per point worth
                            $perPointValue = $pointValue / $pointChunk;
                            $loyaltyAmount = $loyaltyPoints * $perPointValue;

                            if ($loyaltyAmount > $subTotal) {
                                $loyaltyAmount = $subTotal;
                                $loyaltyPoints = floor($subTotal / $perPointValue);
                            }
                        }
                    }


                }
            }

            $totalAmount = $subTotal + $deliveryCharges - $loyaltyAmount;

            if ($couponType === 'Flat') {
                $totalAmount -= $couponAmount;
            } elseif ($couponType === 'Percentage') {
                $couponAmount = ($subTotal * ($couponAmount / 100));
                $totalAmount -= $couponAmount;
            }

            $totalAmount = max(0, $totalAmount);

            $paymentGatewayEnable = false;
            $walletApplyAmount = 0;
            $finalAmount = $totalAmount;
            $mainWalletAmount = 0;

            if ($customerId) {
                $wallet = $this->Wallets->getMyWalletAmount($customerId);
                $mainWalletAmount = $wallet->balance ?? 0;

                $data = $this->request->getData();
                $requestedWalletUse = (float)($data['wallet_redeem_amount'] ?? 0);

                if ($requestedWalletUse < 0) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('Invalid wallet redeem amount.')
                    ]));
                }

                $walletToApply = min($requestedWalletUse, $mainWalletAmount, $finalAmount);

                if ($walletToApply > 0) {
                    $walletApplyAmount = $walletToApply;
                    $finalAmount -= $walletApplyAmount;

                    if ($finalAmount <= 0) {
                        $paymentGatewayEnable = true;
                        $finalAmount = 0;
                        $data['payment_method'] = 'Wallet';
                    }
                }
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => [
                    'total_items' => $totalItems,
                    'subtotal' => $subTotal,
                    'discount_amount' => $discountAmount,
                    'coupon_amount' => $couponAmount,
                    'loyalty_points_redeemed' => $loyaltyPoints,
                    'loyalty_points_worth' => number_format($loyaltyAmount, 2),
                    'loyalty_points_remaining' => $loyaltyPointsRemaining - $loyaltyPoints,
                    'delivery_charge' => $deliveryCharges,
                    'total_amount' => $finalAmount,
                    'wallet_redeem_amount' => $walletApplyAmount,
                    'wallet_amount' => $mainWalletAmount,
                    'payment_gateway_enable' => $paymentGatewayEnable,
                    'shipping_method' => $shippingMethod
                ],
                'message' => __('Order review data updated successfully.')
            ];

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }


    //M
    public function confirmOrder()
    {
        try{
            //$deviceToken= $this->request->getData('devicetoken');
            if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {

                $customerId = $identity->get('_matchingData')['Customers']['id'];
                $custDetail = $this->Customers->get($customerId);
                $deviceToken = $custDetail['fcm_token'];

                
                $data = $this->request->getData();
                $payment_method = $data['payment_method'];
                $total_amount = $data['total_amount'];
                $order_online_source = $data['order_online_source'];
                $order_no = $this->Orders->generateUniqueOrderNum($order_online_source);

                // Handle coupon/offer information
                $offer_id = null;
                $discount_amount = 0;
                $offer_type = null;
                $offer_code = null;
                
                if (!empty($data['coupon_code']) && !empty($data['coupon_amount']) && $data['coupon_amount'] > 0) {
                    // Fetch offer details from coupon code
                    $offer = $this->Offers->find()
                        ->where(['offer_code' => $data['coupon_code'], 'status' => 'A'])
                        ->first();
                    
                    if ($offer) {
                        $offer_id = $offer->id;
                        $offer_amount = (float)$data['coupon_amount'];
                        $offer_type = $offer->offer_type;
                        $offer_code = $offer->offer_code;
                        
                        // Validate that the coupon amount matches the offer
                        // $expectedDiscount = 0;
                        // if ($offer->offer_type === 'Flat') {
                        //     $expectedDiscount = (float)$offer->discount;
                        // } elseif ($offer->offer_type === 'Percentage') {
                        //     $expectedDiscount = ($total_amount * (float)$offer->discount) / 100;
                        //     // Apply max discount cap if exists
                        //     if (!empty($offer->max_amt_per_disc_value) && $expectedDiscount > $offer->max_amt_per_disc_value) {
                        //         $expectedDiscount = (float)$offer->max_amt_per_disc_value;
                        //     }
                        // }
                        
                        // Validate coupon amount (with small tolerance for floating point precision)
                        // if (abs($discount_amount - $expectedDiscount) > 0.01) {
                        //     $result = [
                        //         'status' => 'error',
                        //         'code' => 400,
                        //         'message' => __('Invalid coupon amount. Expected: ₹' . number_format($expectedDiscount, 2) . ', Received: ₹' . number_format($discount_amount, 2))
                        //     ];
                        //     $this->response = $this->response->withStatus(400);
                        //     goto label;
                        // }
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 400,
                            'message' => __('Invalid or expired coupon code')
                        ];
                        $this->response = $this->response->withStatus(400);
                        goto label;
                    }
                }

                $data['customer_id'] = $customerId;
                $data['order_number'] = $order_no;
                $data['order_date'] = date('Y-m-d H:i:s');
                $data['status'] = "Pending";
                $add_order = 0;

                if ($data['payment_gateway_enable'] == true) {     //COD
                        $result2 = [
                            'message' => __('Your order has been successfully created.')
                        ];
                        $add_order = 1;
                        $payment_status = 'Paid';
                        $data['payment_method'] = "Wallet";
                }
                if ($payment_method == 'MTN MoMo') {      //MTN
                    $mobile_no = $data['mobile_no'];
                    // call pg api
                    $pg_result = $this->Mtn->initiatePayment($order_no, $total_amount, $mobile_no);
                    $api_status = $pg_result['status'];
                    $httpcode = $pg_result['httpcode'];
                  
                    if ($api_status == 'error') {

                        if ($httpcode == 200) {
                            $result = [
                                'status' => 'error',
                                'code' => $httpcode,
                                'payment_status' => $pg_result['payment_status'],
                                'message' => $pg_result['reason']
                            ];
                        } else {
                            $result = [
                                'status' => 'error',
                                'code' => $httpcode,
                                'error_code' => $pg_result['error_code'],
                                'message' => $pg_result['message']
                            ];
                        }
                        $this->response = $this->response->withStatus($httpcode);
                        goto label;
                    } else {
                        $s_payment_status = $pg_result['payment_status'];
                        if ($s_payment_status == 'SUCCESSFUL') {
                            $payment_status = 'Paid';
                            $s_message = __('Payment Successful! Thank you for your payment; your order has been successfully created.');
                        } else {
                            $payment_status = 'Pending';
                            $s_message = __('Payment is still pending! your order has been successfully created.');
                        }
                        $result2 = [
                            'payment_status' => $s_payment_status,
                            'message' => $s_message
                        ];
                        $add_order = 1;
                    }
                } elseif ($payment_method == 'Wave') {    //WAVE

                    // call pg api
                    $pg_result = $this->Wave->createCheckoutSession($order_no, $total_amount);
                    $api_status = $pg_result['status'];
                    $httpcode = $pg_result['httpcode'];
                    if ($api_status == 'error') {
                        $result = [
                            'status' => 'error',
                            'code' => $httpcode,
                            'error_code' => $pg_result['error_code'],
                            'message' => $pg_result['message']
                        ];
                        $this->response = $this->response->withStatus($httpcode);
                        goto label;
                    } else {

                        $wave_checkout_sessionID = $pg_result['data']['id'];
                        $wave_checkout_status = $pg_result['data']['checkout_status'];
                        $wave_client_reference = $pg_result['data']['client_reference'];
                        $wave_error_url = $pg_result['data']['error_url'];
                        $wave_success_url = $pg_result['data']['success_url'];
                        $wave_business_name = $pg_result['data']['business_name'];
                        $wave_payment_status = $pg_result['data']['payment_status'];
                        $wave_launch_url = $pg_result['data']['wave_launch_url'];
                        $wave_when_created = $pg_result['data']['when_created'];
                        $wave_when_expires = $pg_result['data']['when_expires'];

                        $result2 = [
                            'message' => __('Checkout session created'),
                            'wave_launch_url' => $wave_launch_url
                        ];
                        $payment_status = 'Pending';
                        $add_order = 1;
                    }
                } elseif ($payment_method == 'Waiver') {     //COD

                    $result2 = [
                        'message' => __('Your order has been successfully created.')
                    ];
                    $payment_status = intval($data['total_amount']) > 0 ? 'Pending' : 'Paid';
                    $add_order = 1;
                } elseif ($payment_method == 'Cash on Delivery') {     //COD

                    $result2 = [
                        'message' => __('Your order has been successfully created.')
                    ];
                    $payment_status = 'Pending';
                    $add_order = 1;
                } elseif ($payment_method == 'Credit') {     //COD

                    $result2 = [
                        'message' => __('Your order has been successfully created.')
                    ];
                    $payment_status = 'Pending';
                    $add_order = 1;
                } elseif ($payment_method == 'Wallet') {     //COD

                    $result2 = [
                        'message' => __('Your order has been successfully created.')
                    ];
                    $payment_status = 'Paid';
                    $add_order = 1;
                } else {

                    $result = [
                        'status' => 'error',
                        'message' => __('Please select payment method')
                    ];
                    goto label;
                }
                if ($add_order) {
                    // if ($data['delivery_mode'] == 'delivery') {
                    //     $data['status'] = 'Approved';
                    // }

                    // Normalize blank string to null before anything else
                    if (empty($data['cart_credit_payment_id'])) {
                        $data['cart_credit_payment_id'] = null;
                    } else {
                        $cpid = $data['cart_credit_payment_id'];
                        $exists = $this->CartCreditPayments->exists(['id' => $cpid]);
                        if (!$exists) {
                            $data['cart_credit_payment_id'] = null;
                        }
                    }

                    $order = $this->Orders->newEmptyEntity();
                    $transactionData = [
                        'invoice_number' => $this->Transactions->generateUniqueInvoiceNum(),
                        'transaction_number' => $this->Transactions->generateUniqueTransactionNum(),
                        'transaction_date' => date('Y-m-d H:i:s'),
                        'amount' => $data['total_amount'],
                        'payment_method' => $data['payment_method'],
                        'payment_status' => $payment_status,
                    ];

                    // Add MTN extra details
                    if ($payment_method == 'MTN MoMo') {
                        $transactionData += [                            
                            'transactionID' => $pg_result['data']['financialTransactionId'],
                            'momo_payerID'  => $pg_result['data']['payer']['partyId'],
                            'momo_referenceID' => $pg_result['referenceId'],
                            'momo_externalID' => $pg_result['data']['externalId'],
                        ];
                    }

                    // Add Wave extra details
                    if ($payment_method == 'Wave') {
                        $transactionData += [
                            //'transactionID' => null,
                            'wave_checkout_sessionID' => $wave_checkout_sessionID,
                            'wave_checkout_status' => $wave_checkout_status,
                            'wave_client_referenceID' => $wave_client_reference,
                        ];
                    }

                    $data['transactions'] = [$transactionData];
                    $data['delivery_date'] = $this->CalculateEstimatedDelivery(deliveryMode: $data['delivery_mode_type']);
                    $data['payment_method'] = $data['payment_method'];
                    $data['wallet_redeem_amount'] = $data['wallet_redeem_amount'] ?? 0;
                    $data['wallet_payment_type'] = 'debit'; // 'credit' or 'debit'
                    
                    // Add offer information to order data
                    if ($offer_id) {
                        $data['offer_id'] = $offer_id;
                        $data['offer_amount'] = $offer_amount;
                    }
                    
                    // $data['delivery_date'] = [$transactionData];

                    $order = $this->Orders->patchEntity($order, $data, [
                        'associated' => ['OrderItems', 'Transactions']
                    ]);
                    
                    $save_order = $this->Orders->save($order);

                    if ($save_order) {

                        if ($data['loyalty_amount'] > 0 && $data['loyalty_points_redeemed'] > 0) {
                            $loyalty = $this->Loyalty->find()->where(['customer_id' => $data['customer_id'], 'status' => 'A'])->first();
                            if ($loyalty) {
                                $loyalty->spent_amount += $data['loyalty_amount'];
                                $loyalty->points -= $data['loyalty_points_redeemed'];
                                $this->Loyalty->save($loyalty); // ✅ Save the changes
                            }
                        }

                        $customerGroupsQuery = $this->CustomerGroupMappings
                            ->find()
                            ->select(['customer_group_id'])
                            ->where(['customer_id' => $data['customer_id'], 'status' => 'A'])
                            ->all();

                        $customerGroupIds = [];
                        foreach ($customerGroupsQuery as $row) {
                            $customerGroupIds[] = $row->customer_group_id;
                        }

                        if (!empty($customerGroupIds)) {
                            $loyalty_setting = $this->LoyaltySettings
                                ->find()
                                ->where([
                                    'customer_group_id IN' => $customerGroupIds,
                                    // 'earning_threshold_amount <=' => $data['subtotal_amount'],
                                    'status' => 'A'
                                ])
                                ->order(['earning_threshold_amount' => 'DESC'])
                                ->first();

                            if ($loyalty_setting) {
                                $subtotal_amount = round((float)$data['total_amount'] - (float)$data['delivery_charge'], 2);
                                $earning_threshold_amount = $loyalty_setting->earning_threshold_amount;
                                $points = $loyalty_setting->earning_points;

                                $earned_points = 0;
                                if ($earning_threshold_amount > 0) {
                                    $earned_points = floor($subtotal_amount / $earning_threshold_amount) * $points;
                                }

                                // Use new loyalty earning method with expiration extension
                                if ($earned_points > 0) {
                                    $loyaltyResult = $this->Loyalty->earnLoyaltyPoints(
                                        $data['customer_id'], 
                                        $earned_points, 
                                        'standard'
                                    );
                                    
                                }
                            }
                        }

                        /* -----Start add order to zoho crm----- */
                
                        $order_id = $save_order->id;
                        $ordered_from = "Customer App";
                        $zoho_res = $this->Zoho->createCRMSalesOrder($order_id, $ordered_from);

                        /* -----End add order to zoho crm----- */  

                        $this->WebsiteFunction->OrderConfirmationEmail($save_order->id);
                        $result1 = [
                            'status' => 'success',
                            'code' => 200,
                            'data' => $order
                        ];
                       
                        //
                        $result = array_merge($result1, $result2);
                       
                        $cart = $this->Carts->find()
                            ->where(['customer_id' => $customerId])
                            ->first();
                           

                        /*if ($cart) {
                            $this->CartCreditPayments->deleteAll(['cart_id' => $cart->id]);
                            $this->Carts->CartItems->deleteAll(['cart_id' => $cart->id]);
                            $this->Carts->delete($cart);
                        }*/

                        if ($cart) {
                            // 1. Delete only unused credit payments (not used in orders)
                            $cartCreditPayments = $this->CartCreditPayments->find()
                                ->where(['cart_id' => $cart->id])
                                ->all();

                            $hasUsedCredits = false;

                            foreach ($cartCreditPayments as $payment) {
                                $usedInOrder = $this->Orders->exists(['cart_credit_payment_id' => $payment->id]);
                                if ($usedInOrder) {
                                    $hasUsedCredits = true;
                                    continue; // keep this credit payment
                                }

                                // Safe to delete unused cart credit payment
                                $this->CartCreditPayments->delete($payment);
                            }

                            // 2. Delete cart items (always safe)
                            $this->Carts->CartItems->deleteAll(['cart_id' => $cart->id]);

                            // 3. Delete cart ONLY if no used credit payments remain
                            if (!$hasUsedCredits) {
                                $this->Carts->delete($cart);
                            } else {
                                // Optional: log or skip silently
                                //Log::write('info', "Cart ID {$cart->id} not deleted due to linked credit payments used in orders.");
                            }
                        }

                        $title = 'Order Confirmed - #' . $order['order_number'];
                        $body  = 'Your order has been confirmed and is now being processed.';
                        $customData = [
                            "notification_type" => "order",
                            "id" => (string)$save_order->id,
                            "category" => "order"
                        ];
                        $response = $this->Global->sendNotification(
                            [$deviceToken],
                            $title,
                            $body,
                            $customData
                        );
                        $this->updateWalletBalance = $this->Wallets->updateWalletBalance($customerId, $data['wallet_redeem_amount'], 'debit', 'Order Payment');
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $validationErrors = $order->getErrors();
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'errors' => $validationErrors,
                            'message' => __('There was an issue creating your order. Please try again.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                }

            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
       
    }
    catch (\Exception $e) {
        echo 'Error: ' . $e->getMessage();
    }

        label:
        $this->set(compact('result'));

        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //Calculate Estimated Delivery Date
    private function CalculateEstimatedDelivery($deliveryMode)
    {
        $now = new DateTime();
        $currentHour = (int)$now->format('H');
        $siteSettings = $this->SiteSettings->find()
            ->select(['express_delivery_order_cutoff_time'])
            ->first();
        $express_delivery_order_cutoff_time = (int)$siteSettings->express_delivery_order_cutoff_time;
        if ($deliveryMode === 'express') {
            if ($currentHour >= $express_delivery_order_cutoff_time) {
                $now->modify('+1 day'); // Move to next day if after cutoff
            }
        } else {
            $now->modify('+2 days'); // Standard delivery in 48 hours
        }
        return $now->format('Y-m-d h:i:s');
    }
    //M
    public function redeemWalletPoints()
    {
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('User is not authenticated')
            ];
            $this->response = $this->response->withStatus(200);
        } else {
            if ($this->request->is('post')) {
                $userId = $identity->get('_matchingData')['Customers']['id'];
                $data = $this->request->getData();

                $redeemPoints = isset($data['redeem_points']) ? (float)$data['redeem_points'] : 0;
                $loyaltyCategory = isset($data['loyalty_category']) ? $data['loyalty_category'] : '';
                $loyaltyPoints = isset($data['loyalty_points']) ? $data['loyalty_points'] : 0;

                if ($loyaltyPoints < $redeemPoints) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Redeem points should be less than or equal to your available points.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $loyaltyPointsWorth = 0.00;
                    
                    /*$Loyalty_Redeem = Configure::read('Constants.REDEEM_LOYALTY');

                    if ($loyaltyCategory === 'Standard') {
                        $loyaltyPointsWorth = $redeemPoints * floatval($Loyalty_Redeem['Standard']);
                    } elseif ($loyaltyCategory === 'VIP') {
                        $loyaltyPointsWorth = $redeemPoints * floatval($Loyalty_Redeem['VIP']);
                    }*/

                    $loyaltyRecord = $this->Loyalty->find()->where(['customer_id' => $userId, 'status' => 'A'])->first();

                    if ($loyaltyRecord) {                        

                        $customerGroupsQuery = $this->CustomerGroupMappings
                        ->find()
                        ->select(['customer_group_id'])
                        ->where(['customer_id' => $userId, 'status' => 'A'])
                        ->all();

                        $customerGroupIds = [];
                        foreach ($customerGroupsQuery as $row) {
                            $customerGroupIds[] = $row->customer_group_id;
                        }

                        if (!empty($customerGroupIds)) {

                            $loyalty_setting = $this->LoyaltySettings
                                ->find()
                                ->where([
                                    'customer_group_id IN' => $customerGroupIds,
                                    'status' => 'A'
                                ])
                                ->order(['redeem_point_value' => 'DESC'])
                                ->first();

                            if ($loyalty_setting) {
                                
                                $loyalty_redeem_point_value = $loyalty_setting->redeem_point_value;
                                $loyalty_redeem_points = $loyalty_setting->redeem_points;

                                /*if ($redeemPoints > $loyalty_setting->redeem_points) {
                                    $result = [
                                        'status' => 'error',
                                        'code' => 200,
                                        'message' => __('The loyalty points you are trying to redeem exceed the allowed limit of {0} points.', $loyalty_setting->redeem_points)
                                    ];
                                    goto label;
                                }*/

                                $final_redeem_points = $redeemPoints / $loyalty_redeem_points;
                                $loyaltyPointsWorth = $final_redeem_points * $loyalty_redeem_point_value;

                                $loyaltyRecord->points -= $redeemPoints;
                                $loyaltyRecord->spent_amount += $loyaltyPointsWorth;
                            }
                        }

                        //if ($this->Loyalty->save($loyaltyRecord)) {
                            $result = [
                                'status' => 'success',
                                'code' => 200,
                                'data' => [
                                    'loyalty_amount' => number_format($loyaltyPointsWorth, 2),
                                    'loyalty_points_redeemed' => $redeemPoints,
                                    'loyalty_points_worth' => number_format($loyaltyPointsWorth, 2),
                                    'loyalty_points_remaining' => $loyaltyRecord->points
                                ],
                                'message' => __('Loyalty points redeemed successfully.')
                            ];
                            $this->response = $this->response->withStatus(200);
                        /*} else {
                            $result = [
                                'status' => 'error',
                                'code' => 200,
                                'message' => __('Failed to update loyalty points. Please try again.')
                            ];
                            $this->response = $this->response->withStatus(200);
                        }*/
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Loyalty record not found.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                }
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 405,
                    'message' => __('Method not allowed')
                ];
                $this->response = $this->response->withStatus(405);
            }
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function addCard()
    {
        if ($this->request->is('post')) { // Changed to 'post' for adding a card
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $customerId = $identity->get('_matchingData')['Customers']['id'];
                $data = $this->request->getData();

                $data['customer_id'] = $customerId;

                $cardId = $this->CustomerCards->add_record($data);

                if ($cardId) {
                    $result = [
                        'status' => 'success',
                        'code' => 201,
                        'data' => ['id' => $cardId],
                        'message' => __('Card added successfully.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to add card. Please try again.')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function editCard($id)
    {
        if ($this->request->is(['patch', 'post', 'put'])) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $savedCard = $this->CustomerCards->get($id);

                if ($savedCard->customer_id !== $customerId) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Unauthorized to edit this card.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $data = $this->request->getData();

                    // Use the model method to update the record
                    $updatedCard = $this->CustomerCards->update_record($id, $data);

                    if ($updatedCard) {
                        $result = [
                            'status' => 'success',
                            'code' => 200,
                            'data' => $updatedCard,
                            'message' => __('Card updated successfully.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Failed to update card. Please try again.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function deleteCard($id)
    {
        if ($this->request->is(['patch', 'post', 'put'])) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $savedCard = $this->CustomerCards->get($id);

                if ($savedCard->customer_id !== $customerId) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Unauthorized to delete this card.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    if ($this->CustomerCards->delete($savedCard)) {
                        $result = [
                            'status' => 'success',
                            'code' => 200,
                            'message' => __('Card deleted successfully.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Failed to delete card. Please try again.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function listCards()
    {
        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $savedCards = $this->CustomerCards->find()
                    ->where(['customer_id' => $customerId])
                    ->toArray();

                if ($savedCards) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => $savedCards,
                        'message' => __('Saved cards retrieved successfully.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('No saved cards found.')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function getFaqs()
    {
        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $query = $this->request->getQuery('search', null);

                $faqs = $this->FaqCategories->find()
                    ->contain([
                        'Faqs' => function ($q) use ($query) {
                            $q = $q->where(['Faqs.status' => 'A']);
                            if ($query) {
                                $q = $q->where(['Faqs.title LIKE' => '%' . $query . '%']);
                            }
                            return $q;
                        }
                    ])
                    ->where(['FaqCategories.status' => 'A'])
                    ->toArray();


                $response = [];
                foreach ($faqs as $category) {
                    $response[] = [
                        'category_name' => $category->name,
                        'faqs' => array_map(function ($faq) {
                            return [
                                'question' => $faq->title,
                                'answer' => $faq->content
                            ];
                        }, $category->faqs)
                    ];
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $response,
                    'message' => __('FAQs fetched successfully')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public function updateNotificationSetting()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => 'error', 'code' => 200, 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $customerId = $identity->get('_matchingData')['Customers']['id'];
            $userId = $identity->get('id');

            if (!in_array($data['notification_on_off'], [1, 0])) {
                $result = ['status' => 'error', 'code' => 200, 'message' => __('Invalid notification setting.')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $customer = $this->Customers->get($customerId);
            $user = $this->Users->get($userId);

            if($data['notification_type'] == 'Push') {
                $customer->push_notification = $data['notification_on_off'];
                $user->push_notification = $data['notification_on_off'];
                $this->Customers->save($customer);
            } else if($data['notification_type'] == 'Email') {
                $user->email_notifications = $data['notification_on_off'];
            } elseif($data['notification_type'] == 'Sms') {
                $user->sms_notification = $data['notification_on_off'];
            } elseif($data['notification_type'] == 'Whatsapp') {
                $user->whatsapp_notification = $data['notification_on_off'];
            } elseif($data['notification_type'] == 'App') {
                $user->app_notifications = $data['notification_on_off'];
            } 

            if ($this->Users->save($user)) {
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('Notification setting updated successfully.'),
                    'data' => ['notification' => $data['notification_on_off']]
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'error', 'code' => 200, 'message' => __('Failed to update notification setting.')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function myWallet()
    {

        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $wallet = $this->Wallets->find()
                    ->select(['balance'])
                    ->where(['customer_id' => $customerId])
                    ->first();

                $transactions = $this->Transactions->find()
                    ->innerJoinWith('Orders', function ($q) use ($customerId) {
                        return $q->where(['Orders.customer_id' => $customerId]);
                    })
                    ->order(['Transactions.transaction_date' => 'DESC'])
                    ->toArray();

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => [
                        'wallet_amount' => $wallet ? $wallet->balance : 0,
                        'transactions' => $transactions,
                    ],
                    'message' => __('Wallet details retrieved successfully.')
                ];

                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function settings()
    {

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        $googlemapkey = Configure::read('Settings.GOOGLE_MAP_API_KEY');

        $data = [
            'currency' => $currencySymbol,
            'google_map_api_key' => $googlemapkey
        ];

        $result = [
            'status' => 'success',
            'code' => 200,
            'data' => $data,
            'message' => __('Setting listing successfully.')
        ];
        $this->response = $this->response->withStatus(200);

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function contactUsQueryTypes()
    {

        if ($this->request->is('get')) {

            $categoriesQuery = $this->ContactQueryTypes->getAllQueryTypes();
            $categoriesArray = $categoriesQuery->toArray();

            if (empty($categoriesArray)) {
                $result = [
                    'status' => 'error',
                    'message' => __('No query type found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'success',
                    'data' => $categoriesArray
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    public
    function contactUs()
    {

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            if (empty($data['name'])) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Name is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['phone_number'])) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Phone number is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['email_id']) || !filter_var($data['email_id'], FILTER_VALIDATE_EMAIL)) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Valid email ID is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['query_type'])) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Query type is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['message'])) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Message is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $queryType = $this->ContactQueryTypes->find()
                ->select(['name'])
                ->where(['id' => $data['query_type']])
                ->first();

            $type = $queryType ? $queryType->name : __('No Query Type provided');

            $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
            $to = $adminEmails[0];
            $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
            $subject = 'Contact Us Form Submission';
            $template = 'contact_us';

            $viewVars = [
                'name' => $data['name'],
                'phone' => $data['phone_number'],
                'email' => $data['email_id'],
                'query_type' => $type,
                'message' => $data['message'],
            ];

            $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);

            if ($sendEmail) {
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('Your message has been sent successfully.')
                ];
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to send the email. Please try again later.')
                ];
            }

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    public
    function requestResetPassword()
    {
        $this->request->allowMethod(['post']);
        $email = $this->request->getData('email');

        $user = $this->Users->findByEmail($email)->first();
        if (!$user) {

            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('User not found.')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $token = Security::hash(Security::randomBytes(25));
        $user->password_reset_token = $token;
        $user->token_created_at = date('Y-m-d H:i:s');

        if ($this->Users->save($user)) {
            $resetLink = Router::url(['controller' => 'Users', 'action' => 'resetPassword', $token], true);
            $resetLink = str_replace('/api/v1.0', '', $resetLink);
            // echo "<pre>"; print_r($resetLink); die;

            $to = trim($user->email);
            $subject = "Reset your Password for Babiken";
            $template = "forgot_password";

            $viewVars = array('resetLink' => $resetLink, 'token' => $token, 'userId' => $user->id, 'username' => $user->first_name . ' ' . $user->last_name, 'datetime' => date('d-m-Y H:i:s'));

            $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
            if ($send_email) {
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('Please check your email to reset your password.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 405,
                    'message' => __('Unable to send the password reset email. Please try again.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Unable to send the password reset email. Please try again.')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $result = [
            'status' => 'error',
            'code' => 200,
            'message' => __('Failed to generate reset token.')
        ];
        $this->response = $this->response->withStatus(200);
        goto label;

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M for test mail only
    public
    function sendTestMail()
    {

        $to = '<EMAIL>';
        $subject = "Reset your Password for Babiken";
        $template = "forgot_password";

        $viewVars = array('token' => 'test', 'userId' => 'test', 'username' => 'test', 'datetime' => date('d-m-Y H:i:s'));

        $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
        if ($send_email) {
            $result = [
                'status' => 'success',
                'code' => 200,
                'message' => __('Please check your email to reset your password.')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        } else {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Unable to send the password reset email. Please try again.')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //M
    public
    function getBannerAds()
    {
        $currentDate = date('Y-m-d');
        $banners = $this->BannerAds->find()
            ->select([
                'id',
                'title',
                'summary',
                'ad_location',
                'ad_type',
                'category_id',
                'brand_id',
                'product_id',
                'url_link',
                'display_in_web',
                'display_in_mobile',
                'web_image',
                'mobile_image',
                'ad_position',
                'start_date',
                'end_date',
                'display_order',
                'status',
                'created',
                'modified'
            ])
            ->where([
                'status' => 'A', // Active banners
                'OR' => [
                    ['start_date <=' => $currentDate, 'end_date >=' => $currentDate], // Within date range
                    ['start_date IS' => null, 'end_date IS' => null] // No date restriction
                ]
            ])
            ->order(['display_order' => 'ASC'])
            ->toArray();

        // Prepend domain to image paths if necessary
        foreach ($banners as &$banner) {
            if (!empty($banner['web_image'])) {
                $banner['web_image'] = $this->Media->getCloudFrontURL($banner['web_image']);
            }
            if (!empty($banner['mobile_image'])) {
                $banner['mobile_image'] = $this->Media->getCloudFrontURL($banner['mobile_image']);
            }
        }

        // Format the response
        $result = [
            'status' => 'success',
            'code' => 200,
            'data' => $banners,
            'message' => 'Banner ads fetched successfully'
        ];

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }


    //M
    public
    function listMunicipality()
    {

        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {

                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $municipalities = $this->Municipalities->fetchMunicipalities();

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $municipalities,
                    'message' => __('Municipalities listing successfully')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function brandList()
    {

        if ($this->request->is('get')) {

            $category_id = $this->request->getQuery('category_id');

            $brands = $this->Brands->getBrandList($category_id);

            foreach ($brands as $brand) {
                if ($brand['brand_logo'] != '' || $brand['brand_logo'] != null) {
                    $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
                }
                if ($brand['web_banner'] != '' || $brand['web_banner'] != null) {
                    $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
                }
                if ($brand['mobile_banner'] != '' || $brand['mobile_banner'] != null) {
                    $brand['mobile_banner'] = $this->Media->getCloudFrontURL($brand['mobile_banner']);
                }
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $brands,
                'message' => __('Brand listing successfully')
            ];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function addToCart()
    {
        if (!$this->request->is('post')) {

            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getHeader('guest-token')[0] ?? null;
        $identity = $this->request->getAttribute('identity');

        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->response = $this->response->withHeader('guest-token', $guestToken);
        }

        // Validate Cart Item Data
        $requiredFields = ['product_id', 'quantity'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {

                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __("Field '{0}' is required", $field)
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        }

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->get('_matchingData')['Customers']['id'] : null;

        // Find or Create Cart
        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        // echo "<pre>"; print_r($cartConditions); die;
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $cart = $this->Carts->newEntity([
                'customer_id' => $customerId,
                'guest_token' => $guestToken,
            ]);
            if (!$this->Carts->save($cart)) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to create cart')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        }

        // Add or Update Cart Item
        $connection = $this->CartItems->getConnection();
        $connection->begin();
        try {
            // Check if the item already exists in the cart
            $productVariantId = !empty($data['product_variant_id']) ? $data['product_variant_id'] : null;
            $productAttributeId = !empty($data['product_attribute_id']) ? $data['product_attribute_id'] : null;

            $query = $this->CartItems->find()->where([
                'cart_id' => $cart->id,
                'product_id' => $data['product_id'],
            ]);

            if ($productVariantId !== null) {
                $query->where(['product_variant_id' => $productVariantId]);
            } else {
                $query->where(['product_variant_id IS' => null]);
            }

            if ($productAttributeId !== null) {
                $query->where(['product_attribute_id' => $productAttributeId]);
            } else {
                $query->where(['product_attribute_id IS' => null]);
            }
            $existingItem = $query->first();
            // if ($productVariantId) {
            //     $existingItem = $this->CartItems->find()
            //         ->where([
            //             'cart_id' => $cart->id,
            //             'product_id' => $data['product_id'],
            //             'product_variant_id' => $productVariantId,
            //         ])
            //         ->first();
            // } else {
            //     $existingItem = $this->CartItems->find()
            //         ->where([
            //             'cart_id' => $cart->id,
            //             'product_id' => $data['product_id'],
            //             'product_variant_id IS' => null,
            //             'product_attribute_id IS' => null,
            //         ])
            //         ->first();
            // }


            if ($existingItem) {
                // Update quantity if the item exists
                $existingItem->quantity += $data['quantity'];
                if (!$this->CartItems->save($existingItem)) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to update cart item')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            } else {
                // Add a new cart item
                $newItem = $this->CartItems->newEntity([
                    'cart_id' => $cart->id,
                    'product_id' => $data['product_id'],
                    'product_variant_id' => $data['product_variant_id'] ?? null,
                    'product_attribute_id' => $data['product_attribute_id'] ?? null,
                    'quantity' => $data['quantity'],
                ]);

                if (!$this->CartItems->save($newItem)) {
                    $message = json_encode($newItem->geterrors());
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => $message
                    ];
                    // $result = [
                    //     'status' => 'error',
                    //     'code' => 200,
                    //     'message' => __('Failed to add cart item')
                    // ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }
            $cartItemId = $existingItem->id ?? $newItem->id;

            // if (!empty($data['attributes']) && is_array($data['attributes'])) {
            //     // Get all existing product_attribute_ids for this cart_item_id
            //     $existingAttributes = $this->CartItemAttributes->find()
            //         ->where(['cart_item_id' => $cartItemId])
            //         ->all()
            //         ->indexBy('product_attribute_id')
            //         ->toArray();

            //     // Extract new attribute IDs from $data['attributes']
            //     $newAttributeIds = array_column($data['attributes'], 'product_attribute_id');

            //     // Mark attributes that are not in the new list as 'D'
            //     foreach ($existingAttributes as $attributeId => $attribute) {
            //         if (!in_array($attributeId, $newAttributeIds)) {
            //             $attribute->status = 'D';
            //             $this->CartItemAttributes->save($attribute);
            //         }
            //     }

            //     foreach ($data['attributes'] as $attributes) {
            //         $attributeId = $attributes['product_attribute_id'];

            //         // Skip saving if the attribute already exists and is active
            //         if (isset($existingAttributes[$attributeId]) && $existingAttributes[$attributeId]->status === 'A') {
            //             continue;
            //         }

            //         $cartItemAttribute = $this->CartItemAttributes->newEntity([
            //             'cart_id' => $cart->id,
            //             'cart_item_id' => $cartItemId,
            //             'product_id' => $data['product_id'],
            //             'product_attribute_id' => $attributeId,
            //             'status' => 'A', // Active
            //         ]);

            //         if (!$this->CartItemAttributes->save($cartItemAttribute)) {
            //             $result = [
            //                 'status' => 'error',
            //                 'code' => 200,
            //                 'message' => $cartItemAttribute->getErrors()
            //             ];
            //             $this->response = $this->response->withStatus(200);
            //             goto label;
            //         }
            //     }
            // }

            $connection->commit();


            $price = null;
            if (isset($data['product_variant_id'])) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['promotion_price'])
                    ->where(['id' => $data['product_variant_id']])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
            }

            if (!$price) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price'])
                    ->where(['id' => $data['product_id']])
                    ->first();

                $price = $product ? $product->promotion_price : null;
            }

            if ($productVariantId) {
                $updatedItem = $this->CartItems->find()
                    ->where([
                        'cart_id' => $cart->id,
                        'product_id' => $data['product_id'],
                        'product_variant_id' => $productVariantId,
                    ])
                    ->first();
            } else {
                $updatedItem = $this->CartItems->find()
                    ->where([
                        'cart_id' => $cart->id,
                        'product_id' => $data['product_id'],
                        'product_variant_id IS' => null
                    ])
                    ->first();
            }

            if ($price !== null) {
                $updatedItemResult['price'] = $price;
                $updatedItemResult['total_price'] = number_format($updatedItem->quantity * $price, 2, '.', '');
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Price not found for the selected product/variant')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => $updatedItemResult,
                'message' => __('Cart item updated successfully'),
                'guest_token' => $guestToken
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        } catch (\Exception $e) {
            $connection->rollback();
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Failed to update cart')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function updateCartItem()
    {
        if (!$this->request->is(['patch', 'post', 'put'])) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getHeader('guest-token')[0] ?? null;
        $identity = $this->request->getAttribute('identity');

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->get('_matchingData')['Customers']['id'] : null;

        if (!$customerId && !$guestToken) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Customer ID or Guest Token is required')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Validate Cart Item Data
        if (empty($data['cart_item_id']) || empty($data['quantity'])) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __("Cart item ID and quantity are required")
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        // echo "<pre>"; print_r($cartConditions); die;
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $result = [
                'status' => 'error',
                'code' => 404,
                'message' => __('Cart not found')
            ];
            $this->response = $this->response->withStatus(404);
            goto label;
        }

        $connection = $this->CartItems->getConnection();
        $connection->begin();
        try {
            // Find the cart item to update
            $cartItem = $this->CartItems->find()
                ->where([
                    'cart_id' => $cart->id,
                    'id' => $data['cart_item_id']
                ])
                ->first();

            if (!$cartItem) {
                $result = [
                    'status' => 'error',
                    'code' => 404,
                    'message' => __('Cart item not found')
                ];
                $this->response = $this->response->withStatus(404);
                goto label;
            }

            // Update quantity
            $cartItem->quantity = $data['quantity'];

            if (!$this->CartItems->save($cartItem)) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to update cart item')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $connection->commit();

            $price = null;
            if ($cartItem->product_variant_id) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['promotion_price'])
                    ->where(['id' => $cartItem->product_variant_id])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
            }

            if (!$price) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price'])
                    ->where(['id' => $cartItem->product_id])
                    ->first();

                $price = $product ? $product->promotion_price : null;
            }

            if ($price !== null) {
                $cartItem['price'] = $price;
                $cartItem['total_price'] = number_format($cartItem->quantity * $price, 2, '.', '');
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Price not found for the selected product/variant')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'message' => __('Cart item updated successfully'),
                'data' => $cartItem
            ];
            $this->response = $this->response->withStatus(200);
        } catch (\Exception $e) {
            $connection->rollback();
            $result = [
                'status' => 'error',
                'code' => 500,
                'message' => __('Failed to update cart')
            ];
            $this->response = $this->response->withStatus(500);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function deleteCartItem()
    {
        if (!$this->request->is('post')) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getHeader('guest-token')[0] ?? null;
        $identity = $this->request->getAttribute('identity');

        // Validate cart item ID
        if (empty($data['cart_item_id'])) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Cart Item ID is required')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->get('_matchingData')['Customers']['id'] : null;

        if (!$customerId && !$guestToken) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Customer ID or guest token is required')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        // echo "<pre>"; print_r($cartConditions); die;
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $result = [
                'status' => 'error',
                'code' => 404,
                'message' => __('Cart not found')
            ];
            $this->response = $this->response->withStatus(404);
            goto label;
        }

        $cartItem = $this->CartItems->find()
            ->where([
                'cart_id' => $cart->id,
                'id' => $data['cart_item_id']
            ])
            ->first();

        if (!$cartItem) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Cart item not found or invalid cart item ID')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Delete the cart item
        if (!$this->CartItems->delete($cartItem)) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Failed to delete cart item')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $result = [
            'status' => 'success',
            'code' => 200,
            'message' => __('Cart item deleted successfully')
        ];
        $this->response = $this->response->withStatus(200);

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function viewCartNew()
    {
        if (!$this->request->is('get')) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $guestToken = $this->request->getHeader('guest-token')[0] ?? null;
        $identity = $this->request->getAttribute('identity');

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->get('_matchingData')['Customers']['id'] : null;

        // If neither customer_id nor guest_token is provided, return an error
        if (!$customerId && !$guestToken) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Customer ID or guest token is required')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Find the cart based on customer_id or guest_token
        $cartConditions = $customerId ? ['Carts.customer_id' => $customerId] : ['Carts.guest_token' => $guestToken];

        // print_r($cartConditions); die;

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain([
                'CartItems' => [
                    'Products' => [
                        'Merchants' => [
                            'Users'
                        ]
                    ],
                    'ProductVariants',
                    'ProductAttributes' => [
                        'Attributes',
                        'AttributeValues'
                    ]
                ]
            ])
            ->first();

        if (!$cart) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Cart not found')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Prepare cart data for response
        $cartItems = [];
        $total_quantity = 0;
        foreach ($cart->cart_items as $item) {

            $price = null;
            if ($item->product_variant_id) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['sales_price', 'promotion_price'])
                    ->where(['id' => $item->product_variant_id])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
                $sales_price = $productVariant ? $productVariant->sales_price : null;
            }

            if (!$price) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price', 'sales_price'])
                    ->where(['id' => $item->product_id])
                    ->first();

                $price = $product ? $product->promotion_price : null;
                $sales_price = $product ? $product->sales_price : null;
            }

            if ($price !== null) {
                if ($item->product_variant_id) {
                    $image = $this->ProductVariantImages->getDefaultProductVariantImage($item->product_variant_id);
                    if ($image) {
                        $image = $this->Media->getCloudFrontURL($image);
                    }
                } else {
                    $image = $this->ProductImages->getDefaultProductImage($item->product_id);
                    if ($image) {
                        $image = $this->Media->getCloudFrontURL($image);
                    }
                }

                $rating = $this->Reviews->getAverageRating($item->product_id);
                $total_review = $this->Reviews->getTotalReviews($item->product_id);
                //$discount = $this->Products->getDiscount($item->product_id);
                if($item->product_variant_id){
                    $discount = $this->Products->getDiscountProduct($item->product_id, $item->product_variant_id);

                } else {
                    $discount = $this->Products->getDiscountProduct($item->product_id);
                }
                
                $availability_status = $this->Products->getAvailabilityStatus($item->product_id);

                // $CartAttributes = $this->CartItemAttributes->find()
                //     ->contain(['ProductAttributes' => ['Attributes', 'AttributeValues']])
                //     ->where(['cart_item_id' => $item->id, 'CartItemAttributes.status' => 'A'])
                //     ->all()
                //     ->toArray();


                $cartItems[] = [
                    'product_image' => $image,
                    'cart_item_id' => $item->id,
                    'product_id' => $item->product_id,
                    'product_reference' => $item->product->product_reference,
                    'product_variant_id' => $item->product_variant_id,
                    'product_name' => $item->product->name ?? '',
                    'reference_name' => $item->product->reference_name ?? '',
                    'COD_in_city' => $item->product->COD_in_city,
                    'COD_out_city' => $item->product->COD_out_city,
                    'avl_on_credit' => $item->product->avl_on_credit,
                    'variant_name' => $item->product_variant->variant_name ?? '',
                    'product_attribute_id' => $item->product_attribute_id,
                    'product_attribute_name' => $item->product_attribute ? $item->product_attribute->attribute->name : '',
                    'product_attribute_value' => $item->product_attribute ? $item->product_attribute->attribute_value->value : '',
                    'quantity' => $item->quantity,
                    'sales_price' => $sales_price,
                    'price' => $price,
                    'total_price' => number_format(($item->quantity * $price), 2, '.', ''),
                    'rating' => $rating,
                    'total_reviews' => $total_review,
                    'discount' => $discount,
                    'availability_status' => $availability_status,
                    'merchant_id'         => $item->product->merchant->id ?? null,
                    'merchant_name'       => $item->product->merchant->company_name ?? null,
                    'merchant_user_fname'  => $item->product->merchant->user->first_name ?? null,
                    'merchant_user_lname'  => $item->product->merchant->user->last_name ?? null
                ];

                $total_quantity = $total_quantity + $item->quantity;
            }
        }

        $cartCreditPayments = $this->CartCreditPayments->find()
            ->where(['cart_id' => $cart->id, 'CartCreditPayments.status' => 'A'])
            ->first();

        $creditApplication = null;
        if ($cartCreditPayments) {
            $creditApplication = $this->CreditApplications->find()
                ->where(['id' => $cartCreditPayments->credit_application_id, 'CreditApplications.status' => 'A'])
                ->first();
        }

        // Build related products from cart categories
        $relatedProducts = [];
        try {
            $cartProductIds = array_unique(array_column($cartItems, 'product_id'));

            if (!empty($cartProductIds)) {
                // Fetch category rows for cart products
                $pcRows = $this->Products->ProductCategories->find()
                    ->select(['product_id', 'category_id', 'level'])
                    ->where(['product_id IN' => $cartProductIds])
                    ->enableHydration(false)
                    ->toArray();

                // Determine deepest (sub) and parent categories per product
                $byProduct = [];
                foreach ($pcRows as $row) {
                    $pid = $row['product_id'];
                    if (!isset($byProduct[$pid])) {
                        $byProduct[$pid] = ['min' => $row, 'max' => $row];
                    } else {
                        if ($row['level'] < $byProduct[$pid]['min']['level']) {
                            $byProduct[$pid]['min'] = $row;
                        }
                        if ($row['level'] > $byProduct[$pid]['max']['level']) {
                            $byProduct[$pid]['max'] = $row;
                        }
                    }
                }

                $subCategoryIds = [];
                $parentCategoryIds = [];
                foreach ($byProduct as $p) {
                    $subCategoryIds[] = $p['max']['category_id'];
                    $parentCategoryIds[] = $p['min']['category_id'];
                }
                $subCategoryIds = array_values(array_unique(array_filter($subCategoryIds)));
                $parentCategoryIds = array_values(array_unique(array_filter($parentCategoryIds)));

                // Helper to fetch related products by category ids
                $fetchRelated = function (array $categoryIds) use ($cartProductIds) {
                    if (empty($categoryIds)) {
                        return [];
                    }
                    $q = $this->Products->find()
                        ->select(['Products.id', 'Products.name', 'Products.COD_in_city', 'Products.COD_out_city', 'Products.product_reference', 'Products.reference_name'])
                        ->where([
                            'Products.status' => 'A',
                            'Products.approval_status' => 'Approved',
                            'Products.id NOT IN' => $cartProductIds,
                        ])
                        ->innerJoinWith('ProductCategories', function ($q) use ($categoryIds) {
                            return $q->where(['ProductCategories.category_id IN' => $categoryIds]);
                        })
                        ->group(['Products.id'])
                        ->limit(20);
                    return $q->toArray();
                };

                // Try subcategories first, fallback to parent categories
                $related = $fetchRelated($subCategoryIds);
                if (empty($related)) {
                    $related = $fetchRelated($parentCategoryIds);
                }

                foreach ($related as $prod) {
                    $pid = $prod->id;
                    // Fetch full product details as in productView
                    $fullProduct = $this->Products->find()
                        ->where(['Products.id' => $pid])
                        ->contain([
                            'Brands',
                            'Suppliers',
                            'Merchants.Users',
                            'ProductImages' => function ($q) { return $q->where(['ProductImages.status' => 'A']); },
                            'ProductCategories',
                            'ProductVariants' => function ($q) {
                                return $q->select([
                                    'ProductVariants.id',
                                    'ProductVariants.product_id',
                                    'ProductVariants.variant_name',
                                    'ProductVariants.reference_name',
                                    'ProductVariants.sku',
                                    'ProductVariants.variant_size',
                                    'ProductVariants.variant_weight',
                                    'ProductVariants.purchase_price',
                                    'ProductVariants.sales_price',
                                    'ProductVariants.promotion_price',
                                    'ProductVariants.quantity',
                                    'ProductVariants.variant_description',
                                    'ProductVariants.status',
                                    'product_variant_discount' => $q->newExpr()->add('ROUND(((ProductVariants.sales_price - ProductVariants.promotion_price) / ProductVariants.sales_price) * 100, 2)')
                                ])->where(['ProductVariants.status' => 'A']);
                            },
                            'ProductVariants.ProductVariantImages' => function ($q) { return $q->where(['ProductVariantImages.status' => 'A']); },
                        ])
                        ->first();
                    if ($fullProduct) {
                        // Map images to CloudFront URLs
                        $productImages = [];
                        foreach ($fullProduct->product_images ?? [] as $img) {
                            if (!empty($img->image)) {
                                $productImages[] = $this->Media->getCloudFrontURL($img->image);
                            }
                        }
                        $mainImage = $productImages[0] ?? '';

                        // Map product variants images
                        $variants = [];
                        foreach ($fullProduct->product_variants ?? [] as $variant) {
                            $variantArr = $variant->toArray();
                            if (!empty($variant->product_variant_images)) {
                                foreach ($variantArr['product_variant_images'] as &$vimg) {
                                    if (!empty($vimg['image'])) {
                                        $vimg['image'] = $this->Media->getCloudFrontURL($vimg['image']);
                                    }
                                    if (!empty($vimg['video'])) {
                                        $vimg['video'] = $this->Media->getCloudFrontURL($vimg['video']);
                                    }
                                }
                                unset($vimg);
                            }
                            $variants[] = $variantArr;
                        }

                        // Compose full product array
                        $productArr = [
                            'id' => $fullProduct->id,
                            'zoho_productId' => $fullProduct->zoho_productId ?? null,
                            'url_key' => $fullProduct->url_key ?? null,
                            'merchant_id' => $fullProduct->merchant_id ?? null,
                            'brand_id' => $fullProduct->brand_id ?? null,
                            'supplier_id' => $fullProduct->supplier_id ?? null,
                            'name' => $fullProduct->name ?? '',
                            'product_reference' => $fullProduct->product_reference ?? null,
                            'reference_name' => $fullProduct->reference_name ?? null,
                            'description' => $fullProduct->description ?? null,
                            'details' => $fullProduct->details ?? null,
                            'product_image' => $mainImage,
                            'product_preference' => $fullProduct->product_preference ?? null,
                            'product_size' => $fullProduct->product_size ?? null,
                            'product_weight' => $fullProduct->product_weight ?? null,
                            'product_model' => $fullProduct->product_model ?? null,
                            'product_tags' => $fullProduct->product_tags ?? null,
                            'sku' => $fullProduct->sku ?? null,
                            'barcode' => $fullProduct->barcode ?? null,
                            'qrcode' => $fullProduct->qrcode ?? null,
                            'scanned_barcode' => $fullProduct->scanned_barcode ?? null,
                            'quantity' => $fullProduct->quantity ?? null,
                            'purchase_price' => $fullProduct->purchase_price ?? null,
                            'product_price' => $fullProduct->product_price ?? null,
                            'merchant_price' => $fullProduct->merchant_price ?? null,
                            'sales_price' => $fullProduct->sales_price ?? null,
                            'promotion_price' => $fullProduct->promotion_price ?? null,
                            'promotion_start_date' => $fullProduct->promotion_start_date ?? null,
                            'promotion_end_date' => $fullProduct->promotion_end_date ?? null,
                            'max_buy_limit' => $fullProduct->max_buy_limit ?? null,
                            'COD_in_city' => $fullProduct->COD_in_city ?? null,
                            'COD_out_city' => $fullProduct->COD_out_city ?? null,
                            'avl_on_credit' => $fullProduct->avl_on_credit ?? null,
                            'warranty' => $fullProduct->warranty ?? null,
                            'return_allow' => $fullProduct->return_allow ?? null,
                            'return_time_period' => $fullProduct->return_time_period ?? null,
                            'meta_title' => $fullProduct->meta_title ?? null,
                            'meta_keyword' => $fullProduct->meta_keyword ?? null,
                            'meta_description' => $fullProduct->meta_description ?? null,
                            'status' => $fullProduct->status ?? null,
                            'approval_status' => $fullProduct->approval_status ?? null,
                            'admin_comment' => $fullProduct->admin_comment ?? null,
                            'approved_at' => $fullProduct->approved_at ?? null,
                            'created' => $fullProduct->created ? $fullProduct->created->i18nFormat('yyyy-MM-dd\THH:mm:ssxxx') : null,
                            'modified' => $fullProduct->modified ? $fullProduct->modified->i18nFormat('yyyy-MM-dd\THH:mm:ssxxx') : null,
                            '_matchingData' => isset($prod->_matchingData) ? $prod->_matchingData : null,
                            'rating' => $this->Reviews->getAverageRating($pid),
                            'total_review' => $this->Reviews->getTotalReviews($pid),
                            'discount' => $this->Products->getDiscount($pid),
                            'availability_status' => $this->Products->getAvailabilityStatus($pid),
                            'product_variants' => $variants,
                        ];
                        $relatedProducts[] = $productArr;
                    }
                }
            }
        } catch (\Throwable $e) {
            $relatedProducts = [];
        }

        $result = [
            'status' => 'success',
            'code' => 200,
            'message' => __('Cart retrieved successfully'),
            'data' => [
                'cart_id' => $cart->id,
                'total_items' => $total_quantity,
                'cart_items' => $cartItems,
                'related_products' => $relatedProducts,
                'cartCreditPayments' => $cartCreditPayments ?: [],
                'creditApplication' => $creditApplication ?: []
            ]
        ];

        $this->response = $this->response->withStatus(200);

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    public function clearCart()
    {
        if (!$this->request->is('get')) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $guestToken = $this->request->getHeader('guest-token')[0] ?? null;
        $identity = $this->request->getAttribute('identity');

        $customerId = $identity ? $identity->get('_matchingData')['Customers']['id'] ?? null : null;

        if (!$customerId && !$guestToken) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Customer ID or guest token is required')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $cartConditions = $customerId ? ['Carts.customer_id' => $customerId] : ['Carts.guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products', 'ProductVariants']])
            ->first();

        if (!$cart) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Cart not found')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $this->Carts->CartItems->deleteAll(['cart_id' => $cart->id]);
        $this->Carts->delete($cart);

        $result = [
            'status' => 'success',
            'code' => 200,
            'message' => __('Cart cleared successfully')
        ];

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function convertCart()
    {
        if ($this->request->is('post')) {
            // Get logged-in user and guest token
            $identity = $this->request->getAttribute('identity');
            $guestToken = $this->request->getHeaderLine('guest-token');

            // Check authentication and guest token
            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'message' => __('User is not authenticated.')
                ];
                $this->response = $this->response->withStatus(200);
            } elseif (empty($guestToken)) {
                $result = [
                    'status' => 'error',
                    'message' => __('Guest token is required.')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                $customerId = $identity->get('_matchingData')['Customers']['id'];

                $guestCart = $this->Carts->find()
                    ->where(['guest_token' => $guestToken])
                    ->contain(['CartItems'])
                    ->first();

                if (!$guestCart) {
                    $result = [
                        'status' => 'error',
                        'message' => __('No cart found for the provided guest token.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {

                    $userCart = $this->Carts->find()
                        ->where(['customer_id' => $customerId])
                        ->contain(['CartItems'])
                        ->first();

                    if ($userCart) {
                        foreach ($guestCart->cart_items as $item) {
                            $existingItem = $this->Carts->CartItems->find()
                                ->where(['cart_id' => $userCart->id, 'product_id' => $item->product_id])
                                ->first();

                            if ($existingItem) {
                                $existingItem->quantity += $item->quantity;
                                $this->Carts->CartItems->save($existingItem);
                            } else {
                                $item->cart_id = $userCart->id;
                                $item->isNew(true);
                                $this->Carts->CartItems->save($item);
                            }
                        }

                        $this->Carts->delete($guestCart);

                        $result = [
                            'status' => 'success',
                            'message' => __('Cart items have been merged successfully.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $guestCart->customer_id = $customerId;
                        $guestCart->guest_token = null;

                        if ($this->Carts->save($guestCart)) {
                            $result = [
                                'status' => 'success',
                                'message' => __('Cart has been successfully converted to the logged-in user.')
                            ];
                            $this->response = $this->response->withStatus(200);
                        } else {
                            $result = [
                                'status' => 'error',
                                'message' => __('Failed to convert the cart.')
                            ];
                            $this->response = $this->response->withStatus(500);
                        }
                    }
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed.')
            ];
            $this->response = $this->response->withStatus(405);
        }

        // Serialize the response
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function showroomAddress()
    {
        if (!$this->request->is('get')) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $queryParams = $this->request->getQueryParams();
        $search = $queryParams['search'] ?? null;

        $conditions = ['Showrooms.status' => 'A'];
        if ($search) {
            $conditions['OR'] = [
                'Showrooms.name LIKE' => '%' . $search . '%',
                'Showrooms.address LIKE' => '%' . $search . '%'
            ];
        }

        // $showrooms = $this->Showrooms->find('all')
        //     ->where($conditions);
        $showrooms = $this->Showrooms->find('all', [
            'contain' => ['ShowroomImages']
        ])->where($conditions)->toArray();

        foreach ($showrooms as $showroom) {
            if ($showroom['images'] != '' || $showroom['images'] != null) {
                $showroom['images'] = $this->Media->getCloudFrontURL($showroom['image']);
            }
        }
        if ($showrooms) {
            $result = ['status' => 'success', 'data' => $showrooms];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = ['status' => 'success', 'data' => $showrooms, 'message' => __('No data found')];
            $this->response = $this->response->withStatus(200);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public
    function availableOffers()
    {

        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                $offers = $this->Offers->homeOffers('mobile');
                foreach ($offers as &$offer) {
                    if (!empty($offer['mobile_image'])) {
                        $offer['mobile_image'] = $this->Media->getCloudFrontURL($offer['mobile_image']);
                    }
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $offers,
                    'message' => __('Offers listed successfully')
                ];
                $this->response = $this->response->withStatus(405);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    public function getPartners()
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                $partners = $this->Partners->GetAllPartners();
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $partners,
                    'message' => __('Partners listed successfully')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    public function getPartnerDetails($id)
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                $partners = $this->Partners->getPartner($id);
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $partners,
                    'message' => __('Partners listed successfully')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    /* Support Desk*/

    //S support desk
    public function listSupportTickets()
    {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $userId  = $identity->get('id');
                $customerId = $identity->get('_matchingData')['Customers']['id'];
                $roleName = 'Customer';
                $showroom = 0;
            }

            $data = $this->request->getQuery();

            if ($data['page']) {
                $page = (int)$data['page'];
            } else {
                $page = 1;
            }
            if ($data['limit']) {
                $limit = (int)$data['limit'];
            } else {
                $limit = 10;
            }

            $filter_ticket_status = $data['status'];
            $filter_support_category = $data['support_category_id'];
            //issue name/ticket ID
            $search_str = $data['search_str'];

            //$unread_tickets = $this->SupportTickets->unreadSupportTickets();
            //$unread_count = count($unread_tickets);

            $open_count = $this->SupportTickets->find()
                ->where(['status' => 'Open', 'customer_id' => $customerId])
                ->count();

            $tickets = $this->SupportTickets->listSupportTickets($customerId, $showroom, $roleName, $filter_ticket_status, $filter_support_category, $search_str, $page, $limit);

            if ($tickets) {
                $result = ['status' => 'success', 'data' => [/*'unread_count'=>$unread_count, 'unread_tickets'=>$unread_tickets, */'open_count' => $open_count, 'tickets' => $tickets]];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => (object)[], 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S support desk
    public function viewSupportTicket()
    {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $userId  = $identity->get('id');
                $customerId = $identity->get('_matchingData')['Customers']['id'];
            }

            $data = $this->request->getQuery();
            $support_ticket_id = $data['id'];

            //mark is read 1
            /*$ticket_entity = $this->SupportTickets->get($support_ticket_id);        
            $isread['is_read'] = 1;
            $ticket_entity = $this->SupportTickets->patchEntity($ticket_entity, $isread);
            $res = $this->SupportTickets->save($ticket_entity);*/

            $detail = $this->SupportTickets->viewCustTicketDetail($support_ticket_id);

            if ($detail) {
                $result = ['status' => 'success', 'data' => $detail];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => (object)[], 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S support desk
    public function replySupportTicket()
    {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $customerId = $identity->get('_matchingData')['Customers']['id'];
                $userId  = $identity->get('id');
            }

            $data = $this->request->getData();
            $support_ticket_id = $data['id'];
            $data['updated_by'] = $userId;

            $addUpdate = $this->SupportTicketUpdates->addUpdate($support_ticket_id, $data);

            if ($addUpdate) {
                $ticket_update_id = $addUpdate;
                $uploadImages = !empty($data['images']) ? $this->supportImageUpload('ticket_update_id', $ticket_update_id) : [];
            }

            if ($addUpdate) {
                $result = ['status' => 'success', 'message' => __('Comment added successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'error', 'message' => __('Unable to add comment. Please try again.')];
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S support desk
    public function addSupportTicket()
    {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $userId  = $identity->get('id');
                $userEmail  = $identity->get('email');
                $customerId = $identity->get('_matchingData')['Customers']['id'];
                $roleName = 'Customer';
                $showroom = 0;
            }

            $data = $this->request->getData();
            $data['customer_id'] = $customerId;
            $data['customer_email'] = $userEmail;
            $data['created_by'] = $userId;
            $data['updated_by'] = $userId;

            $timestamp = FrozenTime::now()->format('YmdHis'); // Current date and time in YYYYMMDDHHMMSS format
            $randomNumber = mt_rand(1000, 9999); // Random 4-digit number
            $data['ticketID'] = "$timestamp-$randomNumber";

            $addTicket = $this->SupportTickets->addTicket($data);
            /*if($addTicket){
                $ticket_id = $addTicket;
                $addUpdate = $this->SupportTicketUpdates->addUpdate($ticket_id, $data); 
            }*/
            if ($addTicket) {
                $uploadImages = !empty($data['images']) ? $this->supportImageUpload('ticket_id', $ticket_id) : [];
            }

            if ($addTicket) {
                $result = ['status' => 'success', 'message' => __('Ticket created successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'error', 'message' => __('Unable to create ticket. Please try again.')];
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    private function supportImageUpload($flag, $id)
    {
        $files = $this->request->getData('images');
        $uploadedImages = [];
        $i = 0;
        foreach ($files as $file) {
            if ($file->getError() === UPLOAD_ERR_OK) {
                $fileName = trim($file->getClientFilename());
                if (!empty($fileName)) {
                    $imageTmpName = $file->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.SUPPORT_DESK');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                    } else {
                        $supportImage = $this->SupportTicketImages->newEmptyEntity();
                        if ($flag == 'ticket_update_id') {
                            $supportImage->support_ticket_update_id = $id;
                        } else {
                            $supportImage->support_ticket_id = $id;
                        }
                        $supportImage->image = $folderPath . $imageFile;
                        $i++;
                        if ($this->SupportTicketImages->save($supportImage)) {
                            $uploadedImages[] = $folderPath . $imageFile; // Collecting the image paths
                        } else {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                        }
                    }
                }
            }
        }
        return $uploadedImages;
    }

    //S support desk
    public function updateTicketStatus()
    {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $userId  = $identity->get('id');
                $customerId = $identity->get('_matchingData')['Customers']['id'];
            }

            $data = $this->request->getData();
            $support_ticket_id = $data['id'];
            $data['status'] = 'Closed';

            $changeStatus = $this->SupportTickets->changeStatus($support_ticket_id, $data);

            if ($changeStatus) {
                $result = ['status' => 'success', 'message' => __('Status changed successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'error', 'message' => __('Unable to change status. Please try again.')];
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S support desk
    public function listSupportCategories()
    {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if ($identity) {
                $userId = $identity->get('id');
                $customerId = $identity->get('_matchingData')['Customers']['id'];
            }

            $listCategory = $this->SupportCategories->getAllSupportCategories();

            if ($listCategory) {
                $result = ['status' => 'success', 'data' => $listCategory];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'success', 'data' => $listCategory, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    /* Credit Payments */
    public function addCreditApplications()
    {
        if (!$this->request->is('post')) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getHeader('guest-token')[0] ?? null;
        $identity = $this->request->getAttribute('identity');

        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->response = $this->response->withHeader('guest-token', $guestToken);
        }

        // Required fields for CreditApplications
        $creditApplicationFields = ['registration_id', 'name', 'email', 'country_code', 'phone_number'];

        // Required fields for CartCreditPayments
        $cartCreditPaymentFields = [
            'cart_id',
            'product_id',
            'product_variant_id',
            'credit_payment_terms_id',
            'emi_interest_percentage',
            'emi_interest_amount',
            'total_emi_amount',
            'status'
        ];

        // Validate CreditApplications fields
        foreach ($creditApplicationFields as $field) {
            if (empty($data[$field])) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __("Field '{0}' is required", $field)
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        }

        // Validate CartCreditPayments fields
        foreach ($cartCreditPaymentFields as $field) {
            if (empty($data[$field])) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __("Field '{0}' is required", $field)
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        }

        // Check if registration_id already exists
        $existingApplication = $this->CreditApplications->find()
            ->where(['registration_id' => $data['registration_id']])
            ->first();

        if ($existingApplication) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Registration ID already exists')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Get Customer ID or use provided one
        $customerId = $identity ? $identity->get('_matchingData')['Customers']['id'] : null;

        // Save Credit Application
        $application = $this->CreditApplications->newEntity([
            'customer_id' => $customerId,
            'registration_id' => $data['registration_id'],
            'name' => $data['name'],
            'email' => $data['email'],
            'country_code' => $data['country_code'],
            'phone_number' => $data['phone_number']
        ]);

        if (!$this->CreditApplications->save($application)) {
            $this->log(json_encode($application->getErrors()), 'debug');
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Failed to create Credit Application')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Save Cart Credit Payment
        $cartCreditPayment = $this->CartCreditPayments->newEntity([
            'cart_id' => $data['cart_id'],
            'customer_id' => $customerId,
            'product_id' => $data['product_id'],
            'product_variant_id' => $data['product_variant_id'],
            'credit_application_id' => $application->id,
            'credit_payment_terms_id' => $data['credit_payment_terms_id'],
            'emi_interest_percentage' => $data['emi_interest_percentage'],
            'emi_interest_amount' => $data['emi_interest_amount'],
            'total_emi_amount' => $data['total_emi_amount'],
            'status' => $data['status']
        ]);

        if (!$this->CartCreditPayments->save($cartCreditPayment)) {
            $this->log(json_encode($cartCreditPayment->getErrors()), 'debug');
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Failed to save Cart Credit Payment')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $result = [
            'status' => 'success',
            'code' => 200,
            'message' => __('Credit Application and Cart Credit Payment saved successfully'),
            'data' => [
                'credit_application_id' => $application->id,
                'cart_credit_payment_id' => $cartCreditPayment->id
            ]
        ];

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    /* Support Desk*/

    //Salse persons
    public function listSalespersons()
    {
    
        $salesperson = $this->Users->find('list', [
            'keyField' => 'id',
            'valueField' => function ($row) {
                return $row->first_name . ' ' . $row->last_name;
            },
        ])
        ->contain(['Roles'])
        ->where([
            'Users.status' => 'A',
            'Roles.name' => 'Sales Person'
        ])
        ->toArray();
        if(!empty($salesperson))
        {
            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => [
                    'salespersons' => $salesperson,
                ]
            ];
        }
        else{
            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => [
                    'salespersons' => 'No Sales Persion Found.'
                ]
            ];
        }
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);

    }

    // Ax
    public function myWalletList()
    {
        if (!$this->request->is('get')) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $identity = $this->request->getAttribute('identity');
        if (!$identity) {
            $result = [
                'status' => 'error', 
                'code' => 401,
                'message' => __('User is not authenticated')
            ];
            $this->response = $this->response->withStatus(401);
            goto label;
        }
       
        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where([
                'Users.status' => 'A',
                'Users.id' => $identity->get('id')
            ])
            ->first();
        
        $customerId = $users->customer->id;
        $wallet = $this->Wallets->getMyWalletAmount($customerId);
        $walletAmount = $wallet ? ($wallet->balance ?? 0.00) : 0.00;

        $filter = $this->request->getQuery('filter', 'all');
        $page = (int)$this->request->getQuery('page', 1);
        $limit = (int)$this->request->getQuery('limit', 10);

        $transactions = $this->Orders->WalletOrderTransactionsPaginated($customerId, $filter, $page, $limit);

        $result = [
            'status' => 'success',
            'code' => 200,
            'data' => [
                'wallet_amount' => $walletAmount,
                'transactions' => $transactions,
                'filter' => $filter,
                'page' => $page,
                'limit' => $limit
            ]
        ];

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    /**
     * Update order status based on the status of its items
     * This method automatically determines and updates the order status
     * based on the collective status of all order items
     */
    private function updateOrderStatusBasedOnItems($orderId)
    {
        
        try {
            
            // Get the order with all its items
            $order = $this->Orders->get($orderId, [
                'contain' => ['OrderItems']
            ]);

            if (empty($order->order_items)) {
                return false;
            }

            // Get all unique item statuses
            $itemStatuses = array_unique(array_column($order->order_items, 'status'));
            
            // Determine new order status based on item statuses
            $newOrderStatus = $this->calculateOrderStatus($itemStatuses, $order->order_items);
      
            // Update order status if it has changed
            if ($newOrderStatus !== $order->status) {
                $order->status = $newOrderStatus;
                
                if ($this->Orders->save($order)) {
                    // Add tracking history for order status change
                    $trackingData = [
                        'order_id' => $orderId,
                        'status' => $newOrderStatus,
                        'comment' => 'Order status automatically updated based on item statuses',
                    ];
                    $this->OrderTrackingHistories->add_record($trackingData);
                    
                    return true;
                }
            }
            
            return true;
        } catch (\Exception $e) {
            // Log error but don't fail the main operation
            return false;
        }
    }

    /**
     * Calculate the appropriate order status based on item statuses
     */
    private function calculateOrderStatus($itemStatuses, $orderItems)
    {
        $totalItems = count($orderItems);
        $cancelledItems = 0;
        $deliveredItems = 0;
        $shippedItems = 0;
        $processingItems = 0;
        $pendingItems = 0;
        $returnedItems = 0;

        // Count items by status
        foreach ($orderItems as $item) {
            switch ($item->status) {
                case 'Delivered':
                    $deliveredItems++;
                    break;
                case 'Shipped':
                    $shippedItems++;
                    break;
                case 'Processing':
                    $processingItems++;
                    break;
                case 'Pending':
                    $pendingItems++;
                    break;
                case 'Pending Cancellation':
                    $pendingItems++;
                    break;
                case 'Cancelled':
                    $cancelledItems++;
                    break;
                case 'Returned':
                    $returnedItems++;
                    break;
            }
        }

        // Business logic for status determination
        if ($cancelledItems === $totalItems) {
            return 'Cancelled';
        } elseif ($returnedItems === $totalItems) {
            return 'Returned';
        } elseif ($deliveredItems === $totalItems) {
            return 'Delivered';
        } elseif ($deliveredItems > 0 && ($deliveredItems + $cancelledItems) === $totalItems) {
            return 'Partially Delivered';
        } elseif ($shippedItems > 0) {
            return 'Shipped';
        } elseif ($processingItems > 0) {
            return 'Processing';
        } elseif ($cancelledItems > 0 || $pendingItems > 0) {
            return 'Pending Cancellation';
        } elseif ($returnedItems > 0) {
            return 'Pending Return';
        } else {
            // Default to pending
            return 'Pending';
        }
    }

    //New function: Order cancellation using OrderReturns table (to match web application behavior)
    public function orderCancel($orderId = null)
    {
        $result = [];

        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $customerId = $identity->get('_matchingData')['Customers']['id'];
            $custDetail = $this->Customers->get($customerId);
            $deviceToken = $custDetail['fcm_token'];

            $userId = $identity['id'];
            $user = $this->Users->get($userId);

            if (empty($orderId)) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Order ID is required')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            try {
                $order = $this->Orders->get($orderId, [
                    'contain' => ['OrderItems']
                ]);
            } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Order not found')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if ($order->customer_id !== $customerId) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Unauthorized to cancel this order')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $anyDelivered = false;
            foreach ($order->order_items as $item) {
                if ($item->status === 'Shipped') {
                    $anyDelivered = true;
                    break;
                }

                if ($item->status === 'Delivered') {
                    $anyDelivered = true;
                    break;
                }
            }

            if ($anyDelivered) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Cannot cancel order as one or more items have been shipped/delivered')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            // Note: Individual item cancellation checks are now handled within the loop
            // This allows partial order cancellations and prevents duplicate records per item

            $data = $this->request->getData();
            
            if($order->payment_method == 'Cash on Delivery') {
                $order->status = 'Cancelled';
                $order_status = 'Cancelled';
                $tracking_status = 'Cancelled';
            } else {
                $order->status = 'Pending Cancellation';
                $order_status = 'Pending Cancellation';  // Fixed: Should match order status
                $tracking_status = 'Pending Cancellation';
            }

            if ($this->Orders->save($order)) {
                // Reduce loyalty points for this cancelled order
                $orderSubtotal = (float)$order->subtotal_amount;
                $loyaltyResult = $this->Loyalty->reduceLoyaltyPointsForOrder(
                    $customerId, 
                    $orderSubtotal, 
                    'Order cancellation'
                );
                
                // Create cancellation records for each order item (product-by-product approach)
                $cancellationSuccess = true;
                foreach ($order->order_items as $item) {
                    // Check if cancellation already exists for this order item
                    $existingCancellation = $this->OrderReturns->find()
                        ->where([
                            'order_item_id' => $item->id,
                            'request_type' => 'Cancellation'
                        ])
                        ->first();
                    
                    // Skip if cancellation already exists for this item
                    if ($existingCancellation) {
                        continue;
                    }
                    
                    $cancellationData = [
                        'order_id' => $orderId,
                        'customer_id' => $customerId,
                        'order_item_id' => $item->id, // Individual item ID for product-by-product tracking
                        'order_return_category_id' => $data['reason_id'],
                        'return_quantity' => $item->quantity, // Quantity of this specific item
                        'return_product_image' => null,
                        'request_type' => 'Cancellation',
                        'status' => $order_status === 'Pending Cancellation' ? 'Pending' : $order_status,
                        'return_to' => null,
                        'return_to_id' => null,
                        'pickup_required' => 'No',
                        'pickup_charge' => 0,
                        'reason' => $data['reason'] ?? null,
                        'requested_by' => $userId,
                        'requested_at' => date('Y-m-d H:i:s'),
                        'return_amount' => (float)$item->total_price, // Amount for this specific item
                        'customer_address_id' => null
                    ];
                    
                    $cancellation = $this->OrderReturns->newEntity($cancellationData);
                    if (!$this->OrderReturns->save($cancellation)) {
                        $cancellationSuccess = false;
                        break;
                    }
                }

                if ($cancellationSuccess) {
                    $itemStatus = ($order->status === 'Cancelled') ? 'Cancelled' : 'Pending Cancellation';
                    $this->OrderItems->updateAll(['status' => $itemStatus], ['order_id' => $orderId]);

                    // Update order status based on item statuses
                    $this->updateOrderStatusBasedOnItems($order->id);

                    $trackingData = [
                        'order_id' => $orderId,
                        'status' => $tracking_status,
                        'comment' => $data['reason'] ?? null,
                    ];
                    $tracking = $this->OrderTrackingHistories->add_record($trackingData);

                    // Get tracking history for the order
                    $trackingHistory = $this->OrderTrackingHistories->find()
                        ->select(['id', 'order_id', 'status', 'comment', 'updated'])
                        ->where(['order_id' => $orderId])
                        ->order(['updated' => 'ASC'])
                        ->toArray();

                    $formattedTrackingHistory = array_map(function ($tracking) {
                        return [
                            'status' => $tracking->status,
                            'comment' => $tracking->comment,
                            'updated_at' => $tracking->updated,
                        ];
                    }, $trackingHistory);

                    $category = $this->OrderCancellationCategories->find()
                        ->select(['name'])
                        ->where(['id' => $data['reason_id']])
                        ->first();

                    $reasonName = $category ? $category->name : __('No reason provided');

                    $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
                    $to = $adminEmails[0];
                    $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
                    $subject = 'Order Cancellation Request';
                    $template = 'order_cancellation';
                    $viewVars = [
                        'order_number' => $order['order_number'],
                        'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
                        'reason' => $reasonName,
                        'comment' => $data['reason'] ?? 'No comment provided',
                        'canceled_at' => $cancellationData['requested_at'],
                    ];

                    $sendEmail = $this->WebsiteFunction->orderCancellationRequestEmail($data['order_item_id'] ?? null, $data['reason_id'], $data['reason'], date('Y-m-d H:i:s'));

                    if ($sendEmail) {
                        $result = [
                            'status' => 'success',
                            'code' => 200,
                            'message' => __('Order cancelled has been initiated successfully, and an email notification has been sent to the admin.'),
                            'data' => [
                                'order_id' => $orderId,
                                'order_number' => $order['order_number'],
                                'status' => $order->status,
                                'tracking_history' => $formattedTrackingHistory
                            ]
                        ];
                        //Order cancell pushnotification.
                        $title = 'Order cancelled - #' . $order['order_number'];
                        $body  = 'Your order has been cancelled.';
                        $customData = [
                            "notification_type" => "order",
                            "id" => (string)$orderId,
                            "category" => "order"
                        ];
                        $response = $this->Global->sendNotification(
                            [$deviceToken],
                            $title,
                            $body,
                            $customData
                        );
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Order cancelled has been initiated successfully, but the email notification could not be sent.'),
                            'data' => [
                                'order_id' => $orderId,
                                'order_number' => $order['order_number'],
                                'status' => $order->status,
                                'tracking_history' => $formattedTrackingHistory
                            ]
                        ];
                        
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to cancel the order')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to cancel the order')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //New function: Order item cancellation using OrderReturns table (to match web application behavior)
    public function orderItemCancel($orderItemId = null)
    {
        if (!$this->request->is('post')) {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
            goto label;
        }

        $identity = $this->request->getAttribute('identity');
        if (!$identity) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('User is not authenticated')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $customerId = $identity->get('_matchingData')['Customers']['id'];
        $custDetail = $this->Customers->get($customerId);
        $deviceToken = $custDetail['fcm_token'];

        if (empty($orderItemId)) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Order item id is required')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }
        $data = $this->request->getData();
        $cancelQuantity = isset($data['cancel_quantity']) ? (int)$data['cancel_quantity'] : null;
        try{
            $orderItem = $this->OrderItems->get($orderItemId, [
                'contain' => ['Products']
            ]);
            if ($cancelQuantity && $cancelQuantity > 0) {
                if ($cancelQuantity > $orderItem->quantity) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Cancelled quantity cannot be greater than ordered quantity.')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                } else if ($cancelQuantity < $orderItem->quantity) {
                    // Split item: reduce original, create new for cancellation
                    $originalQuantity = $orderItem->quantity;
                    $orderItem->quantity = $originalQuantity - $cancelQuantity;
                    $orderItem->total_price = $orderItem->price * $orderItem->quantity;
                    $this->OrderItems->save($orderItem);
                    $newItemData = $orderItem->toArray();
                    unset($newItemData['id'], $newItemData['product']); // Remove id and product association
                    $newItemData['quantity'] = $cancelQuantity;
                    $newItemData['total_price'] = $orderItem->price * $cancelQuantity;
                    $newItemData['status'] = 'Pending Cancellation';
                    $newItem = $this->OrderItems->newEntity($newItemData);
                    $this->OrderItems->save($newItem);
                    $orderItemId = $newItem->id;
                    $orderItem = $newItem;
                }
            }
            // Reduce loyalty points for this order item (full cancellation only)
            if ($cancelQuantity === null || $cancelQuantity == $orderItem->quantity) {
                $orderItemSubtotal = (float)$orderItem->total_price;
                $loyaltyResult = $this->Loyalty->reduceLoyaltyPointsForOrder(
                    $customerId, 
                    $orderItemSubtotal, 
                    'Order item cancellation'
                );
                
            }
        } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Order item not found')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $order = $this->Orders->get($orderItem->order_id);
        if ($order->customer_id !== $customerId) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Unauthorized to cancel this order')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }
        if ($orderItem->status === 'Shipped') {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Cannot cancel item has been shipped')
            ];
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        // Check for existing cancellation in OrderReturns table
        $existingCancellation = $this->OrderReturns->find()
            ->where([
                'order_item_id' => $orderItemId,
                'request_type' => 'Cancellation'
            ])
            ->first();

        if ($existingCancellation) {
            if ($existingCancellation->status === 'Pending') {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Cancellation has already been initiated')
                ];
            } elseif ($existingCancellation->status === 'Approved') {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Cancellation has already in progress')
                ];
            } elseif ($existingCancellation->status === 'Completed') {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('This item has already been cancelled')
                ];
            }
            $this->response = $this->response->withStatus(200);
            goto label;
        }

        $userId = $identity['id'];
        $user = $this->Users->get($userId);

        // Process cancellation
        $data = $this->request->getData();
    
        // Create cancellation record in OrderReturns table
        $cancellationData = [
            'order_id' => $orderItem->order_id,
            'order_item_id' => $orderItemId,
            'order_return_category_id' => $data['reason_id'],
            'return_quantity' => $orderItem->quantity,
            'return_product_image' => null,
            'request_type' => 'Cancellation',
            'status' => 'Pending',
            'return_to' => null,
            'return_to_id' => null,
            'pickup_required' => 'No',
            'pickup_charge' => 0,
            'note' => $data['reason'] ?? null,
            'requested_by' => $userId,
            'requested_at' => date('Y-m-d H:i:s'),
            'return_amount' => (float)$orderItem->total_price,
            'customer_address_id' => null
        ];

        $orderItem->status = 'Pending Cancellation';

        if ($this->OrderItems->save($orderItem)) {
           
            // Update order status based on item statuses
            file_put_contents(LOGS . 'debug.log', "Calling updateOrderStatusBasedOnItems for order {$order->id}\n", FILE_APPEND);
            $updateResult = $this->updateOrderStatusBasedOnItems($order->id);
            file_put_contents(LOGS . 'debug.log', "updateOrderStatusBasedOnItems result: " . ($updateResult ? 'true' : 'false') . "\n", FILE_APPEND);
           
            $cancellation = $this->OrderReturns->newEntity($cancellationData);
           
            if ($this->OrderReturns->save($cancellation)) {
                $trackingData = [
                    'order_item_id' => $orderItemId,
                    'status' => 'Pending Cancellation',
                    'comment' => $data['comment'] ?? null,
                ];
                $this->OrderTrackingHistories->add_record($trackingData);

                // Get tracking history for the order item
                $trackingHistory = $this->OrderTrackingHistories->find()
                    ->select(['id', 'order_id', 'order_item_id', 'status', 'comment', 'updated'])
                    ->where(['order_item_id' => $orderItemId])
                    ->order(['updated' => 'ASC'])
                    ->toArray();

                $formattedTrackingHistory = array_map(function ($tracking) {
                    return [
                        'status' => $tracking->status,
                        'comment' => $tracking->comment,
                        'updated_at' => $tracking->updated,
                    ];
                }, $trackingHistory);

                $category = $this->OrderCancellationCategories->find()
                    ->select(['name'])
                    ->where(['id' => $data['reason_id']])
                    ->first();

                $reasonName = $category ? $category->name : __('No reason provided');

                $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
                $to = $adminEmails[0];
                $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
                $subject = 'Order Cancellation Request';
                $template = 'order_item_cancellation';
                $viewVars = [
                    'order_number' => $order['order_number'],
                    'order_item_product' => $orderItem['product']['name'] ?? '-',
                    'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
                    'reason' => $reasonName,
                    'comment' => $data['reason'] ?? 'No comment provided',
                    'canceled_at' => $cancellationData['requested_at'],
                ];

                $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);

                if ($sendEmail) {

                    //Order item cancel pushnotification.
                    $title = 'Order Item cancelled - #' . $order['order_number'];
                    $body  = 'Your order item has been cancelled.';
                    $customData = [
                        "notification_type" => "orderItem",
                        "id" => (string)$orderItemId,
                        "category" => "order"
                    ];
                    $response = $this->Global->sendNotification(
                        [$deviceToken],
                        $title,
                        $body,
                        $customData
                    );

                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => __('Order item cancelled has been initiates successfully, and an email notification has been sent to the admin.'),
                        'data' => [
                            'order_item_id' => $orderItemId,
                            'order_id' => $orderItem->order_id,
                            'status' => $orderItem->status,
                            'tracking_history' => $formattedTrackingHistory
                        ]
                    ];
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Order item cancelled has been initiates successfully, but the email notification could not be sent to the admin.'),
                        'data' => [
                            'order_item_id' => $orderItemId,
                            'order_id' => $orderItem->order_id,
                            'status' => $orderItem->status,
                            'tracking_history' => $formattedTrackingHistory
                        ]
                    ];
                }
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to initiate cancellation')
                ];
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Failed to update order item status')
            ];
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    /**
     * Get cancellation information for an order item
     * 
     * @param int $orderItemId
     * @return array
     */
    private function getOrderItemCancellationInfo($orderItemId)
    {
        $cancellationInfo = [
            'has_cancellation_request' => false,
            'cancellation_status' => null,
            'cancellation_reason' => null,
            'cancellation_comment' => null,
            'requested_at' => null,
            'processed_at' => null,
            'processed_by' => null
        ];

        // Check for cancellation in OrderReturns table (used for cancellations)
        $orderReturn = $this->OrderReturns->find()
            ->contain(['OrderReturnCategories', 'VerifiedByUser'])
            ->where([
                'order_item_id' => $orderItemId,
                'request_type' => 'Cancellation'
            ])
            ->first();

        if ($orderReturn) {
            $cancellationInfo['has_cancellation_request'] = true;
            $cancellationInfo['cancellation_status'] = $orderReturn->status;
            $cancellationInfo['cancellation_reason'] = $orderReturn->order_return_category ? $orderReturn->order_return_category->name : null;
            $cancellationInfo['cancellation_comment'] = $orderReturn->note;
            $cancellationInfo['requested_at'] = $orderReturn->requested_at;
            $cancellationInfo['processed_at'] = $orderReturn->verified_time;
            $cancellationInfo['processed_by'] = $orderReturn->verified_by_user ? 
                trim($orderReturn->verified_by_user->first_name . ' ' . $orderReturn->verified_by_user->last_name) : null;
        } else {
            // Check for cancellation in OrderCancellations table (alternative method)
            $orderCancellation = $this->OrderCancellations->find()
                ->contain(['OrderCancellationCategories'])
                ->where(['order_item_id' => $orderItemId])
                ->first();

            if ($orderCancellation) {
                $cancellationInfo['has_cancellation_request'] = true;
                $cancellationInfo['cancellation_status'] = $orderCancellation->status;
                $cancellationInfo['cancellation_reason'] = $orderCancellation->order_cancellation_category ? $orderCancellation->order_cancellation_category->name : null;
                $cancellationInfo['cancellation_comment'] = $orderCancellation->reason;
                $cancellationInfo['requested_at'] = $orderCancellation->canceled_at;
                $cancellationInfo['processed_at'] = $orderCancellation->updated;
            }
        }

        return $cancellationInfo;
    }

    /**
     * Get return information for an order item
     * 
     * @param int $orderItemId
     * @return array
     */
    private function getOrderItemReturnInfo($orderItemId)
    {
        $returnInfo = [
            'has_return_request' => false,
            'return_status' => null,
            'return_reason' => null,
            'return_comment' => null,
            'requested_at' => null,
            'processed_at' => null,
            'processed_by' => null,
            'return_quantity' => null,
            'return_amount' => null
        ];

        // Check for return in OrderReturns table
        $orderReturn = $this->OrderReturns->find()
            ->contain(['OrderReturnCategories', 'VerifiedByUser'])
            ->where([
                'order_item_id' => $orderItemId,
                'request_type' => 'Return'
            ])
            ->first();

        if ($orderReturn) {
            $returnInfo['has_return_request'] = true;
            $returnInfo['return_status'] = $orderReturn->status;
            $returnInfo['return_reason'] = $orderReturn->order_return_category ? $orderReturn->order_return_category->name : null;
            $returnInfo['return_comment'] = $orderReturn->note;
            $returnInfo['requested_at'] = $orderReturn->requested_at;
            $returnInfo['processed_at'] = $orderReturn->verified_time;
            $returnInfo['processed_by'] = $orderReturn->verified_by_user ? 
                trim($orderReturn->verified_by_user->first_name . ' ' . $orderReturn->verified_by_user->last_name) : null;
            $returnInfo['return_quantity'] = $orderReturn->return_quantity;
            $returnInfo['return_amount'] = $orderReturn->return_amount;
        }

        return $returnInfo;
    }

}
