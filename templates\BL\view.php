<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Zone> $zones
 */
?>

<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #toRemovePadding {
        margin-bottom: 0px !important;
        padding-bottom: 0px !important;
    }

    #remove-border {
        border: none;
        background-color: transparent;
    }

    .modal-dialog{
        max-width: 1000px !important;
    }

    label {
    width: 115px !important;
    }

</style>
<?php $this->end();
?>

<section class="section">
    <div
        class="section-header d-flex justify-content-between align-items-center mb-3"
    >
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item"><a
                    href="<?= $this->Url->build(['controller' => 'BL', 'action' => 'index']) ?>"><?= __('BL') ?></a>
            </li>
            <li class="breadcrumb-item active"><?= __('View') ?></li>
        </ul>
        <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
            <small class="p-10 fw-bold"><?= __('BACK') ?></small>
            <span class="rotate me-2">⤣</span>
        </button>
    </div>

    <h5 class="m-l-10 p-t-10 p-b-10" style="color: black;"><?= __('View BLs') ?></h5>

    <div class="section-body">
        <div class="container-fluid">
            

            <div class="card-body">
                <div class="container-fluid">
                    <div class="row">
                    <div class="col-sm-7">
                    <form>

                        <div class="form-group row">
                            <label for="warehouse" class="col-sm-2 col-form-label fw-bold"><?= __('BL Number') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $Bl->bl_no ? $Bl->bl_no : '-' ?></p>
                            </div>
                        </div>
            
                        <div class="form-group row">
                            <label for="supplier" class="col-sm-2 col-form-label fw-bold"><?= __('BL Date') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $Bl->bl_date ? $Bl->bl_date : '-' ?></p>
                            </div>
                        </div>
                        <?php                           
                            $supplier = $this->Supplier->getSupplierDetails($Bl->supplier_id);
                        ?>
                        <div class="form-group row">
                            <label for="supplier" class="col-sm-2 col-form-label fw-bold"><?= __('Supplier') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $supplier->name ? $supplier->name : '-' ?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="supplier" class="col-sm-2 col-form-label fw-bold"><?= __('PO Number:') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?= $Bl->stock_request_id ? $Bl->stock_request_id : '-' ?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="supplier" class="col-sm-2 col-form-label fw-bold"></label>
                            <div class="col-sm-5 ps-5">
                            </div>
                        </div>


                    </form>
                    </div>
                    <div class="col-sm-5 ps-5">
                        <?php if (!empty($Bl->bl_image)): ?>
+                            <img src=<?= $Bl->bl_image ?> alt="BL Image" class="img-fluid" style="max-width: 100%; height: auto;">                        <?php endif; ?>
                            
                    </div>
                </div>
            </div>

            <div class="card-body pt-0">
                <div class="card" id="toRemovePadding">
                    <div class="card-header p-0">
                        <h4 style="display: none;"></h4>
                        <div class="card-header-form">
                            <form>
                                <div class="input-group">
                                    <input
                                        type="text"
                                        class="form-control search-control"
                                        placeholder="Search By product Name"
                                        id="customSearchBox"
                                    />
                                    <div class="input-group-btn">
                                        <button class="btn">
                                            <i
                                                class="fas fa-search"
                                            ></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="table-responsive" tabindex="1">
                            <table id="stockRequestTable" class="table dataTable table-hover table-xl mb-0 mt-4" id="stockTable">
                                <thead>
                                    <tr>
                                        <th><?= __('Product Name') ?></th>
                                        <th><?= __('Product Variant') ?></th>
                                        <th><?= __('Product Attribute') ?></th>
                                        <th><?= __('SKU') ?></th>
                                        <th><?= __('Quantity') ?></th>
                                        <th><?= __('Defective Quantity') ?></th>
                                        <th><?= __('Damaged Quantity') ?></th>

                                        
                                        <!-- <th>< ?= __('Accepted') ?></th> -->
                                        <!-- <th>< ?= __('Rejected') ?></th> -->
                                    </tr>
                                </thead>
                                <tbody id="table_datalist">
                                    <!-- Static Rows -->
                                    <?php foreach ($BLItems as $item): 
                                        $product = $this->WebsiteFunction->getProductById($item->product_id);
                                        if(!is_null($item->product_variant_id)):
                                        $variant = $this->WebsiteFunction->getVariantById($item->product_variant_id);
                                    else:
                                        $variant=null;
                                    endif;

                                    if(!is_null($item->product_attribute_id)):
                                        $attribute = $this->WebsiteFunction->getAttributeById($item->product_attribute_id);
                                        $attributeValue = $this->WebsiteFunction->getValueByAttributeValueId($attribute->attribute_value_id);            
                                    else:
                                        $attributeValue = '';    
                                    endif;
                                    
                                    ?>
                                        <tr>
                                            <td><?php echo $product->name; ?></td>
                                            <td><?= !empty($variant) ? $variant->variant_name : '' ?></td>
                                            <td>
                                                <?php echo isset($attributeValue[0]) ? $attributeValue[0] : '';  ?>
                                            </td>
                                               
                                            <td>
                                                <?php echo $product->sku; ?>
                                            </td>

                                            <td><?php echo $item->requested_quantity; ?>
                                            </td>

                                            <td><?php echo $item->defective_quantity; ?></td>
                                            <td><?php echo $item->damaged_quantity; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script>

    // var paginationCount = <?= json_encode($paginationCount) ?>;
    // var table = $("#stockRequestTable").DataTable({
    //     order: [],
    //     columnDefs: [
    //         { orderable: false, targets: -1 }
    //     ],
    //     dom: 'rtip',
    //     pageLength: paginationCount,
    //     "columns": [
    //         { "data": "product_name" },
    //         { "data": "product_variant" },
    //         { "data": "product_attribute" },
    //         { "data": "sku" },
    //         { "data": "quantity" },
    //         { "data": "value" }
    //     ]
    // });

    var paginationCount = <?= json_encode($paginationCount) ?>;

    

    var columns = [
        { "data": "product_name" },
        { "data": "product_variant" },
        { "data": "product_attribute" },
        { "data": "sku" },
        { "data": "quantity" },
        {'data': "defective_quantity"},
        {'data': "damaged_quantity"},
    ];
    

    // Add the "value" column only if the role is Admin
    

    var table = $("#stockRequestTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": columns
    });

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });
</script>
<?php $this->end(); ?>