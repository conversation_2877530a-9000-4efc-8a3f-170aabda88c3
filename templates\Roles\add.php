<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\User $role
 */
?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Users") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'index']) ?>"><?= __("Roles") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Add") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<?php
function groupModules($modules)
{
    $grouped = [];
    foreach ($modules as $module) {
        $grouped[$module->parent_id][] = $module;
    }
    return $grouped;
}

$jsonOutput = [];

function renderModules($modules, &$jsonOutput, $parentId = null, $level = 0)
{
    $groupedModules = groupModules($modules);
    if (isset($groupedModules[$parentId])) {
        foreach ($groupedModules[$parentId] as $module) {
            $hasChildren = isset($groupedModules[$module->id]);

            if ($module->parent_id === null) {
                $rowClass = $hasChildren ? "parent-{$module->id} child-0" : "child-0";
            } else {
                $rowClass = $hasChildren ? "parent-{$module->id} child-{$module->parent_id}" : "child-{$module->parent_id}";
            }

            echo "<tr class=\"{$rowClass}\">";
            echo "<td data-label=\"Module Name\"><p style=\"margin-left: " . ($level * 15) . "px; margin-bottom: 0px;\">{$module->display_name}</p></td>";

            $permissionId = $module->id;

            if ($hasChildren) {
                echo "<td data-label=\"View\">
                        <input type=\"checkbox\" name=\"Permission[{$permissionId}][_read]\" class=\"ace view\" value=\"1\" id=\"Permission{$permissionId}Read\">
                        <label for=\"Permission{$permissionId}Read\" class=\"checkbox\"></label>
                      </td>";
                echo "<td data-label=\"Create\">
                        <input type=\"checkbox\" name=\"Permission[{$permissionId}][_create]\" class=\"ace create\" value=\"1\" id=\"Permission{$permissionId}Create\">
                        <label for=\"Permission{$permissionId}Create\" class=\"checkbox\"></label>
                      </td>";
                echo "<td data-label=\"Update\">
                        <input type=\"checkbox\" name=\"Permission[{$permissionId}][_update]\" class=\"ace update\" value=\"1\" id=\"Permission{$permissionId}Update\">
                        <label for=\"Permission{$permissionId}Update\" class=\"checkbox\"></label>
                      </td>";
                echo "<td data-label=\"Approve\">
                      <input type=\"checkbox\" name=\"Permission[{$permissionId}][_approve]\" class=\"ace approve\" value=\"1\" id=\"Permission{$permissionId}Approve\">
                      <label for=\"Permission{$permissionId}Approve\" class=\"checkbox\"></label>
                    </td>";
                echo "<td data-label=\"StoreBased\">
                    <input type=\"checkbox\" name=\"Permission[{$permissionId}][_store]\" class=\"ace store\" value=\"1\" id=\"Permission{$permissionId}StoreBased\">
                    <label for=\"Permission{$permissionId}StoreBased\" class=\"checkbox\"></label>
                  </td>";
                echo "<td data-label=\"Delete\">
                        <input type=\"checkbox\" name=\"Permission[{$permissionId}][_delete]\" class=\"ace delete\" value=\"1\" id=\"Permission{$permissionId}Delete\">
                        <label for=\"Permission{$permissionId}Delete\" class=\"checkbox\"></label>
                      </td>";
            } else {
                // Checkboxes for modules without children (with names)
                echo "<td data-label=\"View\">
                        <input type=\"checkbox\" name=\"Permission[{$permissionId}][_read]\" class=\"ace view\" value=\"1\" id=\"Permission{$permissionId}Read\">
                        <label for=\"Permission{$permissionId}Read\" class=\"checkbox\"></label>
                      </td>";
                echo "<td data-label=\"Create\">
                        <input type=\"checkbox\" name=\"Permission[{$permissionId}][_create]\" class=\"ace create\" value=\"1\" id=\"Permission{$permissionId}Create\">
                        <label for=\"Permission{$permissionId}Create\" class=\"checkbox\"></label>
                      </td>";
                echo "<td data-label=\"Update\">
                        <input type=\"checkbox\" name=\"Permission[{$permissionId}][_update]\" class=\"ace update\" value=\"1\" id=\"Permission{$permissionId}Update\">
                        <label for=\"Permission{$permissionId}Update\" class=\"checkbox\"></label>
                      </td>";
                echo "<td data-label=\"Approve\">
                      <input type=\"checkbox\" name=\"Permission[{$permissionId}][_approve]\" class=\"ace approve\" value=\"1\" id=\"Permission{$permissionId}Approve\">
                      <label for=\"Permission{$permissionId}Approve\" class=\"checkbox\"></label>
                    </td>";
                echo "<td data-label=\"StoreBased\">
                    <input type=\"checkbox\" name=\"Permission[{$permissionId}][_store]\" class=\"ace store\" value=\"1\" id=\"Permission{$permissionId}StoreBased\">
                    <label for=\"Permission{$permissionId}StoreBased\" class=\"checkbox\"></label>
                  </td>";
                echo "<td data-label=\"Delete\">
                        <input type=\"checkbox\" name=\"Permission[{$permissionId}][_delete]\" class=\"ace delete\" value=\"1\" id=\"Permission{$permissionId}Delete\">
                        <label for=\"Permission{$permissionId}Delete\" class=\"checkbox\"></label>
                      </td>";
            }
            echo "</tr>";

            // Recursively render children
            renderModules($modules, $jsonOutput, $module->id, $level + 1);
        }
    }
}
$jsonOutput = [];
?>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20" style="color: #004958"><?= __("Add Role") ?></h6>
            <?php echo $this->Form->create($role, ['id' => 'add', 'novalidate' => true]); ?>
            <div class="card-body">
                <div class="form-group row">
                    <label for="first_name" class="col-sm-2 col-form-label fw-bold">Role Name <sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5 main-field">
                        <?php echo $this->Form->control('name', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'name',
                            'placeholder' => 'Role Name',
                            'label' => false
                        ]); ?>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="first_name" class="col-sm-2 col-form-label">Description <sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5 main-field">
                        <?php echo $this->Form->control('description', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'description',
                            'placeholder' => 'Role Description',
                            'label' => false
                        ]); ?>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="status" class="col-sm-2 col-form-label fw-bold">
                        <?= __("Status") ?> <sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5 main-field">
                        <?= $this->Form->control('status', [
                            'type' => 'select',
                            'class' => 'form-control form-select',
                            'id' => 'status',
                            'options' => [
                                'A' => __('Active'),
                                'I' => __('Inactive'),
                                'D' => __('Deleted')
                            ],
                            'empty' => __('Select Status'),
                            'label' => false
                        ]); ?>
                    </div>
                </div>

                <style>
                    .table:not(.table-sm):not(.table-md):not(.dataTable) td,
                    .table:not(.table-sm):not(.table-md):not(.dataTable) th {
                        padding: 0 0px;
                        height: 60px;
                    }


                    .roles input {
                        height: auto !important;
                        width: unset !important;
                        margin: 22px 0px -13px 0px;
                    }

                    .table-responsive {
                        position: relative;
                    }

                    .table-header th {
                        position: sticky;
                        top: 0;
                        background: white;
                        /* Ensure it does not become transparent */
                        z-index: 10;
                        /* Keep it above the table body */
                    }

                    .table-striped tbody tr:nth-of-type(odd) {
                        background-color: transparent !important;
                        --bs-table-accent-bg: transparent !important;
                    }

                    .table-striped tbody tr:nth-of-type(odd) {
                        background-color: transparent(0, 0, 0, .02);
                    }

                    .table-striped>tbody>tr:nth-of-type(odd) {
                        --bs-table-accent-bg: var(--bs-table-striped-bg);
                        color: var(--bs-table-striped-color);
                    }

                    .table-striped tbody tr:nth-of-type(odd):hover,
                    .table-striped tbody tr:nth-of-type(even):hover {
                        background-color: transparent !important;
                        --bs-table-accent-bg: transparent !important;
                    }

                    tr[class^="parent-"] td:first-child p {
                        color: teal;
                    }

                    tr[class^="child-0"] {
                        color: teal;
                    }

                    .child-0 td {
                        color: teal;
                        background-color: #0080800d;
                    }
                </style>
                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table id="role-module-datatable" class="table table-striped roles">
                        <thead class="table-header">
                            <tr>
                                <th class="width300">Module Name</th>
                                <th class="width150">View</th>
                                <th class="width150">Create</th>
                                <th class="width150">Update</th>
                                <th class="width150">Approve</th>
                                <th class="width150">Store Based<br><small>(Turning this field on will restrict the role to get access to only data for their showrooms)</small></th>
                                <th class="width150">Delete</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php echo renderModules($modules, $jsonOutput); ?>
                        </tbody>
                    </table>
                </div>

                <div id="permission-error-message" class="text-danger"></div>
                <div class="form-group row">
                    <div class="col-sm-10 offset-sm-2">
                        <button type="submit" class="btn btn-primary"><?= __("Save") ?></button>
                        <button type="reset" class="btn btn-primary"><?= __("Reset") ?></button>
                    </div>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script>
    $(document).ready(function() {

        $.validator.addMethod("alphabetic", function(value, element) {
            return this.optional(element) || /^[a-zA-Z\s]+$/.test(value);
        }, "Please enter only alphabetic characters and spaces.");

        $.validator.addMethod("checkAtLeastOne", function(value, element) {
            alert($(element).closest('form').find('input:checkbox:checked').length);
            return $(element).closest('form').find('input:checkbox:checked').length > 0;
        }, "Please select at least one permission.");

        $("#add").validate({
            ignore: "",
            rules: {
                'name': {
                    required: true,
                    alphabetic: true
                },
                'description': {
                    required: true
                },
                'status': {
                    required: true
                }
            },
            messages: {
                'name': {
                    required: "Please enter role name"
                },
                'description': {
                    required: "Please enter role description"
                },
                'status': {
                    required: "Please select status"
                }
            },
            submitHandler: function(form) {
                var $permissions = $('input[name^="Permission"]:checked').length;
                if ($permissions === 0) {
                    $('#permission-error-message').text("Please select at least one permission");
                } else {
                    $('#permission-error-message').text("");
                    $('button[type="submit"]').attr('disabled', 'disabled');
                    form.submit();
                }
            },
            errorPlacement: function(error, element) {
                error.appendTo(element.closest(".main-field"));
            },
            highlight: function(element) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element) {
                $(element).removeClass('is-invalid');
            }
        });
    });

    $(function() {
        $.fn.parentClick = function(level) {
            var class_name, temp, parent_class, childs;

            class_name = $(this).parents("tr").attr("class");
            temp = class_name.match(/parent-\d+/);
            if (temp === null) {
                return;
            }
            parent_class = temp[0];
            childs = parent_class.replace("parent", "child");

            $("." + childs).find('input.' + level).prop("checked", $(this).prop("checked"));
            $(this).siblingParentClick(level);
            $(this).disableSiblingParent(level);

            $('.' + childs).find('input.' + level).each(function(key, element) {
                $(element).parentClick(level);
                $(element).checkViewParents(level);
            });
        };

        $.fn.checkClickedChild = function(level) {
            var class_name, temp, child_class;

            class_name = $(this).parents("tr").attr("class");
            temp = class_name.match(/child-\d+/);
            child_class = temp[0];

            if ($("." + child_class + " input." + level + ":checked").length) {
                var parents = child_class.replace("child", "parent");
                $("." + parents).find('input.' + level).prop("checked", true);
            }
        }

        $.fn.siblingCheckClickedChild = function(level) { // This function is not used
            if ($("." + $(this).parents("tr").attr("class") + " input." + level + ":checked").length) {
                var parents = $(this).parents("tr").attr("class").replace("child", "parent");
                $("." + parents).find('input.' + level).prop("checked", true);
            }
        }

        $.fn.childClick = function(level) {
            var class_name, temp, child_class, parents;

            class_name = $(this).parents("tr").attr("class");
            temp = class_name.match(/child-\d+/);
            if (temp === null) {
                return;
            }
            child_class = temp[0];
            parents = child_class.replace("child", "parent");

            if ($("." + child_class + " input." + level + ":checked").length == 0) {
                $("." + parents).find('input.' + level).prop("checked", $(this).prop("checked"));
            }
            $(this).checkClickedChild(level);
            $(this).siblingChildClick(level);
            $(this).disableSiblingChild(level);

            $('.' + parents).find('input.' + level).each(function(key, element) {
                $(element).childClick(level);
                $(element).checkView(level);
            });
        };

        $.fn.siblingChildClick = function(level) {
            var class_name, temp, child_class;
            var row = $(this).parents("tr").attr("class");
            class_name = $(this).parents("tr").attr("class");
            temp = class_name.match(/child-\d+/);
            child_class = temp[0];
            var parents = child_class.replace("child", "parent");
            switch (level) {
                case "create": {
                    $(this).parents("tr").find("input.view").prop("checked", $(this).prop("checked"));
                    $('.' + parents + " input.view").prop("checked", $(this).prop("checked"));
                    $(this).checkClickedChild("view");
                    break;
                }
                case "update": {
                    $(this).parents("tr").find("input.view").prop("checked", $(this).prop("checked"));
                    $('.' + parents + " input.view").prop("checked", $(this).prop("checked"));
                    $(this).checkClickedChild("view");
                    break;
                }
                case "delete": {
                    $(this).parents("tr").find("input.view").prop("checked", $(this).prop("checked"));
                    $('.' + parents + " input.view").prop("checked", $(this).prop("checked"));
                    $(this).checkClickedChild("view");
                    break;
                }

            }
        }

        $.fn.siblingParentClick = function(level) {
            var class_name, temp, parent_class;
            var row = $(this).parents("tr").attr("class");
            class_name = $(this).parents("tr").attr("class");
            temp = class_name.match(/parent-\d+/);
            parent_class = temp[0];
            var childs = parent_class.replace("parent", "child");
            switch (level) {
                case "create": {
                    $('.' + parent_class + " input.view").prop("checked", $(this).prop("checked"));
                    $('.' + childs + " input.view").prop("checked", $(this).prop("checked"));
                    break;
                }
                case "update": {
                    $('.' + parent_class + " input.view").prop("checked", $(this).prop("checked"));
                    $('.' + childs + " input.view").prop("checked", $(this).prop("checked"));
                    break;
                }
                case "delete": {
                    $('.' + parent_class + " input.view").prop("checked", $(this).prop("checked"));
                    $('.' + childs + " input.view").prop("checked", $(this).prop("checked"));
                    break;
                }

            }
        }

        $.fn.disableSiblingChild = function(level) {
            var class_name, temp, child_class;
            var row = $(this).parents("tr").attr("class");
            class_name = $(this).parents("tr").attr("class");
            temp = class_name.match(/child-\d+/);
            child_class = temp[0];
            var parents = child_class.replace("child", "parent");
            switch (level) {
                case "create": {
                    if ($(this).prop("checked"))
                        $(this).parents("tr").find("input.view").addClass("disabledCheckbox");
                    else
                        $(this).parents("tr").find("input.view").removeClass("disabledCheckbox");

                    if ($("." + child_class + " input.view:checked").length) {
                        var parents = child_class.replace("child", "parent");
                        $("." + parents).find('input.view').addClass("disabledCheckbox");
                    } else {
                        var parents = child_class.replace("child", "parent");
                        $("." + parents).find('input.view').removeClass("disabledCheckbox");
                    }
                    break;
                }
                case "update": {
                    if ($(this).prop("checked"))
                        $(this).parents("tr").find("input.view").addClass("disabledCheckbox");
                    else
                        $(this).parents("tr").find("input.view").removeClass("disabledCheckbox");
                    if ($("." + child_class + " input.view:checked").length) {
                        var parents = child_class.replace("child", "parent");
                        $("." + parents).find('input.view').addClass("disabledCheckbox");
                    } else {
                        var parents = child_class.replace("child", "parent");
                        $("." + parents).find('input.view').removeClass("disabledCheckbox");
                    }
                    break;
                }
                case "delete": {
                    if ($(this).prop("checked"))
                        $(this).parents("tr").find("input.view").addClass("disabledCheckbox");
                    else
                        $(this).parents("tr").find("input.view").removeClass("disabledCheckbox");
                    if ($("." + child_class + " input.view:checked").length) {
                        var parents = child_class.replace("child", "parent");
                        $("." + parents).find('input.view').addClass("disabledCheckbox");
                    } else {
                        var parents = child_class.replace("child", "parent");
                        $("." + parents).find('input.view').removeClass("disabledCheckbox");
                    }
                    break;
                }
            }
        }

        $.fn.disableSiblingParent = function(level) {
            var class_name, temp, parent_class;
            var row = $(this).parents("tr").attr("class");
            class_name = $(this).parents("tr").attr("class");
            temp = class_name.match(/parent-\d+/);
            parent_class = temp[0];
            var childs = parent_class.replace("parent", "child");
            switch (level) {
                case "create": {
                    if ($(this).prop("checked")) {
                        $('.' + parent_class + " input.view").addClass("disabledCheckbox");
                        $('.' + childs + " input.view").addClass("disabledCheckbox");
                    } else {
                        $('.' + parent_class + " input.view").removeClass("disabledCheckbox");
                        $('.' + childs + " input.view").removeClass("disabledCheckbox");
                    }
                    break;
                }
                case "update": {
                    if ($(this).prop("checked")) {
                        $('.' + parent_class + " input.view").addClass("disabledCheckbox");
                        $('.' + childs + " input.view").addClass("disabledCheckbox");
                    } else {
                        $('.' + parent_class + " input.view").removeClass("disabledCheckbox");
                        $('.' + childs + " input.view").removeClass("disabledCheckbox");
                    }
                    break;
                }
                case "delete": {
                    if ($(this).prop("checked")) {
                        $('.' + parent_class + " input.view").addClass("disabledCheckbox");
                        $('.' + childs + " input.view").addClass("disabledCheckbox");
                    } else {
                        $('.' + parent_class + " input.view").removeClass("disabledCheckbox");
                        $('.' + childs + " input.view").removeClass("disabledCheckbox");
                    }
                    break;
                }
            }
        }

        $.fn.checkView = function(level) {
            var class_name, temp, child_class;
            if ($(this).parents("tr").find('input.create').prop("checked") || $(this).parents("tr").find('input.update').prop("checked") || $(this).parents("tr").find('input.delete').prop("checked")) {
                class_name = $(this).parents("tr").attr("class");
                temp = class_name.match(/child-\d+/);
                child_class = temp[0];
                $(this).parents("tr").find('input.view').prop("checked", true).addClass("disabledCheckbox");
                var parents = child_class.replace("child", "parent");
                $("." + parents).find('input.view').prop("checked", true).addClass("disabledCheckbox");
            }
        };

        $.fn.checkViewParents = function(level) {
            var class_name, temp, parent_class;
            class_name = $(this).parents("tr").attr("class");
            temp = class_name.match(/parent-\d+/);
            if (temp === null) {
                return;
            }
            parent_class = temp[0];
            var childs = parent_class.replace("parent", "child");
            if ($(this).parents("tr").find('input.create').prop("checked") || $(this).parents("tr").find('input.update').prop("checked") || $(this).parents("tr").find('input.delete').prop("checked")) {
                $(this).parents("tr").find('input.view').prop("checked", true).addClass("disabledCheckbox");
                $("." + childs).each(function(key, element) {
                    if ($(element).find('input.create').prop("checked") || $(element).find('input.update').prop("checked") || $(element).find('input.delete').prop("checked")) {
                        $(element).find('input.view').prop("checked", true).addClass("disabledCheckbox");
                    }
                });
            }
        };

        $('tr[class*="parent"] input.view').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).parentClick("view")
            };
        });
        $('tr[class*="parent"] input.create').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).parentClick("create");
                $(this).checkViewParents("create");
            };
        });
        $('tr[class*="parent"] input.update').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).parentClick("update");
                $(this).checkViewParents("update");
            };
        });
        $('tr[class*="parent"] input.approve').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).parentClick("approve");
                $(this).checkViewParents("approve");
            };
        });
        $('tr[class*="parent"] input.store').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).parentClick("store");
                $(this).checkViewParents("store");
            };
        });
        $('tr[class*="parent"] input.delete').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).parentClick("delete");
                $(this).checkViewParents("delete");
            };
        });

        $('tr[class*="child"] input.view').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).childClick("view")
            };
        });
        $('tr[class*="child"] input.create').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).childClick("create");
                $(this).checkView("create");
            };
        });
        $('tr[class*="child"] input.update').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).childClick("update");
                $(this).checkView("update");
            };
        });
        $('tr[class*="child"] input.approve').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).childClick("approve");
                $(this).checkView("approve");
            };
        });
        $('tr[class*="child"] input.store').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).childClick("store");
                $(this).checkView("store");
            };
        });
        $('tr[class*="child"] input.delete').on("click", function() {
            if (!$(this).hasClass("disabledCheckbox")) {
                $(this).childClick("delete");
                $(this).checkView("delete");
            };
        });

        $('body').on("click", ".disabledCheckbox", function() {
            return false;
        });
    });
</script>
<?php $this->end(); ?>