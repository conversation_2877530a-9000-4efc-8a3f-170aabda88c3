<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Merchant $merchant
 * @var array $documentTypes
 * @var array $merchantOrders
 * @var array $orderStats
 * @var array $merchantRatings
 * @var array $ratingStats
 * @var array $merchantProducts
 * @var array $merchantSettlements
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<style>
    .seller-header-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }
    .seller-avatar {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: rgba(255,255,255,0.25);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        font-weight: bold;
        margin-right: 1.5rem;
        border: 3px solid rgba(255,255,255,0.3);
    }
    .quick-actions {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
        justify-content: flex-end;
    }
    .quick-actions .btn {
        border-radius: 8px;
        font-weight: 500;
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
    }
    .quick-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .nav-tabs {
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 2rem;
    }
    .nav-tabs .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        color: #6c757d;
        font-weight: 600;
        padding: 1rem 1.5rem;
        border-radius: 0;
        transition: all 0.3s ease;
    }
    .nav-tabs .nav-link:hover {
        color: #007bff;
        background: rgba(0, 123, 255, 0.05);
    }
    .nav-tabs .nav-link.active {
        border-bottom-color: #007bff;
        color: #007bff;
        background: none;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 1.75rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #f8f9fa;
        transition: all 0.3s ease;
        text-align: center;
    }
    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }
    .stats-number {
        font-size: 2.25rem;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 0.5rem;
        line-height: 1;
    }
    .stats-label {
        color: #6c757d;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
    }
    .content-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #f8f9fa;
        margin-bottom: 2rem;
    }
    .content-card .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 1.25rem 1.5rem;
        border-radius: 12px 12px 0 0;
    }
    .content-card .card-header h6 {
        margin: 0;
        font-weight: 600;
        color: #495057;
    }
    .content-card .card-body {
        padding: 1.5rem;
    }
    .document-status {
        padding: 0.375rem 0.875rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .status-verified { background: #d4edda; color: #155724; }
    .status-pending { background: #fff3cd; color: #856404; }
    .status-rejected { background: #f8d7da; color: #721c24; }
    .product-thumbnail {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
    }
    .rating-stars {
        color: #ffc107;
    }
    .bank-account-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid #e9ecef;
    }
    .bank-account-item .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f3f4;
    }
    .info-row:last-child {
        border-bottom: none;
    }
    .info-label {
        font-weight: 600;
        color: #495057;
        min-width: 140px;
    }
    .info-value {
        color: #6c757d;
        text-align: right;
    }
    .btn-map {
        background: #007bff;
        color: white;
        border: none;
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        font-size: 0.875rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
    }
    .btn-map:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }
    .table th {
        background: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }
    .table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }
    .modal-content {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    }
    .modal-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 12px 12px 0 0;
        padding: 1.5rem;
    }
    .modal-body {
        padding: 1.5rem;
    }
    .modal-footer {
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 12px 12px;
        padding: 1.25rem 1.5rem;
    }
    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ced4da;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }
    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    .btn {
        border-radius: 8px;
        font-weight: 500;
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
    }
    .btn:hover {
        transform: translateY(-1px);
    }
    .badge {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        border-radius: 20px;
        font-weight: 600;
    }
    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #e9ecef;
    }
    .tab-content {
        border-radius: 0 0 12px 12px;
        padding: 1.5rem;
        background: white;
        border: 1px solid #f8f9fa;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    #sellerTabs{
        border-radius: 12px 12px 0 0;
        background: white;
        border: 1px solid #f8f9fa;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    button{
        margin-right: 5px !important;
    }
    .container-fluid{
        padding-left: 0px !important;
        padding-right: 0px !important;
    }

    .tab-content, .container-fluid, .seller-header-card {
        position: static !important;
        z-index: auto !important;
        overflow: visible !important;
    }
    
    /* Modal Z-Index Fix - Modal must be higher than backdrop and loader overlay */
    .modal {
        z-index: 10000 !important;
        top: 10% !important;
    }
    .modal-backdrop {
        display: none !important;
        z-index: -1 !important;
    }
    .modal-dialog {
        z-index: 10001 !important;
    }
    .modal-content {
        z-index: 10002 !important;
    }
    #productsTable input{
        display: none !important;
        height: 15px !important;
    }
</style>
<?php $this->end(); ?>

<div class="section-header d-flex justify-content-between align-items-center mb-4">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Merchants") ?></li>
        <li class="breadcrumb-item"><a href="<?= $this->Url->build(['controller' => 'Sellers', 'action' => 'index']) ?>"><?= __("Manage Merchants") ?></a></li>
        <li class="breadcrumb-item active"><?= __("View") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<!-- Section 1: Merchant Overview (Header Card) -->
<div class="seller-header-card">
    <div class="row align-items-center">
        <div class="col-md-7">
            <div class="d-flex align-items-center">
                <div class="seller-avatar">
                    <?php if (!empty($merchant->merchant_logo)): ?>
                        <img src="<?= $this->Url->webroot($merchant->merchant_logo) ?>" alt="Merchant Logo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                    <?php else: ?>
                        <?= strtoupper(substr($merchant->company_name, 0, 2)) ?>
                    <?php endif; ?>
                </div>
                <div>
                    <h2 class="mb-2"><?= h($merchant->company_name) ?></h2>
                    <p class="mb-3 opacity-75 fs-5"><?= h($merchant->business_type) ?></p>
                    <div class="d-flex flex-wrap gap-4 mb-3">
                        <span class="d-flex align-items-center">
                            <i class="fas fa-id-card me-2"></i> <?= h($merchant->merchant_code) ?>
                        </span>
                        <span class="d-flex align-items-center">
                            <i class="fas fa-envelope me-2"></i> <?= h($merchant->user->email ?? 'N/A') ?>
                        </span>
                        <span class="d-flex align-items-center">
                            <i class="fas fa-phone me-2"></i> <?= h($merchant->phone_number) ?>
                        </span>
                    </div>
                    <div class="d-flex flex-wrap gap-3">
                        <span class="badge bg-<?= $merchant->verification_status === 'Verified' ? 'success' : ($merchant->verification_status === 'In Progress' ? 'warning' : 'secondary') ?>">
                            Verification: <?= h($merchant->verification_status ?? 'Not Started') ?>
                        </span>
                        <span class="badge bg-<?= $merchant->approval_status === 'Approved' ? 'success' : ($merchant->approval_status === 'Rejected' ? 'danger' : 'warning') ?>">
                            Status: <?= h($merchant->approval_status ?? 'Pending') ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-5">
            <div class="quick-actions">
                <a href="<?= $this->Url->build(['action' => 'edit', $merchant->id]); ?>" class="btn btn-light">
                    <i class="fas fa-edit me-1"></i> Edit
                </a>
                <?php if (in_array($merchant->approval_status, ['Pending', 'Info Requested'])): ?>
                    <button class="btn btn-success" onclick="changeStatus('Approved')">
                        <i class="fas fa-check me-1"></i> Approve
                    </button>
                    <button class="btn btn-danger" onclick="changeStatus('Rejected')">
                        <i class="fas fa-times me-1"></i> Reject
                    </button>
                    <?php if ($merchant->approval_status === 'Pending'): ?>
                        <button class="btn btn-warning" onclick="showRequestInfo()">
                            <i class="fas fa-info me-1"></i> Request Info
                        </button>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php if ($merchant->approval_status === 'Info Requested'): ?>
        <div class="mt-3 p-3 bg-warning bg-opacity-10 border border-warning rounded w-100">
            <div class="d-flex align-items-start">
                <i class="fa fa-info-circle text-white me-2 mt-1"></i>
                <div>
                    <strong class="text-white">Information Requested:</strong>
                    <p class="mb-0 mt-1"><?= !empty($merchant->admin_note) ? h($merchant->admin_note) : 'Additional information has been requested from the merchant.' ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Debug Info (remove this after testing) -->
    <?php if (isset($_GET['debug'])): ?>
        <div class="mt-3 p-3 bg-info bg-opacity-10 border border-info rounded w-100">
            <div class="d-flex align-items-start">
                <i class="fa fa-bug text-info me-2 mt-1"></i>
                <div>
                    <strong class="text-info">Debug Info:</strong>
                    <p class="mb-0 mt-1">
                        Status: <?= h($merchant->approval_status) ?><br>
                        Admin Note: <?= h($merchant->admin_note) ?><br>
                        Admin Note Empty: <?= empty($merchant->admin_note) ? 'Yes' : 'No' ?>
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Merchant Banner Section -->
<?php if (!empty($merchant->banner)): ?>
<div class="container-fluid mb-4">
    <div class="card">
        <div class="card-body p-0">
            <img src="<?= $this->Url->webroot($merchant->banner) ?>" alt="Merchant Banner" class="w-100" style="max-height: 300px; object-fit: cover; border-radius: 8px;">
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Tabbed Content -->
<div class="container-fluid">
    <ul class="nav nav-tabs" id="sellerTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                <i class="fas fa-chart-line me-2"></i> Overview
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="kyc-tab" data-bs-toggle="tab" data-bs-target="#kyc" type="button" role="tab">
                <i class="fas fa-file-alt me-2"></i> KYC & Documents
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab">
                <i class="fas fa-box me-2"></i> Products
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">
                <i class="fas fa-shopping-cart me-2"></i> Orders
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="earnings-tab" data-bs-toggle="tab" data-bs-target="#earnings" type="button" role="tab">
                <i class="fas fa-money-bill-wave me-2"></i> Earnings & Commission
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab">
                <i class="fas fa-star me-2"></i> Ratings & Reviews
            </button>
        </li>
    </ul>

    <div class="tab-content" id="sellerTabsContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <!-- Key Stats -->
            <div class="mb-4">
                <h5 class="section-title">Key Stats</h5>
                <div class="stats-grid">
                    <div class="stats-card">
                        <div class="stats-number"><?= count($merchantProducts) ?></div>
                        <div class="stats-label">Total Products Listed</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number"><?= $orderStats['total_orders'] ?? 0 ?></div>
                        <div class="stats-label">Total Orders Received</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number">₹<?= number_format($orderStats['avg_order_value'] ?? 0, 2) ?></div>
                        <div class="stats-label">Avg Order Value</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number">0%</div>
                        <div class="stats-label">Return Rate</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number"><?= $orderStats['total_orders'] > 0 ? round(($orderStats['delivered_orders'] / $orderStats['total_orders']) * 100, 1) : 0 ?>%</div>
                        <div class="stats-label">Fulfillment Rate</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number"><?= $orderStats['canceled_orders'] ?? 0 ?></div>
                        <div class="stats-label">Cancellations</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number">0h</div>
                        <div class="stats-label">Avg Dispatch Time</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number"><?= number_format($ratingStats['avg_rating'] ?? 0, 1) ?></div>
                        <div class="stats-label">Merchant Rating</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Store Details -->
                <div class="col-md-6">
                    <div class="content-card">
                        <div class="card-header">
                            <h6 class="mb-0">Business Details</h6>
                        </div>
                        <div class="card-body">
                            <div class="info-row">
                                <span class="info-label">Merchant Logo</span>
                                <span class="info-value">
                                    <?php if (!empty($merchant->merchant_logo)): ?>
                                        <img src="<?= $this->Url->webroot($merchant->merchant_logo) ?>" alt="Merchant Logo" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;">
                                    <?php else: ?>
                                        <span class="text-muted">No logo uploaded</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Merchant Banner</span>
                                <span class="info-value">
                                    <?php if (!empty($merchant->banner)): ?>
                                        <img src="<?= $this->Url->webroot($merchant->banner) ?>" alt="Merchant Banner" style="width: 100px; height: 50px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;">
                                    <?php else: ?>
                                        <span class="text-muted">No banner uploaded</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Business Description</span>
                                <span class="info-value"><?= h($merchant->description ?? 'No description available') ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Business Address</span>
                                <span class="info-value"><?= h($merchant->address_line1) ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Categories</span>
                                <span class="info-value">
                                    <?php 
                                    if (!empty($merchant->category_ids)) {
                                        // Convert comma-separated string to array
                                        $categoryIds = explode(',', $merchant->category_ids);
                                        if (!empty($categoryIds)) {
                                            $categoryNames = [];
                                            foreach ($categoryIds as $categoryId) {
                                                $categoryId = trim($categoryId);
                                                if (!empty($categoryId) && isset($businessCategories[$categoryId])) {
                                                    $categoryNames[] = $businessCategories[$categoryId];
                                                }
                                            }
                                            echo !empty($categoryNames) ? implode(', ', $categoryNames) : 'N/A';
                                        } else {
                                            echo 'N/A';
                                        }
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Commission Rate</span>
                                <span class="info-value"><?= h($merchant->commission_rate ?? '0.00') ?>%</span>
                            </div>
                            <div class="mt-3">
                                <a href="<?= $mapUrl ?>" target="_blank" class="btn-map" title="View <?= h($merchant->company_name) ?> location on Google Maps">
                                    <i class="fas fa-map-marker-alt"></i> View on Map
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bank Details -->
                <div class="col-md-6">
                    <div class="content-card">
                        <div class="card-header">
                            <h6 class="mb-0">Bank Account Details</h6>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($merchant->bank_acc_no) || !empty($merchant->ifsc_code)): ?>
                                <div class="bank-account-item">
                                    <span class="badge bg-primary mb-3">Primary Account</span>
                                    <div class="row">
                                        <div class="col-6 mb-2">
                                            <small class="text-muted d-block">Account Number</small>
                                            <strong><?= h($merchant->bank_acc_no ?? 'Not provided') ?></strong>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted d-block">IFSC Code</small>
                                            <strong><?= h($merchant->ifsc_code ?? 'Not provided') ?></strong>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted d-block">Tax ID</small>
                                            <strong><?= h($merchant->tax_identification_no ?? 'Not provided') ?></strong>
                                        </div>
                                        <div class="col-6 mb-2">
                                            <small class="text-muted d-block">PAN Number</small>
                                            <strong><?= h($merchant->pan_no ?? 'Not provided') ?></strong>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-university fa-3x mb-3"></i>
                                    <p>No bank accounts found</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- KYC & Documents Tab -->
        <div class="tab-pane fade" id="kyc" role="tabpanel">
            <div class="content-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">KYC Documents</h6>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadDocumentModal">
                        <i class="fas fa-upload me-1"></i> Upload Document
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="documentsTable">
                            <thead>
                                <tr>
                                    <th>Document Type</th>
                                    <th>File</th>
                                    <th>Status</th>
                                    <th>Uploaded On</th>
                                    <th>Verified By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($merchant->merchant_documents)): ?>
                                    <?php foreach ($merchant->merchant_documents as $document): ?>
                                        <tr>
                                            <td><strong><?= h($documentTypes[$document->document_type] ?? $document->document_type) ?></strong></td>
                                            <td>
                                                <?php if (!empty($document->uploaded_file)): ?>
                                                    <?= $this->Html->link('View/Download', $this->Url->webroot($document->uploaded_file), ['target' => '_blank', 'class' => 'btn btn-sm btn-primary']) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No file</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $statusClass = 'status-pending';
                                                $statusText = 'Pending';
                                                if ($document->status === 'A') {
                                                    $statusClass = 'status-verified';
                                                    $statusText = 'Verified';
                                                } elseif ($document->status === 'D') {
                                                    $statusClass = 'status-rejected';
                                                    $statusText = 'Rejected';
                                                }
                                                ?>
                                                <span class="document-status <?= $statusClass ?>"><?= $statusText ?></span>
                                            </td>
                                            <td><?= h($document->created->format('d M Y')) ?></td>
                                            <td>
                                                <?php 
                                                if ($document->status === 'A') {
                                                    echo 'Admin';
                                                    echo '<br><small class="text-muted">' . h($document->created->format('d M Y H:i')) . '</small>';
                                                } else {
                                                    echo '-';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($document->status === 'A'): ?>
                                                    <button class="btn btn-sm btn-danger me-1" onclick="rejectDocument(<?= $document->id ?>)">Reject</button>
                                                <?php else: ?>
                                                    <button class="btn btn-sm btn-success me-1" onclick="verifyDocument(<?= $document->id ?>)">Verify</button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                                            <p>No documents uploaded yet</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Tab -->
        <div class="tab-pane fade" id="products" role="tabpanel">
            <div class="content-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Products Listed</h6>
                    <div class="d-flex gap-2">
                        <!-- <select class="form-select" style="width: auto;">
                            <option>Bulk Actions</option>
                            <option>Activate</option>
                            <option>Deactivate</option>
                            <option>Feature</option>
                        </select>
                        <button class="btn btn-primary">Apply</button> -->
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="productsTable">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Views</th>
                                    <th>Rating</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($merchantProducts)): ?>
                                    <?php foreach ($merchantProducts as $product): ?>
                                        <tr>
                                            <td><input type="checkbox" name="product_ids[]" value="<?= $product->id ?>"></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($product->product_images) && !empty($product->product_images[0]->image_url)): ?>
                                                        <img src="<?= $this->Url->webroot($product->product_images[0]->image_url) ?>" class="product-thumbnail me-2" alt="<?= h($product->name) ?>">
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?= h($product->name) ?></strong>
                                                        <br><small class="text-muted">SKU: <?= h($product->sku) ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php 
                                                $categories = [];
                                                foreach ($product->product_categories as $productCategory) {
                                                    $categories[] = $productCategory->category->name;
                                                }
                                                echo implode(', ', $categories);
                                                ?>
                                            </td>
                                            <td>₹<?= number_format($product->sales_price ?? 0, 2) ?></td>
                                            <td><?= $product->quantity ?? 0 ?></td>
                                            <td>
                                                <span class="badge bg-<?= $product->status === 'A' ? 'success' : 'secondary' ?>">
                                                    <?= $product->status === 'A' ? 'Active' : 'Inactive' ?>
                                                </span>
                                            </td>
                                            <td>0</td>
                                            <td>
                                                <div class="rating-stars">
                                                    <?php
                                                    $rating = 0;
                                                    for ($i = 1; $i <= 5; $i++) {
                                                        echo $i <= $rating ? '★' : '☆';
                                                    }
                                                    ?>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="#" class="btn btn-sm btn-primary">View</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center text-muted py-4">
                                            <i class="fas fa-box fa-2x mb-2"></i>
                                            <p>No products listed yet</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Tab -->
        <div class="tab-pane fade" id="orders" role="tabpanel">
            <div class="content-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Orders</h6>
                    <div class="d-flex gap-2">
                        <!-- <select class="form-select" style="width: auto;">
                            <option>All Status</option>
                            <option>Pending</option>
                            <option>Shipped</option>
                            <option>Delivered</option>
                            <option>Returned</option>
                            <option>Cancelled</option>
                        </select> -->
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="ordersTable">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Date</th>
                                    <th>Product</th>
                                    <th>Qty</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Payment</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($merchantOrders)): ?>
                                    <?php foreach ($merchantOrders as $merchantOrder): ?>
                                        <tr>
                                            <td><?= h($merchantOrder->order_number) ?></td>
                                            <td><?= h($merchantOrder->created->format('d M Y')) ?></td>
                                            <td>
                                                <?php 
                                                $productNames = [];
                                                foreach ($merchantOrder->order->order_items as $orderItem) {
                                                    $productNames[] = $orderItem->product->name;
                                                }
                                                echo implode(', ', array_slice($productNames, 0, 2));
                                                if (count($productNames) > 2) {
                                                    echo ' +' . (count($productNames) - 2) . ' more';
                                                }
                                                ?>
                                            </td>
                                            <td><?= count($merchantOrder->order->order_items) ?></td>
                                            <td>₹<?= number_format($merchantOrder->merchant_total, 2) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $merchantOrder->status === 'delivered' ? 'success' : ($merchantOrder->status === 'pending' ? 'warning' : 'info') ?>">
                                                    <?= ucfirst($merchantOrder->status) ?>
                                                </span>
                                            </td>
                                            <td>Paid</td>
                                            <td>
                                                <a href="#" class="btn btn-sm btn-primary">View</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center text-muted py-4">
                                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                            <p>No orders found</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Earnings & Commission Tab -->
        <div class="tab-pane fade" id="earnings" role="tabpanel">
            <div class="row">
                <div class="col-md-6">
                    <div class="content-card">
                        <div class="card-header">
                            <h6 class="mb-0">Commission Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="info-row">
                                <span class="info-label">Current Commission %</span>
                                <span class="info-value"><?= h($merchant->commission_rate ?? '0.00') ?>%</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Commission Rule Type</span>
                                <span class="info-value">Fixed</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="content-card">
                        <!-- <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Earnings Summary</h6>
                            <button class="btn btn-sm btn-primary">Download CSV</button>
                        </div> -->
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Period</th>
                                            <th>Gross Sales</th>
                                            <th>Commission</th>
                                            <th>Net Payable</th>
                                            <th>Settled?</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($merchantSettlements)): ?>
                                            <?php foreach ($merchantSettlements as $settlement): ?>
                                                <tr>
                                                    <td><?= h($settlement->created->format('M Y')) ?></td>
                                                    <td>₹<?= number_format($settlement->settlement_amount, 2) ?></td>
                                                    <td>₹<?= number_format($settlement->commission_deducted, 2) ?></td>
                                                    <td>₹<?= number_format($settlement->final_payout, 2) ?></td>
                                                    <td>
                                                        <span class="badge bg-<?= $settlement->settlement_status === 'completed' ? 'success' : ($settlement->settlement_status === 'pending' ? 'warning' : 'info') ?>">
                                                            <?= ucfirst($settlement->settlement_status) ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted py-3">
                                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                                    <p>No earnings data available</p>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ratings & Reviews Tab -->
        <div class="tab-pane fade" id="reviews" role="tabpanel">
            <div class="content-card">
                <div class="card-header">
                    <h6 class="mb-0">Ratings & Reviews</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="reviewsTable">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Rating</th>
                                    <th>Customer</th>
                                    <th>Review Snippet</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($merchantRatings)): ?>
                                    <?php foreach ($merchantRatings as $rating): ?>
                                        <tr>
                                            <td>Merchant Service</td>
                                            <td>
                                                <div class="rating-stars">
                                                    <?php
                                                    for ($i = 1; $i <= 5; $i++) {
                                                        echo $i <= $rating->rating ? '★' : '☆';
                                                    }
                                                    ?>
                                                    <span class="ms-2"><?= $rating->rating ?>/5</span>
                                                </div>
                                            </td>
                                            <td><?= h($rating->customer->user->first_name . ' ' . $rating->customer->user->last_name) ?></td>
                                            <td>
                                                <?php 
                                                $comment = $rating->comment;
                                                echo strlen($comment) > 50 ? substr($comment, 0, 50) . '...' : $comment;
                                                ?>
                                            </td>
                                            <td><?= h($rating->created->format('d M Y')) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center text-muted py-4">
                                            <i class="fas fa-star fa-2x mb-2"></i>
                                            <p>No reviews available</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- IMPORTANT: Modals must be outside of .container-fluid and .tab-content for proper stacking -->
<!-- Upload Document Modal -->
<div class="modal fade" id="uploadDocumentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= $this->Form->create(null, ['type' => 'file', 'url' => ['action' => 'uploadDocument', $merchant->id]]) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <?= $this->Form->control('document_type', [
                        'label' => 'Document Type', 
                        'class' => 'form-control select2',
                        'options' => $documentTypes,
                        'empty' => 'Select Document Type'
                    ]) ?>
                </div>
                <div class="mb-3">
                    <?= $this->Form->control('document_file', ['type' => 'file', 'label' => 'Choose File', 'class' => 'form-control-file']) ?>
                </div>
                <div class="mb-3">
                    <?= $this->Form->control('notes', ['label' => 'Notes', 'class' => 'form-control', 'type' => 'textarea']) ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary">Upload</button>
            </div>
            <?= $this->Form->end() ?>
        </div>
    </div>
</div>

<!-- Request Info Modal -->
<div class="modal fade" id="requestInfoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Request More Information</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= $this->Form->create(null, ['url' => ['action' => 'changeStatus', $merchant->id]]) ?>
            <div class="modal-body">
                <input type="hidden" name="status" value="Info Requested">
                <div class="mb-3">
                    <label class="form-label">Message to Merchant</label>
                    <textarea name="info_note" class="form-control" rows="4" placeholder="Enter the information you need from the merchant..." required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-warning">Send Request</button>
            </div>
            <?= $this->Form->end() ?>
        </div>
    </div>
</div>

<!-- Reject Document Modal -->
<div class="modal fade" id="rejectDocumentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= $this->Form->create(null, ['url' => ['action' => 'rejectDocument']]) ?>
            <div class="modal-body">
                <input type="hidden" name="document_id" id="rejectDocumentId">
                <input type="hidden" name="merchant_id" value="<?= $merchant->id ?>">
                <div class="mb-3">
                    <label class="form-label">Rejection Reason</label>
                    <textarea name="rejection_reason" class="form-control" rows="4" placeholder="Please provide a reason for rejecting this document..." required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-danger">Reject Document</button>
            </div>
            <?= $this->Form->end() ?>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2();
    
    // Select all checkbox
    $('#selectAll').change(function() {
        $('input[type="checkbox"]').prop('checked', $(this).prop('checked'));
    });
});

function changeStatus(status) {
    swal({
        title: '<?= __('Are you sure?') ?>',
        text: '<?= __('Are you sure you want to') ?> ' + status.toLowerCase() + ' <?= __('this merchant?') ?>',
        icon: status === 'Rejected' ? 'error' : 'warning',
        buttons: {
            cancel: {
                text: '<?= __('Cancel') ?>',
                value: false,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true,
            },
            confirm: {
                text: '<?= __('Yes, proceed!') ?>',
                value: true,
                visible: true,
                className: 'btn btn-success',
                closeModal: true
            }
        }
    }).then((willProceed) => {
        if (willProceed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?= $this->Url->build(['action' => 'changeStatus', $merchant->id]) ?>';
            
            const statusInput = document.createElement('input');
            statusInput.type = 'hidden';
            statusInput.name = 'status';
            statusInput.value = status;
            
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_csrfToken';
            csrfInput.value = '<?= $this->request->getAttribute('csrfToken') ?>';
            
            form.appendChild(statusInput);
            form.appendChild(csrfInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function verifyDocument(documentId) {
    swal({
        title: '<?= __('Verify Document') ?>',
        text: '<?= __('Are you sure you want to verify this document?') ?>',
        icon: 'info',
        buttons: {
            cancel: {
                text: '<?= __('Cancel') ?>',
                value: false,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true,
            },
            confirm: {
                text: '<?= __('Yes, verify!') ?>',
                value: true,
                visible: true,
                className: 'btn btn-success',
                closeModal: true
            }
        }
    }).then((willVerify) => {
        if (willVerify) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?= $this->Url->build(['action' => 'verifyDocument']) ?>';
            
            const documentInput = document.createElement('input');
            documentInput.type = 'hidden';
            documentInput.name = 'document_id';
            documentInput.value = documentId;
            
            const merchantInput = document.createElement('input');
            merchantInput.type = 'hidden';
            merchantInput.name = 'merchant_id';
            merchantInput.value = '<?= $merchant->id ?>';
            
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_csrfToken';
            csrfInput.value = '<?= $this->request->getAttribute('csrfToken') ?>';
            
            form.appendChild(documentInput);
            form.appendChild(merchantInput);
            form.appendChild(csrfInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function showRequestInfo() {
    $('#requestInfoModal').modal('show');
}

function rejectDocument(documentId) {
    document.getElementById('rejectDocumentId').value = documentId;
    $('#rejectDocumentModal').modal('show');
}
</script>
<?php $this->end(); ?> 