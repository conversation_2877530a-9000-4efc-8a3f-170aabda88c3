<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;
use Cake\I18n\FrozenTime;

/**
 * Offers Controller
 *
 * @property \App\Model\Table\OffersTable $Offers
 */
class OffersController extends AppController
{
    /**
     * Index method 
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected \App\Model\Table\ShowroomsTable $Showrooms;
    protected \App\Model\Table\CategoriesTable $Categories;
    protected \App\Model\Table\ProductsTable $Products;
    protected \App\Model\Table\CustomerGroupsTable $CustomerGroups;
    protected $Roles;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Categories = $this->fetchTable('Categories');
        $this->Products = $this->fetchTable('Products');
        $this->CustomerGroups = $this->fetchTable('CustomerGroups');
        $this->Roles = $this->fetchTable('Roles');
    }

    public function index()
    {
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');

        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');

        $query = $this->Offers->find()
            ->contain([
                'OfferShowrooms' => function ($q) {
                    return $q->where(['OfferShowrooms.status !=' => 'D'])
                        ->contain(['Showrooms']);
                },
                'OfferCustomerGroups' => function ($q) {
                    return $q->where(['OfferCustomerGroups.status !=' => 'D'])
                        ->contain(['CustomerGroups']);
                }
            ])
            ->where(['Offers.status !=' => 'D'])
            ->groupBy('Offers.Id')
            ->order(['Offers.offer_name' => 'ASC']);

        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $query->leftJoinWith('OfferShowrooms.Showrooms')
                    ->where([
                        'OR' => [
                            'Showrooms.showroom_manager' => $user->id,
                            // 'OfferShowrooms.id IS NULL'
                        ],
                        'OfferShowrooms.status' => 'A',
                        'redeem_mode !=' => 'Online'
                    ]);
            } elseif ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $query->leftJoinWith('OfferShowrooms.Showrooms')
                    ->where([
                        'OR' => [
                            'Showrooms.showroom_supervisor' => $user->id,
                            // 'OfferShowrooms.id IS NULL'
                        ],
                        'OfferShowrooms.status' => 'A',
                        'redeem_mode !=' => 'Online'
                    ]);
            }
        }

        $offers = $query->all();
        $redeemmodes = Configure::read('Constants.REDEEM_MODE');
        $offerType = Configure::read('Constants.OFFER_TYPE');
        $customerGroup = $this->CustomerGroups->find('list', [
            'keyField' => 'name',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($customerGroup)) {
            $customerGroup = ['' => 'No Customer Group available'];
        }
        // $customerGroup = Configure::read('Constants.CUSTOMER_GROUP');
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $this->set(compact('offers', 'redeemmodes', 'offerType', 'customerGroup', 'status', 'statusMap', 'dateFormat', 'timeFormat'));
    }

    /**
     * View method
     *
     * @param string|null $id Offer id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $offer = $this->Offers->find()
            ->contain([
                'OfferShowrooms' => function ($q) {
                    return $q->where(['OfferShowrooms.status !=' => 'D'])
                        ->contain(['Showrooms']);
                },
                'OfferCategories' => function ($q) {
                    return $q->where(['OfferCategories.status !=' => 'D'])
                        ->contain(['Categories']);
                },
                'OfferCustomerGroups' => function ($q) {
                    return $q->where(['OfferCustomerGroups.status !=' => 'D'])
                        ->contain(['CustomerGroups']);
                },
            ])
            ->where(['Offers.id' => $id])
            ->firstOrFail();

        $selectedShowrooms = [];
        foreach ($offer->offer_showrooms as $offerShowroom) {
            $selectedShowrooms[] = $offerShowroom->showroom->name;
        }
        $selectedShowrooms = implode(', ', $selectedShowrooms);

        $selectedCustomerGroups = [];
        foreach ($offer->offer_customer_groups as $offerCustomerGroup) {
            $selectedCustomerGroups[] = $offerCustomerGroup->customer_group->name;
        }
        $selectedCustomerGroups = implode(', ', $selectedCustomerGroups);

        $selectedCategories = [];
        foreach ($offer->offer_categories as $offerCategory) {
            $selectedCategories[] = $offerCategory->category->name;
        }
        $selectedCategories = implode(', ', $selectedCategories);

        $web_image = $this->Media->getCloudFrontURL($offer->web_image);
        $mobile_image = $this->Media->getCloudFrontURL($offer->mobile_image);
        $statuses = Configure::read('Constants.STATUS');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $redeemmodes = Configure::read('Constants.REDEEM_MODE');
        $offerType = Configure::read('Constants.OFFER_TYPE');
        $customerGroup = Configure::read('Constants.CUSTOMER_GROUP');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';
        $this->set(compact('offer', 'selectedShowrooms', 'web_image', 'mobile_image', 'statuses', 'dateFormat', 'timeFormat', 'redeemmodes', 'offerType', 'customerGroup', 'currencySymbol', 'selectedCategories', 'decimalSeparator', 'thousandSeparator', 'selectedCustomerGroups'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $offer = $this->Offers->newEmptyEntity();
        $showroomsQuery = $this->Showrooms->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A']); // Apply base filter
        
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
        
        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
        
        if (!empty($user) && $this->storeBased) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $showroomsQuery->where(['showroom_manager' => $user->id]);
            } elseif ($user->role_id == $ShowroomSupervisorRoleId) {
                $showroomsQuery->where(['showroom_supervisor' => $user->id]);
            }
        }
        
        $showrooms = $showroomsQuery->all()->toArray();
        
        if (empty($showrooms)) {
            $showrooms = ['' => 'No showrooms available'];
        }        

        $customerGroup = $this->CustomerGroups->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($customerGroup)) {
            $customerGroup = ['' => 'No Customer Group available'];
        }

        $redeemmodes = Configure::read('Constants.REDEEM_MODE');
        $offerType = Configure::read('Constants.OFFER_TYPE');
        // $customerGroup = Configure::read('Constants.CUSTOMER_GROUP');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        $this->set([
            'webImageSize' => Configure::read('Constants.COUPON_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.COUPON_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.COUPON_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.COUPON_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.COUPON_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.COUPON_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.COUPON_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.COUPON_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.COUPON_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.COUPON_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.COUPON_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.COUPON_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.COUPON_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.COUPON_MOB_IMAGE_MAX_HEIGHT'),
        ]);

        $categories = $this->Categories->find('all')->where(['status' => 'A'])
            ->order(['parent_id' => 'ASC', 'name' => 'ASC'])
            ->toArray();

        $formattedCategories = $this->formatCategories($categories);


        $this->set(compact('offer', 'redeemmodes', 'offerType', 'customerGroup', 'showrooms', 'currencySymbol', 'formattedCategories'));
        if ($this->request->is('post')) {
            $offerData = $this->request->getData();
            $existingOffer = $this->Offers->find()
                ->where([
                    'offer_code' => $offerData['offer_code'],
                    'status !=' => 'D'
                ])->first();

            if ($existingOffer) {
                $this->Flash->error(__('The offer code already exists. Please, use a different code.'));
            } else {
                $offerData = $this->request->getData();
                $allowedFormats = Configure::read('Constants.COUPON_WEB_IMAGE_TYPE');
                $maxWebImageSize = Configure::read('Constants.COUPON_WEB_IMAGE_SIZE') * 1024 * 1024;
                $maxMobileImageSize = Configure::read('Constants.COUPON_MOB_IMAGE_SIZE') * 1024 * 1024;

                if (!empty($offerData['web_image_file']) && $offerData['web_image_file']->getError() === UPLOAD_ERR_OK) {
                    $web_image = $offerData['web_image_file'];
                    $webImageName = trim($web_image->getClientFilename());
                    $webImageSize = $web_image->getSize();
                    $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                    if (!in_array($webImageExt, $allowedFormats)) {
                        return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                    }

                    if ($webImageSize > $maxWebImageSize) {
                        return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                    }

                    list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                    $minWidth = Configure::read('Constants.COUPON_WEB_IMAGE_MIN_WIDTH');
                    $maxWidth = Configure::read('Constants.COUPON_WEB_IMAGE_MAX_WIDTH');
                    $minHeight = Configure::read('Constants.COUPON_WEB_IMAGE_MIN_HEIGHT');
                    $maxHeight = Configure::read('Constants.COUPON_WEB_IMAGE_MAX_HEIGHT');

                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                    }

                    if (!empty($webImageName)) {
                        $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.COUPON_WEB_IMAGE');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                        $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                        if ($uploadResult === 'Success') {
                            $offerData['web_image'] = $uploadFolder . $webImageFile;
                        }
                    }
                }

                if (!empty($offerData['mobile_image_file']) && $offerData['mobile_image_file']->getError() === UPLOAD_ERR_OK) {
                    $mob_image = $offerData['mobile_image_file'];
                    $mobImageName = trim($mob_image->getClientFilename());
                    $mobImageSize = $mob_image->getSize();
                    $mobImageExt = strtolower(pathinfo($mobImageName, PATHINFO_EXTENSION));

                    if (!in_array($mobImageExt, $allowedFormats)) {
                        return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                    }

                    if ($mobImageSize > $maxMobileImageSize) {
                        return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileImageSize . ' MB.'));
                    }

                    list($width, $height) = getimagesize($mob_image->getStream()->getMetadata('uri'));
                    $minWidth = Configure::read('Constants.COUPON_MOB_IMAGE_MIN_WIDTH');
                    $maxWidth = Configure::read('Constants.COUPON_MOB_IMAGE_MAX_WIDTH');
                    $minHeight = Configure::read('Constants.COUPON_MOB_IMAGE_MIN_HEIGHT');
                    $maxHeight = Configure::read('Constants.COUPON_MOB_IMAGE_MAX_HEIGHT');

                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                    }

                    if (!empty($mobImageName)) {
                        $mobImageTmpName = $mob_image->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.COUPON_MOB_IMAGE');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $mobImageFile = pathinfo($mobImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobImageExt;
                        $uploadResult = $this->Media->upload($mobImageTmpName, $targetdir, $mobImageFile, $uploadFolder);
                        if ($uploadResult === 'Success') {
                            $offerData['mobile_image'] = $uploadFolder . $mobImageFile;
                        }
                    }
                }


                $offerData['free_shipping'] = !empty($offerData['free_shipping']) ? 1 : 0;
                $offer = $this->Offers->patchEntity($offer, $offerData);

                if ($this->Offers->save($offer)) {
                    $offerId = $offer->id;
                    $showroomIds = $this->request->getData('offer_showrooms._ids');
                    $categoryIds = $this->request->getData('offer_categories._ids');
                    $CustomerGroupIds = $this->request->getData('offer_customer_groups._ids');
                    $this->saveOfferCategories($offerId, $categoryIds, $formattedCategories);
                    $this->saveOfferShowrooms($offerId, $showroomIds);
                    $this->saveOfferCustomerGroups($offerId, $CustomerGroupIds);

                    $this->Flash->success(__('The offer has been saved.'));
                    return $this->redirect(['action' => 'index']);
                } else {
                    $this->Flash->error(__('The offer could not be saved. Please, try again.'));
                }
            }
        }
    }

    protected function formatCategories($categories, $parentId = null, $level = 0)
    {
        $result = [];
        foreach ($categories as $category) {
            if ($category->parent_id == $parentId) {
                $result[$category->id] = [
                    'text' => str_repeat('--', $level) . ' ' . $category->name,
                    'level' => $level
                ];
                $result += $this->formatCategories($categories, $category->id, $level + 1);
            }
        }
        return $result;
    }

    /**
     * Edit method
     *
     * @param string|null $id Offer id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $offer = $this->Offers->find()
            ->contain([
                'OfferShowrooms' => function ($q) {
                    return $q->where(['OfferShowrooms.status !=' => 'D']);
                },
                'OfferCategories' => function ($q) {
                    return $q->where(['OfferCategories.status !=' => 'D']);
                },
                'OfferCustomerGroups' => function ($q) {
                    return $q->where(['OfferCustomerGroups.status !=' => 'D']);
                },
            ])
            ->where(['Offers.id' => $id])
            ->firstOrFail();


        $redeemmodes = Configure::read('Constants.REDEEM_MODE');
        $offerType = Configure::read('Constants.OFFER_TYPE');
        // $customerGroup = Configure::read('Constants.CUSTOMER_GROUP');
        $statuses = Configure::read('Constants.STATUS');
        $showroomsQuery = $this->Showrooms->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A']); // Apply base filter
        
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
        
        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
        
        if (!empty($user) && $this->storeBased) {
            if ($user->role_id == $ShowroomManagerRoleId) {
                $showroomsQuery->where(['showroom_manager' => $user->id]);
            } elseif ($user->role_id == $ShowroomSupervisorRoleId) {
                $showroomsQuery->where(['showroom_supervisor' => $user->id]);
            }
        }
        
        $showrooms = $showroomsQuery->all()->toArray();
        
        if (empty($showrooms)) {
            $showrooms = ['' => 'No showrooms available'];
        }        
        $customerGroup = $this->CustomerGroups->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($customerGroup)) {
            $customerGroup = ['' => 'No Customer Group available'];
        }
        $this->set([
            'webImageSize' => Configure::read('Constants.COUPON_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.COUPON_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.COUPON_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.COUPON_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.COUPON_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.COUPON_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.COUPON_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.COUPON_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.COUPON_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.COUPON_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.COUPON_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.COUPON_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.COUPON_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.COUPON_MOB_IMAGE_MAX_HEIGHT'),
        ]);
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $selectedShowrooms = array_column($offer->offer_showrooms, 'showroom_id');
        $selectedCustomerGroups = array_column($offer->offer_customer_groups, 'customer_group_id');
        $web_image = $this->Media->getCloudFrontURL($offer->web_image);
        $mobile_image = $this->Media->getCloudFrontURL($offer->mobile_image);

        $categories = $this->Categories->find('all')->where(['status' => 'A'])
            ->order(['parent_id' => 'ASC', 'name' => 'ASC'])
            ->toArray();

        $formattedCategories = $this->formatCategories($categories);
        $selectedCategories = array_column($offer->offer_categories, 'category_id');

        $this->set(compact('offer', 'redeemmodes', 'offerType', 'customerGroup', 'showrooms', 'selectedShowrooms', 'web_image', 'mobile_image', 'statuses', 'currencySymbol', 'formattedCategories', 'selectedCategories', 'selectedCustomerGroups'));
        if ($this->request->is(['patch', 'post', 'put'])) {
            $offerData = $this->request->getData();
            $settingStatus = false;
            $associatedOrder = false;
            if ($offerData['status'] != $offer['status']) {
                $settingStatus = true;
            }

            if ($this->Offers->isOfferAssociatedWithOrders($id)) {
                $associatedOrder = true;
            }

            if (!($settingStatus) && $associatedOrder) {
                $this->Flash->error(__('This offer is associated with existing orders and cannot be edited.'));
                return $this->redirect(['action' => 'index']);
            }

            $existingOffer = $this->Offers->find()
                ->where([
                    'offer_code' => $offerData['offer_code'],
                    'status !=' => 'D',
                    'id !=' => $id
                ])->first();

            if ($existingOffer) {
                $this->Flash->error(__('The offer code already exists. Please, use a different code.'));
            } else {
                $allowedFormats = Configure::read('Constants.COUPON_WEB_IMAGE_TYPE');
                $maxWebImageSize = Configure::read('Constants.COUPON_WEB_IMAGE_SIZE') * 1024 * 1024;
                $maxMobileImageSize = Configure::read('Constants.COUPON_MOB_IMAGE_SIZE') * 1024 * 1024;

                if (!empty($offerData['web_image_file']) && $offerData['web_image_file']->getError() === UPLOAD_ERR_OK) {
                    $web_image = $offerData['web_image_file'];
                    $webImageName = trim($web_image->getClientFilename());
                    $webImageSize = $web_image->getSize();
                    $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                    if (!in_array($webImageExt, $allowedFormats)) {
                        return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                    }

                    if ($webImageSize > $maxWebImageSize) {
                        return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                    }

                    list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                    $minWidth = Configure::read('Constants.COUPON_WEB_IMAGE_MIN_WIDTH');
                    $maxWidth = Configure::read('Constants.COUPON_WEB_IMAGE_MAX_WIDTH');
                    $minHeight = Configure::read('Constants.COUPON_WEB_IMAGE_MIN_HEIGHT');
                    $maxHeight = Configure::read('Constants.COUPON_WEB_IMAGE_MAX_HEIGHT');

                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        return $this->Flash->error(__('Web image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                    }

                    if (!empty($webImageName)) {
                        $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.COUPON_WEB_IMAGE');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                        $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                        if ($uploadResult === 'Success') {
                            $offerData['web_image'] = $uploadFolder . $webImageFile;
                        }
                    }
                }

                if (!empty($offerData['mobile_image_file']) && $offerData['mobile_image_file']->getError() === UPLOAD_ERR_OK) {
                    $mob_image = $offerData['mobile_image_file'];
                    $mobImageName = trim($mob_image->getClientFilename());
                    $mobImageSize = $mob_image->getSize();
                    $mobImageExt = strtolower(pathinfo($mobImageName, PATHINFO_EXTENSION));

                    if (!in_array($mobImageExt, $allowedFormats)) {
                        return $this->Flash->error(__('Invalid file type for mobile image. Only ' . $allowedFormats . ' are allowed.'));
                    }

                    if ($mobImageSize > $maxMobileImageSize) {
                        return $this->Flash->error(__('Mobile image size exceeds the maximum allowed size of ' . $maxMobileImageSize . ' MB.'));
                    }

                    list($width, $height) = getimagesize($mob_image->getStream()->getMetadata('uri'));
                    $minWidth = Configure::read('Constants.COUPON_MOB_IMAGE_MIN_WIDTH');
                    $maxWidth = Configure::read('Constants.COUPON_MOB_IMAGE_MAX_WIDTH');
                    $minHeight = Configure::read('Constants.COUPON_MOB_IMAGE_MIN_HEIGHT');
                    $maxHeight = Configure::read('Constants.COUPON_MOB_IMAGE_MAX_HEIGHT');

                    if ($width < $minWidth || $width > $maxWidth || $height < $minHeight || $height > $maxHeight) {
                        return $this->Flash->error(__('Mobile image dimensions must be between ' . $minWidth . 'x' . $minHeight . ' and ' . $maxWidth . 'x' . $maxHeight . ' pixels.'));
                    }

                    if (!empty($mobImageName)) {
                        $mobImageTmpName = $mob_image->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $filePath = Configure::read('Constants.COUPON_MOB_IMAGE');
                        $folderPath = $uploadFolder . $filePath;
                        $targetdir = WWW_ROOT . $folderPath;
                        $mobImageFile = pathinfo($mobImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $mobImageExt;
                        $uploadResult = $this->Media->upload($mobImageTmpName, $targetdir, $mobImageFile, $uploadFolder);
                        if ($uploadResult === 'Success') {
                            $offerData['mobile_image'] = $uploadFolder . $mobImageFile;
                        }
                    }
                }


                $offerData['free_shipping'] = !empty($offerData['free_shipping']) ? 1 : 0;
                if ($settingStatus && $associatedOrder) {
                    $offer['status'] = $offerData['status'];
                } else {
                    $offer = $this->Offers->patchEntity($offer, $offerData);
                }
                if ($this->Offers->save($offer)) {
                    $offerId = $offer->id;
                    $categoryIds = $this->request->getData('offer_categories._ids');
                    $this->saveOfferCategories($offerId, $categoryIds, $formattedCategories);

                    $showroomIds = $this->request->getData('offer_showrooms._ids');
                    $this->saveOfferShowrooms($offerId, $showroomIds);

                    $CustomerGroupIds = $this->request->getData('offer_customer_groups._ids');
                    $this->saveOfferCustomerGroups($offerId, $CustomerGroupIds);

                    $this->Flash->success(__('The offer has been saved.'));

                    return $this->redirect(['action' => 'index']);
                } else {
                    $this->Flash->error(__('The offer could not be saved. Please, try again.'));
                }
            }
        }
    }


    /**
     * Delete method
     *
     * @param string|null $id Offer id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $offer = $this->Offers->get($id);
        $response = ['success' => false, 'message' => 'The Offer could not be deleted. Please, try again.'];
        if ($offer) {
            if ($this->Offers->delete($offer)) {
                $response = ['success' => true, 'message' => 'The Offer has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The Offer could not be deleted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The Offer does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    /**
     * Function to generate a unique code 
     *
     * @return json response
     */
    public function getUniqueCode()
    {
        $Coupon_code_length = Configure::read('Constants.COUPON_CODE_LENGTH');
        do {
            $Coupon_code = $this->generateRandomCode($Coupon_code_length);
        } while ($this->isCouponCodeExists($Coupon_code));
        $response = [
            'status' => 'success',
            'message' => __('Coupon not found.'),
            'code' => $Coupon_code
        ];
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    /**
     * Function to generate a random code with given length
     *
     * @param int $length
     * @return string
     */
    private function generateRandomCode($length)
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomCode = '';

        for ($i = 0; $i < $length; $i++) {
            $randomCode .= $characters[rand(0, $charactersLength - 1)];
        }

        return $randomCode;
    }

    /**
     * Function to check if the coupon code already exists in the Offers table
     *
     * @param string $code
     * @return bool
     */
    private function isCouponCodeExists($code)
    {
        return $this->Offers->exists(['offer_code' => $code]);
    }

    /**
     * Save the offer_showrooms association.
     *
     * @param int $offerId The offer ID.
     * @param array $showroomIds Array of showroom IDs.
     * @return void
     */
    protected function saveOfferShowrooms($offerId, $showroomIds)
    {
        $offerShowroomsTable = $this->Offers->OfferShowrooms;

        $offerShowroomsTable->updateAll(
            ['status' => 'D'],
            ['offer_id' => $offerId]
        );
        $showroomIds = is_array($showroomIds) ? array_filter($showroomIds) : [];
        if (!empty($showroomIds)) {
            $data = [];
            foreach ($showroomIds as $showroomId) {
                $data[] = [
                    'offer_id' => $offerId,
                    'showroom_id' => $showroomId,
                    'status' => 'A'
                ];
            }

            $entities = $offerShowroomsTable->newEntities($data);
            if (!$offerShowroomsTable->saveMany($entities)) {
                $this->Flash->error(__('The showroom associations could not be saved. Please, try again.'));
            }
        }
    }

    protected function saveOfferCustomerGroups($offerId, $CustomerGroupIds)
    {
        $OfferCustomerGroupsTable = $this->Offers->OfferCustomerGroups;

        $OfferCustomerGroupsTable->updateAll(
            ['status' => 'D'],
            ['offer_id' => $offerId]
        );
        $CustomerGroupIds = is_array($CustomerGroupIds) ? array_filter($CustomerGroupIds) : [];
        if (!empty($CustomerGroupIds)) {
            $data = [];
            foreach ($CustomerGroupIds as $customerGroupId) {
                $data[] = [
                    'offer_id' => $offerId,
                    'customer_group_id' => $customerGroupId,
                    'status' => 'A'
                ];
            }

            $entities = $OfferCustomerGroupsTable->newEntities($data);
            if (!$OfferCustomerGroupsTable->saveMany($entities)) {
                $this->Flash->error(__('The customer group associations could not be saved. Please, try again.'));
            }
        }
    }

    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $imageType = $this->request->getData('image_type');
        $offerId = $this->request->getData('image_id');

        if (!$imageType || !$offerId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $offer = $this->Offers->get($offerId);

        if (!$offer) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Offer not found']));
        }

        if ($imageType === 'web') {
            $uploadFolder = Configure::read('Constants.COUPON_WEB_IMAGE');
        } else if ($imageType === 'mobile') {
            $uploadFolder = Configure::read('Constants.COUPON_MOB_IMAGE');
        }

        $imageField = $imageType === 'web' ? 'web_image' : 'mobile_image';
        $existingImagePath = $offer->{$imageField};

        if ($existingImagePath) {
            $filePath = WWW_ROOT . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $offer->{$imageField} = null;
            if ($this->Offers->save($offer)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update offer']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }

    public function getCouponClaim()
    {
        $this->request->allowMethod(['post']);

        $couponCode      = $this->request->getData('coupon_code');
        // $dateString = $this->request->getData('order_date'); // "2025-02-23T18:14"
        // $orderDate = FrozenTime::createFromFormat("yyyy-MM-dd'T'HH:mm", $dateString);

        // if (!$orderDate) {
        //     $errors = FrozenTime::getLastErrors();
        //     throw new \Exception('Invalid order date format: ' . json_encode($errors));
        // }

        // Convert to SQL datetime format:
        $sqlDate = date('Y-m-d H:i:s');
        $subTotal        = $this->request->getData('subTotal');
        $showroomId      = $this->request->getData('showroom_id');
        $productIds      = $this->request->getData('product_ids');
        $customerGroups  = $this->request->getData('customer_groups');
        $type  = $this->request->getData('type');

        // Get logged-in user
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);

            // If role is Call Center Agent or Supervisor → force Online
            if (in_array(strtolower($role->name), ['call center agent', 'call center supervisor'])) {
                $type = 'Online';
            }
        }

        if (!is_array($customerGroups)) {

            $decoded = json_decode($customerGroups, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $customer_groups = $decoded;
            } else {

                $customer_groups = [$customerGroups];
            }
        }

        $offer = $this->Offers->find()
            ->where(['offer_code' => $couponCode, 'redeem_mode IN' => [$type, 'Both']])
            ->contain([
                'OfferShowrooms' => function ($q) {
                    return $q->where(['OfferShowrooms.status' => 'A']);
                },
                'OfferCategories' => function ($q) {
                    return $q->where(['OfferCategories.status' => 'A']);
                },
                'OfferCustomerGroups' => function ($q) {
                    return $q->where(['OfferCustomerGroups.status' => 'A']);
                }
            ])
            ->first();

        if (!$offer) {
            return $this->jsonResponse(false, 'Invalid coupon code or it doesnot apply to stores.');
        }
        if ($offer->status !== 'A') {
            return $this->jsonResponse(false, 'This coupon is not active.');
        }
        $offer_start_date = $offer->offer_start_date->format('Y-m-d H:i:s');
        if ($offer_start_date > $sqlDate) {
            return $this->jsonResponse(false, 'This coupon is not valid yet.');
        }
       
        $offer_end_date = $offer->offer_end_date->format('Y-m-d H:i:s');
        if (!empty($offer->offer_end_date) && $offer_end_date < $sqlDate) {
            return $this->jsonResponse(false, 'This coupon has expired.');
        }
        if (!empty($offer->offer_showrooms)) {
            $validShowroom = false;
            foreach ($offer->offer_showrooms as $offerShowroom) {
                if ($offerShowroom->showroom_id == $showroomId) {
                    $validShowroom = true;
                    break;
                }
            }
            if (!$validShowroom) {
                return $this->jsonResponse(false, 'This coupon is not valid for the selected showroom.');
            }
        }

        if ($subTotal < $offer->min_cart_value) {
            return $this->jsonResponse(false, 'Minimum cart value of ₹' . $offer->min_cart_value . ' is required.');
        }

        if (!empty($customer_groups) && !empty($offer->offer_customer_groups)) {
            $validCustomerGroup = false;
            foreach ($offer->offer_customer_groups as $offerCustomerGroup) {
                $this->log(json_encode($offerCustomerGroup->customer_group_id), 'debug');
                $this->log(json_encode($customer_groups), 'debug');
                if (in_array($offerCustomerGroup->customer_group_id, $customer_groups)) {

                    $validCustomerGroup = true;
                    break;
                }
            }
            if (!$validCustomerGroup) {
                return $this->jsonResponse(false, 'This coupon is not valid for your customer group.');
            }
        }

        $validProducts = [];
        if (!empty($productIds) && !empty($offer->offer_categories)) {
            foreach ($productIds as $productId) {
                foreach ($offer->offer_categories as $offerCategory) {
                    if ($this->Products->ProductCategories->exists([
                        'ProductCategories.product_id'  => $productId,
                        'ProductCategories.category_id' => $offerCategory->category_id,
                    ])) {
                        $validProducts[] = $productId;
                    }
                }
            }

            if (empty($validProducts)) {
                return $this->jsonResponse(false, 'Selected products do not match the coupon categories.');
            }
        }

        return $this->jsonResponse(true, 'Coupon applied successfully!', [
            'id'            => $offer->id,
            'offer_code'  => $offer->offer_code,
            'offer_amount'  => $offer->discount,
            'offer_type'    => $offer->offer_type,
            'min_cart_value' => $offer->min_cart_value,
            'cat_products'  => $validProducts,
            'max_allowed'   => $offer->max_amt_per_disc_value,
            'is_free_shipping'   => $offer->free_shipping
        ]);
    }

    private function jsonResponse($success, $message, $data = [])
    {
        $response = ['success' => $success, 'message' => $message] + $data;
        $this->response = $this->response->withType('application/json');
        return $this->response->withStringBody(json_encode($response));
    }


    protected function saveOfferCategories($offerId, $categoryIds, $formattedCategories)
    {
        $offerCategoriesTable = $this->Offers->OfferCategories;

        $offerCategoriesTable->updateAll(
            ['status' => 'D'],
            ['offer_id' => $offerId]
        );
        $categoryIds = is_array($categoryIds) ? array_filter($categoryIds) : [];

        if (!empty($categoryIds)) {

            $data = [];
            foreach ($categoryIds as $categoryId) {
                $categoryLevel = $formattedCategories[$categoryId]['level'] ?? 0;

                $data[] = [
                    'offer_id' => $offerId,
                    'category_id' => $categoryId,
                    'level' => $categoryLevel,
                    'status' => 'A'
                ];
            }

            $entities = $offerCategoriesTable->newEntities($data);
            if (!$offerCategoriesTable->saveMany($entities)) {
                $this->Flash->error(__('The category associations could not be saved. Please, try again.'));
            }
        }
    }
}
