<?php

namespace App\Controller\Component;

use Cake\Controller\Component;

use Cake\Database\Expression\QueryExpression;
use Cake\ORM\TableRegistry;
use Cake\Mailer\Mailer;
use Cake\Core\Configure;
use Cake\Utility\Text;
use Cake\Event\Event;
use Cake\Utility\Security;
use Cake\Auth\DefaultPasswordHasher;
//use QRcode;
use Cake\Http\Session;
use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Twilio\Rest\Client;
use SendGrid\Mail\Mail;
use Cake\View\View;
use Cake\Log\Log;
use Picqer\Barcode\BarcodeGeneratorPNG;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevel;



class StockComponent extends Component
{

	protected $StockMovements;
	protected $StockMovementItems;
	protected $ProductStocks;
	protected $Orders;
	protected $BL;
	protected $BLItems;
	protected $Products;
	protected $ProductItems;


	public function initialize($config): void
    {
        parent::initialize($config);
        
        $this->StockMovements = TableRegistry::getTableLocator()->get('StockMovements');
        $this->StockMovementItems = TableRegistry::getTableLocator()->get('StockMovementItems');
        $this->ProductStocks = TableRegistry::getTableLocator()->get('ProductStocks');
        $this->Orders = TableRegistry::getTableLocator()->get('Orders');
        $this->BL = TableRegistry::getTableLocator()->get('BL');
        $this->BLItems = TableRegistry::getTableLocator()->get('BLItem');
        $this->Products = TableRegistry::getTableLocator()->get('Products');
		$this->ProductItems = TableRegistry::getTableLocator()->get('ProductItems');

        //$this->Products = $this->getController()->loadModel('Products');
    }


	/*
		* Add Incoming/Outgoing Stock to/from Warehouse/Showroom
		* reference_type ('stock_request', 'purchase', 'return')
		* referenceID (stock_request_id, order_id, stock_return_id)
		* movement_type (Incoming, Outgoing)
		* $ws (Warehouse or Showroom), $ws_id (warehouse_id or showroom_id)
	*/

	//Add Incoming Stock to Warehouse	
    public function addWarehouseInStock ($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $stock_movement_id,$stock_movement_item_id, $bl, $blItemId, $driver_id){
		
    	$ws = 'Warehouse';
    	$movement_type = 'Incoming';
		if($stock_movement_id == null)
		{
    		$addStockID = $this->StockMovements->addStock($ws, $warehouse_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id,$bl->id);
		}
		else
		{
			$addStockID = $stock_movement_id;
		}
		Log::debug("add stock id ".$addStockID);
    	if($addStockID) {

    		foreach ($stock_items as $key => $value) {
    			$addData = array();
				if($stock_movement_item_id == null)
				{
    				$addStockItemID = $this->StockMovementItems->addStockItems($addStockID, $value);
				}
				else
				{
					$addStockItemID = $stock_movement_item_id;
				}
					$totalItems = $this->StockMovementItems->find()
				->where(['stock_movement_id' => $addStockID])
				->count();

				$completedItems = $this->StockMovementItems->find()
				->where(['stock_movement_id' => $addStockID, 'status' => 'Completed'])
				->count();
				

				if ($totalItems > 0 && $totalItems == $completedItems) {
				// Update StockMovement to Completed
					$this->StockMovements->updateAll(
					[
						'verify_status' => 'Completed',
						'verify_time' => date('Y-m-d H:i:s')
					],
					['id' => $addStockID]
					);
				}
					// Base condition with product_id
					$conditions = [
						'warehouse_id' => $warehouse_id,
						'product_id' => $value['product_id']
					];

					// Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
					if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
						$conditions['product_variant_id'] = $value['product_variant_id'];
					}

					// Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
					if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
						$conditions['product_attribute_id'] = $value['product_attribute_id'];
					}

		        // Find the existing product stock
					$stockExist = $this->ProductStocks->find()
						->where($conditions)
						->first();
	
					if($stockExist){
						$productStockID = $stockExist->id;
						$existQuantity  = $stockExist->quantity;
						$increaseQuantity = $existQuantity + $value['quantity'];
						$addData['quantity'] = $increaseQuantity;
						$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $addData);

					} else {
						$addProductStock = $this->ProductStocks->addProductStock($ws, $warehouse_id, $value);
					}
				
				$item = $this->generateBarcode($value['product_id'],$value['product_variant_id'],$value['product_attribute_id'], $value['quantity'], $addStockItemID,$bl->bl_no, $addStockID,$blItemId, $value['defective_quantity'], $reference_type,$referenceID);
				Log::debug("Item.... ");
				Log::debug(print_r($item, true));
			    			
    		
		}
			
			return [
				'stock_movement_id' => $addStockID,
				'stock_movement_item_id' => $addStockItemID,
				'first_product_count' => $item['first_product_count']
			];
    		// return $addStockID;
    	} else {
    		return false;
    	}
    }

	//Generate Barcode for each product stock item
	public function generateBarcode($product_id,$product_variant_id,$product_attribute_id, $qty, $addStockItemID, $bl_no, $addStockID,$blItemId, $defective_quantity,$reference_type,$referenceID)
	{
		Log::debug("Generating barcode...");

		Log::debug("Product ID: " . $product_id);
		try {
		$product = $this->Products->get($product_id);

		$generator = new \Picqer\Barcode\BarcodeGeneratorPNG();
		$savePath = 'barcodes/';
		if (!file_exists($savePath)) {
			mkdir($savePath, 0755, true);
		}
		$currenttime = date('dmYHis');


		$lastitemadded = $this->ProductItems->getLastBarcodeForItem($addStockItemID,$product_id);
		

				$qtyReceived = $qty - $defective_quantity;

		$startfrom = 0;
		if(!empty($lastitemadded))
		{
			$lastbarcoderow = end($lastitemadded);
			$lastbarcodeid = $lastbarcoderow['first_product_count'];
			$startfrom = $lastbarcodeid +1;
		}
	
		for ($i = $startfrom; $i < $qtyReceived+$startfrom; $i++) {
				  $code = $product_id.'-'.$referenceID.'-'.$blItemId.'-'.$bl_no.'-'.$addStockID.'-'.$addStockItemID.'-'.$product_variant_id.'-'.$product_attribute_id.'-'.$product->sku.'-'.$currenttime."-".($i + 1);

    $filename = $savePath . $code . '.png';
$qrCode = new \Endroid\QrCode\QrCode($code);



        $writer = new PngWriter();
        $result = $writer->write(qrCode: $qrCode);
// Label (text below QR code)
// $label = new Label(
//     text: $code,
//     font: new NotoSans(12),               // font size
//     alignment: new LabelAlignmentCenter() // centered below QR
// );

// Writer
// $writer = new PngWriter();

// Generate result
// $result = $writer->write($qrCode, null, $label);
Log::debug($filename);
// Save file
$result->saveToFile($filename);
// OR output directly to browser
// header('Content-Type: '.$result->getMimeType());
// echo $result->getString();


// Save file in CakePHP webroot
// $result->saveToFile($filename);

        // file_put_contents($filename, $result->getString());
    

    $productItemdata = $this->ProductItems->newEmptyEntity();
    $productItemdata->barcode = $filename; 	
	$productItemdata->product_id = $product_id;
	$productItemdata->product_variant_id = $product_variant_id;
	$productItemdata->product_attribute_id = $product_attribute_id;
	$productItemdata->stock_movement_item_id = $addStockItemID;
	$productItemdata->unique_id = $code;
    $this->ProductItems->save($productItemdata); 
		}
			

		return $productItemdata;
		return true;
		}
	catch (\Exception $e) {
		Log::debug('Error generating barcode: ' . $e->getMessage());
		echo 'Error: ' . $e->getMessage();
	}

}
    public function addWarehouseInStockWithoutUpdatingProductStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id) {
	    $ws = 'Warehouse';
	    $movement_type = 'Incoming';
	    
	    $addStockID = $this->StockMovements->addStock($ws, $warehouse_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
	    
	    if ($addStockID) {
	        foreach ($stock_items as $value) {
	            // Add stock movement items
	            $this->StockMovementItems->addStockItems($addStockID, $value);
	        }
	        return $addStockID;
	    }
	    return false;
	}

	public function addWarehouseOutStockWithoutUpdatingProductStock	($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id) {
	    $ws = 'Warehouse';
	    $movement_type = 'Outgoing';

	    $addStockID = $this->StockMovements->addStock($ws, $warehouse_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
	    
	    if ($addStockID) {
	        foreach ($stock_items as $value) {
	            // Add stock movement items
	            $this->StockMovementItems->addStockItems($addStockID, $value);
	        }
	        return true;
	    }
	    return false;
	}

    //Add Incoming Stock to Showroom
    public function addShowroomInStock ($showroom_id, $movement_date, $reference_type, $referenceID, $stock_items, $image, $driver_id){

    	$ws = 'Showroom';
    	$movement_type = 'Incoming';
    	$addStockID = $this->StockMovements->addStock($ws, $showroom_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
    	if($addStockID) {
    		foreach ($stock_items as $key => $value) {

    			$addData = array();
    			$addStockItemID = $this->StockMovementItems->addStockItems($addStockID, $value);
    			
	            //find the existing product stock
	    		// $stockExist = $this->ProductStocks->find()
			    //     ->where(['showroom_id' => $showroom_id, 'product_id' => $value['product_id']])
			    //     ->first();

    			// Base condition with product_id
		        $conditions = [
		            'showroom_id' => $showroom_id,
		            'product_id' => $value['product_id']
		        ];

		        // Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
				    $conditions['product_variant_id'] = $value['product_variant_id'];
				}

				// Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
				    $conditions['product_attribute_id'] = $value['product_attribute_id'];
				}
				
		        // Find the existing product stock
		        $stockExist = $this->ProductStocks->find()
		            ->where($conditions)
		            ->first();

    			if($stockExist){
    				$productStockID = $stockExist->id;
    				$existQuantity  = $stockExist->quantity;
    				$increaseQuantity = $existQuantity + $value['quantity'];
    				$addData['quantity'] = $increaseQuantity;
    				$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $addData);
    			} else {
    				$addProductStock = $this->ProductStocks->addProductStock($ws, $showroom_id, $value);
    			}    			    			
    		}
    		return true;
    	} else {
    		return false;
    	}
    }


    //Add Outgoing Stock from Warehouse
    public function addWarehouseOutStock ($warehouse_id, $movement_date, $reference_type, $referenceID, $stock_items, $image, $driver_id, $stockMovementId, $stockMovementItemId){

		 

    	$ws = 'Warehouse';
		if($stockMovementId == null)
		{
			$movement_type = 'Outgoing';
			$addStockID = $this->StockMovements->addStock($ws, $warehouse_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);
		}else{
			$addStockID = $stockMovementId;
		}

    	if($addStockID) {
    		foreach ($stock_items as $key => $value) {
    			$reduceData = array();
				// if($stockMovementItemId == null)
				// {
    				$addStockItemID = $this->StockMovementItems->addStockItems($addStockID, $value, $stockMovementItemId);
				// }
				// else
				// {
				// 	$addStockItemID = $stockMovementItemId;
				// }
    			// Base condition with product_id
		        $conditions = [
		            'warehouse_id' => $warehouse_id,
		            'product_id' => $value['product_id']
		        ];

		        // Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
				    $conditions['product_variant_id'] = $value['product_variant_id'];
				}

				// Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
				    $conditions['product_attribute_id'] = $value['product_attribute_id'];
				}

		        // Find the existing product stock
		        $stockExist = $this->ProductStocks->find()
		            ->where($conditions)
		            ->first();

    			if($stockExist){
    				$productStockID = $stockExist->id;
    				$existQuantity  = $stockExist->quantity;
    				$decreaseQuantity = $existQuantity - $value['quantity'];
    				// $reduceData['quantity'] = $decreaseQuantity;

    				// Prepare update data
				    $reduceData = [
				        'quantity' => $decreaseQuantity,
				        'reduce_reserved_stock' => $value['quantity']  // Deduct from reserved stock
				    ];

    				$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $reduceData);
    			} else {
    				$addProductStock = $this->ProductStocks->addProductStock($ws, $warehouse_id, $value);
    			}    			    			
    		}
    		return true;
    	} else {
    		return false;
    	}
    }

    
    //Add Outgoing Stock from Showroom
    public function addShowroomOutStock ($showroom_id, $movement_date, $reference_type, $referenceID, $stock_items, $image, $driver_id){

    	$ws = 'Showroom';
    	$movement_type = 'Outgoing';
    	$addStockID = $this->StockMovements->addStock($ws, $showroom_id, $movement_type, $movement_date, $reference_type, $referenceID, $image, $driver_id);

    	if($addStockID) {
    		foreach ($stock_items as $key => $value) {

    			$reduceData = array();
    			$addStockItemID = $this->StockMovementItems->addStockItems($addStockID, $value);
    			
	            //find the existing product stock
	    		// $stockExist = $this->ProductStocks->find()
			    //     ->where(['showroom_id' => $showroom_id, 'product_id' => $value['product_id']])
			    //     ->first();

    			// Base condition with product_id
		        $conditions = [
		            'showroom_id' => $showroom_id,
		            'product_id' => $value['product_id']
		        ];

		        // Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
				    $conditions['product_variant_id'] = $value['product_variant_id'];
				}

				// Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
				if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
				    $conditions['product_attribute_id'] = $value['product_attribute_id'];
				}
				
		        // Find the existing product stock
		        $stockExist = $this->ProductStocks->find()
		            ->where($conditions)
		            ->first();

    			if($stockExist){
    				$productStockID = $stockExist->id;
    				$existQuantity  = $stockExist->quantity;
    				$decreaseQuantity = $existQuantity - $value['quantity'];
    				// $reduceData['quantity'] = $decreaseQuantity;

    				// Prepare update data
				    $reduceData = [
				        'quantity' => $decreaseQuantity,
				        'reduce_reserved_stock' => $value['quantity']  // Deduct from reserved stock
				    ];
    					
    				$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $reduceData);
    			} else {
    				$addProductStock = $this->ProductStocks->addProductStock($ws, $showroom_id, $value);
    			}    			    			
    		}
    		return true;
    	} else {
    		return false;
    	}
    }

    //S
    public function getProductStockOLD($showrooms, $items)
    { 
        foreach ($items as $item) {
            $productId = $item['product_id'];
            $variantId = $item['variant_id'] ?? null;
            $attributeId = $item['attribute_id'] ?? null;
            $rowIndex = $item['row_index'];
            $conditions = [
                'product_id' => $productId,
                'product_variant_id IS' => $variantId,
                'product_attribute_id IS' => $attributeId,
            ];

            if (!empty($showrooms)) {
                $conditions['showroom_id IN'] = $showrooms;
            }

            $avlQuantity = $this->ProductStocks->find()
                ->select([
                    'available_quantity' => (new QueryExpression('SUM(quantity) - SUM(reserved_stock)'))
                ])
                ->where($conditions)
                ->first();
            $availableQuantity = $avlQuantity ? max(0, (int) $avlQuantity->available_quantity) : 0;

            $resultData[] = [
                'row_index' => $rowIndex,
                'available_stock' => $availableQuantity ?? 0
            ];
        } 
        return $resultData;           
    }

    public function getProductStock($showrooms, $productId, $variantId, $attributeId)
    { 
        $conditions = [
            'product_id' => $productId,
            'product_variant_id IS' => $variantId,
            'product_attribute_id IS' => $attributeId,
        ];

        if (!empty($showrooms)) {
            $conditions['showroom_id IN'] = $showrooms;
        }

        $avlQuantity = $this->ProductStocks->find()
            ->select([
                'available_quantity' => (new QueryExpression('SUM(quantity) - SUM(reserved_stock)'))
            ])
            ->where($conditions)
            ->first();
        $availableQuantity = $avlQuantity ? max(0, (int) $avlQuantity->available_quantity) : 0;        
            
        $available_stock = $availableQuantity ?? 0;
        
        return $available_stock;           
    }

    //S
    public function reserveStock($showrooms, $productId, $productVarId, $attributeId, /*$prevquantity,*/ $current_quantity)
    {        

        $conditions = [];

        if (!empty($showrooms)) {
            $conditions['showroom_id IN'] = $showrooms;
        }

        if (!empty($productId)) {
            $conditions['product_id'] = $productId;
        }

        if (!empty($productVarId)) {
            $conditions['product_variant_id'] = $productVarId;
        }

        if (!empty($attributeId)) {
            $conditions['product_attribute_id'] = $attributeId;
        }

        $avlQuantity = $this->ProductStocks->find()
            ->where($conditions)
            ->first();
        $currentQuantity = (int) $current_quantity;
        if ($avlQuantity) {

            $reservedStocks = (int) $avlQuantity->reserved_stock;
            //$prevQuantity = (int) $prevquantity;


            $newReservedStocks = $reservedStocks /*- $prevQuantity*/ + $currentQuantity;

            if ($newReservedStocks < 0) {
                $newReservedStocks = 0;
                $avlQuantity->reserved_stock = $newReservedStocks;
                $this->ProductStocks->save($avlQuantity);
                $response = [
                    'status' => 'error',
                    'prevquantity' => $currentQuantity
                ];
            } else {
                $avlQuantity->reserved_stock = $newReservedStocks;
                $this->ProductStocks->save($avlQuantity);
                $response = [
                    'status' => 'success',
                    'prevquantity' => $currentQuantity
                ];
            }
        } else {
            $response = [
                'status' => 'error',
                'prevquantity' => $currentQuantity
            ];
        }       
        return $response;
    }

    //S
    public function outgoingStock($orderId)
    {
        $order = $this->Orders->find()
            ->contain(['OrderItems'])
            ->where(['Orders.id' => $orderId, 'Orders.delivery_mode' => 'pickup'])
            ->first();

        if ($order && !empty($order->order_items)) {

            foreach ($order->order_items as $item) {
                $conditions = [
                    'showroom_id' => $order->showroom_id,
                    'product_id' => $item->product_id,
                    'product_variant_id IS' => $item->product_variant_id,
                    'product_attribute_id IS' => $item->product_attribute_id,
                ];

                $stock = $this->ProductStocks->find()
                    ->where($conditions)
                    ->first();

                if ($stock) {
                    $stock->quantity -= $item->quantity;
                    $stock->reserved_stock -= $item->quantity;

                    if ($this->ProductStocks->save($stock)) {
                        // Now update order status
                        $order->status = 'Delivered';
                        if (!$this->Orders->save($order)) {
                            $response = 'Stock updated, but failed to update order status.';
                            return $response;
                        }
                    } else {
                        $response = 'Failed to update stock.';
                        return $response;
                    }
                }
            }

            $response = 'Stock marked as outgoing.';
        } else {
            $response = 'Order not found.';
        }
        return $response;
    }

    //S
    public function clearStock($showrooms, $productId, $productVarId, $attributeId, $current_quantity)
    { 
        $conditions = [];

        if (!empty($showrooms)) {
            $conditions['showroom_id IN'] = $showrooms;
        }

        if (!empty($productId)) {
            $conditions['product_id'] = $productId;
        }

        if (!empty($productVarId)) {
            $conditions['product_variant_id'] = $productVarId;
        }

        if (!empty($attributeId)) {
            $conditions['product_attribute_id'] = $attributeId;
        }

        $avlQuantity = $this->ProductStocks->find()
            ->where($conditions)
            ->first();
        $currentQuantity = (int) $current_quantity;
        if ($avlQuantity) {

            $reservedStocks = (int) $avlQuantity->reserved_stock;
            $currentQuantity = (int) $currentQuantity;


            $newReservedStocks = $reservedStocks - $currentQuantity;

            if ($newReservedStocks < 0) {
                $newReservedStocks = 0;
                $avlQuantity->reserved_stock = $newReservedStocks;
                $this->ProductStocks->save($avlQuantity);
                $response = [
                    'status' => 'error',
                    'currentQuantity' => $currentQuantity
                ];
            } else {
                $avlQuantity->reserved_stock = $newReservedStocks;
                $this->ProductStocks->save($avlQuantity);
                $response = [
                    'status' => 'success',
                    'currentQuantity' => $currentQuantity
                ];
            }
        } else {
            $response = [
                'status' => 'error',
                'currentQuantity' => $currentQuantity
            ];
        }
        return $response;
    }

	//Add BL component
    public function addBL($supplierID, $stockRequestId, $bl_no,$image, $bldate, $bl_items){
			$addBLID = $this->BL->addBL($supplierID, $stockRequestId, $bl_no,$image, $bldate);
		
			if($addBLID) {
				foreach ($bl_items as $key => $value) {
					$addData = array();
					$addBLItemID = $this->BLItems->addBLItems($addBLID, $value);

	
					// Base condition with product_id
					$conditions = [
						// 'warehouse_id' => $warehouse_id,
						'product_id' => $value['product_id']
					];
	
					// Check if product_variant_id is set, not null, not an empty string, and not the string 'null'
					// if (!empty($value['product_variant_id']) && $value['product_variant_id'] !== 'null') {
					// 	$conditions['product_variant_id'] = $value['product_variant_id'];
					// }
	
					// // Check if product_attribute_id is set, not null, not an empty string, and not the string 'null'
					// if (!empty($value['product_attribute_id']) && $value['product_attribute_id'] !== 'null') {
					// 	$conditions['product_attribute_id'] = $value['product_attribute_id'];
					// }
	
					// // Find the existing product stock
					// $stockExist = $this->ProductStocks->find()
					// 	->where($conditions)
					// 	->first();
	
					// if($stockExist){
					// 	$productStockID = $stockExist->id;
					// 	$existQuantity  = $stockExist->quantity;
					// 	$increaseQuantity = $existQuantity + $value['quantity'];
					// 	$addData['quantity'] = $increaseQuantity;
					// 	$updateProductStock = $this->ProductStocks->updateProductStock($productStockID, $addData);
	
					// } else {
					// 	$addProductStock = $this->ProductStocks->addProductStock($ws, $warehouse_id, $value);
					// }    			    			
				}
				return $addBLID;
			} else {
				return false;
			}
		}

		

}

?>