<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Merchant Entity
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $business_type
 * @property string|null $url_key
 * @property string $company_name
 * @property string|null $description
 * @property string|null $merchant_identifier
 * @property string|null $commission_rate
 * @property string|null $merchant_logo
 * @property string|null $banner
 * @property string|null $country_code
 * @property string $phone_number
 * @property string $address_line1
 * @property string|null $address_line2
 * @property string|null $country
 * @property int $state_id
 * @property int $city_id
 * @property string $zipcode
 * @property string|null $tax_identification_no
 * @property string|null $bank_acc_no
 * @property string|null $ifsc_code
 * @property string|null $pan_no
 * @property string|null $business_license
 * @property string|null $business_certificate
 * @property string $approval_status
 * @property string|null $reason
 * @property string|null $admin_note
 * @property string $verification_status
 * @property int $document_submitted
 * @property \Cake\I18n\Date $supplier_approval_date
 * @property int $confirmation_email_sent
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $meta_keyword
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\User $user
 * @property \App\Model\Entity\State $state
 * @property \App\Model\Entity\City $city
 * @property \App\Model\Entity\Brand[] $brands
 * @property \App\Model\Entity\MerchantDocument[] $merchant_documents
 * @property \App\Model\Entity\MerchantOrder[] $merchant_orders
 * @property \App\Model\Entity\MerchantSettlement[] $merchant_settlements
 * @property \App\Model\Entity\MerchantRating[] $merchant_ratings
 * @property \App\Model\Entity\Offer[] $offers
 * @property \App\Model\Entity\Order[] $orders
 * @property \App\Model\Entity\ProductDeal[] $product_deals
 * @property \App\Model\Entity\Product[] $products
 * @property \App\Model\Entity\Supplier[] $suppliers
 */
class Merchant extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'user_id' => true,
        'category_id' => true,
        'category_ids' => true,
        'business_type' => true,
        'url_key' => true,
        'company_name' => true,
        'description' => true,
        'merchant_identifier' => true,
        'commission_rate' => true,
        'merchant_logo' => true,
        'banner' => true,
        'country_code' => true,
        'phone_number' => true,
        'address_line1' => true,
        'address_line2' => true,
        'country' => true,
        'state_id' => true,
        'city_id' => true,
        'zipcode' => true,
        'tax_identification_no' => true,
        'bank_acc_no' => true,
        'ifsc_code' => true,
        'pan_no' => true,
        'business_license' => true,
        'business_certificate' => true,
        'approval_status' => true,
        'reason' => true,
        'admin_note' => true,
        'verification_status' => true,
        'document_submitted' => true,
        'approval_date' => true,
        'confirmation_email_sent' => true,
        'meta_title' => true,
        'meta_description' => true,
        'meta_keyword' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
        'user' => true,
        'category' => true,
        'state' => true,
        'city' => true,
        'brands' => true,
        'merchant_documents' => true,
        'merchant_orders' => true,
        'merchant_settlements' => true,
        'merchant_ratings' => true,
        'offers' => true,
        'orders' => true,
        'product_deals' => true,
        'products' => true,
        'suppliers' => true,
    ];

    /**
     * Get merchant code
     *
     * @return string
     */
    protected function _getMerchantCode()
    {
        if (!empty($this->merchant_identifier)) {
            return $this->merchant_identifier;
        }
        
        // Generate code from ID if merchant_identifier is empty
        return 'SL-' . str_pad((string)$this->id, 6, '0', STR_PAD_LEFT);
    }
}
