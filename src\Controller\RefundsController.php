<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class RefundsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Orders;
    protected $OrderItems;
    protected $Products;
    protected $Roles;
    protected $ProductAttributes;
    protected $OrderReturnCategories;
    protected $Warehouses;
    protected $Showrooms;
    protected $Shipments;
    protected $ShipmentOrders;
    protected $ShipmentOrderItems;
    protected $CustomerAddresses;
    protected $ZoneMunicipalities;
    protected $OrderReturns;
    protected $Wallets;
    protected $RefundTransactions;
    protected $ShowroomCashRefunds;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Products = $this->fetchTable('Products');
        $this->Roles = $this->fetchTable('Roles');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->ShipmentOrderItems = $this->fetchTable('ShipmentOrderItems');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->ZoneMunicipalities = $this->fetchTable('ZoneMunicipalities');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->Wallets = $this->fetchTable('Wallets');
        $this->RefundTransactions = $this->fetchTable('RefundTransactions');
        $this->ShowroomCashRefunds = $this->fetchTable('ShowroomCashRefunds');
    }

    public function processRefund()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : 'FCFA';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : ',';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : ' ';

        $data = $this->request->getData();

        $returnId = $data['return_id'] ?? null;
        $method = $data['refund_method'] ?? null;
        $pickupCharge = isset($data['pickup_charge']) ? (float)$data['pickup_charge'] : 0;
        $totalRefund = isset($data['total_refund']) ? (float)$data['total_refund'] : 0;

        $response = ['success' => false, 'message' => ''];

        if (!$returnId || !$method || $totalRefund <= 0) {
            $response['message'] = __('Invalid refund data.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        $orderReturn = $this->OrderReturns->find()
            ->contain([
                'OrderItems' => [
                    'Products' => function ($q) {
                        return $q->select(['Products.id', 'Products.name', 'Products.sku']);
                    },
                    'ProductVariants' => function ($q) {
                        return $q->select(['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.sku']);
                    },
                    'ProductAttributes' => function ($q) {
                        return $q->select(['ProductAttributes.id', 'ProductAttributes.attribute_id', 'ProductAttributes.attribute_value_id']);
                    }
                ],
                'Orders' => [
                    'Customers' => ['Users']
                ],
                'Users' => function ($q) {
                    return $q->select(['Users.id', 'Users.first_name', 'Users.last_name']);
                },
                'VerifiedByUser' => function ($q) {
                    return $q->select(['VerifiedByUser.id', 'VerifiedByUser.first_name', 'VerifiedByUser.last_name']);
                },
                'OrderReturnCategories' => function ($q) {
                    return $q->select(['OrderReturnCategories.id', 'OrderReturnCategories.name']);
                },
                'RefundTransactions' => function ($q) {
                    return $q->order(['RefundTransactions.created_at' => 'DESC']);
                }
            ])
            ->where(['OrderReturns.id' => $returnId])
            ->first();


        if (!$orderReturn) {
            $response['message'] = __('Return not found.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        $customerId = $orderReturn->order->customer_id ?? null;
        if (!$customerId) {
            $response['message'] = __('Customer not associated with this return.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            // Update return record
            $orderReturn->status = 'Refunded';

            if (!$this->OrderReturns->save($orderReturn)) {
                throw new \Exception(__('Failed to update return status.'));
            }

            if (strtolower($method) === 'wallet') {
                $wallet = $this->Wallets->find()
                    ->where(['customer_id' => $customerId])
                    ->first();

                if (!$wallet) {
                    $wallet = $this->Wallets->newEntity([
                        'customer_id' => $customerId,
                        'balance' => $totalRefund
                    ]);

                    if (!$this->Wallets->save($wallet)) {
                        throw new \Exception(__('Failed to create new wallet.'));
                    }
                } else {
                    $wallet->balance += $totalRefund;
                    if (!$this->Wallets->save($wallet)) {
                        throw new \Exception(__('Failed to update wallet balance.'));
                    }
                }

                $walletId = $wallet->id;
            } else {
                $walletId = null;
            }

            $productName = $orderReturn->order_item->product->name ?? 'Product';
            $variant = $orderReturn->order_item->product_variant->variant_name ?? null;
            $attributeName = $orderReturn->order_item->attributes['attribute_name'] ?? null;
            $attributeValue = $orderReturn->order_item->attributes['attribute_value'] ?? null;

            $productDetails = $productName;
            if ($variant || $attributeName) {
                $productDetails .= ' (';
                if ($variant) {
                    $productDetails .= $variant;
                }
                if ($attributeName && $attributeValue) {
                    $productDetails .= ($variant ? ', ' : '') . "$attributeName: $attributeValue";
                }
                $productDetails .= ')';
            }

            $note = __(
                'Refund of {0} {1} processed via {2} for Order #{3}, Product: {4}.',
                number_format((float)$totalRefund, 0, $decimalSeparator, $thousandSeparator),
                $currencySymbol,
                ucfirst($method),
                $orderReturn->order_id,
                $productDetails
            );

            // Save refund transaction
            $transaction = $this->RefundTransactions->newEntity([
                'order_return_id' => $orderReturn->id,
                'wallet_id' => $walletId,
                'customer_id' => $customerId,
                'method' => ucfirst($method),
                'pickup_charge' => $pickupCharge,
                'product_refund' => $orderReturn->return_amount,
                'total_refund' => $totalRefund,
                'note' => $note
            ]);

            if (!$this->RefundTransactions->save($transaction)) {
                throw new \Exception(__('Failed to save refund transaction.'));
            }

            // ✅ If refund method is Cash AND return_to = Showroom AND pickup_required = Yes → log into showroom_cash_refunds
            if ($method === 'cash' &&
                strtolower($orderReturn->return_to) === 'showroom' &&
                strtolower($orderReturn->pickup_required) === 'yes') {

                $refundRecord = $this->ShowroomCashRefunds->newEntity([
                    'showroom_id'     => $orderReturn->order->showroom_id ?? null,
                    'order_return_id' => $orderReturn->id,
                    'amount'          => $totalRefund,
                    'status'          => 'Open',
                    'created_by'      => $this->request->getAttribute('identity')->get('id'),
                ]);

                if (!$this->ShowroomCashRefunds->save($refundRecord)) {
                    throw new \Exception(__('Failed to log showroom cash refund.'));
                }
            }

            $response['success'] = true;
            $response['message'] = __('Refund processed successfully.');
        } catch (\Exception $ex) {
            $conn->rollback();
            $this->log("Refund Error: " . $ex->getMessage(), 'error');
            $response['message'] = $ex->getMessage();
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function view($id = null)
    {
        $orderReturn = $this->OrderReturns->find()
            ->contain([
                'OrderItems' => [
                    'Products' => function ($q) {
                        return $q->select(['Products.id', 'Products.name', 'Products.sku']);
                    },
                    'ProductVariants' => function ($q) {
                        return $q->select(['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.sku']);
                    },
                    'ProductAttributes' => function ($q) {
                        return $q->select(['ProductAttributes.id', 'ProductAttributes.attribute_id', 'ProductAttributes.attribute_value_id']);
                    }
                ],
                'Orders' => [
                    'Customers' => ['Users']
                ],
                'Users' => function ($q) {
                    return $q->select(['Users.id', 'Users.first_name', 'Users.last_name']);
                },
                'VerifiedByUser' => function ($q) {
                    return $q->select(['VerifiedByUser.id', 'VerifiedByUser.first_name', 'VerifiedByUser.last_name']);
                },
                'OrderReturnCategories' => function ($q) {
                    return $q->select(['OrderReturnCategories.id', 'OrderReturnCategories.name']);
                },
                'RefundTransactions' => function ($q) {
                    return $q->order(['RefundTransactions.created_at' => 'DESC']);
                }
            ])
            ->where(['OrderReturns.id' => $id])
            ->first();

        // Add return_to_name manually
        $returnTo = $orderReturn->return_to ?? null;
        $returnToId = $orderReturn->return_to_id ?? null;

        $returnToName = __('N/A');

        if(strtolower($orderReturn->pickup_required) === 'yes') {
            if (strtolower($returnTo) === 'showroom' && $returnToId) {
                $showroom = $this->Showrooms->find()
                    ->select(['id', 'name'])
                    ->where(['id' => $returnToId])
                    ->first();

                if ($showroom) {
                    $returnToName = __('Showroom: ') . $showroom->name;
                }
            } elseif (strtolower($returnTo) === 'warehouse' && $returnToId) {
                $warehouse = $this->Warehouses->find()
                    ->select(['id', 'name'])
                    ->where(['id' => $returnToId])
                    ->first();

                if ($warehouse) {
                    $returnToName = __('Warehouse: ') . $warehouse->name;
                }
            }
        }

        $orderReturn->return_to_name = $returnToName;

        // Handle Product Attribute name/value if present
        if (!empty($orderReturn->order_item->product_attribute_id)) {

            $attribute = $this->ProductAttributes->find()
                ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                ->contain([
                    'Attributes' => ['fields' => ['Attributes.name']],
                    'AttributeValues' => ['fields' => ['AttributeValues.value']]
                ])
                ->first();

            if ($attribute) {
                $orderReturn->order_item->attributes = [
                    'attribute_name' => $attribute->attribute->name ?? '',
                    'attribute_value' => $attribute->attribute_value->value ?? ''
                ];
            }
        }

        // Handle Return Images
        if (!empty($orderReturn->return_product_image)) {
            $imageUrls = explode(',', $orderReturn->return_product_image);

            $imageUrls = array_map(function ($image) {
                return $this->Media->getCloudFrontURL(trim($image));
            }, $imageUrls);

            $orderReturn->return_product_image = implode(',', $imageUrls);
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('orderReturn', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    }


}
