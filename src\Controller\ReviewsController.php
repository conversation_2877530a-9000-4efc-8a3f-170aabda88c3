<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;
use Cake\ORM\Query;
use Cake\Database\Expression\QueryExpression;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $contentpages
 */
class ReviewsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $ReviewImages;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->ReviewImages = $this->fetchTable('ReviewImages');
    }
    
    public function index()
    {
        // $reviews = $this->Reviews->find()
        //     ->where(['Reviews.status IN' => ['A', 'I']])
        //     ->order(['Reviews.id' => 'DESC'])->toArray();

        $reviews = $this->Reviews->find()
            ->select([
                'Reviews.id',
                'Reviews.status',
                'Reviews.customer_id',
                'Reviews.product_id',
                'Reviews.rating',
                'Reviews.comment',
                'user_id'   => 'Customers.user_id',
                'full_name' => $this->Reviews->Customers->Users->find()
                    ->func()
                    ->concat(['Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal']),
                'product_name' => 'Products.name'
            ])
            ->where(['Reviews.status IN' => ['A', 'I']])
            ->contain([
                'Customers' => [
                    'Users' => function (Query $q) {
                        return $q->select([
                            'Users.id',
                            'Users.first_name',
                            'Users.last_name'
                        ]);
                    }
                ],
                'Products' => function (Query $q) {
                    return $q->select(['Products.id', 'Products.name']);
                }
            ])
            ->order(['Reviews.id' => 'DESC'])
            ->toArray();


        $this->set(compact('reviews'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        // $review = $this->Reviews->get($id, contain: [
        //     'ReviewImages'
        // ]);

        $review = $this->Reviews->get($id, [
            'contain' => [
                'ReviewImages',
                'Customers' => [
                    'Users' => function (\Cake\ORM\Query $q) {
                        return $q->select([
                            'Users.id',
                            'Users.first_name',
                            'Users.last_name',
                            'full_name' => $q->func()->concat([
                                'Users.first_name' => 'literal',
                                ' ',
                                'Users.last_name' => 'literal'
                            ])
                        ]);
                    }
                ],
                'Products' => function (\Cake\ORM\Query $q) {
                    return $q->select(['Products.id', 'Products.name']);
                }
            ]
        ]);

        if (!empty($review->review_images)) {
            foreach ($review->review_images as &$image) {
                $image->image_url = $this->Media->getCloudFrontURL($image->image_url);
            }
        }

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('review', 'dateFormat', 'timeFormat'));
    }

    private function handleFileUploads($reviewId)
    {
        $files = $this->request->getData('review_image');
        $uploadedImages = [];

        foreach ($files as $file) {
            if ($file->getError() === UPLOAD_ERR_OK) {
                $fileName = trim($file->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $file->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.PRODUCT_REVIEW');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                    } else {
                        $reviewImage = $this->Reviews->ReviewImages->newEmptyEntity();
                        $reviewImage->review_id = $reviewId;
                        $reviewImage->image_url = $folderPath . $imageFile;

                        if ($this->Reviews->ReviewImages->save($reviewImage)) {
                            $uploadedImages[] = $folderPath . $imageFile; // Collecting the image paths
                        } else {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                        }
                    }
                }
            }
        }
        return $uploadedImages;
    }

    /**
     * Edit method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function edit($id = null)
    {

        // $review = $this->Reviews->get($id, contain: ['ReviewImages']);
        $review = $this->Reviews->get($id, [
            'contain' => [
                'ReviewImages',
                'Customers' => [
                    'Users' => function (\Cake\ORM\Query $q) {
                        return $q->select([
                            'Users.id',
                            'Users.first_name',
                            'Users.last_name',
                            'full_name' => $q->func()->concat([
                                'Users.first_name' => 'literal',
                                ' ',
                                'Users.last_name'  => 'literal'
                            ])
                        ]);
                    }
                ],
                'Products' => function (\Cake\ORM\Query $q) {
                    return $q->select([
                        'Products.id',
                        'Products.name'
                    ]);
                }
            ]
        ]);

        
        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            $review = $this->Reviews->patchEntity($review, $data);

            // $deletedImages = json_decode($this->request->getData('deleted_images'), true);
            // if (!empty($deletedImages)) {
            //     $this->ReviewImages->deleteAll(['id IN' => $deletedImages]);
            // }

            if ($this->Reviews->save($review)) {

                $reviewId = $review->id;

                $uploadedImages = !empty($this->request->getData('review_image')) ? $this->handleFileUploads($reviewId) : [];

                if (!empty($uploadedImages)) {
                    $firstImage = $uploadedImages[0];
                    $review->image_url = $firstImage;
                    $this->Reviews->save($review);
                }

                $this->Flash->success(__('The review has been updated.'));

                return $this->redirect(['action' => 'index']);
            }

            $this->Flash->error(__('The review could not be updated. Please, try again.'));
        }

        if (!empty($review->review_images)) {
            foreach ($review->review_images as &$image) {
                $image->image_url = $this->Media->getCloudFrontURL($image->image_url);
            }
        }

        $imageSize = Configure::read('Constants.CONTENT_WEB_IMAGE_SIZE');
        $webImageMinWidth = Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_WIDTH');
        $webImageMaxWidth = Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_WIDTH');
        $webImageMinHeight = Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_HEIGHT');
        $webImageMaxHeight = Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_HEIGHT');
        $webImageType = Configure::read('Constants.CONTENT_WEB_IMAGE_TYPE');

        $file_acceptance_msg = __('Only '.$webImageType.' files are allowed. Max size: '.$imageSize.'MB. Dimensions: '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight.'');

        $this->set([
            'webImageMinWidth' => Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_HEIGHT'),
            'webImageSize' => Configure::read('Constants.CONTENT_WEB_IMAGE_SIZE'),
        ]);

        $this->set(compact('review', 'file_acceptance_msg'));

    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The content page could not be deleted. Please, try again.'];

        try {
            $record = $this->Reviews->get($id);
            $record->status = 'D';

            if ($this->Reviews->save($record)) {
                $response = ['success' => true, 'message' => 'The review has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
