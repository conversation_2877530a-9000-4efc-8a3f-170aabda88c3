<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/export-tables/buttons.dataTables.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
    }

</style>
<?php $this->end(); ?>

<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Reports') ?></h4>
            </li>
        </ul>
    </div>

    <div class="row mt-5 date_picker mx-1">
        <div class="col-sm-3 m-r-50">
            <h5 style="color: #f77f00;"><?= __('Sales Person Performance') ?></h5>
        </div>
        <div class="col-sm-7 text-end">
            <div class="row align-items-center mb-2">
                <div class="col-md-7">

                </div>
                <div class="col-sm-5">
                    <div>
                        <div class="col-sm-7">
                        </div>
                        <div class="col-sm-9 m-l-125">
                            <select id="date-period" class="form-select m-l-100" onchange="handleChange(this)">
                                <option value="current_month"><?= __('Current Month') ?></option>
                                <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                                <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                                <option value="current_year"><?= __('Current Year') ?></option>
                                <option value="4"><?= __('Custom') ?></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row align-items-center mb-2 d-none" id="datesappear">
                <form id="dateRangeForm" class="d-flex">
                    <div class="col-sm-3">
                        <label for="from-date" class="col-form-label fw-400 m-r-10"><?= __('From Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="from-date" name="from-date" />
                    </div>
                    <div class="col-sm-3">
                        <label for="to-date" class="col-form-label fw-400 m-r-10"><?= __('To Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="to-date" name="to-date" />
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-primary btn-sm" type="submit"><?= __('Submit') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="section-body mt-3">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12 mb-5">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Sales Person Ranking') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="chart13"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row align-items-center justify-content-between mb-4 d-none">
                <div class="col-sm-6">
                    <form class="d-flex align-items-center">
                        <div class="input-group me-2 mt-0">
                            <input type="text" id="search_box" class="form-control search_control"
                                placeholder="Search">
                            <div class="input-group-btn">
                                <button class="btn search_control_btn" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-sm-6 text-end">
                    <button type="button" id="export_button" class="btn btn_export me-0 font-16"
                        data-toggle="tooltip" data-placement="right" title="Download">
                        <img src="../../img/download.svg" alt="download" />
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="rank_table p-3 pb-0">
                        <div class="table_title">
                            <h4 class="font-18 mb-4"><?= __('Ranking Table') ?></h4>
                        </div>
                        <div class="table-responsive" id="table_report" tabindex="1">
                            <table class="table table-hover table-xl mb-0" id="OrdersTable">
                                <thead>
                                    <tr>
                                        <th><?= __('Sales Person Name') ?></th>
                                        <th><?= __('Showroom Name') ?></th>
                                        <th><?= __('Total Sales') ?></th>
                                        <th><?= __('Total Orders') ?></th>
                                        <th><?= __('Average Sales Value') ?></th>
                                        <th><?= __('Total Bonus') ?></th>
                                    </tr>
                                </thead>
                                <tbody id="table_datalist">
                                    <?php foreach ($salesTableData as $data): ?>
                                        <tr>
                                            <td><?= h($data->sales_person_name) ?></td>
                                            <td><?= h($data->showroom_name) ?></td>
                                            <td><?= h(number_format($data->total_sales, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></td>
                                            <td><?= h($data->total_orders) ?></td>
                                            <td><?= h(number_format($data->average_sales_value, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></td>
                                            <td><?= h(number_format($data->total_bonus, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</section>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>">
</script>
<!-- DataTables Buttons JS -->
<script src="<?= $this->Url->webroot('bundles/datatables/export-tables/dataTables.buttons.min.js') ?>"></script>

<!-- JSZip (For Excel export support) -->
<script src="<?= $this->Url->webroot('bundles/datatables/export-tables/jszip.min.js') ?>"></script>

<!-- Buttons HTML5 export JS -->
<script src="<?= $this->Url->webroot('bundles/datatables/export-tables/buttons.html5.min.js') ?>"></script>

<!-- Buttons Print export JS -->
<script src="<?= $this->Url->webroot('bundles/datatables/export-tables/buttons.print.min.js') ?>"></script>

<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>

<script>

    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#OrdersTable").DataTable({
        dom: 'Bfrtip',  // B - Buttons, f - filter input, r - processing display, t - table, i - table information, p - pagination
        buttons: [
            {
                extend: 'csvHtml5',
                text: '<i class="fas fa-file-download"></i>',  // CSV icon
                titleAttr: 'Export CSV',
                title: 'Sale Person Performance Reports',
                exportOptions: {
                    columns: ':visible'
                }
            }
        ],
        columnDefs: [{
                orderable: false,
                targets: [-1]
            }, // Make the last column non-sortable
        ],
        order: [],
        // dom: "rtip", // Remove the default search box
        pageLength: paginationCount
    });

    let chart;
    $(function () {

        chart13();

    });

    function chart13() {
        var options = {
            series: [{
                name: "Sales",
                data: <?php echo json_encode($orderCounts); ?>,
            }],
            chart: {
                type: "bar",
                height: 350,
                dropShadow: {
                    enabled: true,
                    color: "#000",
                    top: 18,
                    left: 7,
                    blur: 10,
                    opacity: 0.2,
                },
                toolbar: {
                    show: false,
                },
            },
            colors: ["#0d839b"],
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: "10%",
                    endingShape: "rounded",
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            xaxis: {
                categories: <?php echo json_encode($salesPersons); ?>,
                title: {
                    text: "Sales Persons",
                },
            },
            yaxis: {
                title: {
                    text: "Sales",
                },
                labels: {
                    offsetX: 0,
                    offsetY: 0,
                    style: {
                        color: "#8e8da4",
                    },
                },
            },
            legend: {
                position: "top",
                horizontalAlign: "right",
                labels: {
                    colors: "#8e8da4",
                    useSeriesColors: false,
                },
            },
            fill: {
                opacity: 1,
            },
            tooltip: {
                theme: "dark",
                marker: {
                    show: true,
                },
                x: {
                    show: true,
                },
            },
        };

        chart = new ApexCharts(document.querySelector("#chart13"), options);
        chart.render() // Render the chart for the first time
        .then(() => {
            console.log("Chart initialized successfully:", chart);
        })
        .catch((error) => {
            console.error("Failed to render chart:", error);
        });
    }

    $('#dateRangeForm').on('submit', function(event) {
        
        event.preventDefault();
        fetchFilterGraphData();

    });

    function handleChange(answer) {
        
        if (answer.value == 4) {
            document.getElementById('datesappear').classList.remove('d-none');
        } else 
        {

            document.getElementById('datesappear').classList.add('d-none');

            const filterValue = document.getElementById('date-period').value;
            let startDate, endDate;
            const currentDate = moment();

            switch (filterValue) {
                case "current_month":
                    startDate = moment().startOf('month');
                    endDate = moment().endOf('month');
                    break;
                case "last_3_months":
                    startDate = moment().subtract(3, 'months').startOf('month');
                    endDate = moment();
                    break;
                case "last_6_months":
                    startDate = moment().subtract(6, 'months').startOf('month');
                    endDate = moment();
                    break;
                case "current_year":
                    startDate = moment().startOf('year');
                    endDate = moment();
                    break;
                case "custom":
                    // You can integrate a custom date picker here for manual selection
                    startDate = null;
                    endDate = null;
                    break;
            }

            // if (startDate && endDate) {
            //     // Custom filter for date range
            //     $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
            //         const orderDate = moment(data[3], 'YYYY-MM-DD'); // Assuming order_date is in 6th column (index 5)
            //         return orderDate.isBetween(startDate, endDate, 'day', '[]'); // Inclusive of start and end dates
            //     });
            // } else {
            //     // Remove any previously applied filters if "custom" or no date range selected
            //     $.fn.dataTable.ext.search.pop();
            // }

            // // Redraw the DataTable with the new filter
            // $('#OrdersTable').DataTable().draw();

            /** GRAPH FILTER **/
            fetchFilterGraphData();
        }
    }

    function fetchFilterGraphData()
    {
        /** GRAPH FILTER **/
        const selectedValue = document.getElementById('date-period').value;

        // var showroom_id = $("#showroom-option option:selected").val();
        // var order_status = $("#orderStatus option:selected").val();
        // var payment_status = $("#paymentStatus option:selected").val();

        // console.log(selectedValue);
        if(selectedValue == 4)
        {   
            var fromDate = $('#from-date').val();
            var toDate = $('#to-date').val();
            
            var startDateString = fromDate;
            var endDateString = toDate;
            // console.log(monthLabels);
        }
        else
        {
            let newStartDate = '';
            let newEndDate = new Date();
            
            switch (selectedValue) {
                case 'current_month':
                    newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth(), 2);
                    newEndDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() + 1, 1);
                    break;
                case 'last_3_months':
                    newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() - 2, 2);
                    newEndDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() + 1, 1);
                    break;
                case 'last_6_months':
                    newStartDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() - 5, 2);
                    newEndDate = new Date(newEndDate.getFullYear(), newEndDate.getMonth() + 1, 1);
                    break;
                case 'current_year':
                    newStartDate = new Date(newEndDate.getFullYear(), 0, 2);
                    newEndDate = new Date(newEndDate.getFullYear(), 11, 31);
                    break;
            }
            // Convert dates to string format (Y-m-d)
            var startDateString = newStartDate.toISOString().split('T')[0];
            var endDateString = newEndDate.toISOString().split('T')[0];
            // var monthLabels = getMonthsBetween(newStartDate, newEndDate);
        }

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Reports', 'action' => 'filterSalesPersonPerformance']); ?>',
            type: 'POST',
            data: {
                startDate: startDateString,
                endDate: endDateString
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {

                // console.log(response)
                updateGraph(response.salesPersons, response.orderCounts);
                // setTimeout(() => {
                // }, 1000);
            },
            error: function() {
                swal('<?= __('Failed') ?>', '<?= __('Failed to fetch data. Please try again.') ?>', 'error');
            }
        });

        // Fetch data based on the selected range
        // fetchFilterGraphData(startDateString, endDateString, monthLabels);
    }

    function updateGraph(salesPersons, orderCounts) { 

        if (chart) {
            chart.destroy();
            console.log("Chart destroyed.");
        }

        const newOptions = {
            series: [{
                name: "Sales",
                data: orderCounts,
            }],
            chart: {
                type: "bar",
                height: 350,
                dropShadow: {
                    enabled: true,
                    color: "#000",
                    top: 18,
                    left: 7,
                    blur: 10,
                    opacity: 0.2,
                },
                toolbar: {
                    show: false,
                },
            },
            colors: ["#0d839b"],
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: "10%",
                    endingShape: "rounded",
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            xaxis: {
                categories: salesPersons,
                title: {
                    text: "Sales Persons",
                },
            },
            yaxis: {
                title: {
                    text: "Sales",
                },
                labels: {
                    offsetX: 0,
                    offsetY: 0,
                    style: {
                        color: "#8e8da4",
                    },
                },
            },
            legend: {
                position: "top",
                horizontalAlign: "right",
                labels: {
                    colors: "#8e8da4",
                    useSeriesColors: false,
                },
            },
            fill: {
                opacity: 1,
            },
            tooltip: {
                theme: "dark",
                marker: {
                    show: true,
                },
                x: {
                    show: true,
                },
            },
        };

        // Create a new chart instance
        chart = new ApexCharts(document.querySelector("#chart13"), newOptions);
        chart.render().then(() => {
            console.log("Chart rendered successfully.");
        }).catch(err => {
            console.error("Error rendering chart:", err);
        });

    }

</script>
<?php $this->end(); ?>