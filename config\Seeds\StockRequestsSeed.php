<?php
declare(strict_types=1);

use Migrations\BaseSeed;

/**
 * StockRequests seed.
 */
class StockRequestsSeed extends BaseSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeds is available here:
     * https://book.cakephp.org/migrations/4/en/seeding.html
     *
     * @return void
     */
    public function run(): void
    {
        $data = [
            [
                'requested_by' => 2,
                'requestor_type' => 'Warehouse',
                'supplier_id' => 2,
                'warehouse_id' => 1,
                'showroom_id' => null,
                'to_showroomID' => null,
                'manager_review_status' => 'Pending',
                'manager_reviewed_time' => null,
                'reviewed_by' => null,
                'supervisor_verify_status' => 'Pending',
                'supervisor_verified_time' => null,
                'verified_by' => null,
                'request_status' => 'Pending',
                'request_date' => date('Y-m-d H:i:s'),
                'edit_lock_by' => null,
                'edit_lock_time' => null,
                'review_note' => "no-stock-request",
                'verify_note' => null,
                'status' => 'A',
                'created' => date('Y-m-d H:i:s'),
                'modified' => date('Y-m-d H:i:s'),
            ],
            // Add more rows here...
        ];

        $table = $this->table('stock_requests');
        $table->insert($data)->save();
    }
}
