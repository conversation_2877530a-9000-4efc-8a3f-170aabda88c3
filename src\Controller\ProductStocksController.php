<?php

declare(strict_types=1);

namespace App\Controller;

use Cake\Database\Expression\QueryExpression;

/**
 * ProductStocks Controller
 *
 * @property \App\Model\Table\ProductStocksTable $ProductStocks
 */
class ProductStocksController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected \App\Model\Table\ProductsTable $Products;
    protected \App\Model\Table\OrdersTable $Orders;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->Products = $this->fetchTable('Products');
        $this->Orders = $this->fetchTable('Orders');
    }

    public function index()
    {
        $query = $this->ProductStocks->find()
            ->contain(['Showrooms', 'Warehouses', 'Products', 'ProductVariants', 'ProductAttributes']);
        $productStocks = $this->paginate($query);

        $this->set(compact('productStocks'));
    }

    /**
     * View method
     *
     * @param string|null $id Product Stock id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $productStock = $this->ProductStocks->get($id, contain: ['Showrooms', 'Warehouses', 'Products', 'ProductVariants', 'ProductAttributes']);
        $this->set(compact('productStock'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $productStock = $this->ProductStocks->newEmptyEntity();
        if ($this->request->is('post')) {
            $productStock = $this->ProductStocks->patchEntity($productStock, $this->request->getData());
            if ($this->ProductStocks->save($productStock)) {
                $this->Flash->success(__('The product stock has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product stock could not be saved. Please, try again.'));
        }
        $showrooms = $this->ProductStocks->Showrooms->find('list', limit: 200)->all();
        $warehouses = $this->ProductStocks->Warehouses->find('list', limit: 200)->all();
        $products = $this->ProductStocks->Products->find('list', limit: 200)->all();
        $productVariants = $this->ProductStocks->ProductVariants->find('list', limit: 200)->all();
        $productAttributes = $this->ProductStocks->ProductAttributes->find('list', limit: 200)->all();
        $this->set(compact('productStock', 'showrooms', 'warehouses', 'products', 'productVariants', 'productAttributes'));
    }

    /**
     * Edit method
     *
     * @param string|null $id Product Stock id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $productStock = $this->ProductStocks->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $productStock = $this->ProductStocks->patchEntity($productStock, $this->request->getData());
            if ($this->ProductStocks->save($productStock)) {
                $this->Flash->success(__('The product stock has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The product stock could not be saved. Please, try again.'));
        }
        $showrooms = $this->ProductStocks->Showrooms->find('list', limit: 200)->all();
        $warehouses = $this->ProductStocks->Warehouses->find('list', limit: 200)->all();
        $products = $this->ProductStocks->Products->find('list', limit: 200)->all();
        $productVariants = $this->ProductStocks->ProductVariants->find('list', limit: 200)->all();
        $productAttributes = $this->ProductStocks->ProductAttributes->find('list', limit: 200)->all();
        $this->set(compact('productStock', 'showrooms', 'warehouses', 'products', 'productVariants', 'productAttributes'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Product Stock id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $productStock = $this->ProductStocks->get($id);
        if ($this->ProductStocks->delete($productStock)) {
            $this->Flash->success(__('The product stock has been deleted.'));
        } else {
            $this->Flash->error(__('The product stock could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    public function getWarehouseStocks()
    {
        $warehouse_id = $this->request->getQuery('warehouse_id');

        $connection = $this->Products->getConnection();

        $sql = "
            CREATE TEMPORARY TABLE temp_latest_product_supplier_prices AS
            SELECT 
                sp.product_id,
                sp.supplier_price AS latest_price
            FROM 
                supplier_products sp
            WHERE 
                sp.status = 'A'
            AND 
                sp.id = (
                    SELECT MAX(sub_sp.id) 
                    FROM supplier_products sub_sp
                WHERE sub_sp.product_id = sp.product_id 
        )";

        $connection->execute($sql);

        $connection = $this->Products->getConnection();

        $sql = "
            CREATE TEMPORARY TABLE temp_latest_variant_supplier_prices AS
            SELECT 
                sp.product_id,
                sp.product_variant_id,
                sp.supplier_price AS latest_price
            FROM 
                supplier_products sp
            WHERE 
                sp.status = 'A'
            AND 
                sp.id = (
                    SELECT MAX(sub_sp.id) 
                    FROM supplier_products sub_sp
                    WHERE sub_sp.product_id = sp.product_id 
                    AND sub_sp.product_variant_id = sp.product_variant_id 
            )";

        $connection->execute($sql);

        $stocks = $this->ProductStocks->find('all')
            ->where(['ProductStocks.warehouse_id' => $warehouse_id])
            ->contain(['Products' => ['ProductCategories'], 'ProductVariants'])
            ->join([
                'ProductCategories' => [
                    'table' => 'product_categories',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductStocks.product_id = ProductCategories.product_id',
                        'level' => 1
                    ],
                ],
                'Categories' => [
                    'table' => 'categories',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductCategories.category_id = Categories.id',
                    ],
                ],
            ])
            ->join([
                'LatestProductSupplierPrices' => [
                    'table' => 'temp_latest_product_supplier_prices',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductStocks.product_id = LatestProductSupplierPrices.product_id',
                    ],
                ],
            ])
            ->join([
                'LatestVariantSupplierPrices' => [
                    'table' => 'temp_latest_variant_supplier_prices',
                    'type' => 'LEFT',
                    'conditions' => [
                        'ProductStocks.product_variant_id = LatestVariantSupplierPrices.product_variant_id',
                    ],
                ],
            ])
            ->select([
                'Id' => 'ProductStocks.id',
                'product_name' => 'Products.name',
                'variant_name' => 'CASE 
            WHEN ProductStocks.product_variant_id IS NOT NULL THEN ProductVariants.variant_name 
            ELSE NULL END',
                'sku' => 'CASE 
            WHEN ProductStocks.product_variant_id IS NOT NULL THEN ProductVariants.sku 
            ELSE Products.sku END',
                'in_stock' => 'SUM(ProductStocks.quantity)',
                'value' => 'CASE 
            WHEN ProductStocks.product_variant_id IS NOT NULL 
                THEN SUM(LatestVariantSupplierPrices.latest_price * ProductStocks.quantity) 
            ELSE SUM(LatestProductSupplierPrices.latest_price * ProductStocks.quantity) END',
                'min_product_quantity' => 'MIN(Categories.min_product_quantity)',
            ])
            ->group([
                'ProductStocks.id',
                'Products.id',
                'ProductVariants.id',
                'Categories.id',
            ])->order(['ProductStocks.quantity' => 'ASC'])
            ->toArray();



        $connection->execute("DROP TEMPORARY TABLE IF EXISTS temp_latest_product_supplier_prices");
        $connection->execute("DROP TEMPORARY TABLE IF EXISTS temp_latest_variant_supplier_prices");

        $response = ['stocks' => $stocks];
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function getStockDetails($id)
    {
        $this->request->allowMethod(['get']);

        $stock = $this->ProductStocks->get($id, [
            'contain' => ['Products', 'ProductVariants']
        ]);

        $response = [
            'status' => 'success',
            'data' => [
                'total_stock' => $stock->quantity ?? 0,
                'reserved_stock' => $stock->reserved_stock ?? 0,
                'defective_stock' => $stock->defective_stock ?? 0,
                'service_centre_stock' => $stock->service_centre_stock ?? 0,
                'purchased_stock' => $stock->purchased_stock ?? 0,
            ]
        ];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function reserveStock()
    {
        $this->autoRender = false;
        $this->request->allowMethod(['post']);

        $productId = $this->request->getData('productId');
        $productVarId = $this->request->getData('productVarId') ?? [];
        $attributeId = $this->request->getData('attributeId');
        $showrooms = $this->request->getData('showrooms') ?? [];
        $prevquantity = $this->request->getData('prevquantity');
        $current_quantity = $this->request->getData('current_quantity');
        $conditions = [];

        if (!empty($showrooms)) {
            $conditions['showroom_id IN'] = $showrooms;
        }

        if (!empty($productId)) {
            $conditions['product_id'] = $productId;
        }

        if (!empty($productVarId)) {
            $conditions['product_variant_id'] = $productVarId;
        }

        if (!empty($attributeId)) {
            $conditions['product_attribute_id'] = $attributeId;
        }

        $avlQuantity = $this->ProductStocks->find()
            ->where($conditions)
            ->first();
        $currentQuantity = (int) $current_quantity;
        if ($avlQuantity) {

            $reservedStocks = (int) $avlQuantity->reserved_stock;
            $prevQuantity = (int) $prevquantity;


            $newReservedStocks = $reservedStocks - $prevQuantity + $currentQuantity;

            if ($newReservedStocks < 0) {
                $newReservedStocks = 0;
                $avlQuantity->reserved_stock = $newReservedStocks;
                $this->ProductStocks->save($avlQuantity);
                $response = [
                    'status' => 'error',
                    'prevquantity' => $currentQuantity
                ];
            } else {
                $avlQuantity->reserved_stock = $newReservedStocks;
                $this->ProductStocks->save($avlQuantity);
                $response = [
                    'status' => 'success',
                    'prevquantity' => $currentQuantity
                ];
            }
        } else {
            $response = [
                'status' => 'error',
                'prevquantity' => $currentQuantity
            ];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    // public function fetchStock()
    // {
    //     $this->request->allowMethod(['post', 'ajax']);
    //     $this->autoRender = false;

    //     $response = ['status' => 'error', 'message' => 'Invalid request.', 'data' => []];

    //     $items = $this->request->getData('items'); // Expecting an array of {product_id, variant_id, attribute_id, row_index}
    //     $showrooms = $this->request->getData('showroom_id'); // Can be an array or a single ID

    //     if (!empty($items)) {

    //         if (!empty($showrooms) && !is_array($showrooms)) {
    //             $showrooms = [$showrooms];
    //         } else {
    //             $showrooms = [];
    //         }

    //         $resultData = [];

    //         foreach ($items as $item) {
    //             $productId = $item['product_id'];
    //             $variantId = $item['variant_id'] ?? null;
    //             $attributeId = $item['attribute_id'] ?? null;
    //             $rowIndex = $item['row_index'];
    //             $conditions = [
    //                 'product_id' => $productId,
    //                 'product_variant_id IS' => $variantId,
    //                 'product_attribute_id IS' => $attributeId,
    //             ];

    //             if (!empty($showrooms)) {
    //                 $conditions['showroom_id IN'] = $showrooms;
    //             }

    //             $avlQuantity = $this->ProductStocks->find()
    //                 ->select([
    //                     'available_quantity' => (new QueryExpression('SUM(quantity) - SUM(reserved_stock)'))
    //                 ])
    //                 ->where($conditions)
    //                 ->first();
    //             $availableQuantity = $avlQuantity ? max(0, (int) $avlQuantity->available_quantity) : 0;

    //             $resultData[] = [
    //                 'row_index' => $rowIndex,
    //                 'available_stock' => $availableQuantity ?? 0
    //             ];
    //         }

    //         $response = [
    //             'status' => 'success',
    //             'data' => $resultData
    //         ];
    //     }

    //     $this->response = $this->response->withType('application/json');
    //     $this->response = $this->response->withStringBody(json_encode($response));
    //     return $this->response;
    // }

    public function fetchStock()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $response = ['status' => 'error', 'message' => 'Invalid request.', 'data' => []];

        $items = $this->request->getData('items'); // Array of {product_id, variant_id, attribute_id, row_index}
        $showrooms = $this->request->getData('showroom_id'); // Single or multiple showroom IDs

        if (!empty($items)) {

            if (!empty($showrooms) && !is_array($showrooms)) {
                $showrooms = [$showrooms];
            } elseif (empty($showrooms)) {
                $showrooms = [];
            }

            $resultData = [];

            foreach ($items as $item) {
                $productId = $item['product_id'] ?? null;
                $variantId = $item['variant_id'] ?? null;
                $attributeId = $item['attribute_id'] ?? null;
                $rowIndex = $item['row_index'] ?? null;

                $conditions = [
                    'product_id' => $productId
                ];

                if (!empty($variantId)) {
                    $conditions['product_variant_id'] = $variantId;
                }

                if (!empty($attributeId)) {
                    $conditions['product_attribute_id'] = $attributeId;
                }

                if (!empty($showrooms)) {
                    $conditions['showroom_id IN'] = $showrooms;
                }

                $avlQuantity = $this->ProductStocks->find()
                    ->select([
                        'available_quantity' => (new QueryExpression('SUM(quantity) - SUM(reserved_stock)'))
                    ])
                    ->where($conditions)
                    ->first();

                $availableQuantity = $avlQuantity ? max(0, (int)$avlQuantity->available_quantity) : 0;

                $resultData[] = [
                    'row_index' => $rowIndex,
                    'available_stock' => $availableQuantity
                ];
            }

            $response = [
                'status' => 'success',
                'data' => $resultData
            ];
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    // public function outgoingStock()
    // {
    //     $this->request->allowMethod(['post', 'ajax']);
    //     $this->autoRender = false;

    //     $response = ['status' => 'error', 'message' => 'Invalid request.'];

    //     $orderId = $this->request->getData('orderId');

    //     if (empty($orderId)) {
    //         $response['message'] = 'Order ID is required.';
    //         return $this->respondJson($response);
    //     }

    //     $order = $this->Orders->find()
    //         ->contain(['OrderItems'])
    //         ->where(['Orders.id' => $orderId, 'Orders.delivery_mode' => 'pickup'])
    //         ->first();

    //     if (!$order || empty($order->order_items)) {
    //         $response['message'] = 'Order not found or no items.';
    //         return $this->respondJson($response);
    //     }

    //     // process each item
    //     foreach ($order->order_items as $item) {
    //         $conditions = [
    //             'showroom_id' => $order->showroom_id,
    //             'product_id' => $item->product_id,
    //             'product_variant_id IS' => $item->product_variant_id,
    //             'product_attribute_id IS' => $item->product_attribute_id,
    //         ];

    //         $stock = $this->ProductStocks->find()
    //             ->where($conditions)
    //             ->first();

    //         if ($stock) {
    //             $stock->quantity -= $item->quantity;
    //             $stock->reserved_stock -= $item->quantity;

    //             if (!$this->ProductStocks->save($stock)) {
    //                 $response['message'] = "Failed to update stock for product ID {$item->product_id}.";
    //                 return $this->respondJson($response);
    //             }
    //         }

    //         // Mark individual item as Delivered
    //         $item->status = 'Delivered';
    //         if (!$this->Orders->OrderItems->save($item)) {
    //             $response['message'] = "Failed to update status for order item ID {$item->id}.";
    //             return $this->respondJson($response);
    //         }
    //     }

    //     // Finally mark order as Delivered
    //     $order->status = 'Delivered';
    //     if (!$this->Orders->save($order)) {
    //         $response['message'] = 'Stock updated, but failed to update order status.';
    //         return $this->respondJson($response);
    //     }

    //     $response = [
    //         'status' => 'success',
    //         'message' => 'Order and items marked as Delivered, and stock updated.'
    //     ];

    //     return $this->respondJson($response);
    // }

    public function outgoingStock()
    {
        $this->request->allowMethod(['post', 'ajax']);
        $this->autoRender = false;

        $response = ['status' => 'error', 'message' => 'Invalid request.'];

        $orderId = $this->request->getData('orderId');

        if (empty($orderId)) {
            $response['message'] = 'Order ID is required.';
            return $this->respondJson($response);
        }

        $order = $this->Orders->find()
            ->contain([
                'OrderItems',
                'Customers.Users' // ✅ so $order->customer->user is available
            ])
            ->where(['Orders.id' => $orderId])
            ->first();

        if (!$order || empty($order->order_items)) {
            $response['message'] = 'Order not found or no items.';
            return $this->respondJson($response);
        }

        // process each item
        foreach ($order->order_items as $item) {
            $conditions = [
                'showroom_id' => $order->showroom_id,
                'product_id' => $item->product_id,
                'product_variant_id IS' => $item->product_variant_id,
                'product_attribute_id IS' => $item->product_attribute_id,
            ];

            $stock = $this->ProductStocks->find()
                ->where($conditions)
                ->first();

            if ($stock) {
                $stock->quantity -= $item->quantity;
                $stock->reserved_stock -= $item->quantity;

                if (!$this->ProductStocks->save($stock)) {
                    $response['message'] = "Failed to update stock for product ID {$item->product_id}.";
                    return $this->respondJson($response);
                }
            }

            // Mark individual item as Delivered
            $item->status = 'Delivered';
            if (!$this->Orders->OrderItems->save($item)) {
                $response['message'] = "Failed to update status for order item ID {$item->id}.";
                return $this->respondJson($response);
            }
        }

        // ✅ Only trigger email if status actually changes
        $originalStatus = $order->status;
        $order->status = 'Delivered';

        if (!$this->Orders->save($order)) {
            $response['message'] = 'Stock updated, but failed to update order status.';
            return $this->respondJson($response);
        }

        if ($originalStatus !== 'Delivered' && $order->status === 'Delivered') {
            // Send Delivered Email to Customer
            $customerUser = $order->customer->user ?? null;

            if (!empty($customerUser) && !empty($customerUser->email)) {
                $customerName = trim(($customerUser->first_name ?? '') . ' ' . ($customerUser->last_name ?? '')) ?: __('Customer');
                $customerGreeting = __('Dear {0},', $customerName);

                // Build email data
                $customerEmailData = [
                    'greeting'     => $customerGreeting,
                    'logo_url'     => $logoUrl ?? null,
                    'order_id'     => $order->id,
                    'order_number' => $order->order_number ?? $order->id,
                    'order_date'   => $order->order_date ? $order->order_date->format('Y-m-d') : date('Y-m-d'),
                ];

                // Subject for customer
                $customerSubject = __("Your Order #{$order->order_number} Has Been Delivered");

                // ✅ Send customer email
                $this->Global->send_email(
                    [$customerUser->email],     // to
                    null,                       // from
                    $customerSubject,           // subject
                    'customer_order_delivered', // template
                    $customerEmailData
                );
            }

            // Send Push Notification
            if (!empty($order->customer->fcm_token)) {
                $tokens = [$order->customer->fcm_token];
                $title  = __('Order Delivered');
                $body   = __('Your order #{0} has been delivered successfully.', [$order->order_number ?? $order->id]);

                $customData = [
                    'type'         => 'order',
                    'action'       => 'delivered',
                    'order_id'     => $order->id,
                    'order_number' => $order->order_number ?? $order->id,
                    'status'       => 'Delivered'
                ];

                $this->Global->sendNotification($tokens, $title, $body, $customData);
            }
        }

        $response = [
            'status' => 'success',
            'message' => 'Order and items marked as Delivered, and stock updated.'
        ];

        return $this->respondJson($response);
    }

    private function respondJson($data)
    {
        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($data));
    }

    public function clearStock()
    {
        $this->autoRender = false;
        $this->request->allowMethod(['post']);

        $productId = $this->request->getData('productId');
        $productVarId = $this->request->getData('productVarId') ?? [];
        $attributeId = $this->request->getData('attributeId');
        $showrooms = $this->request->getData('showrooms') ?? [];
        $current_quantity = $this->request->getData('current_quantity');
        $conditions = [];

        if (!empty($showrooms)) {
            $conditions['showroom_id IN'] = $showrooms;
        }

        if (!empty($productId)) {
            $conditions['product_id'] = $productId;
        }

        if (!empty($productVarId)) {
            $conditions['product_variant_id'] = $productVarId;
        }

        if (!empty($attributeId)) {
            $conditions['product_attribute_id'] = $attributeId;
        }

        $avlQuantity = $this->ProductStocks->find()
            ->where($conditions)
            ->first();
        $currentQuantity = (int) $current_quantity;
        if ($avlQuantity) {

            $reservedStocks = (int) $avlQuantity->reserved_stock;
            $currentQuantity = (int) $currentQuantity;


            $newReservedStocks = $reservedStocks - $currentQuantity;

            if ($newReservedStocks < 0) {
                $newReservedStocks = 0;
                $avlQuantity->reserved_stock = $newReservedStocks;
                $this->ProductStocks->save($avlQuantity);
                $response = [
                    'status' => 'error',
                    'currentQuantity' => $currentQuantity
                ];
            } else {
                $avlQuantity->reserved_stock = $newReservedStocks;
                $this->ProductStocks->save($avlQuantity);
                $response = [
                    'status' => 'success',
                    'currentQuantity' => $currentQuantity
                ];
            }
        } else {
            $response = [
                'status' => 'error',
                'currentQuantity' => $currentQuantity
            ];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }
}
