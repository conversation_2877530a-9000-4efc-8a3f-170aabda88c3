<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Datasource\EntityInterface;
use Cake\Database\Expression\IdentifierExpression;

/**
 * Warehouses Model
 *
 * @property \App\Model\Table\CitiesTable&\Cake\ORM\Association\BelongsTo $Cities
 * @property \App\Model\Table\MunicipalitiesTable&\Cake\ORM\Association\BelongsTo $Municipalities
 * @property \App\Model\Table\UsersTable&\Cake\ORM\Association\BelongsTo $Users
 * @property \App\Model\Table\InventoriesTable&\Cake\ORM\Association\HasMany $Inventories
 * @property \App\Model\Table\ShipmentsTable&\Cake\ORM\Association\HasMany $Shipments
 * @property \App\Model\Table\WarehouseStocksTable&\Cake\ORM\Association\HasMany $WarehouseStocks
 *
 * @method \App\Model\Entity\Warehouse newEmptyEntity()
 * @method \App\Model\Entity\Warehouse newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Warehouse> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Warehouse get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Warehouse findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Warehouse patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Warehouse> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Warehouse|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Warehouse saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Warehouse>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Warehouse>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Warehouse>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Warehouse> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Warehouse>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Warehouse>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Warehouse>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Warehouse> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class WarehousesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('warehouses');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Cities', [
            'foreignKey' => 'city_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Municipalities', [
            'foreignKey' => 'municipality_id',
        ]);
        $this->belongsTo('Managers', [
            'className' => 'Users',
            'foreignKey' => 'manager_id',
            'joinType' => 'INNER',
        ]);

        $this->belongsTo('Assistants', [
            'className' => 'Users',
            'foreignKey' => 'assistant_id',
            'joinType' => 'LEFT',
        ]);

        $this->hasMany('Inventories', [
            'foreignKey' => 'warehouse_id',
        ]);
        $this->hasMany('Shipments', [
            'foreignKey' => 'warehouse_id',
        ]);
        $this->hasMany('ProductStocks', [
            'foreignKey' => 'warehouse_id',
        ]);
        $this->hasMany('PurchaseOrderRequest', [
            'foreignKey' => 'delivery_address',
        ]);
        $this->hasMany('StockRequests', [
            'foreignKey' => 'warehouse_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->email('email')
            ->allowEmptyString('email');

        $validator
            ->scalar('warehouse_no_area')
            ->maxLength('warehouse_no_area', 255)
            ->requirePresence('warehouse_no_area', 'create')
            ->notEmptyString('warehouse_no_area');

        $validator
            ->scalar('address_line1')
            ->maxLength('address_line1', 255)
            ->requirePresence('address_line1', 'create')
            ->notEmptyString('address_line1');

        $validator
            ->nonNegativeInteger('city_id')
            ->notEmptyString('city_id');

        $validator
            ->integer('municipality_id')
            ->allowEmptyString('municipality_id');

        // $validator
        //     ->scalar('country_code')
        //     ->maxLength('country_code', 255)
        //     ->requirePresence('country_code', 'create')
        //     ->notEmptyString('country_code');

        // $validator
        //     ->scalar('warehouse_phone_no')
        //     ->maxLength('warehouse_phone_no', 255)
        //     ->requirePresence('warehouse_phone_no', 'create')
        //     ->notEmptyString('warehouse_phone_no');

        $validator
            ->scalar('location')
            ->maxLength('location', 255)
            ->allowEmptyString('location');

        $validator
            ->nonNegativeInteger('manager_id')
            ->notEmptyString('manager_id');

        $validator
            ->nonNegativeInteger('assistant_id')
            ->allowEmptyString('assistant_id');

        $validator
            ->decimal('capacity')
            ->allowEmptyString('capacity');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['city_id'], 'Cities'), ['errorField' => 'city_id']);
        $rules->add($rules->existsIn(['municipality_id'], 'Municipalities'), ['errorField' => 'municipality_id']);
        $rules->add($rules->existsIn(['manager_id'], 'Managers'), ['errorField' => 'manager_id']);
        $rules->add($rules->existsIn(['assistant_id'], 'Assistants'),['errorField' => 'assistant_id']);

        return $rules;
    }

    public function getWarehouses()
    {

        $query = $this->find('all')
            ->select(['id', 'name'])
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC']);

        $warehouses = $query->toArray();


        foreach ($warehouses as $warehouse) {

            $warehouseTreeArray[$warehouse->id] = $warehouse->name;
        }

        return $warehouseTreeArray;
    }

    public function delete(EntityInterface $warehouse, array $options = []): bool
    {
        $warehouse->status = 'D';
        if ($this->save($warehouse)) {
            return true;
        }
        return false;
    }

    //Z all warehouses details
    public function allWarehouses()
    {
        $warehouses = $this->find()->select(['Warehouses.id', 'Warehouses.name', 'Cities.city_name', 'Warehouses.warehouse_no_area', 'Warehouses.address_line1', 'Warehouses.warehouse_phone_no', 'Warehouses.capacity', 'Warehouses.email', 'Warehouses.country_code'])->contain(['Cities'])->where(['Warehouses.status' => 'A'])->order(['Warehouses.name' => 'ASC'])->toArray();
        return $warehouses;
    }

    //z
    public function warehouseStocks($warehouseId)
    {
        return $this->ProductStocks->find()
            ->select([
                'ProductStocks.id',
                'ProductStocks.product_attribute_id',
                'ProductStocks.quantity',
                'ProductStocks.reserved_stock',
                'ProductStocks.defective_stock',
                'ProductStocks.service_center_stock',
                'ProductStocks.purchased_stock',
                'ShowroomWarehouse' => $this->ProductStocks->find()->func()->coalesce(['Showrooms.name', 'Warehouses.name']),
                'Products.name',
                'Products.reference_name',
                'ProductVariants.variant_name',
                'SKU' => $this->ProductStocks->find()->func()->coalesce([
                    new IdentifierExpression('ProductVariants.sku'),
                    new IdentifierExpression('Products.sku')
                ]),
                'Attributes.name',
                'AttributeValues.value'
            ])
            ->contain([
                'Warehouses' => ['fields' => ['Warehouses.id', 'Warehouses.name']],
                'Products' => ['fields' => ['Products.id', 'Products.name', 'Products.purchase_price', 'Products.sku']],
                'ProductVariants' => ['fields' => ['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.purchase_price', 'ProductVariants.sku']],
                'ProductAttributes' => function ($q) {
                    return $q->select([
                        'ProductAttributes.id',
                        'ProductAttributes.product_id',
                        'ProductAttributes.attribute_id',
                        'ProductAttributes.attribute_value_id'
                    ])
                        ->leftJoinWith('Attributes') // Ensures attributes are fetched when available
                        ->leftJoinWith('AttributeValues'); // Ensures attribute values are fetched when available
                }
            ])
            ->where(['ProductStocks.warehouse_id' => $warehouseId])
            ->order(['ProductStocks.id' => 'DESC']);
    }

    //Get warehouse by manager
    public function getWarehousesByManagerId(int $managerId)
    {
        return $this->find()
            ->where(['Warehouses.manager_id' => $managerId])
            ->first();
    }
}
