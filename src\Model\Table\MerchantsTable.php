<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Database\Expression\QueryExpression;

/**
 * Merchants Model
 *
 * @property \App\Model\Table\UsersTable&\Cake\ORM\Association\BelongsTo $Users
 * @property \App\Model\Table\StatesTable&\Cake\ORM\Association\BelongsTo $States
 * @property \App\Model\Table\CitiesTable&\Cake\ORM\Association\BelongsTo $Cities
 * @property \App\Model\Table\BrandsTable&\Cake\ORM\Association\HasMany $Brands
 * @property \App\Model\Table\MerchantDocumentsTable&\Cake\ORM\Association\HasMany $MerchantDocuments
 * @property \App\Model\Table\MerchantOrdersTable&\Cake\ORM\Association\HasMany $MerchantOrders
 * @property \App\Model\Table\MerchantSettlementsTable&\Cake\ORM\Association\HasMany $MerchantSettlements
 * @property \App\Model\Table\OffersTable&\Cake\ORM\Association\HasMany $Offers
 * @property \App\Model\Table\OrdersTable&\Cake\ORM\Association\HasMany $Orders
 * @property \App\Model\Table\ProductDealsTable&\Cake\ORM\Association\HasMany $ProductDeals
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\HasMany $Products
 * @property \App\Model\Table\SuppliersTable&\Cake\ORM\Association\HasMany $Suppliers
 *
 * @method \App\Model\Entity\Merchant newEmptyEntity()
 * @method \App\Model\Entity\Merchant newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Merchant> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Merchant get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Merchant findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Merchant patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Merchant> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Merchant|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Merchant saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Merchant>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Merchant>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Merchant>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Merchant> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Merchant>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Merchant>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Merchant>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Merchant> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class MerchantsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('merchants');
        $this->setDisplayField('company_name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Users', [
            'foreignKey' => 'user_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('States', [
            'foreignKey' => 'state_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Cities', [
            'foreignKey' => 'city_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Categories', [
            'foreignKey' => 'category_id',
            'joinType' => 'LEFT',
        ]);
        $this->hasMany('Brands', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('MerchantDocuments', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('MerchantOrders', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('MerchantSettlements', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('MerchantRatings', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('Offers', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('Orders', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('ProductDeals', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('Products', [
            'foreignKey' => 'merchant_id',
        ]);
        $this->hasMany('Suppliers', [
            'foreignKey' => 'merchant_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('user_id')
            ->notEmptyString('user_id');

        $validator
            ->scalar('business_type')
            ->maxLength('business_type', 50)
            ->allowEmptyString('business_type');

        $validator
            ->scalar('url_key')
            ->maxLength('url_key', 100)
            ->allowEmptyString('url_key');

        $validator
            ->scalar('company_name')
            ->maxLength('company_name', 255)
            ->requirePresence('company_name', 'create')
            ->notEmptyString('company_name');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('merchant_identifier')
            ->maxLength('merchant_identifier', 20)
            ->allowEmptyString('merchant_identifier');

        $validator
            ->decimal('commission_rate')
            ->allowEmptyString('commission_rate');

        $validator
            ->scalar('merchant_logo')
            ->maxLength('merchant_logo', 255)
            ->allowEmptyString('merchant_logo');

        $validator
            ->scalar('banner')
            ->maxLength('banner', 255)
            ->allowEmptyString('banner');

        $validator
            ->scalar('country_code')
            ->maxLength('country_code', 5)
            ->allowEmptyString('country_code');

        $validator
            ->scalar('phone_number')
            ->maxLength('phone_number', 20)
            ->requirePresence('phone_number', 'create')
            ->notEmptyString('phone_number');

        $validator
            ->scalar('address_line1')
            ->maxLength('address_line1', 255)
            ->requirePresence('address_line1', 'create')
            ->notEmptyString('address_line1');

        $validator
            ->scalar('address_line2')
            ->maxLength('address_line2', 255)
            ->allowEmptyString('address_line2');

        $validator
            ->scalar('country')
            ->maxLength('country', 100)
            ->allowEmptyString('country');

        $validator
            ->nonNegativeInteger('state_id')
            ->allowEmptyString('state_id');

        $validator
            ->nonNegativeInteger('city_id')
            ->notEmptyString('city_id');

        $validator
            ->scalar('zipcode')
            ->maxLength('zipcode', 20)
            ->allowEmptyString('zipcode');

        $validator
            ->scalar('category_ids')
            ->maxLength('category_ids', 255)
            ->allowEmptyString('category_ids');

        $validator
            ->scalar('tax_identification_no')
            ->maxLength('tax_identification_no', 255)
            ->allowEmptyString('tax_identification_no');

        $validator
            ->scalar('bank_acc_no')
            ->maxLength('bank_acc_no', 20)
            ->allowEmptyString('bank_acc_no');

        $validator
            ->scalar('ifsc_code')
            ->maxLength('ifsc_code', 15)
            ->allowEmptyString('ifsc_code');

        $validator
            ->scalar('pan_no')
            ->maxLength('pan_no', 20)
            ->allowEmptyString('pan_no');

        $validator
            ->scalar('business_license')
            ->maxLength('business_license', 255)
            ->allowEmptyString('business_license');

        $validator
            ->scalar('business_certificate')
            ->maxLength('business_certificate', 255)
            ->allowEmptyString('business_certificate');

        $validator
            ->scalar('approval_status')
            ->notEmptyString('approval_status');

        $validator
            ->scalar('reason')
            ->allowEmptyString('reason');

        $validator
            ->scalar('admin_note')
            ->allowEmptyString('admin_note');

        $validator
            ->scalar('verification_status')
            ->notEmptyString('verification_status');

        $validator
            ->notEmptyString('document_submitted');

        $validator
            ->date('approval_date')
            ->allowEmptyDate('approval_date');

        $validator
            ->notEmptyString('confirmation_email_sent');

        $validator
            ->scalar('meta_title')
            ->maxLength('meta_title', 500)
            ->allowEmptyString('meta_title');

        $validator
            ->scalar('meta_description')
            ->maxLength('meta_description', 500)
            ->allowEmptyString('meta_description');

        $validator
            ->scalar('meta_keyword')
            ->maxLength('meta_keyword', 500)
            ->allowEmptyString('meta_keyword');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['user_id'], 'Users'), ['errorField' => 'user_id']);
        //$rules->add($rules->existsIn(['state_id'], 'States'), ['errorField' => 'state_id']);
        $rules->add($rules->existsIn(['city_id'], 'Cities'), ['errorField' => 'city_id']);

        return $rules;
    }

    //S
    public function sellerList($filter_city, $search_str, $page, $limit)
    {

        // Build the query with pagination
        $sellerQuery = $this->find()
            ->select([
                'Merchants.id',
                'Merchants.user_id',
                'Merchants.business_type',
                'Merchants.company_name',
                'Merchants.description',
                'Merchants.merchant_logo',
                'Merchants.address_line1',
                'Merchants.address_line2',
                'Merchants.country',
                'Merchants.zipcode',
                'Users.first_name',
                'Users.last_name'  
            ])
            ->contain([
                'Cities' => function ($q1) {
                    return $q1->select(['Cities.id', 'Cities.city_name']);
                }               
            ])
            ->join([
                'Users' => [
                    'table' => 'users',
                    'type' => 'INNER',
                    'conditions' => 'Merchants.user_id = Users.id'
                ]
            ])            
            ->where([
                'Merchants.status' => 'A',
                'Merchants.approval_status' => 'Approved'              
            ])
            ->order(['Merchants.company_name' => 'Asc'])
            ->page($page, $limit); // Pagination here

        // Apply filters if present
        if (!empty($filter_city)) {
            $sellerQuery->where(['Merchants.city_id' => $filter_city]);
        }

        if (!empty($search_str)) {
            $sellerQuery->where(function (QueryExpression $exp) use ($search_str) {
                return $exp->or_([
                    'Users.first_name LIKE' => '%' . $search_str . '%',
                    'Users.last_name LIKE' => '%' . $search_str . '%',
                    'Merchants.company_name LIKE' => '%' . $search_str . '%'
                ]);
            });
        }
        // Execute the query
        $sellers = $sellerQuery->all();
        return $sellers->toArray();
    }

    /**
     * Get top sellers ranked by total sales and average merchant rating.
     * Falls back to default listing if no signals exist upstream.
     */
    public function getTopSellers(int $limit = 20): array
    {
        $q = $this->find()
            ->select([
                'Merchants.id',
                'Merchants.user_id',
                'Merchants.business_type',
                'Merchants.company_name',
                'Merchants.description',
                'Merchants.merchant_logo',
                'Merchants.banner',
                'Merchants.address_line1',
                'Merchants.address_line2',
                'Merchants.country',
                'Merchants.zipcode',
                'Users.first_name',
                'Users.last_name',
                // Aggregates
                'total_sales' => $this->find()->func()->count('DISTINCT Orders.id'),
                'avg_rating' => $this->find()->func()->avg('MerchantRatings.rating')
            ])
            ->join([
                'Users' => [
                    'table' => 'users',
                    'type' => 'INNER',
                    'conditions' => 'Merchants.user_id = Users.id'
                ]
            ])
            ->contain([
                'Cities' => function ($q1) {
                    return $q1->select(['Cities.id', 'Cities.city_name']);
                }
            ])
            ->leftJoinWith('Orders')
            ->leftJoinWith('MerchantRatings')
            ->where([
                'Merchants.status' => 'A',
                'Merchants.approval_status' => 'Approved'
            ])
            ->group(['Merchants.id'])
            ->order([
                'total_sales' => 'DESC',
                'avg_rating' => 'DESC',
                'Merchants.company_name' => 'ASC'
            ])
            ->limit($limit);

        return $q->toArray();
    }

    public function getCategories()
    {
        return [
            'franchisee' => 'Franchisee',
            'retailer' => 'Retailer',
            'individual_seller' => 'Individual Seller'
        ];
    }

    /**
     * Generate merchant code
     *
     * @return string
     */
    public function generateMerchantCode()
    {
        $lastMerchant = $this->find()->order(['id' => 'DESC'])->first();
        $nextId = $lastMerchant ? $lastMerchant->id + 1 : 1;
        return 'SL-' . str_pad((string)$nextId, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Before save callback
     *
     * @param \Cake\Event\EventInterface $event
     * @param \Cake\Datasource\EntityInterface $entity
     * @param \ArrayObject $options
     * @return void
     */
    public function beforeSave(\Cake\Event\EventInterface $event, \Cake\Datasource\EntityInterface $entity, \ArrayObject $options)
    {
        // Generate merchant_identifier if not set
        if ($entity->isNew() && empty($entity->merchant_identifier)) {
            $entity->merchant_identifier = $this->generateMerchantCode();
        }
    }

}
