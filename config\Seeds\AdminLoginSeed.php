<?php
declare(strict_types=1);

use Migrations\BaseSeed;
use Authentication\PasswordHasher\DefaultPasswordHasher;

/**
 * AdminLoginSeed seed.
 */
class AdminLoginSeed extends BaseSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeds is available here:
     * https://book.cakephp.org/migrations/4/en/seeding.html
     *
     * @return void
     */
    public function run(): void
    {
        $data = [
            'first_name'  => 'Babiken',
            'last_name'   => 'Admin',
            'email'       => '<EMAIL>',
            'password'    => (new DefaultPasswordHasher())->hash('Admin@123'),
            'country_code'=> '225',
            'mobile_no'   => '8054244064',        
            'user_type'   => 'Admin',
            'role_id'     =>  1
        ];

        $table = $this->table('users');
        $table->insert($data)->save();
    }
}
