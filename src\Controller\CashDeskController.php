<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class CashDeskController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $OrderReturns;
    protected $Suppliers;
    protected $Showrooms;
    protected $Users;
    protected $Roles;
    protected $SupplierPayment;
    protected $Orders;
    protected $CashHandovers;
    protected $CashDeskClosures;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Suppliers = $this->fetchTable('Suppliers');
        $this->Users = $this->fetchTable('Users');
        $this->Roles = $this->fetchTable('Roles');
        $this->SupplierPayment = $this->fetchTable('SupplierPayment');
        $this->Orders = $this->fetchTable('Orders');
        $this->CashHandovers = $this->fetchTable('CashHandovers');
        $this->CashDeskClosures = $this->fetchTable('CashDeskClosures');
    }

    public function index()
    {
        $requested_user = $this->Authentication->getIdentity();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');

        $suppliers = $this->Suppliers->find()
            ->select(['Suppliers.id', 'Suppliers.name'])
            ->where(['Suppliers.status IN' => ['A', 'I']])
            ->order(['Suppliers.name' => 'ASC'])
            ->toArray();

        $showrooms = [];
        $conditions = [];

        if ($requested_user) {
            $role = $this->Roles->get($requested_user->role_id);

            // Role-based showroom access
            if (strtolower($role->name) === 'showroom supervisor') {
                $showrooms = $this->Showrooms->find()
                    ->where([
                        'Showrooms.showroom_supervisor' => $requested_user->id,
                        'Showrooms.status' => 'A'
                    ])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->all() // Fetch results
                    ->extract('id') // Extract IDs
                    ->toList(); // Convert to an array


                if (!empty($supervisorShowrooms)) {
                    $conditions['SupplierPayment.showroom_id IN'] = $supervisorShowrooms;
                }

            } elseif (strtolower($role->name) === 'showroom manager') {
                $showrooms = $this->Showrooms->find()
                    ->where([
                        'Showrooms.showroom_manager' => $requested_user->id,
                        'Showrooms.status' => 'A'
                    ])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $requested_user->id])
                    ->first();

                if ($managerShowroom) {
                    
                    $managerShowroomId = $managerShowroom->id;

                    $conditions['SupplierPayment.showroom_id'] = $managerShowroomId;
                }

            } else {
                $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();
            }
        }

        $supplier_payment = $this->SupplierPayment->find()
            ->where($conditions)
            ->contain(['Showrooms', 'Suppliers', 'SupplierPurchaseOrders'])
            ->order(['SupplierPayment.created' => 'DESC'])
            ->toArray();

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('suppliers', 'showrooms', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'requested_user', 'role', 'supplier_payment', 'dateFormat', 'timeFormat'));


    }

    // public function cashDesk()
    // {
    //     $this->request->allowMethod(['post']);
    //     $this->autoRender = false;

    //     try {
    //         $identity = $this->request->getAttribute('identity');
    //         if (!$identity) {
    //             throw new \Exception(__('User not authenticated.'));
    //         }

    //         $roleId  = $identity->get('role_id');
    //         $userId  = $identity->get('id');

    //         // Load role name
    //         $role     = $this->Users->Roles->get($roleId);
    //         $roleName = $role->name;

    //         $data = $this->request->getData();
    //         $showroom_id = $data['showroom_id'] ?? null;
    //         if (empty($showroom_id)) {
    //             throw new \Exception(__('Showroom ID is required.'));
    //         }

    //         // Roles we consider sales roles
    //         $roles = $this->Roles->find()
    //             ->where(['name IN' => ['Showroom Manager', 'Sales Person'], 'status' => 'A'])
    //             ->all()
    //             ->toArray();
    //         $all_roles = array_column($roles, 'id');

    //         // Initialize
    //         $cash_handovers = [];
    //         $sales_data     = [];
    //         $today_sales    = 0;
    //         $total_paid     = 0;
    //         $total_cash     = 0;
    //         $available_cash = 0;
    //         $cash_desk_close_amount = 0;

    //         if ($roleName === 'Showroom Manager') {
    //             // Manager’s own sales
    //             $manager_sales = $this->Orders->getManagerSales($showroom_id, $userId);

    //             // Salespersons’ sales
    //             $sales_data = $this->Orders->salesPersonOrder($showroom_id);
    //             foreach ($sales_data as $sale) {
    //                 $today_sales += $sale['total_amount'];
    //             }

    //             $total_cash = $manager_sales + $today_sales;

    //             // Handover cash already given by manager
    //             $handoverCashObj = $this->CashHandovers->handoverByManager($userId);
    //             $handoverCash    = $handoverCashObj->total ?? 0;

    //             $supplier_payment_data = $this->SupplierPayment->getSupplierPayments([$showroom_id]);
    //             foreach ($supplier_payment_data as $payment) {
    //                 $total_paid += $payment['amount'];
    //             }

    //             $cash_desk_close_amount = $total_cash - $total_paid - $handoverCash;
    //             $available_cash         = $cash_desk_close_amount;

    //         } elseif ($roleName === 'Showroom Supervisor') {
    //             // Cash handovers received
    //             $cash_handovers = $this->CashHandovers->cashHandoverDetail($userId, $showroom_id);
    //             foreach ($cash_handovers as $handover) {
    //                 $total_cash += $handover['amount'];
    //             }

    //             $supplierShowroom = $this->Showrooms->listShowroomByUser($userId, 'Showroom Supervisor');
    //             $showroomIds = array_column($supplierShowroom, 'id');

    //             $supplier_payment_data = $this->SupplierPayment->getSupplierPayments($showroomIds);
    //             foreach ($supplier_payment_data as $payment) {
    //                 $total_paid += $payment['amount'];
    //             }

    //             $cash_desk_close_amount = $total_cash - $total_paid;
    //             $available_cash         = $cash_desk_close_amount;
    //         }

    //         // Last cash desk close
    //         $cash_desk_close = $this->CashDeskClosures->find()
    //             ->select(['closing_time'])
    //             ->where(['closed_by' => $userId])
    //             ->order(['closing_time' => 'DESC'])
    //             ->first();
    //         $last_cash_desk_close = $cash_desk_close
    //             ? $cash_desk_close->closing_time->format('Y-m-d H:i')
    //             : __('N/A');

    //         // Currency format
    //         $currencyConfig    = Configure::read('Settings.Currency.format');
    //         $currencySymbol    = $currencyConfig['currency_symbol'] ?? 'FCFA';
    //         $thousandSeparator = $currencyConfig['thousand_separator'] ?? ',';

    //         $result = [
    //             'status' => 'success',
    //             'data'   => [
    //                 'cash_handovers'         => $cash_handovers,
    //                 'sales_data'             => $sales_data,
    //                 'today_sales'            => number_format((float)$today_sales, 0, '', $thousandSeparator),
    //                 'total_paid'             => number_format((float)$total_paid, 0, '', $thousandSeparator) . ' ' . $currencySymbol,
    //                 'available_cash'         => number_format((float)$available_cash, 0, '', $thousandSeparator) . ' ' . $currencySymbol,
    //                 'cash_desk_close_amount' => number_format((float)$cash_desk_close_amount, 0, '', $thousandSeparator) . ' ' . $currencySymbol,
    //                 'last_cash_desk_close'   => $last_cash_desk_close
    //             ]
    //         ];
    //     } catch (\Exception $e) {
    //         $this->log('CashDesk Error: ' . $e->getMessage(), 'error');
    //         $result = ['status' => 'error', 'message' => $e->getMessage()];
    //     }

    //     return $this->response
    //         ->withType('application/json')
    //         ->withStringBody(json_encode($result));
    // }

    public function cashDesk()
    {
        $this->request->allowMethod(['post']); // Only allow POST
        $this->autoRender = false; // No template rendering
        $this->viewBuilder()->setClassName('Json'); // Force JSON response

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        try {
            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                throw new \Exception(__('User not authenticated.'));
            }

            $roleId  = $identity->get('role_id');
            $userId  = $identity->get('id');

            // Load the role name based on the role_id
            $role = $this->Users->Roles->get($roleId);
            $roleName = $role->name;

            // Allowed roles
            $roles = $this->Roles->find()
                ->where([
                    'name IN' => ['Showroom Manager', 'Sales Person'],
                    'status' => 'A'
                ])
                ->all()
                ->toArray();

            $all_roles = array_column($roles, 'id');

            $cash_handovers = [];
            $sales_data = [];
            $today_sales = 0;
            $total_paid = 0;
            $total_cash = 0;

            $data = $this->request->getData();
            $showroom_id = $data['showroom_id'] ?? null;

            if (!$showroom_id) {
                throw new \Exception(__('Showroom ID is required.'));
            }

            if ($roleName === 'Showroom Manager') {
                // Sales order
                $sales_data = $this->Orders->salesPersonOrder($showroom_id);
                $manager_sales = $this->Orders->getManagerSales($showroom_id, $userId);

                foreach ($sales_data as $value1) {
                    $today_sales += $value1['total_amount'];
                }

                $total_cash = number_format(($manager_sales + $today_sales), 2, '.', '');
                $showroomIds = $showroom_id;

            } elseif ($roleName === 'Showroom Supervisor') {
                // Cash handovers
                $cash_handovers = $this->CashHandovers->cashHandoverDetail($userId, $showroom_id);
                $today_cash = 0;
                foreach ($cash_handovers as $value3) {
                    $today_cash += $value3['amount'];
                }

                $total_cash = number_format($today_cash, 2, '.', '');
                $supplierShowroom = $this->Showrooms->listShowroomByUser($userId, 'Showroom Supervisor');
                $showroomIds = array_column($supplierShowroom, 'id');
            }

            // Supplier payments
            $supplier_payment_data = $this->SupplierPayment->getSupplierPayments($userId);
            if ($supplier_payment_data) {
                foreach ($supplier_payment_data as $value2) {
                    $total_paid += $value2['amount'];
                }
            }
            $total_paid = number_format($total_paid ?? 0, 2, '.', '');

            // Cash desk close logic
            $cash_desk_close_amount = 0;
            if ($roleName === 'Showroom Manager') {
                $handoverCashObj = $this->CashHandovers->handoverByManager($userId);
                $handoverCash = $handoverCashObj->total ?? 0;

                $cash_desk_close_amount = $total_cash - $total_paid - $handoverCash;
                $available_cash = $cash_desk_close_amount;
            } else {
                $todayClosure = 0; // Can add actual query if required

                if ($todayClosure) {
                    $available_cash = $total_cash - $total_paid - $todayClosure->total_cash;
                } else {
                    $available_cash = $total_cash - $total_paid;
                }
            }

            // Last cash desk close
            $cash_desk_close = $this->CashDeskClosures->find()
                ->select(['closing_time'])
                ->where(['closed_by' => $userId])
                ->order(['closing_time' => 'DESC'])
                ->first();

            $last_cash_desk_close = $cash_desk_close->closing_time ?? null;

            // Success response
            $result = [
                'status' => 'success',
                'data' => [
                    'cash_handovers' => $cash_handovers,
                    'sales_data' => $sales_data,
                    'supplier_payment_data' => $supplier_payment_data,
                    'today_sales' => $today_sales,
                    'total_paid' => $total_paid,
                    // 'available_cash' => $available_cash,
                    'available_cash' => number_format((float)$available_cash, 0, '', $thousandSeparator).' '.$currencySymbol,
                    'cash_desk_close_amount' => $cash_desk_close_amount,
                    'last_cash_desk_close' => $last_cash_desk_close
                ]
            ];
        } catch (\Exception $e) {
            // Error response
            $result = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
            $this->response = $this->response->withStatus(400);
        }

        return $this->response
        ->withType('application/json')
        ->withStringBody(json_encode($result));
    }

    public function cashDeskClose()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        try {
            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                throw new \Exception(__('User not authenticated.'));
            }

            $roleId = $identity->get('role_id');
            $userId = $identity->get('id');
            $role = $this->Users->Roles->get($roleId);
            $roleName = $role->name;

            $data = $this->request->getData();
            $data['closed_by'] = $userId;

            if (empty($data['showroom_id'])) {
                throw new \Exception(__('Showroom ID is required.'));
            }

            $showroom = $this->Showrooms->showroomDetailById($data['showroom_id']);
            if (!$showroom) {
                throw new \Exception(__('Showroom not found.'));
            }

            // 🔹 Get Opening Balance from previous day’s closing
            $previousClosure = $this->CashDeskClosures->find()
                ->where([
                    'showroom_id' => $data['showroom_id'],
                    'closed_by' => $userId,
                    'DATE(closing_time) <' => date('Y-m-d')
                ])
                ->order(['closing_time' => 'DESC'])
                ->first();

            $opening_balance = $previousClosure ? $previousClosure->closing_balance : 0;
            $data['opening_balance'] = $opening_balance;

            // 🔹 Calculate closing balance (carry forward if partial handover)
            $data['closing_balance'] = $opening_balance + $data['available_cash'] - $data['total_cash'];

            /*
             * available_cash  : Calculated by system (expected amount manager should have)
             * total_cash      : Actual handover by manager to supervisor
             * closing_balance : Carried forward balance (available_cash - total_cash + opening_balance)
             */

            // 🔹 Mark status
            if ($data['total_cash'] == $data['available_cash']) {
                $data['status'] = 'closed'; // fully settled
            } else {
                $data['status'] = 'open'; // partial, balance carried forward
            }

            if ($roleName == 'Showroom Manager') {
                // Save Cash Desk Close
                $cash_desk_close = $this->CashDeskClosures->cashDeskClose($data);

                // Also record Cash Handover if manager handed cash
                if (!empty($data['total_cash'])) {
                    $cash_handover = $this->CashHandovers->newEmptyEntity();
                    $cash_handover['showroom_manager'] = $userId;
                    $cash_handover['handed_to_supervisor'] = $showroom['showroom_supervisor'];
                    $cash_handover['amount'] = $data['total_cash'];
                    $cash_handover['handover_date'] = date('Y-m-d H:i:s');
                    $cash_handover['showroom_id'] = $data['showroom_id'];
                    // $cash_handover['remarks'] = $data['remarks'] ?? null;
                    $this->CashHandovers->save($cash_handover);
                }
            } elseif ($roleName == 'Showroom Supervisor') {
                // Supervisors just close without handover
                $cash_desk_close = $this->CashDeskClosures->cashDeskClose($data);
            } else {
                throw new \Exception(__('Only Showroom Manager or Supervisor can close cash desk.'));
            }

            if (!empty($cash_desk_close)) {
                $result = ['status' => 'success', 'message' => __('Cash desk closed successfully.')];
                return $this->response
                    ->withType('application/json')
                    ->withStringBody(json_encode($result))
                    ->withStatus(200);
            } else {
                throw new \Exception(__('Unable to close, Please try again.'));
            }

        } catch (\Exception $e) {
            $this->log("CashDeskClose Error: " . $e->getMessage(), 'error');
            $result = ['status' => 'error', 'message' => $e->getMessage()];
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode($result))
                ->withStatus(400);
        }
    }

    
}
