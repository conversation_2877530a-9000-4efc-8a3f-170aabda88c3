.carousel-container{
    position: relative;
}
.carousel-container .carousel-wrapper{
    position: relative;
    overflow: hidden;
    width: 100%;
}
.carousel-container .carousel-wrapper .carousel{
    overflow: hidden;
    width: 100%;
}
.carousel-container .carousel-wrapper .carousel button.prev{
    position: absolute;
    top: 50%;
    left: 6%;
    border-radius: 50%;
    border: 1px solid #F9DAB9;
    height: 26px;
}
.carousel-container .carousel-wrapper .carousel button.next{
    position: absolute;
    top: 50%;
    right: 6%;
    border-radius: 50%;
    border: 1px solid #F9DAB9;
    height: 26px;
}
.carousel-container .carousel-wrapper .carousel .carousel-inner-class{
    display: flex;
    transition: transform 0.4s ease-in-out;
    padding-left: 15px;
    padding-right: 15px;
}
.carousel-container .carousel-wrapper#middleBannerCarousel .carousel .carousel-inner-class{
    padding-left: 0px;
    padding-right: 0px;
}
.carousel-container p.carousel-head{
    font-size: 16px;
    font-weight: 600;
    padding-left: 15px;
    padding-right: 15px;
}
.carousel-container .carousel-controls{
    position: absolute;
    top: 0;
    right: 15px;
}
.carousel-container .carousel-controls .carousel-button{
    border-radius: 50%;
    border: 1px solid #F9DAB9;
    height: 26px;
}
.carousel-container .carousel-controls .carousel-button.left-btn{
    margin-right: 8px;
}
.carousel-container .carousel-wrapper .carousel .carousel-inner-class .landscape-images{
    position: relative;
    margin-right: 0px;
    border-radius: 29px;
    box-shadow: none;
    padding: 0px 0px;
    flex: 0 0 100%;
    max-width: 100%;
    margin: 20px 0px 15px;
    margin-right: 0px;
}
.carousel-container .carousel-wrapper .carousel .carousel-inner-class .landscape-images .landscape-images-img{
    width: 100%;
    height: 327px;
    object-fit: cover;
}
.carousel-container #brandCarousel{
    padding:0px 0px;
}
.carousel-container #middleBannerCarousel{
    padding:0px 0px;
}
.carousel-container #dealDayCarousel{
    padding:0px 0px;
}
.carousel-container .carousel-wrapper .carousel .brand-carousel{
    padding-left:0;
    background-color: #fff;
}
.carousel-container .carousel-wrapper .carousel .brand-carousel a{
    position: relative;
    margin-right: 0px;
    border-radius: 29px;
    box-shadow: none;
    padding: 15px;
    flex: 0 0 calc(33% - 0px);
    max-width: calc(33% - 0px);
    margin: 15px 0px 15px;
}
.carousel-container .carousel-wrapper .carousel .brand-carousel a > img{
    width: 92%;
    height: 103px;
    object-fit: cover;
}
.carousel-container .carousel-wrapper .carousel .carousel-inner-class .card{
    flex: 0 0 calc(100% - 0px);
    max-width: calc(100% - 0px);
}
/* Card style */
.card{
    position: relative;
    margin-right: 0px;
    border-radius: 29px;
    box-shadow: 0px 5px 13px #aaa;
    width: 23%;
    padding: 15px;
    margin: 15px 10px 15px;
}
.card:first-child{
    margin-left: 0;
}
.card:last-child{
    margin-right: 0;
}
.card a.card-anchor{
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
}
.card .card-wishlist{
    position: absolute;
    right: 15px;
    top: 15px;
}
.card .card-body .card-image img{
    width: 100%;
    height: 270px;
    object-fit: cover;
}
.card .card-body .card-name .product-name{
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
    text-decoration: none;
}
.card .card-body .card-rating{
    color: #06C270;
    margin-bottom: 14px;
}
.card .card-body .card-rating .card-star{
    color: #000;
}
.card .card-body .card-reference {
    height: 32px;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    margin-top: 15px;
    margin-bottom: 5px;
}
.card .card-body .card-price {
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    margin-top: 10px;
    margin-bottom: 0px;
    color: #ff3b3b;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.card .card-body .card-price .add-to-cart {
    background-color: #0D839B;
    border: 1px solid #0D839B;
    border-radius: 20px;
    height: 41px;
    color: #fff;
    font-size: 11px;
    font-weight: 600;
    padding-left: 12px;
    padding-right: 15px;
}
.card .card-body .card-price .add-to-cart .card-cart-icon {
    margin-right: 5px;
    font-size: 14px;
}
.card .card-body .card-offer .strike-price {
    color: rgba(0,0,0,.3);
    font-size: 12px;
    font-weight: 400;
    text-decoration: line-through;
    margin-bottom: 4px;
}
.card .card-body .card-offer .price-offer {
    background-color: #06C270;
    color: #fff;
    font-size: 12px;
    font-weight: 400;
    display: inline-block;
    border-radius: 5px;
    margin-bottom: 4px;
    padding: 6px 10px;
    position: absolute;
    top: 15px;
    left: 15px;
}
@media screen and (min-width: 576px) {
    .carousel-container .carousel-wrapper .carousel .carousel-inner-class{
        display: flex;
        align-items: flex-start;
        /* flex-wrap: wrap; */
        padding-left: 15px;
        padding-right: 15px;
        box-sizing: border-box;
        gap: 0px;
    }
    .carousel-container p.carousel-head{
        font-size: 20px;
        font-weight: 600;
    }
    .carousel-container .carousel-wrapper{
        position: relative;
        overflow: hidden;
        width: 100%;
        padding: 0 15px;
    }
    .carousel-container .carousel-wrapper .carousel{
        overflow:hidden;
        width: 100%;
    }
    .carousel-container .carousel-controls{
        position: absolute;
        top: 0;
        right: 15px;
    }
    .carousel-container .carousel-wrapper .carousel .carousel-inner-class .card{
        flex: 0 0 calc(50% - 8px);
        width: calc(50% - 8px);
    }
    .carousel-container .carousel-wrapper#dealDayCarousel .carousel .carousel-inner-class .card {
        /* flex: 0 0 calc(50% - 0px);
        width: calc(50% - 0px); */
        flex: 0 0 calc(50% - -13px);
        width: calc(50% - -13px);
    }
    .card{
        position: relative;
        /* margin-right: 30px; */
        border-radius: 29px;
        box-shadow: 0px 5px 13px #aaa;
        width: 29%;
        padding: 15px 10px;
        margin-top: 15px;
        margin-bottom: 15px;
        box-sizing: border-box;
        margin-right: 20px;
    }
    .card:last-child{
        margin-right: 0px;
    }
    .card .card-body .card-image img {
        width: 100%;
        height: 190px;
    }
    .card .card-body .card-price {
        font-size: 13px;
        font-weight: 600;
        line-height: 22px;
        margin-top: 10px;
        margin-bottom: 0px;
        color: #ff3b3b;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .card .card-body .card-price .add-to-cart {
        background-color: #0D839B;
        border: 1px solid #0D839B;
        border-radius: 20px;
        height: 41px;
        color: #fff;
        font-size: 11px;
        font-weight: 600;
        padding-left: 7px;
        padding-right: 7px;
    }
    .card .card-body .card-price .add-to-cart .card-cart-icon {
        margin-right: 5px;
        font-size: 12px;
    }
}
@media screen and (min-width: 768px) {
   .carousel-container .carousel-wrapper .carousel .carousel-inner-class{
        display: flex;
        align-items: flex-start;
        /* flex-wrap: wrap; */
        padding-left: 15px;
        padding-right: 15px;
        box-sizing: border-box;
        gap: 24px;
    }
    .carousel-container p.carousel-head{
        font-size: 20px;
        font-weight: 600;
    }
    .carousel-container .carousel-wrapper {
        position: relative;
        overflow: hidden;
        width: 100%;
        padding: 0 30px;
    }
    .carousel-container .carousel-wrapper .carousel {
        padding-left: 0px;
        overflow: hidden;
        width: 100%;
        padding-right: 0px;
    }
    .carousel-container .carousel-wrapper .carousel .carousel-inner-class {
        display: flex;
        align-items: flex-start;
        /* flex-wrap: wrap; */
        padding-left: 15px;
        padding-right: 10px;
        box-sizing: border-box;
        gap: 0px;
    }
    .carousel-container .carousel-controls{
        position: absolute;
        top: 0;
        right: 15px;
    }
    .carousel-container .carousel-wrapper .carousel .carousel-inner-class .card{
        flex: 0 0 calc(50% - 10px);
        width: calc(50% - 10px);
    }
    .card{
        position: relative;
        border-radius: 29px;
        box-shadow: 0px 5px 13px #aaa;
        width: 29%;
        padding: 15px;
        margin-top: 15px;
        margin-bottom: 15px;
        box-sizing: border-box;
        margin-right: 9px;
    }
    .carousel-container #brandCarousel{
        padding:0px 0px;
    }
    .carousel-container .carousel-wrapper .carousel .brand-carousel{
        padding-left:0;
        background-color: #fff;
    }
    .carousel-container .carousel-wrapper .carousel .brand-carousel a{
        position: relative;
        margin-right: 0px;
        border-radius: 29px;
        box-shadow: none;
        padding: 15px;
        flex: 0 0 calc(33% - 0px);
        max-width: calc(33% - 0px);
        margin: 15px 0px 15px;
    }
    .carousel-container .carousel-wrapper .carousel .brand-carousel a > img{
        width: 92%;
        height: 103px;
        object-fit: cover;
    }
    .card:last-child{
        margin-right: 0px;
    } 
    .card .card-body .card-image img{
        width: 100%;
        height: 280px;
    }
}
@media screen and (min-width: 992px) {
    .carousel-container .carousel-wrapper .carousel .carousel-inner-class{
        display: flex;
        align-items: flex-start;
        padding-left: 11px;
        padding-right: 0px;
        box-sizing: border-box;
        gap: 0px;
    }
    .carousel-container .carousel-wrapper .carousel .carousel-inner-class.brand-carousel{
        background-color: #fff;
        padding-right: 26px;
        margin-bottom: 15px;
    }
    .carousel-container .carousel-wrapper {
        position: relative;
        overflow: hidden;
        width: 100%;
        padding: 0 8px;
    }
    .carousel-container .carousel-wrapper .carousel {
        padding-left: 0px;
        overflow: hidden;
        width: 100%;
        padding-right: 8px;
    }
    .carousel-container .carousel-wrapper .carousel .carousel-inner-class .card{
        flex: 0 0 calc(33% - 12px);
        width: calc(33% - 12px);
    }
    .card{
        position: relative;
        /* margin-right: 11px; */
        border-radius: 29px;
        box-shadow: 0px 5px 13px #aaa;
        width: 29%;
        padding: 15px;
        /* margin-top: 15px;
        margin-bottom: 15px; */
        margin: 15px 20px 15px 0;
    }
    .carousel-container .carousel-wrapper .carousel .brand-carousel a{
        position: relative;
        margin-right: 0px;
        border-radius: 29px;
        box-shadow: none;
        padding: 15px;
        flex: 0 0 calc(20% - 0px);
        max-width: calc(20% - 0px);
        margin: 15px 0px 15px;
    }
    .carousel-container .carousel-wrapper .carousel .brand-carousel a > img{
        width: 92%;
        height: 103px;
        object-fit: cover;
    }
    .card:last-child{
        margin-right: 0px;
    }
    .card a.card-anchor{
        position: absolute;
        display: block;
        width: 100%;
        height: 100%;
    }
    .card .card-wishlist{
        position: absolute;
        right: 15px;
        top: 15px;
    }
    .card .card-body .card-image img{
        width: 100%;
        height: 238px;
    }
    .card .card-body .card-name .top-deal-heading{
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 18px;
        font-size: 18px;
        font-weight: 400;
        line-height: 24px;
        text-decoration: none;
    }
    .card .card-body .card-name .top-deal-subheading{
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 18px;
        font-size: 18px;
        font-weight: 400;
        line-height: 24px;
        text-decoration: none;
    }
    .card .card-body .card-name .product-name{
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 18px;
        font-weight: 600;
        line-height: 24px;
        text-decoration: none;
    }
    .card .card-body .card-rating{
        color: #06C270;
        margin-bottom: 14px;
    }
    .card .card-body .card-rating .card-star{
        color: #000;
    }
    .card .card-body .card-reference {
        height: 32px;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        margin-top: 15px;
        margin-bottom: 5px;
    }
    .card .card-body .card-price {
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        margin-top: 10px;
        margin-bottom: 0px;
        color: #ff3b3b;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .card .card-body .card-price .add-to-cart {
        background-color: #0D839B;
        border: 1px solid #0D839B;
        border-radius: 20px;
        height: 41px;
        color: #fff;
        font-size: 11px;
        font-weight: 600;
        padding-left: 12px;
        padding-right: 15px;
    }
    .card .card-body .card-price .add-to-cart .card-cart-icon {
        margin-right: 5px;
        font-size: 14px;
    }
    .card .card-body .card-offer .strike-price {
        color: rgba(0,0,0,.3);
        font-size: 12px;
        font-weight: 400;
        text-decoration: line-through;
        margin-bottom: 4px;
    }
    .card .card-body .card-offer .price-offer {
        background-color: #06C270;
        color: #fff;
        font-size: 12px;
        font-weight: 400;
        display: inline-block;
        border-radius: 5px;
        margin-bottom: 4px;
        padding: 6px 10px;
        position: absolute;
        top: 15px;
        left: 15px;
    }
}
@media screen and (min-width: 1200px) {
    .carousel-container .carousel-wrapper .carousel .carousel-inner-class .card{
        flex: 0 0 calc(25% - 17px);
        width: calc(25% - 17px);
    }
    .card {
        position: relative;
        /* margin-right: 11px; */
        border-radius: 29px;
        box-shadow: 0px 5px 13px #aaa;
        width: 29%;
        padding: 15px;
        /* margin-top: 15px;
        margin-bottom: 15px; */
        margin: 15px 20px 15px 0;
    }
}
/*@media screen and (min-width: 1400px) {
    
} */