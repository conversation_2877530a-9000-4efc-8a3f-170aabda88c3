<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Category $category
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Catalogue") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Categories', 'action' => 'index']) ?>"><?= __("Categories") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("View") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<h6 class="m-l-10 p-t-10 p-b-10" style="color: black;"> <?= __('View Category') ?></h6>
<div>
    <div class="d-flex <?php echo ($userType != 'Merchant') ? 'justify-content-between' : '';  ?>  tab-head text-center" id="tab-buttons">
        <a href="#viewCategory" class="m-t-5 active tab-list" id="view-prod-general-id">
            <?= __('General') ?>
        </a>
        <a href="#viewAttributes" class="m-t-5 m-b-5 tab-list" id="view-prod-data-id">
            <?= __('Attributes') ?>
        </a>
        
        <?php if($userType != 'Merchant') : ?>
        <a href="#viewSEO" class="m-t-5 m-b-5 tab-list" id="view-prod-attributes-id">
            <?= __('SEO Configuration') ?>
        </a>
        <?php endif; ?>
    </div>
    <div class="tab-content">
        <div class="tab-pane active" id="viewCategory">
            <div class="row">
                <div>
                    <div class="card mb-0 pb-0" id="view-prod-card">
                        <div class="card-body">
                            <div class="form-group row">
                                <label for="name" class="col-sm-2 col-form-label fw-bold"><?= __("Category Name") ?> <span
                                        class="req">*</span></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo !empty($category->name) ? h($category->name) : '-'; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="parent_id"
                                    class="col-sm-2 col-form-label fw-bold"><?= __("Parent Category") ?></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo !empty($category->parent_category) ? h($category->parent_category->name) : '-'; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="description"
                                    class="col-sm-2 col-form-label fw-bold"><?= __("Description") ?></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo !empty($category->description) ? h($category->description) : '-'; ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="category_icon" class="col-sm-2 col-form-label fw-bold"><?= __("Category Icon") ?>
                                    <span class="req">*</span></label>
                                <div class="col-sm-5 main-field">
                                    <?php if (!empty($category->category_icon)) { ?> <img src="<?php echo $category_icon; ?>"
                                            alt="Category Icon" class="preview-img" style="max-width: 100px; max-height: 100px;" /><?php } else {
                                                                                                                                    echo "-";
                                                                                                                                } ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="web_banner" class="col-sm-2 col-form-label fw-bold"><?= __("Web Banner") ?></label>
                                <div class="col-sm-5 main-field">
                                    <?php if (!empty($category->web_banner)) { ?> <img src="<?php echo $web_banner; ?>"
                                            alt="Web Banner" class="preview-img" style="max-width: 100px; max-height: 100px;" /><?php } else {
                                                                                                                                echo "-";
                                                                                                                            } ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="mobile_banner"
                                    class="col-sm-2 col-form-label fw-bold"><?= __("Mobile Banner") ?></label>
                                <div class="col-sm-5 main-field">
                                    <?php if (!empty($category->mobile_banner)) { ?> <img src="<?php echo $mobile_banner; ?>"
                                            alt="Mobile Banner" class="preview-img" style="max-width: 100px; max-height: 100px;" /><?php } else {
                                                                                                                                    echo "-";
                                                                                                                                } ?>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="min_product_quantity"
                                    class="col-sm-2 col-form-label fw-bold"><?= __("Minimum Product Quantity") ?></label>
                                <div class="col-sm-5 main-field">
                                    <?php echo !empty($category->min_product_quantity) ? h($category->min_product_quantity) : '-'; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane section-body" id="viewAttributes">
            <div class="container-fluid p-b-10">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><?= __('Category Attributes') ?></h4>
                    </div>
                    <div id="filter-body-container">
                        <div class="input-group m-l-25">
                            <form accept-charset="utf-8" class="form-inline filter-rating attribute" id="filter-search">
                                <div class="d-flex">
                                    <div class="form-group d-flex align-items-center m-l-20">
                                        <?php echo $this->Form->control('value', [
                                            'type' => 'select',
                                            'options' => $status,
                                            'id' => 'attribute-filter',
                                            'class' => 'form-control form-select p-10',
                                            'label' => false,
                                            'empty' => __('Attribute Status'),
                                            'data' => ['bs-toggle' => 'dropdown'],
                                            'aria-expanded' => 'false'
                                        ]) ?>
                                    </div>
                                    <div class="form-group ms-4" style="margin-top:0.5%">
                                        <button type="submit" id="btnFilterAttribute" class="btn btn-primary p-10">
                                            <i class="fa fa-filter" aria-hidden="true"></i>
                                        </button>
                                        <button type="reset" id="btnResetAttribute" class="btn btn-primary p-10">
                                            <i class="fas fa-redo-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <hr />
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="tblAttributes" role="grid">
                                <thead>
                                    <tr role="row">
                                        <th><?= __('Attribute Name') ?></th>
                                        <th><?= __('Attribute Value') ?></th>
                                        <th><?= __('Status') ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($attributeList)): ?>
                                        <?php foreach ($attributeList as $attribute): ?>
                                            <tr>
                                                <td><?= h($attribute['attribute_name']) ?></td>
                                                <td>
                                                    <?= implode(', ', array_map('h', $attribute['attribute_values'])) ?>
                                                </td>
                                                <td><?= __('Active') ?></td> <!-- Assuming status is always 'Active' -->
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="3"><?= __('No attributes found.') ?></td>
                                        </tr>
                                    <?php endif; ?>


                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php if($userType != 'Merchant') : ?>
        <div class="tab-pane" id="viewSEO">
            <div class="tab-pane active" id="viewProducts">
                <div class="row">
                    <div>
                        <div class="card mb-0 pb-0" id="view-prod-card">
                            <div class="card-body">
                                <div class="form-group row">
                                    <label for="meta_title" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Title") ?></label>
                                    <div class="col-sm-5 main-field">
                                        <?php echo !empty($category->meta_title) ? h($category->meta_title) : '-'; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="meta_keyword"
                                        class="col-sm-2 col-form-label fw-bold"><?= __("Meta Keyword") ?></label>
                                    <div class="col-sm-5 main-field">
                                        <?php echo !empty($category->meta_keyword) ? h($category->meta_keyword) : '-'; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="meta_description"
                                        class="col-sm-2 col-form-label fw-bold"><?= __("Meta Description") ?></label>
                                    <div class="col-sm-5 main-field">
                                        <?php echo !empty($category->meta_description) ? h($category->meta_description) : '-'; ?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?></label>
                                    <div class="col-sm-5 main-field">
                                        <?= !empty($category->status) ? ($category->status === 'A' ? __('Active') : __('Inactive')) : '-' ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php $this->append('script'); ?>
    <script type="text/javascript">
        const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
        const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
        const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";
        const swalInvalidDimensions = "<?= addslashes(__('Image dimensions should be between')); ?>";
    </script>
    <script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
    <script src="<?= $this->Url->webroot('js/filter.js'); ?>"></script>
    <script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
    <script src="<?= $this->Url->webroot('js/images.js'); ?>"></script>
    <script src="<?= $this->Url->webroot('js/image.js'); ?>"></script>
    <script>
        $(document).ready(function() {
            $('#tab-buttons a').on('click', function(e) {
                e.preventDefault();

                $('#tab-buttons a').removeClass('active');
                $('.tab-pane').removeClass('active');

                $(this).addClass('active');
                $($(this).attr('href')).addClass('active');
            });
        });
    </script>
    <?php $this->end(); ?>