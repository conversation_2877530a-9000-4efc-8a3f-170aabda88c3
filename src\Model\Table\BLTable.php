<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * StockRequests Model
 *
 * @property \App\Model\Table\SuppliersTable&\Cake\ORM\Association\BelongsTo $Suppliers
 * @property \App\Model\Table\WarehousesTable&\Cake\ORM\Association\BelongsTo $Warehouses
 * @property \App\Model\Table\ShowroomsTable&\Cake\ORM\Association\BelongsTo $Showrooms
 * @property \App\Model\Table\StockRequestItemsTable&\Cake\ORM\Association\HasMany $StockRequestItems
 * @property \App\Model\Table\SupplierPurchaseOrdersOLDTable&\Cake\ORM\Association\HasMany $SupplierPurchaseOrdersOLD
 *
 * @method \App\Model\Entity\StockRequest newEmptyEntity()
 * @method \App\Model\Entity\StockRequest newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\StockRequest> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\StockRequest get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\StockRequest findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\StockRequest patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\StockRequest> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\StockRequest|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\StockRequest saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\StockRequest>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\StockRequest>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\StockRequest>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\StockRequest> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\StockRequest>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\StockRequest>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\StockRequest>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\StockRequest> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class BLTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('bl');
        $this->setDisplayField('bl_no');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        // $this->belongsTo('Users', [
        //     'foreignKey' => 'requested_by',
        // ]);
        $this->belongsTo('Suppliers', [
            'foreignKey' => 'supplier_id',
        ]);
        $this->belongsTo('Warehouses', [
            'foreignKey' => 'warehouse_id',
        ]);
        $this->belongsTo('Showrooms', [
            'foreignKey' => 'showroom_id',
        ]);
        $this->hasMany('StockRequestItems', [
            'foreignKey' => 'stock_request_id',
        ]);
        $this->hasMany('SupplierPurchaseOrdersOLD', [
            'foreignKey' => 'stock_request_id',
        ]);
        $this->hasMany('SupplierPurchaseOrders', [
            'foreignKey' => 'stock_request_id',
            'joinType' => 'INNER'
        ]);

        $this->hasMany('BlItems', [
            'foreignKey' => 'bl_id',
            'className' => 'BlItem', // optional if conventional
        ]);
        

        // $this->belongsTo('ToShowrooms', [
        //     'className' => 'Showrooms',
        //     'foreignKey' => 'to_showroomID',
        //     'propertyName' => 'to_showroom'
        // ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('requested_by')
            ->requirePresence('requested_by', 'create')
            ->notEmptyString('requested_by');

        $validator
            ->scalar('requestor_type')
            ->requirePresence('requestor_type', 'create')
            ->notEmptyString('requestor_type');

        $validator
            ->nonNegativeInteger('supplier_id')
            ->allowEmptyString('supplier_id');

        $validator
            ->nonNegativeInteger('warehouse_id')
            ->allowEmptyString('warehouse_id');

        $validator
            ->nonNegativeInteger('showroom_id')
            ->allowEmptyString('showroom_id');

        $validator
            ->nonNegativeInteger('to_showroomID')
            ->allowEmptyString('to_showroomID');

        $validator
            ->scalar('manager_review_status')
            ->notEmptyString('manager_review_status');

        $validator
            ->dateTime('manager_reviewed_time')
            ->allowEmptyDateTime('manager_reviewed_time');

        $validator
            ->nonNegativeInteger('reviewed_by')
            ->allowEmptyString('reviewed_by');

        $validator
            ->scalar('supervisor_verify_status')
            ->notEmptyString('supervisor_verify_status');

        $validator
            ->dateTime('supervisor_verified_time')
            ->allowEmptyDateTime('supervisor_verified_time');

        $validator
            ->nonNegativeInteger('verified_by')
            ->allowEmptyString('verified_by');

        $validator
            ->scalar('request_status')
            ->notEmptyString('request_status');

        $validator
            ->dateTime('request_date')
            ->requirePresence('request_date', 'create')
            ->notEmptyDateTime('request_date');

        $validator
            ->scalar('review_note')
            ->allowEmptyString('review_note');

        $validator
            ->scalar('verify_note')
            ->allowEmptyString('verify_note');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['supplier_id'], 'Suppliers'), ['errorField' => 'supplier_id']);
        $rules->add($rules->existsIn(['warehouse_id'], 'Warehouses'), ['errorField' => 'warehouse_id']);
        $rules->add($rules->existsIn(['showroom_id'], 'Showrooms'), ['errorField' => 'showroom_id']);
         $rules->add($rules->isUnique(
        ['supplier_id', 'bl_no'],
        'This BL No already exists for this supplier.'
    ));
        return $rules;
    }

    public function addBL($supplierID, $stockRequestId, $bl_no,$image, $bldate) {
        
        $saveBL = null;
        $saveBL = $this->newEmptyEntity(); 
         
        
        $saveBL->supplier_id  = $supplierID;
        $saveBL->stock_request_id  = $stockRequestId;
        $saveBL->bl_no = $bl_no; 
        $saveBL->bl_image    = $image;
        $saveBL->bl_date = $bldate;
        if ($obj = $this->save($saveBL)) {
            return $obj->id;
        } else {
            return false;
        }
    }

    //Get all BLs
    public function getAllBLRecords()
    {
        return $this->find('all', [
            'order' => ['id' => 'DESC']
        ])->toArray();
    }

    //Get BL by BL ID
    public function getBLById($id)
    {
        return $this->get($id); // Fetches one record by primary key (id)
    }
}
