<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\FrozenTime;
use Cake\I18n\FrozenDate;
use Cake\Utility\Hash;
use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\ORM\Query;
use Cake\Database\Expression\IdentifierExpression;

class DashboardsController extends AppController
{
    protected $Roles;
    protected $Orders;
    protected $Users;
    protected $Products;
    protected $ProductAttributes;
    protected $Showrooms;
    protected $Warehouses;
    protected $Transactions;
    protected $SupplierPayment;
    protected $Suppliers;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $SupplierProducts;
    protected $ProductStocks;
    protected $OrderItems;
    protected $Categories;
    protected $ProductCategories;
    protected $Zones;
    protected $ShowroomExpenses;
    protected $StockRequests;
    protected $StockMovements;
    protected $StockRequestItems;
    protected $StockMovementItems;
    protected $ShowroomUsers;
    protected $Shipments;
    protected $ShipmentOrders;
    protected $ShipmentOrderItems;
    protected $MerchantSettlements;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin'); // Use the admin layout
        $this->loadComponent('Media');

        $this->Roles = $this->fetchTable('Roles');
        $this->Orders = $this->fetchTable('Orders');
        $this->Users = $this->fetchTable('Users');
        $this->Products = $this->fetchTable('Products');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->SupplierPayment = $this->fetchTable('SupplierPayment');
        $this->Suppliers = $this->fetchTable('Suppliers');
        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Categories = $this->fetchTable('Categories');
        $this->ProductCategories = $this->fetchTable('ProductCategories');
        $this->Zones = $this->fetchTable('Zones');
        $this->ShowroomExpenses = $this->fetchTable('ShowroomExpenses');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->StockMovementItems = $this->fetchTable('StockMovementItems');
        $this->ShowroomUsers = $this->fetchTable('ShowroomUsers');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->ShipmentOrderItems = $this->fetchTable('ShipmentOrderItems');
        $this->MerchantSettlements = $this->fetchTable('MerchantSettlements');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'add']);
    }

    public function index()
    {
        $user = $this->Authentication->getIdentity();

        if (!empty($user)) {
            $role = $this->Roles->get($user->role_id);
            $roleName = $role->slug;
            
            $redirectMap = [
                'admin' => ['controller' => 'Dashboards', 'action' => 'adminDashboard'],
                'seller' => ['controller' => 'Dashboards', 'action' => 'sellerDashboard'],
                'showroom-manager' => ['controller' => 'Dashboards', 'action' => 'showroomManagerDashboard'],
                'showroom-supervisor' => ['controller' => 'Dashboards', 'action' => 'supervisorManagerDashboard'],
                'warehouse-manager' => ['controller' => 'Dashboards', 'action' => 'warehouseManagerDashboard'],
                'warehouse-assistant' => ['controller' => 'Dashboards', 'action' => 'warehouseManagerDashboard'],
                'sales-person' => ['controller' => 'Dashboards', 'action' => 'showroomManagerDashboard'],
                'call-center-agent' => ['controller' => 'SupportTickets', 'action' => 'index'],
                'call-center-supervisor' => ['controller' => 'SupportTickets', 'action' => 'index'],
                'after-sales-call-center-agent' => ['controller' => 'SupportTickets', 'action' => 'index'],
            ];
            
            if (isset($redirectMap[$roleName])) {
                return $this->redirect($redirectMap[$roleName]);
            }
            
        } else {
            // If the user is not logged in, redirect to login page
            return $this->redirect(['controller' => 'Users', 'action' => 'login']);
        }
    }

    public function adminDashboard()
    {
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        // Get current month start and end date
        $currentMonthStart = FrozenTime::now()->startOfMonth();
        $currentMonthEnd = FrozenTime::now()->endOfMonth();

        // Base conditions for the query
        $baseConditions = [
            'Orders.status !=' => 'Cancelled',
            'Orders.order_date >=' => $currentMonthStart,
            'Orders.order_date <=' => $currentMonthEnd
        ];

        // Get count of Online Orders
        $onlineOrders = $this->Orders->find()
            ->where(array_merge($baseConditions, ['Orders.order_type' => 'Online']))
            ->count();

        // Get count of Showroom Orders
        $showroomOrders = $this->Orders->find()
            ->where(array_merge($baseConditions, ['Orders.order_type' => 'Showroom']))
            ->count();

        // Get total orders (Online + Showroom)
        $totalOrders = $onlineOrders + $showroomOrders;

        // Get total sales amount
        $totalSalesAmountQuery = $this->Orders->find()
            ->where($baseConditions)
            ->select([
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
            ])
            ->first();

        $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

        // Get total all-time orders (excluding Cancelled)
        $allTimeOrders = $this->Orders->find()
            ->where(['Orders.status !=' => 'Cancelled'])
            ->count();

        // Calculate percentage of orders in the current month
        $percentageOrders = ($allTimeOrders > 0) ? ($totalOrders / $allTimeOrders) * 100 : 0;

        // Normalize role names to lowercase for comparison
        $callCenterRoleNames = ['call center supervisor', 'call center agent'];

        $callCenterRoles = $this->Roles->find()
            ->select(['id'])
            ->where(function (QueryExpression $exp, Query $q) use ($callCenterRoleNames) {
                return $exp->in('LOWER(Roles.name)', $callCenterRoleNames);
            })
            ->all()
            ->extract('id')
            ->toList();

        $callCenterOrders = 0;
        if (!empty($callCenterRoles)) {
            $callCenterOrders = $this->Orders->find()
                ->where(array_merge($baseConditions, ['Orders.created_by_role IN' => $callCenterRoles]))
                ->count();
        }

        // Base query for active users
        $userQuery = $this->Users->find()
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->order(['Users.first_name' => 'ASC']);

        // Filter by role if provided
        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $userQuery->where(['Users.role_id' => $roleId]);
        }

        // Get the total count of active users
        $totalUsers = $userQuery->count();

        $newUsers = 0;
        if ($currentMonthStart && $currentMonthEnd) {
            $users = $userQuery->all(); // Retrieve all users
            
            foreach ($users as $user) {
                $createdDate = $user->created->format('Y-m-d');
                
                if ($createdDate >= $currentMonthStart->format('Y-m-d')) {
                    $newUsers++;
                }
            }
        } else {
            // If no date range is provided, new users count is zero
            $newUsers = 0;
        }

        // Calculate the percentage of new users
        $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;      

        // Fetch total active products
        $totalActiveProducts = $this->Products->find()
            ->where([
                'status' => 'A'
                // 'created >=' => $currentMonthStart,
                // 'created <=' => $currentMonthEnd
            ])
            ->count();

        // Fetch number of new products added in the current month
        $newProducts = $this->Products->find()
            ->where([
                'status' => 'A',
                'created >=' => $currentMonthStart,
                'created <=' => $currentMonthEnd
            ])
            ->count();

        // Calculate the percentage of new products
        $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;         

        // Get the total number of active showrooms
        $totalShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A' 
                // 'created >=' => $currentMonthStart,
                // 'created <=' => $currentMonthEnd
            ])
            ->count(); 

        $newShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A', 
                'created >=' => $currentMonthStart,
                'created <=' => $currentMonthEnd
            ])
            ->count();          

        // Calculate the percentage of new showrooms
        $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

        //ORDER TRENDS APEX CHART
        $currentDate = FrozenTime::now();

        $months = [];
        $orderCounts = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = $currentDate->modify("-$i months");
            $months[$month->format('Y-m')] = [
                'month' => $month->format('F'),
                'order_count' => 0
            ];
        }

        // Query to get orders for the last six months, grouped by month
        $query = $this->Orders->find();
        $query->select([
            'month' => $query->func()->month(['Orders.created' => 'literal']),
            'year' => $query->func()->year(['Orders.created' => 'literal']),
            'order_count' => $query->func()->count('Orders.id')
        ])
        ->where([
            'Orders.status !=' => 'Cancelled', // Exclude canceled orders
            'Orders.created >=' => $currentDate->modify('-6 months')->i18nFormat('yyyy-MM-01 00:00:00')
        ])
        ->group(['year', 'month'])
        ->order(['year' => 'DESC', 'month' => 'DESC']);

        // Fetch the results and map them into the last six months' array
        $results = $query->all();
        foreach ($results as $row) {
            $formattedKey = sprintf('%04d-%02d', $row->year, $row->month);
            if (isset($months[$formattedKey])) {
                $months[$formattedKey]['order_count'] = $row->order_count;
            }
        }

        // Prepare data for the chart
        $monthNames = array_column($months, 'month');
        $orderCounts = array_column($months, 'order_count');

        /**  REVENUE AND EXPENSES CHART **/ 
        $months = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = $currentDate->modify("-$i months");
            $monthKey = $month->format('Y-m');
            $months[$monthKey] = [
                'month' => $month->format('F'),
                'revenue' => 0,
                'expenses' => 0
            ];
        }

        // Define date range
        $startRevenueDate = FrozenTime::now()->subMonths(6)->startOfMonth();
        $endRevenueDate = FrozenTime::now()->endOfMonth();

        // Fetch revenue for the last six months
        $revenueQuery = $this->Transactions->find()
            ->select([
                'month' => 'MONTH(Transactions.created)',
                'year' => 'YEAR(Transactions.created)',
                'total_revenue' => $this->Transactions->find()->func()->sum('Transactions.amount')
            ])
            ->innerJoinWith('Orders', function ($q) {
                return $q->where(['Orders.status !=' => 'Cancelled']);
            })
            ->where([
                'Transactions.created >=' => $startRevenueDate,
                'Transactions.created <=' => $endRevenueDate
            ])
            ->group(['year', 'month'])
            ->order(['year' => 'DESC', 'month' => 'DESC'])
            ->toArray();

        // Map revenue data to months
        foreach ($revenueQuery as $data) {
            $monthKey = sprintf('%04d-%02d', $data->year, $data->month);
            if (isset($months[$monthKey])) {
                $months[$monthKey]['revenue'] = $data->total_revenue / 1000; // Convert to thousands
            }
        }

        // Fetch expenses for the last six months
        $expensesQuery = $this->SupplierPayment->find()
            ->select([
                'month' => 'MONTH(SupplierPayment.created)',
                'year' => 'YEAR(SupplierPayment.created)',
                'total_expenses' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
            ])
            ->innerJoinWith('SupplierPurchaseOrders', function ($q) {
                return $q->where(['SupplierPurchaseOrders.status' => 'A']);
            })
            ->where([
                'SupplierPayment.created >=' => $startRevenueDate,
                'SupplierPayment.created <=' => $endRevenueDate
            ])
            ->group(['year', 'month'])
            ->order(['year' => 'DESC', 'month' => 'DESC'])
            ->toArray();

        // Fetch ShowroomExpenses for the last six months
        $showroomExpensesQuery = $this->ShowroomExpenses->find()
            ->select([
                'month' => 'MONTH(ShowroomExpenses.created)',
                'year' => 'YEAR(ShowroomExpenses.created)',
                'total_expenses' => $this->ShowroomExpenses->find()->func()->sum('ShowroomExpenses.amount')
            ])
            ->where([
                'ShowroomExpenses.created >=' => $startRevenueDate,
                'ShowroomExpenses.created <=' => $endRevenueDate,
                'ShowroomExpenses.status' => 'A'
            ])
            ->group(['year', 'month'])
            ->order(['year' => 'DESC', 'month' => 'DESC'])
            ->toArray();

        // Map expense data to months
        foreach ($expensesQuery as $data) {
            $monthKey = sprintf('%04d-%02d', $data->year, $data->month);
            if (isset($months[$monthKey])) {
                $months[$monthKey]['expenses'] = $data->total_expenses / 1000; // Convert to thousands
            }
        }

        // Merge ShowroomExpenses into months
        foreach ($showroomExpensesQuery as $data) {
            $monthKey = sprintf('%04d-%02d', $data->year, $data->month);
            if (!isset($months[$monthKey])) {
                $months[$monthKey] = [
                    'expenses' => 0
                ];
            }
            $months[$monthKey]['expenses'] += $data->total_expenses / 1000; // Convert to thousands
        }

        // Prepare data for the chart
        $revenueMonthNames = [];
        $revenueData = [];
        $expensesData = [];
        foreach ($months as $data) {
            $revenueMonthNames[] = $data['month'] ?? null;
            $revenueData[] = $data['revenue'] ?? null;
            $expensesData[] = $data['expenses'] ?? null;
        }  

        $pendingBills = $this->SupplierPurchaseOrders->find()
            ->contain([
                'Suppliers',
                'SupplierPurchaseOrdersItems' => function ($q) {
                    return $q->contain(['Products'])
                         ->select([
                            'SupplierPurchaseOrdersItems.product_id',
                            'SupplierPurchaseOrdersItems.product_variant_id',
                            'SupplierPurchaseOrdersItems.product_attribute_id',
                            'SupplierPurchaseOrdersItems.approved_quantity',
                            'SupplierPurchaseOrdersItems.quantity', // Add quantity to select for 'P' status
                            'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
                        ]);
                }
            ])
            ->where([
                'SupplierPurchaseOrders.payment_status' => 'Pending',
                'SupplierPurchaseOrders.status IN' => ['A', 'P']
            ])
            ->all();

        $supplier_payment_pendings = [];

        // Loop through each purchase order and calculate pending amount
        foreach ($pendingBills as $purchaseOrder) {
            $totalPendingAmount = 0;

            foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {

                $supplier_price_conditions = [
                    'supplier_id' => $purchaseOrder->supplier_id,
                    'product_id' => $product->product_id
                ];

                // Check if product_variant_id exists and is not null
                if (!empty($product->product_variant_id)) {
                    $supplier_price_conditions['product_variant_id'] = $product->product_variant_id;
                }

                $supplierPrice = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where($supplier_price_conditions)
                    ->first();

                // Calculate total pending amount for each product
                if ($supplierPrice) {
                    // Check purchase order status to determine the quantity field to use
                    $quantity = ($purchaseOrder->status === 'A') 
                        ? $product->approved_quantity 
                        : $product->quantity;
                    
                    $totalPendingAmount += $supplierPrice->supplier_price * $quantity;
                }
            }

            $totalPendingAmount = number_format((float)$totalPendingAmount, 0, '', $thousandSeparator);

            // Store Bill No and pending amount
            $supplier_payment_pendings[] = [
                'bill_number' => $purchaseOrder->bill_no,
                'pending_amount' => $totalPendingAmount,
                'supplier_name' => $purchaseOrder->supplier->name ?? 'N/A'
            ];
        }

        //TOP 5 supplier Payments
        $supplier_payment = $this->SupplierPayment->find()
            ->contain([
                'Suppliers','SupplierPurchaseOrders'
                ])
            ->order(['SupplierPayment.id' => 'DESC'])->toArray();

        foreach ($supplier_payment as $payment) {
            if (
                isset($payment->supplier_purchase_order->payment_status)
                && $payment->supplier_purchase_order->payment_status === 'Partially Paid'
            ) {
                $orderId = $payment->supplier_purchase_order->id;
                $supplierId = $payment->supplier_id;

                $orderItems = $this->SupplierPurchaseOrdersItems->find()
                    ->where(['supplier_purchase_order_id' => $orderId])
                    ->toArray();

                $actualTotal = 0;

                foreach ($orderItems as $item) {
                    $supplierPrice = null;

                    if (!empty($item->product_variant_id)) {
                        $supplierProduct = $this->SupplierProducts->find()
                            ->select(['supplier_price'])
                            ->where([
                                'supplier_id' => $supplierId,
                                'product_variant_id' => $item->product_variant_id,
                                'status' => 'A'
                            ])
                            ->first();

                        if ($supplierProduct) {
                            $supplierPrice = $supplierProduct->supplier_price;
                        }
                    }

                    if ($supplierPrice === null) {
                        $supplierProduct = $this->SupplierProducts->find()
                            ->select(['supplier_price'])
                            ->where([
                                'supplier_id' => $supplierId,
                                'product_id' => $item->product_id,
                                'status' => 'A'
                            ])
                            ->first();

                        if ($supplierProduct) {
                            $supplierPrice = $supplierProduct->supplier_price;
                        }
                    }

                    if ($supplierPrice !== null) {
                        $actualTotal += $supplierPrice * $item->approved_quantity;
                    }
                }

                $payment->total_order_amount = $actualTotal;
            }
        }

        /** Fetch top 5 selling products for online and showroom sales **/
        // Get top 5 online products
        $topOnlineProducts = $this->OrderItems->find()
            ->select([
                'Products.id',
                'Products.name',
                'total_sales_amount' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
                'total_units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
            ])
            ->contain([
                'Products' => [
                    'ProductImages' => function ($q) {
                        return $q->where([
                            'ProductImages.image_default' => 1,
                            'ProductImages.status' => 'A'
                        ]);
                    }
                ]
            ])
            ->matching('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
                return $q->where([
                    'Orders.order_type' => 'Online',
                    'Orders.order_date >=' => $currentMonthStart,
                    'Orders.order_date <=' => $currentMonthEnd,
                    'Orders.status !=' => 'Cancelled'
                ]);
            })
            ->group(['Products.id'])
            ->order(['total_units_sold' => 'DESC'])
            ->limit(5)
            ->toArray();

        // Fetch top 5 showroom products
        $topShowroomProducts = $this->OrderItems->find()
            ->select([
                'Products.id',
                'Products.name',
                'total_sales_amount' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
                'total_units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
            ])
            ->contain([
                'Products' => [
                    'ProductImages' => function ($q) {
                        return $q->where([
                            'ProductImages.image_default' => 1,
                            'ProductImages.status' => 'A'
                        ]);
                    }
                ]
            ])
            ->matching('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
                return $q->where([
                    'Orders.order_type' => 'Showroom',
                    'Orders.order_date >=' => $currentMonthStart,
                    'Orders.order_date <=' => $currentMonthEnd,
                    'Orders.status !=' => 'Cancelled'
                ]);
            })
            ->group(['Products.id'])
            ->order(['total_units_sold' => 'DESC'])
            ->limit(5)
            ->toArray();

        // Combine both lists
        $topSellingProducts = [
            'online' => $topOnlineProducts,
            'showroom' => $topShowroomProducts
        ];

        foreach ($topSellingProducts as $channel => $products) {
            // Loop through each product in the channel
            foreach ($products as $product) {
                // Check if the product has images
                if (!empty($product->product->product_images)) {
                    // Convert image URL to CloudFront URL
                    $product->product->product_images[0]->image = $this->Media->getCloudFrontURL($product->product->product_images[0]->image);
                }
            }
        }


        /** FETCH TOP 5 PRODUCT CATEGORIES **/
        $totalCategorySalesQuery = $this->OrderItems->find()
            ->innerJoinWith('Products.ProductCategories.Categories')
            ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
                return $q->where([
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.status !=' => 'Cancelled'
                ]);
            })
            ->select([
                'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity')
            ])
            ->first();  

        $totalCategorySales = $totalCategorySalesQuery ? $totalCategorySalesQuery->total_sales : 0;    


        // Step 2: Fetch top 5 product categories and calculate percentage
        $topCategories = $this->OrderItems->find()
            ->select([
                'category_id' => 'Categories.id',
                'category_icon' => 'Categories.category_icon',
                'category_name' => 'Categories.name',
                'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
                'units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
            ])
            ->innerJoinWith('Products.ProductCategories.Categories') // Joining the necessary tables
            ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
                return $q->where([
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.status !=' => 'Cancelled'
                ]);
            })
            ->group(['Categories.id', 'Categories.name']) // Grouping by category
            ->order(['total_sales' => 'DESC']) // Ordering by sales
            ->limit(5) // Limiting to top 5 categories
            ->toArray();

        // Calculate percentage for progress bars
        foreach ($topCategories as &$category) {
            $category['percentage'] = $totalCategorySales > 0 ? ($category['total_sales'] / $totalCategorySales) * 100 : 0;

            if ($category['category_icon']) {
                $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
            }
        }

        /** ONLINE ORDERS AND COUNT**/
        $query = $this->Orders->find()
            ->where([
                'Orders.order_type' => 'Online',
                'Orders.status !=' => 'Cancelled'
            ]);

        $totalOnlineOrders = $query->count();

        $onlineStats = $query->select([
            'online_accounts_created' => $query->func()->count('DISTINCT Orders.customer_id'),
            'online_web_order_count' => $query->func()->sum(
                $query->newExpr("CASE WHEN Orders.order_online_source = 'Web' THEN 1 ELSE 0 END")
            ),
            'online_mobile_order_count' => $query->func()->sum(
                $query->newExpr("CASE WHEN Orders.order_online_source = 'Mobile' THEN 1 ELSE 0 END")
            ),
            'online_social_order_count' => $query->func()->sum(
                $query->newExpr("CASE WHEN Orders.order_online_source = 'Social' THEN 1 ELSE 0 END")
            )
        ])->first();

        /** SHOWROOM NAME AND SHOWROOM ORDERS COUNT **/
        $allShowroomOrdersQuery = $this->Orders->find()
            ->contain(['Showrooms'])
            ->select([
                'showroom_id' => 'Orders.showroom_id',
                'showroom_name' => 'Showrooms.name',
                'order_count' => $this->Orders->find()->func()->count('Orders.id')
            ])
            ->where([
                'Orders.order_type' => 'Showroom',
                'Orders.status !=' => 'Cancelled'
            ])
            ->group(['Orders.showroom_id', 'Showrooms.name'])
            ->enableHydration(false); // disable ORM object hydration

        // Step 2: Execute the query and get array results
        $allShowroomOrdersArray = $allShowroomOrdersQuery->all()->toList(); // ✅ executes the query

        // Step 3: Calculate total and add percentages
        $totalShowroomOrders = array_sum(array_column($allShowroomOrdersArray, 'order_count'));

        $allShowroomOrders = array_map(function ($order) use ($totalShowroomOrders) {
            $order['percentage'] = $totalShowroomOrders > 0
                ? round(($order['order_count'] / $totalShowroomOrders) * 100, 2)
                : 0;
            return $order;
        }, $allShowroomOrdersArray);


        /** PIE CHART 1 **/
        // 1. Define date range for current year
        $currentYearStart = FrozenTime::now()->startOfYear();
        $currentYearEnd = FrozenTime::now()->endOfYear();

        $basicConditions = [
            'Orders.status !=' => 'Cancelled',
            'Orders.created >=' => $currentYearStart,
            'Orders.created <=' => $currentYearEnd,
        ];

        // 2. Online Orders
        $onlineOrdersCount = $this->Orders->find()
            ->where(array_merge($basicConditions, ['Orders.order_type' => 'Online']))
            ->count();

        // 3. Showroom Orders
        $showroomOrdersCount = $this->Orders->find()
            ->where(array_merge($basicConditions, ['Orders.order_type' => 'Showroom']))
            ->count();

        $callCenterOrdersCount = 0;
        if (!empty($callCenterRoles)) {
            $callCenterOrdersCount = $this->Orders->find()
                ->where(array_merge($basicConditions, [
                    'Orders.created_by_role IN' => $callCenterRoles,
                ]))
                ->count();
        }

        /** PIE CHART 2 **/
        $paymentMethods = ['Cheque', 'MTN MoMo', 'Cash on Delivery', 'Wave', 'Cash', 'Credit'];

        $paymentData = $this->Transactions->find()
            ->select([
                'payment_method',
                'count' => $this->Transactions->find()->func()->count('*')
            ])
            ->where([
                'Transactions.payment_method IN' => $paymentMethods,
                'Transactions.created >=' => $currentYearStart,
                'Transactions.created <=' => $currentYearEnd,
            ])
            ->group('payment_method')
            ->enableHydration(false)
            ->all();

        $paymentChartLabels = [];
        $paymentChartData = [];

        foreach ($paymentMethods as $method) {
            $matched = collection($paymentData)->firstMatch(['payment_method' => $method]);
            $paymentChartLabels[] = $method;
            $paymentChartData[] = $matched ? (int)$matched['count'] : 0;
        }

        /** PIE CHART 3 **/
        // 1. Get number of pickups recorded
        $pickupsRecorded = $this->Shipments->find()
            ->where([
                'Shipments.delivery_status' => 'Picked up',
                'Shipments.status' => 'A',
                'Shipments.created >=' => $currentYearStart,
                'Shipments.created <=' => $currentYearEnd
            ])
            ->count();

        // 2. Get number of deliveries completed
        $deliveriesCompleted = $this->Orders->find()
            ->where([
                'Orders.status' => 'Delivered',
                'Orders.created >=' => $currentYearStart,
                'Orders.created <=' => $currentYearEnd
            ])
            ->count();

        // 3. Get number of deliveries pending
        $deliveriesPending = $this->Orders->find()
            ->where([
                'Orders.status' => 'Pending',
                'Orders.shipment_status' => 'Unassigned',
                'Orders.created >=' => $currentYearStart,
                'Orders.created <=' => $currentYearEnd
            ])
            ->count();

        // print_r($deliveriesPending);die;

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : ''; 

        $this->set(compact('totalOrders', 'onlineOrders', 'showroomOrders', 'callCenterOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage', 'monthNames', 'orderCounts', 'revenueMonthNames', 'revenueData', 'expensesData', 'supplier_payment', 'supplier_payment_pendings', 'topSellingProducts', 'topCategories', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'allShowroomOrders', 'onlineStats', 'totalOnlineOrders', 'onlineOrdersCount', 'showroomOrdersCount', 'callCenterOrdersCount', 'paymentChartLabels', 'paymentChartData', 'pickupsRecorded', 'deliveriesCompleted', 'deliveriesPending'));
    }

    public function sellerDashboard()
    {
        $curUserId = $this->Authentication->getIdentity()->id;
        $merchantId = $this->merchantId ?? null;
        
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        // Fetch settlement statistics
        $settlementStats = $this->MerchantSettlements->getMerchantSettlementStats($merchantId);

        // Fetch pending settlements
        $pendingSettlements = $this->MerchantSettlements->find()
            ->contain(['Orders'])
            ->where([
                'MerchantSettlements.merchant_id' => $merchantId,
                'MerchantSettlements.settlement_status' => 'pending'
            ])
            ->order(['MerchantSettlements.created' => 'DESC'])
            ->limit(5)
            ->toArray();

        // Fetch recently settled (completed) settlements
        $completedSettlements = $this->MerchantSettlements->find()
            ->contain(['Orders', 'SettledBy'])
            ->where([
                'MerchantSettlements.merchant_id' => $merchantId,
                'MerchantSettlements.settlement_status' => 'completed'
            ])
            ->order(['MerchantSettlements.created' => 'DESC'])
            ->limit(5)
            ->toArray();

        // Settlement trends: last 6 months final_payout
        $sixMonthsAgo = FrozenTime::now()->subMonths(6)->startOfMonth();
        $settlementTrends = $this->MerchantSettlements->find()
            ->select([
                'month' => 'MONTH(MerchantSettlements.created)',
                'year' => 'YEAR(MerchantSettlements.created)',
                'payout' => $this->MerchantSettlements->find()->func()->sum('MerchantSettlements.final_payout')
            ])
            ->where([
                'MerchantSettlements.merchant_id' => $merchantId,
                'MerchantSettlements.created >=' => $sixMonthsAgo
            ])
            ->group(['year','month'])
            ->order(['year' => 'ASC','month' => 'ASC'])
            ->enableHydration(false)
            ->toArray();

        $settlementMonths = [];
        $settlementAmounts = [];
        // Build a month map for last 6 months
        $cur = FrozenTime::now();
        $map = [];
        for ($i = 5; $i >= 0; $i--) {
            $m = $cur->subMonths($i);
            $key = sprintf('%04d-%02d', (int)$m->format('Y'), (int)$m->format('m'));
            $map[$key] = ['label' => $m->format('M'), 'amount' => 0];
        }
        foreach ($settlementTrends as $row) {
            $key = sprintf('%04d-%02d', (int)$row['year'], (int)$row['month']);
            if (isset($map[$key])) {
                $map[$key]['amount'] = (float)$row['payout'];
            }
        }
        foreach ($map as $entry) {
            $settlementMonths[] = $entry['label'];
            $settlementAmounts[] = $entry['amount'] / 1000; // thousands for chart alignment
        }

        // Fetch merchant order statistics
        $MerchantOrders = $this->fetchTable('MerchantOrders');
        $orderStats = $MerchantOrders->getMerchantOrderStats($merchantId);
        
        // Fetch recent orders
        $recentOrders = $MerchantOrders->getMerchantOrders($merchantId, 10);
        
        // Fetch order statistics for different time periods
        $today = FrozenTime::now()->startOfDay();
        $thisWeek = FrozenTime::now()->startOfWeek();
        $thisMonth = FrozenTime::now()->startOfMonth();
        
        $ordersToday = $MerchantOrders->find()
            ->where([
                'MerchantOrders.merchant_id' => $merchantId,
                'MerchantOrders.created >=' => $today
            ])
            ->count();
            
        $ordersThisWeek = $MerchantOrders->find()
            ->where([
                'MerchantOrders.merchant_id' => $merchantId,
                'MerchantOrders.created >=' => $thisWeek
            ])
            ->count();
            
        $ordersThisMonth = $MerchantOrders->find()
            ->where([
                'MerchantOrders.merchant_id' => $merchantId,
                'MerchantOrders.created >=' => $thisMonth
            ])
            ->count();

        // Fetch top selling products for the merchant
        $Products = $this->fetchTable('Products');
        $topSellingProducts = $Products->find()
            ->contain(['ProductVariants', 'ProductImages'])
            ->join([
                'OrderItems' => [
                    'table' => 'order_items',
                    'type' => 'INNER',
                    'conditions' => 'OrderItems.product_id = Products.id',
                ],
                'Orders' => [
                    'table' => 'orders',
                    'type' => 'INNER',
                    'conditions' => 'Orders.id = OrderItems.order_id',
                ]
            ])
            ->select([
                'Products.id',
                'Products.name',
                'Products.sales_price',
                'Products.promotion_price',
                'total_units_sold' => $Products->find()->func()->sum('OrderItems.quantity'),
                'total_sales_amount' => $Products->find()->func()->sum('OrderItems.total_price')
            ])
            ->where([
                'Products.merchant_id' => $merchantId,
                'Products.status' => 'A',
                'Products.approval_status' => 'Approved',
                'OrderItems.status NOT IN' => ['Cancelled', 'Returned']
            ])
            ->group(['Products.id'])
            ->order(['total_units_sold' => 'DESC'])
            ->limit(5)
            ->toArray();
        
        // Fetch low performing products (products with low sales or stock)
        $lowPerformingProducts = $Products->find()
            ->where([
                'Products.merchant_id' => $merchantId,
                'Products.status' => 'A'
            ])
            ->order(['Products.created' => 'ASC'])
            ->limit(5)
            ->toArray();

        // Fetch low stock alerts - using a different approach since ProductStocks relationship might not be set up
        $lowStockProducts = [];
        try {
            // Try to get low stock products using a direct query to ProductStocks
            $ProductStocks = $this->fetchTable('ProductStocks');
            $lowStockProducts = $ProductStocks->find()
                ->contain(['Products'])
                ->where([
                    'Products.merchant_id' => $merchantId,
                    'Products.status' => 'A',
                    'ProductStocks.quantity <=' => 10
                ])
                ->limit(10)
                ->toArray();
        } catch (Exception $e) {
            // If that fails, just get products with low stock count
            $lowStockProducts = $Products->find()
                ->where([
                    'Products.merchant_id' => $merchantId,
                    'Products.status' => 'A'
                ])
                ->limit(10)
                ->toArray();
        }

        // Fetch revenue trends for the last 6 months
        $revenueTrends = $MerchantOrders->find()
            ->select([
                'month' => 'MONTH(MerchantOrders.created)',
                'year' => 'YEAR(MerchantOrders.created)',
                'revenue' => $MerchantOrders->find()->func()->sum('MerchantOrders.merchant_total')
            ])
            ->where([
                'MerchantOrders.merchant_id' => $merchantId,
                'MerchantOrders.created >=' => $sixMonthsAgo,
                'MerchantOrders.status !=' => 'canceled'
            ])
            ->group(['year','month'])
            ->order(['year' => 'ASC','month' => 'ASC'])
            ->enableHydration(false)
            ->toArray();

        $revenueMonths = [];
        $revenueData = [];
        // Build revenue month map
        $revenueMap = [];
        for ($i = 5; $i >= 0; $i--) {
            $m = $cur->subMonths($i);
            $key = sprintf('%04d-%02d', (int)$m->format('Y'), (int)$m->format('m'));
            $revenueMap[$key] = ['label' => $m->format('M'), 'amount' => 0];
        }
        foreach ($revenueTrends as $row) {
            $key = sprintf('%04d-%02d', (int)$row['year'], (int)$row['month']);
            if (isset($revenueMap[$key])) {
                $revenueMap[$key]['amount'] = (float)$row['revenue'];
            }
        }
        foreach ($revenueMap as $entry) {
            $revenueMonths[] = $entry['label'];
            $revenueData[] = $entry['amount'] / 1000; // thousands for chart alignment
        }

        // Fetch refunds and chargebacks
        $refunds = 0;
        try {
            // Get refunds through the relationship: RefundTransactions -> OrderReturns -> OrderItems -> Products
            $refunds = $this->fetchTable('RefundTransactions')->find()
                ->contain(['OrderReturns.OrderItems.Products'])
                ->matching('OrderReturns.OrderItems.Products', function ($q) use ($merchantId) {
                    return $q->where(['Products.merchant_id' => $merchantId]);
                })
                ->count();
        } catch (Exception $e) {
            // If that fails, just set refunds to 0
            $refunds = 0;
        }

        $this->set(compact(
            'settlementStats', 'pendingSettlements', 'completedSettlements', 
            'settlementMonths', 'settlementAmounts', 'currencySymbol', 
            'decimalSeparator', 'thousandSeparator', 'orderStats', 'recentOrders',
            'ordersToday', 'ordersThisWeek', 'ordersThisMonth', 'topSellingProducts',
            'lowPerformingProducts', 'lowStockProducts', 'revenueMonths', 
            'revenueData', 'refunds'
        ));
    }

    public function showroomManagerDashboard()
    {
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $user = $this->Authentication->getIdentity();

        // Get user role
        $userWithRole = $this->Users->get($user->id, [
            'contain' => ['Roles']
        ]);
        $userRole = $userWithRole->role->name;

        // Initialize variable
        $showroom_detail = null;

        if ($userRole === 'Showroom Manager') {
            $showroom_detail = $this->Showrooms->find()
                ->where(['Showrooms.showroom_manager' => $user->id])
                ->first();
        } elseif ($userRole === 'Sales Person') {
            $showroomUser = $this->ShowroomUsers->find()
                ->where(['ShowroomUsers.user_id' => $user->id, 'ShowroomUsers.status' => 'A'])
                ->contain(['Showrooms'])
                ->first();
            if ($showroomUser) {
                $showroom_detail = $showroomUser->showroom;
            }
        }

        /** GET SHOWROOM DETAILS **/
        // $showroom_detail = $this->Showrooms->find()
        //     ->where(['Showrooms.showroom_manager' => $user->id])
        //     ->first();

        if(!empty($showroom_detail))
        {    
            $noActiveShowroomsMessage = null;

            /** Get current month start and end date **/
            $currentMonthStart = FrozenTime::now()->startOfMonth();
            $currentMonthEnd = FrozenTime::now()->endOfMonth();

            $totalOrdersQuery = $this->Orders->find()
                ->where([
                    'Orders.status !=' => 'Cancelled',
                    'Orders.showroom_id' => $showroom_detail->id,
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.order_type' => 'Showroom'
                ]);                

            /** Get the total number of orders for the current month **/
            $totalOrders = $totalOrdersQuery->count();

            // Total sales amount for the current month
            // Calculate total sales amount for the current month
            $totalSalesAmountQuery = $this->Orders->find()
                ->where([
                    'Orders.status !=' => 'Cancelled',
                    'Orders.showroom_id' => $showroom_detail->id,
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.order_type' => 'Showroom'
                ])
                ->select([
                    'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
                ])
                ->first();

            $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

            // Get the total number of all-time orders (excluding Cancelled orders)
            $allTimeOrdersQuery = $this->Orders->find()
                ->where([
                    'Orders.status !=' => 'Cancelled',
                    'Orders.showroom_id' => $showroom_detail->id
                ]);

            $totalAllTimeOrders = $allTimeOrdersQuery->count();

            // Calculate the percentage of orders in the current month
            $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

            $userQuery = $this->Users->find()
                ->contain(['Roles'])
                ->where(['Users.status' => 'A'])
                ->order(['Users.first_name' => 'ASC']);

            // Filter by role if provided
            $roleId = $this->request->getQuery('role');
            if ($roleId) {
                $userQuery->where(['Users.role_id' => $roleId]);
            }

            // Get the total count of active users
            $totalUsers = $userQuery->count();

            $newUsers = 0;
            if ($currentMonthStart && $currentMonthEnd) {
                $users = $userQuery->all(); // Retrieve all users
                // echo "<pre>";print_r($users);die;
                foreach ($users as $user) {
                    $createdDate = $user->created->format('Y-m-d');
                    // echo "<pre>";print_r($createdDate);
                    // echo "<pre>";print_r($startDate->format('Y-m-d'));
                    if ($createdDate >= $currentMonthStart->format('Y-m-d')) {
                        $newUsers++;
                    }
                }
            } else {
                // If no date range is provided, new users count is zero
                $newUsers = 0;
            }

            // Calculate the percentage of new users
            $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0; 

            /** Fetch total active products **/
            $totalActiveProducts = $this->ProductStocks->find()
                ->contain(['Products'])
                ->where([
                    'ProductStocks.showroom_id' => $showroom_detail->id,
                    'Products.status' => 'A' // Assuming 'status' is in the 'Products' table
                ])
                ->count();

            $todayDate = FrozenTime::now();
            $startOfMonth = $todayDate->startOfMonth();    

            $newProducts = $this->ProductStocks->find()
                ->contain(['Products'])
                ->where([
                    'ProductStocks.showroom_id' => $showroom_detail->id,
                    'Products.status' => 'A', // Filter active products
                    'Products.created >=' => $startOfMonth
                ])
                ->count();    

            /** Calculate the percentage of new products **/
            $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

            /** FETCH Order Trends: **/

            // Get the current date and calculate the date 14 days ago
            // Get the current date (start of today)
            $startDate = FrozenTime::today()->subDays(13)->startOfDay(); // 13 days ago from today
            $endDate = FrozenTime::today()->endOfDay(); // Include today's end of day

            $showroomId = $showroom_detail->id;

            // Query to get the number of orders per day for the last 14 days
            $orderData = $this->Orders->find()
                ->select([
                    'created' => 'Orders.created',
                    'order_count' => $this->Orders->find()->func()->count('Orders.id')
                ])
                ->where([
                    'Orders.created >=' => $startDate,
                    'Orders.created <=' => $endDate,
                    'Orders.status !=' => 'Cancelled',
                    'Orders.showroom_id' => $showroom_detail->id
                ])
                ->group(['created'])  // Group by the date
                ->order(['created' => 'ASC'])  // Sort by date ascending
                ->enableHydration(false)  // Fetch as an array instead of entities
                ->toArray();

            $mergedData = [];
            
            // Loop through the fetched data to merge date and order count
            foreach ($orderData as $data) {
                // Ensure that the created date is formatted as YYYY-MM-DD
                $dateKey = (new FrozenTime($data['created']))->i18nFormat('yyyy-MM-dd');  // Format the date

                // Accumulate the order count for the same date
                if (isset($mergedData[$dateKey])) {
                    $mergedData[$dateKey] += (int)$data['order_count'];  // Add to existing count
                } else {
                    $mergedData[$dateKey] = (int)$data['order_count'];  // Set initial count
                }
            } 

            // Prepare the final data structure for the graph
            $finalOrderTrendsData = [];
            $currentDate = FrozenTime::today();

            // Loop through the last 14 days to fill in the counts
            for ($i = 13; $i >= 0; $i--) {
                $dateKey = $currentDate->subDays($i)->i18nFormat('yyyy-MM-dd');  // Get the date key
                // Set the count to either the value found or 0 if not found
                $finalOrderTrendsData[$dateKey] = $mergedData[$dateKey] ?? 0;  
            }


            /** FETCH REVENUE AND EXPENSES **/
            $startRevenueDate = FrozenTime::now()->subMonths(6)->startOfMonth();
            $endRevenueDate = FrozenTime::now()->endOfMonth();

            // Define the last 6 months from the current month, only month names
            $monthData = [];
            for ($i = 5; $i >= 0; $i--) {
                $month = $currentDate->subMonths($i)->format('F'); // Format as 'Month' only
                $monthData[] = $month;
            }

            // Fetch revenue data
            $revenueQuery = $this->Transactions->find()
                ->select([
                    'month' => 'MONTH(Transactions.created)',
                    'total_revenue' => $this->Transactions->find()->func()->sum('Transactions.amount')
                ])
                ->innerJoinWith('Orders', function ($q) use ($showroomId) {
                    return $q->where([
                        'Orders.status !=' => 'Cancelled',
                        'Orders.showroom_id' => $showroomId
                    ]);
                })
                ->where([
                    'Transactions.created >=' => $startRevenueDate,
                    'Transactions.created <=' => $endRevenueDate
                ])
                ->group(['month'])
                ->order(['month' => 'DESC'])
                ->toArray();

            // Fetch expenses data
            $expensesQuery = $this->SupplierPayment->find()
                ->select([
                    'month' => 'MONTH(SupplierPayment.created)',
                    'total_expenses' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
                ])
                ->innerJoinWith('SupplierPurchaseOrders', function ($q) {
                    return $q->where(['SupplierPurchaseOrders.status' => 'A']);
                })
                ->where([
                    'SupplierPayment.created >=' => $startRevenueDate,
                    'SupplierPayment.created <=' => $endRevenueDate,
                    'SupplierPayment.showroom_id' => $showroomId
                ])
                ->group(['month'])
                ->order(['month' => 'DESC'])
                ->toArray();

            // Fetch ShowroomExpenses
            $showroomExpensesQuery = $this->ShowroomExpenses->find()
                ->select([
                    'month' => 'MONTH(ShowroomExpenses.created)',
                    'total_expenses' => $this->ShowroomExpenses->find()->func()->sum('ShowroomExpenses.amount')
                ])
                ->where([
                    'ShowroomExpenses.created >=' => $startRevenueDate,
                    'ShowroomExpenses.created <=' => $endRevenueDate,
                    'ShowroomExpenses.showroom_id' => $showroomId,
                    'ShowroomExpenses.status' => 'A'
                ])
                ->group(['month'])
                ->order(['month' => 'DESC'])
                ->toArray();

            // Process revenue data and map to each month
            $revenueData = array_fill(0, 6, 0); // Initialize with zeros
            foreach ($revenueQuery as $data) {
                $monthName = \DateTime::createFromFormat('!m', $data->month)->format('F');
                $monthIndex = array_search($monthName, $monthData);
                if ($monthIndex !== false) {
                    $revenueData[$monthIndex] = $data->total_revenue / 1000;  // Convert to thousands
                }
            }

            // Process expenses data and map to each month
            $expensesData = array_fill(0, 6, 0); // Initialize with zeros
            foreach ($expensesQuery as $data) {
                $monthName = \DateTime::createFromFormat('!m', $data->month)->format('F');
                $monthIndex = array_search($monthName, $monthData);
                if ($monthIndex !== false) {
                    $expensesData[$monthIndex] = $data->total_expenses / 1000;  // Convert to thousands
                }
            }

            $monthIndexMap = array_flip($monthData); // Create lookup map for month names

            foreach ($showroomExpensesQuery as $data) {
                $monthName = \DateTime::createFromFormat('!m', $data->month)->format('F');
                $monthIndex = $monthIndexMap[$monthName] ?? null;

                if ($monthIndex !== null) {
                    // Overwrite instead of accumulating
                    $expensesData[$monthIndex] = $data->total_expenses / 1000;
                }
            }

            /** PENDING ACTIONS **/
            /** 1) SUPPLIER PAYMENTS **/
            
            // Fetch pending purchase orders

            // Fetch pending bills using the model directly
            $pendingBills = $this->SupplierPurchaseOrders->find()
                ->contain([
                    'SupplierPurchaseOrdersItems' => function ($q) {
                        return $q->contain(['Products'])
                             ->select([
                                'SupplierPurchaseOrdersItems.product_id',
                                'SupplierPurchaseOrdersItems.product_variant_id',
                                'SupplierPurchaseOrdersItems.product_attribute_id',
                                'SupplierPurchaseOrdersItems.approved_quantity',
                                'SupplierPurchaseOrdersItems.quantity', // Add quantity to select for 'P' status
                                'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
                            ]);
                    }
                ])
                ->where([
                    'SupplierPurchaseOrders.payment_status' => 'Pending',
                    'SupplierPurchaseOrders.status IN' => ['A', 'P']
                ])
                ->all(); 

            $supplier_payment_pendings = [];

            // Loop through each purchase order and calculate pending amount
            foreach ($pendingBills as $purchaseOrder) {
                $totalPendingAmount = 0;

                foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {

                    $supplier_price_conditions = [
                        'supplier_id' => $purchaseOrder->supplier_id,
                        'product_id' => $product->product_id
                    ];

                    // Check if product_variant_id exists and is not null
                    if (!empty($product->product_variant_id)) {
                        $supplier_price_conditions['product_variant_id'] = $product->product_variant_id;
                    }

                    $supplierPrice = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where($supplier_price_conditions)
                        ->first();

                    // Calculate total pending amount for each product
                    if ($supplierPrice) {
                        $quantity = ($purchaseOrder->status === 'A') 
                            ? $product->approved_quantity 
                            : $product->quantity;
                        
                        $totalPendingAmount += $supplierPrice->supplier_price * $quantity;
                    }
                }

                $totalPendingAmount = number_format((float)$totalPendingAmount, 0, '', $thousandSeparator);

                // Store Bill No and pending amount
                $supplier_payment_pendings[] = [
                    'bill_number' => $purchaseOrder->bill_no,
                    'pending_amount' => $totalPendingAmount
                ];
            }    

            /** TOP 5 supplier Payments **/
            $supplier_payment = $this->SupplierPayment->find()
                ->contain([
                    'Suppliers','SupplierPurchaseOrders'
                    ])
                ->where([
                    'SupplierPayment.showroom_id' => $showroomId
                ])
                ->order([
                    'SupplierPayment.id' => 'DESC'
                ])->limit(5)->toArray();

            /** TOP 3 SHOWROOMS **/

            $totalSalesQuery = $this->Orders->find()
                ->select([
                    'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
                ])
                ->where([
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.status !=' => 'Cancelled'
                ])
                ->first();

            $totalSales = $totalSalesQuery ? $totalSalesQuery->total_sales : 0;

            // Step 2: Get the top 3 showrooms with percentage
            $topShowrooms = $this->Orders->find()
                ->select([
                    'showroom_id',
                    'Showrooms.name',
                    'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount'),
                    'order_count' => $this->Orders->find()->func()->count('Orders.id')
                    // 'sales_percentage' => 'SUM(Orders.total_amount) * 100 / ' . $totalSales
                ])
                ->where([
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.status !=' => 'Cancelled',
                    'Orders.showroom_id IS NOT NULL' 
                ])
                ->group('Orders.showroom_id')
                ->order(['total_sales' => 'DESC'])
                ->limit(3)
                ->contain(['Showrooms'])
                ->toArray();

            foreach ($topShowrooms as &$showroom) {
                $showroom->sales_percentage = $totalSales > 0 ? ($showroom->total_sales * 100) / $totalSales : 0;
            }

            /** FETCH TOP 5 PRODUCT CATEGORIES **/
            $totalCategorySalesQuery = $this->OrderItems->find()
                ->innerJoinWith('Products.ProductCategories.Categories')
                ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd, $showroomId) {
                    return $q->where([
                        'Orders.created >=' => $currentMonthStart,
                        'Orders.created <=' => $currentMonthEnd,
                        'Orders.status !=' => 'Cancelled',
                        'Orders.showroom_id' => $showroomId
                    ]);
                })
                ->select([
                    'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity')
                ])
                ->first();  

            $totalCategorySales = $totalCategorySalesQuery ? $totalCategorySalesQuery->total_sales : 0;    

            // Step 2: Fetch top 5 product categories and calculate percentage
            $topCategories = $this->OrderItems->find()
                ->select([
                    'category_id' => 'Categories.id',
                    'category_icon' => 'Categories.category_icon',
                    'category_name' => 'Categories.name',
                    'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
                    'units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
                ])
                ->innerJoinWith('Products.ProductCategories.Categories') // Joining the necessary tables
                ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd, $showroomId) {
                    return $q->where([
                        'Orders.created >=' => $currentMonthStart,
                        'Orders.created <=' => $currentMonthEnd,
                        'Orders.status !=' => 'Cancelled',
                        'Orders.showroom_id' => $showroomId
                    ]);
                })
                ->group(['Categories.id', 'Categories.name']) // Grouping by category
                ->order(['total_sales' => 'DESC']) // Ordering by sales
                ->limit(5) // Limiting to top 5 categories
                ->toArray();

            // Calculate percentage for progress bars
            foreach ($topCategories as &$category) {
                $category['percentage'] = $totalCategorySales > 0 ? ($category['total_sales'] / $totalCategorySales) * 100 : 0;

                if ($category['category_icon']) {
                    $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
                }
            }
        }
        else
        {
            $noActiveShowroomsMessage = __('No showroom are assigned to you.');
            $showroom_detail = null;
            $totalOrders = 0;
            $totalSalesAmount = 0;
            $percentageOrders = 0;
            $totalUsers = 0;
            $newUsers = 0;
            $newUsersPercentage = 0;
            $totalActiveProducts = 0;
            $newProducts = 0;
            $newProductsPercentage = 0;
            $finalOrderTrendsData = [];
            $monthData = [];
            $revenueData = [];
            $expensesData = [];
            $supplier_payment = [];
            $supplier_payment_pendings = [];
            $topShowrooms = [];
            $topCategories = [];
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : ''; 

        $this->set(compact('showroom_detail', 'totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'finalOrderTrendsData', 'monthData', 'revenueData', 'expensesData', 'supplier_payment_pendings', 'supplier_payment', 'topShowrooms', 'topCategories', 'currencySymbol', 'noActiveShowroomsMessage', 'decimalSeparator', 'thousandSeparator', 'userRole'));  
    }

    public function supervisorManagerDashboard()
    {
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $user = $this->Authentication->getIdentity();

        $supervisorId = $user->id;

        /** GET SUPERVISOR DETAILS **/
        $supervisor_detail = $this->Showrooms->find()
            ->where(['Showrooms.showroom_manager' => $user->id])
            ->first();

        // Get current month start and end date
        $currentMonthStart = FrozenTime::now()->startOfMonth();
        $currentMonthEnd = FrozenTime::now()->endOfMonth();

        // Fetch the active zones managed by the supervisor
        $activeShowrooms = $this->Showrooms->find()
            ->select(['id'])
            ->where(['showroom_supervisor' => $supervisorId])
            ->toArray();

        // Check if there are no active showrooms
        if (!empty($activeShowrooms)) 
        {

            $noActiveShowroomsMessage = null;
            // Extract zone IDs
            $showroomIds = Hash::extract($activeShowrooms, '{n}.id');

            // Query for total orders and total sales amount for the current month
            $totalOrdersQuery = $this->Orders->find()
                ->where([
                    'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.order_type' => 'Showroom',
                    'Orders.showroom_id IN' => $this->Showrooms->find()
                        ->select(['id'])
                        ->where(['Showrooms.id IN' => $showroomIds])
                ]);

            // Get the total number of orders for the current month
            $totalOrders = $totalOrdersQuery->count();

            // Total sales amount for the current month
            $totalSalesAmountQuery = $this->Orders->find()
                ->select([
                    'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
                ])
                ->where([
                    'Orders.status !=' => 'Cancelled', // Exclude canceled orders
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.order_type' => 'Showroom',
                    'Orders.showroom_id IN' => $this->Showrooms->find()
                        ->select(['id'])
                        ->where(['Showrooms.id IN' => $showroomIds])
                ])
                ->first();

        
            $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

            // Get the total number of all-time orders (excluding Cancelled orders)
            $allTimeOrdersQuery = $this->Orders->find()
                ->where(['Orders.status !=' => 'Cancelled', 'Orders.showroom_id IN' => $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.id IN' => $showroomIds])
                ]);


            $totalAllTimeOrders = $allTimeOrdersQuery->count();

            // Calculate the percentage of orders in the current month
            $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

            $userQuery = $this->Users->find()
                ->contain(['Roles'])
                ->where(['Users.status' => 'A'])
                ->order(['Users.first_name' => 'ASC']);

            // Filter by role if provided
            $roleId = $this->request->getQuery('role');
            if ($roleId) {
                $userQuery->where(['Users.role_id' => $roleId]);
            }

            // Get the total count of active users
            $totalUsers = $userQuery->count();

            $newUsers = 0;
            if ($currentMonthStart && $currentMonthEnd) {
                $users = $userQuery->all(); // Retrieve all users
                
                foreach ($users as $user) {
                    $createdDate = $user->created->format('Y-m-d');
                    
                    if ($createdDate >= $currentMonthStart->format('Y-m-d')) {
                        $newUsers++;
                    }
                }
            } else {
                // If no date range is provided, new users count is zero
                $newUsers = 0;
            }

            // Calculate the percentage of new users
            $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;      

            // Fetch total active products
            $totalActiveProducts = $this->Products->find()
                ->where([
                    'status' => 'A'
                    // 'created >=' => $currentMonthStart,
                    // 'created <=' => $currentMonthEnd
                ])
                ->count();

            // Fetch number of new products added in the current month
            $newProducts = $this->Products->find()
                ->where([
                    'status' => 'A',
                    'created >=' => $currentMonthStart,
                    'created <=' => $currentMonthEnd
                ])
                ->count();

            // Calculate the percentage of new products
            $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;         

            // Get the total number of active showrooms
            $totalShowrooms = $this->Showrooms->find()
                ->where([
                    'status' => 'A' 
                    // 'created >=' => $currentMonthStart,
                    // 'created <=' => $currentMonthEnd
                ])
                ->count(); 

            $newShowrooms = $this->Showrooms->find()
                ->where([
                    'status' => 'A', 
                    'created >=' => $currentMonthStart,
                    'created <=' => $currentMonthEnd
                ])
                ->count();          

            // Calculate the percentage of new showrooms
            $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

            $currentDate = new \DateTime(); // Current date
            $startDate = (clone $currentDate)->modify('-5 months')->format('Y-m-01'); // Start from the first day of 6 months ago

            $showrooms = $this->Showrooms->find()
                ->where(['Showrooms.showroom_supervisor' => $supervisorId, 'Showrooms.status' => 'A'])
                ->all()
                ->toArray();

            $orderData = [];
            foreach ($showrooms as $showroom) {
                $orders = $this->Orders->find()
                    ->select([
                        'month' => 'MONTH(Orders.created)',
                        'year' => 'YEAR(Orders.created)',
                        'order_count' => $this->Orders->find()->func()->count('*')
                    ])
                    ->where([
                        'Orders.showroom_id' => $showroom->id,
                        'Orders.status !=' => 'Cancelled',
                        'Orders.created >=' => $startDate
                    ])
                    ->group(['year', 'month'])
                    ->order(['year' => 'ASC', 'month' => 'ASC'])
                    ->toArray();

                $orderData[$showroom->name] = $orders;
            }    

            $orderTrendsgraphData = [];
            $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            $currentDate = new \DateTime();
            $monthLabels = [];
            for ($i = 5; $i >= 0; $i--) {
                $date = (clone $currentDate)->modify("-$i months");
                $monthLabels[] = $months[$date->format('n') - 1]; // Get month name and year
            }

            foreach ($orderData as $showroomName => $orders) {
                $orderCounts = array_fill(0, 6, 0); // Initialize an array with 6 slots (one for each month)
                
                foreach ($orders as $order) {
                    // Get the month index for the current order (0 = current month, 1 = previous month, etc.)
                    $orderMonthIndex = array_search($months[$order['month'] - 1], $monthLabels);
                    if ($orderMonthIndex !== false) {
                        $orderCounts[$orderMonthIndex] += $order['order_count'];
                    }
                }

                $orderTrendsgraphData[] = [
                    'name' => $showroomName,
                    'data' => $orderCounts
                ];
            }

            /** FETCH REVENUE TRENDS **/
            $revenueStartDate = (new \DateTime())->modify('-5 months')->format('Y-m-01');
            $revenueEndDate = (new \DateTime())->format('Y-m-t');

            // Fetch revenue trends for the last 6 months for each showroom
            $revenueData = $this->Orders->find()
                ->select([
                    'month' => 'MONTH(Orders.created)',
                    'year' => 'YEAR(Orders.created)',
                    'showroom_id',
                    'total_revenue' => $this->Orders->find()->func()->sum('Orders.total_amount')
                ])
                ->where([
                    'Orders.created >=' => $revenueStartDate,
                    'Orders.created <=' => $revenueEndDate,
                    'Orders.status !=' => 'Cancelled',
                    'Orders.showroom_id IN' => array_column($showrooms, 'id')
                ])
                ->group(['year', 'month', 'showroom_id'])
                ->order(['year' => 'ASC', 'month' => 'ASC'])
                ->toArray();

            $revenueFormattedData = [];
            $revenueMonths = [];

            // Create the month labels (Jan, Feb, etc.)
            for ($i = 5; $i >= 0; $i--) {
                $revenueMonths[] = (new \DateTime())->modify("-{$i} months")->format('M');
            }

            foreach ($showrooms as $showroom) {
                $revenueForShowroom = [];
                foreach ($revenueMonths as $month) {
                    // Find revenue for each month and showroom
                    $revenue = 0;
                    foreach ($revenueData as $data) {
                        if ($data['showroom_id'] == $showroom->id && date('M', mktime(0, 0, 0, $data['month'], 10)) === $month) {
                            $revenue = $data['total_revenue'];
                            break;
                        }
                    }
                    $revenueForShowroom[] = $revenue;
                }
                $revenueFormattedData[] = [
                    'showroom' => $showroom->name,
                    'revenue' => $revenueForShowroom
                ];
            }

            // Get the showroom IDs
            $showroomIds = array_column($showrooms, 'id');

            // Step 2: Fetch payment data from SupplierPurchaseOrders and SupplierPayments
            $supplier_payments = $this->SupplierPayment->find()
                ->select([
                    'Suppliers.name',
                    'SupplierPurchaseOrders.payment_status',
                    'SupplierPayment.amount',
                    'SupplierPayment.showroom_id'
                ])
                ->contain([
                    'Suppliers',
                    'SupplierPurchaseOrders' => [
                        'conditions' => [
                            'SupplierPurchaseOrders.id = SupplierPayment.supplier_purchase_order_id',
                            'SupplierPurchaseOrders.status' => 'A'
                        ]
                    ]
                ])
                ->where([
                    'SupplierPayment.showroom_id IN' => $showroomIds,
                ])
                ->order(['SupplierPayment.id' => 'DESC'])
                ->toArray();


            /** PENDING ACTIONS **/

            /** 1) SUPPLIER PAYMENTS **/
            $pendingBills = $this->SupplierPurchaseOrders->find()
                ->contain([
                    'SupplierPurchaseOrdersItems' => function ($q) {
                        return $q->contain(['Products'])
                             ->select([
                                'SupplierPurchaseOrdersItems.product_id',
                                'SupplierPurchaseOrdersItems.product_variant_id',
                                'SupplierPurchaseOrdersItems.product_attribute_id',
                                'SupplierPurchaseOrdersItems.approved_quantity',
                                'SupplierPurchaseOrdersItems.quantity',
                                'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
                            ]);
                    }
                ])
                ->where([
                    'SupplierPurchaseOrders.payment_status' => 'Pending',
                    'SupplierPurchaseOrders.status IN' => ['A', 'P'],
                    'SupplierPurchaseOrders.deliver_to' => 'showroom',
                    'SupplierPurchaseOrders.id_deliver_to IN' => $showroomIds
                ])
                ->all(); 

            $supplier_payment_pendings = [];

            // Loop through each purchase order and calculate pending amount
            foreach ($pendingBills as $purchaseOrder) {
                $totalPendingAmount = 0;

                foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {


                    // Fetch supplier price
                    $supplier_price_conditions = [
                        'supplier_id' => $purchaseOrder->supplier_id,
                        'product_id' => $product->product_id
                    ];

                    // Check if product_variant_id exists and is not null
                    if (!empty($product->product_variant_id)) {
                        $supplier_price_conditions['product_variant_id'] = $product->product_variant_id;
                    }

                    $supplierPrice = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where($supplier_price_conditions)
                        ->first();

                    // Calculate total pending amount for each product
                    if ($supplierPrice) {
                        $quantity = ($purchaseOrder->status === 'A') 
                            ? $product->approved_quantity 
                            : $product->quantity;
                        
                        $totalPendingAmount += $supplierPrice->supplier_price * $quantity;
                    }
                }

                $totalPendingAmount = number_format((float)$totalPendingAmount, 0, '', $thousandSeparator);

                // Store Bill No and pending amount
                $supplier_payment_pendings[] = [
                    'bill_number' => $purchaseOrder->bill_no,
                    'pending_amount' => $totalPendingAmount
                ];
            }

            /** TOP 3 SHOWROOMS **/

            $totalSalesQuery = $this->Orders->find()
                ->select([
                    'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
                ])
                ->where([
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.status !=' => 'Cancelled'
                ])
                ->first();

            $totalSales = $totalSalesQuery ? $totalSalesQuery->total_sales : 0;

            // Step 2: Get the top 3 showrooms with percentage
            $topShowrooms = $this->Orders->find()
                ->select([
                    'showroom_id',
                    'Showrooms.name',
                    'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount'),
                    'order_count' => $this->Orders->find()->func()->count('Orders.id')
                    // 'sales_percentage' => 'SUM(Orders.total_amount) * 100 / ' . $totalSales
                ])
                ->where([
                    'Orders.created >=' => $currentMonthStart,
                    'Orders.created <=' => $currentMonthEnd,
                    'Orders.status !=' => 'Cancelled',
                    'Orders.showroom_id IS NOT NULL' 
                ])
                ->group('Orders.showroom_id')
                ->order(['total_sales' => 'DESC'])
                ->limit(3)
                ->contain(['Showrooms'])
                ->toArray(); 

            foreach ($topShowrooms as &$showroom) {
                $showroom->sales_percentage = $totalSales > 0 ? ($showroom->total_sales * 100) / $totalSales : 0;
            }      

            /** FETCH TOP 5 PRODUCT CATEGORIES **/
            $totalCategorySalesQuery = $this->OrderItems->find()
                ->innerJoinWith('Products.ProductCategories.Categories')
                ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
                    return $q->where([
                        'Orders.created >=' => $currentMonthStart,
                        'Orders.created <=' => $currentMonthEnd,
                        'Orders.status !=' => 'Cancelled' 
                    ]);
                })
                ->select([
                    'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity')
                ])
                ->first();  

            $totalCategorySales = $totalCategorySalesQuery ? $totalCategorySalesQuery->total_sales : 0;    

            // Step 2: Fetch top 5 product categories and calculate percentage
            $topCategories = $this->OrderItems->find()
                ->select([
                    'category_id' => 'Categories.id',
                    'category_icon' => 'Categories.category_icon',
                    'category_name' => 'Categories.name',
                    'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
                    'units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
                ])
                ->innerJoinWith('Products.ProductCategories.Categories') // Joining the necessary tables
                ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
                    return $q->where([
                        'Orders.created >=' => $currentMonthStart,
                        'Orders.created <=' => $currentMonthEnd,
                        'Orders.status !=' => 'Cancelled'
                    ]);
                })
                ->group(['Categories.id', 'Categories.name']) // Grouping by category
                ->order(['total_sales' => 'DESC']) // Ordering by sales
                ->limit(5) // Limiting to top 5 categories
                ->toArray();

            // Calculate percentage for progress bars
            foreach ($topCategories as &$category) {
                $category['percentage'] = $totalCategorySales > 0 ? ($category['total_sales'] / $totalCategorySales) * 100 : 0;

                if ($category['category_icon']) {
                    $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
                }
            }
        }
        else
        {
            $noActiveShowroomsMessage = __('No showrooms are assigned to you.');
            $totalOrders = 0;
            $totalSalesAmount = 0;
            $percentageOrders = 0;
            $totalUsers = 0;
            $newUsers = 0;
            $newUsersPercentage = 0;
            $totalActiveProducts = 0;
            $newProducts = 0;
            $newProductsPercentage = 0;
            $totalShowrooms = 0;
            $newShowrooms = 0;
            $newShowroomsPercentage = 0;
            $orderTrendsgraphData = [];
            $monthLabels = [];
            $revenueMonths = [];
            $revenueFormattedData = [];
            $supplier_payments = [];
            $supplier_payment_pendings = [];
            $topShowrooms = [];
            $topCategories = [];
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';   
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';    

        $this->set(compact('totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage', 'orderTrendsgraphData', 'monthLabels', 'revenueMonths', 'revenueFormattedData' ,'supplier_payments', 'supplier_payment_pendings', 'topShowrooms' ,'topCategories', 'currencySymbol', 'noActiveShowroomsMessage', 'decimalSeparator', 'thousandSeparator'));
    }

    public function warehouseManagerDashboard()
    {

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $user = $this->Authentication->getIdentity();

        if (!empty($user)) {
            // Get the role of the user
            $role = $this->Roles->get($user->role_id);

            if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'warehouse assistant') {
                
                if(strtolower($role->name) === 'warehouse manager')
                {
                    // Get the warehouse_id for the manager
                    $warehouse_detail = $this->Warehouses->find()
                        ->where(['manager_id' => $user->id])
                        ->first();
                }
                else if(strtolower($role->name) === 'warehouse assistant')
                {
                    // Get the warehouse_id for the manager
                    $warehouse_detail = $this->Warehouses->find()
                        ->where(['assistant_id' => $user->id])
                        ->first();
                }
            }
        }

        /** GET WAREHOUSE DETAILS **/
        if(!empty($warehouse_detail))
        {
            $noActiveWarehouseMessage = null;

            // Fetch warehouse_id for the manager (e.g., from session or role assignment)
            $warehouseId = $warehouse_detail->id;

            // Query to calculate total stocks and low-stock items
            $totalStocks = $this->ProductStocks->find()
                ->select(['total' => 'SUM(quantity)'])
                ->where(['warehouse_id' => $warehouseId])
                ->first()
                ->total;

            // Calculate percentage of low-stock items
            $totalProducts = $this->ProductStocks->find()
                ->where(['warehouse_id' => $warehouseId])
                ->count();

            // Join StockMovements.referenceID = StockRequests.id manually
            // Get all referenceIDs from StockMovements for Incoming to this warehouse
            // Get referenceIDs from StockMovements that are already linked
            $referencedRequestIds = $this->StockMovements->find()
                ->select(['referenceID'])
                ->where([
                    'movement_type' => 'Incoming',
                    'warehouse_id' => $warehouseId,
                    'verify_status' => 'Pending',
                    'referenceID IS NOT' => null
                ])
                ->all()
                ->extract('referenceID')
                ->toArray();

            // Get pending StockRequests not yet linked to any StockMovement
            // $pendingStockRequests = $this->StockRequests->find()
            //     ->where([
            //         'requestor_type' => 'Warehouse',
            //         'status' => 'A',
            //         'request_status' => 'Pending',
            //         'warehouse_id' => $warehouseId,
            //         'id NOT IN' => $referencedRequestIds
            //     ])
            //     ->count();

            // Step 2: Prepare Stock Requests query (those that need to be processed)
            $pendingStockRequestsQuery = $this->StockRequests->find()
                ->where([
                    'StockRequests.status' => 'A',
                    'StockRequests.requestor_type' => 'Warehouse',
                    'StockRequests.warehouse_id' => $warehouseId,
                    'StockRequests.request_status' => 'Pending'
                ]);

            // Step 3: Apply exclusion condition only if reference IDs exist
            if (!empty($referencedRequestIds)) {
                $pendingStockRequestsQuery->where(['StockRequests.id NOT IN' => $referencedRequestIds]);
            }

            // Step 4: Execute the query
            $pendingStockRequests = $pendingStockRequestsQuery->toArray();

            // Get pending Incoming StockMovements
            $pendingStockMovements = $this->StockMovements->find()
                ->where([
                    'movement_type' => 'Incoming',
                    'verify_status' => 'Pending',
                    'warehouse_id' => $warehouseId
                ])
                ->count();

            // Final total pending requests = both types
            $pendingRequests = count($pendingStockRequests) + $pendingStockMovements;

            // Fetch completed stock requests
            $completedRequests = $this->StockRequests->find()
                ->where([
                    'StockRequests.requestor_type' => 'Warehouse',
                    'StockRequests.status' => 'A',
                    'StockRequests.request_status' => 'Completed',
                    'StockRequests.warehouse_id' => $warehouseId
                ])
                ->count();

            // Calculate total requests
            $totalRequests = $pendingRequests + $completedRequests;

            // Percentage of completed requests
            $percentageCompleted = $totalRequests > 0 ? ($completedRequests / $totalRequests) * 100 : 0;

            /** OUTGOING STOCKS **/
            // Total dispatched from StockMovements
            $dispatchedProducts = $this->StockMovements->find()
                ->contain(['StockMovementItems'])
                ->where([
                    'StockMovements.movement_type' => 'Outgoing',
                    'StockMovements.verify_status' => 'Approved',
                    'StockMovements.warehouse_id' => $warehouseId
                ])
                ->all();

            $totalDispatchedItemsFromMovements = 0;
            foreach ($dispatchedProducts as $movement) {
                foreach ($movement->stock_movement_items as $item) {
                    $totalDispatchedItemsFromMovements += $item->quantity;
                }
            }

            // Sum quantity from shipment_order_items
            $shipmentQuantities = $this->ShipmentOrderItems->find()
                ->matching('ShipmentOrders', function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A']);
                })
                ->matching('ShipmentOrders.Shipments', function ($q) use ($warehouseId) {
                    return $q->where([
                        'Shipments.status' => 'A',
                        'Shipments.sender_type' => 'Warehouse',
                        'Shipments.senderID' => $warehouseId,
                        'Shipments.delivery_status NOT IN' => ['Delivered', 'Failed', 'Returned', 'Return Pickup']
                    ]);
                })
                ->select(['total_qty' => 'SUM(ShipmentOrderItems.quantity)'])
                ->first();

            $totalDispatchedItemsFromShipments = $shipmentQuantities->total_qty ?? 0;

            // Combine both dispatched item quantities
            $totalDispatchedItems = $totalDispatchedItemsFromMovements + $totalDispatchedItemsFromShipments;

            // Optional: define a total capacity
            $totalCapacity = 1000;

            // Calculate dispatch percentage
            $dispatchPercentage = ($totalDispatchedItems / $totalCapacity) * 100;
            $dispatchPercentage = min(100, round($dispatchPercentage, 2));

            // Count total movements (dispatch events)
            $totalDispatchedMovementsFromShipments = $this->ShipmentOrderItems->find()
                ->matching('ShipmentOrders', function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A']);
                })
                ->matching('ShipmentOrders.Shipments', function ($q) use ($warehouseId) {
                    return $q->where([
                        'Shipments.status' => 'A',
                        'Shipments.sender_type' => 'Warehouse',
                        'Shipments.senderID' => $warehouseId,
                        'Shipments.delivery_status NOT IN' => ['Delivered', 'Failed', 'Returned', 'Return Pickup']
                    ]);
                })
                ->count();

            $totalDispatchedMovements = count($dispatchedProducts) + $totalDispatchedMovementsFromShipments;

            // Step 1: Fetch stock_request_ids for the given warehouse
            $stockRequestIds = $this->StockRequests->find()
                ->select(['id'])
                ->where([
                    'requestor_type' => 'Warehouse',
                    'warehouse_id' => $warehouseId,
                    'status' => 'A'
                ])
                ->all()
                ->extract('id')
                ->toArray();

            // Step 2: Fetch supplier_purchase_orders linked to these stock_request_ids
            if (!empty($stockRequestIds)) {

                $supplierPurchaseOrders = $this->SupplierPurchaseOrders->find()
                    ->contain([
                        'SupplierPurchaseOrdersItems' => function ($q) {
                            return $q->contain(['Products'])
                                ->select([
                                    'SupplierPurchaseOrdersItems.product_id',
                                    'SupplierPurchaseOrdersItems.product_variant_id',
                                    'SupplierPurchaseOrdersItems.product_attribute_id',
                                    'SupplierPurchaseOrdersItems.approved_quantity',
                                    'SupplierPurchaseOrdersItems.quantity',
                                    'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
                                ]);
                        }
                    ])
                    ->where([
                        'SupplierPurchaseOrders.stock_request_id IN' => $stockRequestIds,
                        'SupplierPurchaseOrders.payment_status' => 'Pending',
                        'SupplierPurchaseOrders.status IN' => ['A', 'P']
                    ])
                    ->all();

            } else {
                $supplierPurchaseOrders = []; // return empty result
            }

            // Step 3: Process the fetched purchase orders to calculate pending amounts
            $supplier_payment_pendings = [];

            foreach ($supplierPurchaseOrders as $purchaseOrder) {
                $totalPendingAmount = 0;

                foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {
                    // Fetch supplier price
                    $supplier_price_conditions = [
                        'supplier_id' => $purchaseOrder->supplier_id,
                        'product_id' => $product->product_id
                    ];

                    // Check if product_variant_id exists and is not null
                    if (!empty($product->product_variant_id)) {
                        $supplier_price_conditions['product_variant_id'] = $product->product_variant_id;
                    }

                    $supplierPrice = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where($supplier_price_conditions)
                        ->first();

                    // Calculate total pending amount for each product
                    if ($supplierPrice) {
                        $quantity = ($purchaseOrder->status === 'A') 
                            ? $product->approved_quantity 
                            : $product->quantity;

                        $totalPendingAmount += $supplierPrice->supplier_price * $quantity;
                    }
                }

                $totalPendingAmount = number_format((float)$totalPendingAmount, 0, '', $thousandSeparator);

                // Store Bill No and pending amount
                $supplier_payment_pendings[] = [
                    'bill_number' => $purchaseOrder->bill_no,
                    'pending_amount' => $totalPendingAmount
                ];

            }

            // TOP 5 SUPPLIER PAYMENTS: Collect supplier_purchase_order_ids for fetching payments
            $supplierPurchaseOrderIds = [];
            foreach ($supplierPurchaseOrders as $purchaseOrder) {
                $supplierPurchaseOrderIds[] = $purchaseOrder->id;
            }

            // Fetch supplier_payments linked to these purchase orders
            $supplier_payment = [];
            if (!empty($supplierPurchaseOrderIds)) {
                $supplier_payment = $this->SupplierPayment->find()
                    ->contain([
                        'Suppliers','SupplierPurchaseOrders'
                    ])
                    ->where([
                        'SupplierPayment.supplier_purchase_order_id IN' => $supplierPurchaseOrderIds
                    ])
                    ->order([
                        'SupplierPayment.id' => 'DESC'
                    ])->limit(5)->toArray();
            }

            // Calculate the start of the current month
            $currentMonthStart = FrozenDate::now()->startOfMonth();
            $currentMonthEnd = FrozenDate::now()->endOfMonth();

            // Fetch stock data for the current month
            $stockData = $this->StockMovements->find()
                ->select([
                    'month' => 'DATE_FORMAT(StockMovements.created, "%b")', // Month name (e.g., Jan, Feb)
                    'total_quantity' => 'SUM(StockMovementItems.quantity)'
                ])
                ->where([
                    'StockMovements.movement_type' => 'Incoming',
                    'StockMovements.warehouse_id' => $warehouseId,
                    'StockMovements.verify_status' => 'Approved',
                    'StockMovements.created >=' => $currentMonthStart,
                    'StockMovements.created <=' => $currentMonthEnd,
                ])
                ->innerJoinWith('StockMovementItems') // Ensures StockMovementItems is joined
                ->group('month')
                ->first(); // Only one row since it's for the current month

            // Initialize data for the last 6 months
            $lastSixMonths = [];
            for ($i = 5; $i >= 0; $i--) {
                $month = FrozenDate::now()->subMonths($i)->format('M'); // Get month name
                $lastSixMonths[$month] = 0; // Default to zero
            }

            // Populate the current month's data if available
            if ($stockData) {
                $lastSixMonths[$stockData->month] = $stockData->total_quantity;
            }

            // Prepare data for the graph
            $incomingStockGraphData = [
                'categories' => array_keys($lastSixMonths), // Month names as labels
                'data' => array_values($lastSixMonths),    // Quantities
            ];

            $incomingStockGraphData['data'] = array_map('intval', $incomingStockGraphData['data']);

            // Fetch stock data for the current month
            $outgoingStockData = $this->StockMovements->find()
                ->select([
                    'month' => 'DATE_FORMAT(StockMovements.created, "%b")', // Month name (e.g., Jan, Feb)
                    'total_quantity' => 'SUM(StockMovementItems.quantity)'
                ])
                ->where([
                    'StockMovements.movement_type' => 'Outgoing',
                    'StockMovements.warehouse_id' => $warehouseId,
                    'StockMovements.verify_status' => 'Approved',
                    'StockMovements.created >=' => $currentMonthStart,
                    'StockMovements.created <=' => $currentMonthEnd,
                ])
                ->innerJoinWith('StockMovementItems') // Ensures StockMovementItems is joined
                ->group('month')
                ->first(); // Only one row since it's for the current month

            // Initialize data for the last 6 months
            $lastSixMonthsOutgoing = [];
            for ($i = 5; $i >= 0; $i--) {
                $month = FrozenDate::now()->subMonths($i)->format('M'); // Get month name
                $lastSixMonthsOutgoing[$month] = 0; // Default to zero
            }

            // Populate the current month's data if available
            if ($outgoingStockData) {
                $lastSixMonthsOutgoing[$outgoingStockData->month] = $outgoingStockData->total_quantity;
            }

            // Prepare data for the graph
            $outgoingStockGraphData = [
                'categories' => array_keys($lastSixMonthsOutgoing), // Month names as labels
                'data' => array_values($lastSixMonthsOutgoing),    // Quantities
            ];

            $outgoingStockGraphData['data'] = array_map('intval', $outgoingStockGraphData['data']);


            /** SHIPMENTS TODAY **/
            $today = FrozenDate::today();

            $shipment_query = $this->Shipments->find()
                ->matching('ShipmentOrders', function ($q) use ($today) {
                    return $q->where([
                        'ShipmentOrders.status' => 'A',
                        'ShipmentOrders.expected_delivery_date' => $today
                    ]);
                })
                ->contain([
                    'Drivers.Users' => function ($q) {
                        return $q->select([
                            'Users.id',
                            'Users.first_name',
                            'Users.last_name'
                        ]);
                    },
                    'DeliveryPartners'
                ])
                ->where([
                    'Shipments.status' => 'A',
                    'Shipments.sender_type' => 'Warehouse',
                    'Shipments.senderID' => $warehouseId,
                    'OR' => [
                        'Shipments.driver_id IS NOT' => null,
                        'Shipments.delivery_partner_id IS NOT' => null
                    ]
                ])
                ->distinct(['Shipments.id']);

            $shipments = $shipment_query->all();
            $count = $shipments->count();

            $todays_shipments = [];
            foreach ($shipments as $shipment) {
                $name = null;

                if (!empty($shipment->driver) && !empty($shipment->driver->user)) {
                    $name = $shipment->driver->user->first_name . ' ' . $shipment->driver->user->last_name;
                } elseif (!empty($shipment->delivery_partner)) {
                    $name = $shipment->delivery_partner->name;
                }

                $todays_shipments[] = [
                    'shipment_id' => $shipment->id,
                    'name' => $name
                ];
            }

            /** LOW INVENTORY PRODUCTS **/
            $maxToShow = 5; // Number of low stock items to show initially

            $productStocks = $this->ProductStocks->find()
                ->select([
                    'ProductStocks.id',
                    'ProductStocks.product_attribute_id',
                    'ProductStocks.quantity',
                    'ProductStocks.reserved_stock',
                    'ProductStocks.defective_stock',
                    'ProductStocks.service_center_stock',
                    'ProductStocks.purchased_stock',
                    'ShowroomWarehouse' => $this->ProductStocks->find()->func()->coalesce(['Showrooms.name', 'Warehouses.name']),
                    'Products.name',
                    'Products.reference_name',
                    'ProductVariants.variant_name',
                    'SKU' => $this->ProductStocks->find()->func()->coalesce([
                        new IdentifierExpression('ProductVariants.sku'),
                        new IdentifierExpression('Products.sku')
                    ])
                ])
                ->contain([
                    'Showrooms' => ['fields' => ['Showrooms.id', 'Showrooms.name']],
                    'Warehouses' => ['fields' => ['Warehouses.id', 'Warehouses.name']],
                    'Products' => ['fields' => ['Products.id', 'Products.name', 'Products.purchase_price', 'Products.sku']],
                    'ProductVariants' => ['fields' => ['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.purchase_price', 'ProductVariants.sku']],
                ])
                ->where(['ProductStocks.warehouse_id' => $warehouseId])
                ->order(['ProductStocks.id' => 'DESC'])
                ->toArray();

            $lowInventoryProducts = [];

            foreach ($productStocks as &$stock) {
                $stock->attributes = [];

                if ($stock->product_attribute_id) {
                    $attributes = $this->ProductAttributes->find()
                        ->where(['ProductAttributes.id' => $stock->product_attribute_id])
                        ->contain([
                            'Attributes' => ['fields' => ['Attributes.name']],
                            'AttributeValues' => ['fields' => ['AttributeValues.value']]
                        ])
                        ->first();

                    if ($attributes) {
                        $stock->attributes = [
                            'attribute_name' => $attributes->attribute->name ?? '',
                            'attribute_value' => $attributes->attribute_value->value ?? ''
                        ];
                    }
                }

                $productId = $stock->product->id ?? null;
                $productVariantId = $stock->product_variant_id ?? null;

                if ($productVariantId) {
                    $supplierProduct = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where(['product_variant_id' => $productVariantId])
                        ->first();

                    if ($supplierProduct) {
                        $stock->supplier_price = $supplierProduct->supplier_price;
                    } else {
                        $fallback = $this->SupplierProducts->find()
                            ->select(['supplier_price'])
                            ->where(['product_id' => $productId])
                            ->first();

                        $stock->supplier_price = $fallback ? $fallback->supplier_price : 0;
                    }
                } else {
                    $fallback = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where(['product_id' => $productId])
                        ->first();

                    $stock->supplier_price = $fallback ? $fallback->supplier_price : 0;
                }

                $stock->value = $stock->supplier_price * $stock->quantity;

                if ($productId) {
                    $categoryData = $this->Products->find()
                        ->select(['Categories.min_product_quantity'])
                        ->leftJoinWith('ProductCategories.Categories')
                        ->where(['Products.id' => $productId])
                        ->first();

                    $stock->min_product_quantity = $categoryData['_matchingData']['Categories']['min_product_quantity'] ?? null;
                } else {
                    $stock->min_product_quantity = null;
                }

                if (
                    is_numeric($stock->min_product_quantity) &&
                    $stock->quantity < $stock->min_product_quantity
                ) {
                    $lowInventoryProducts[] = $stock;
                }
            }

            // Split the results
            $initialLowInventory = array_slice($lowInventoryProducts, 0, $maxToShow);
            $remainingLowInventory = array_slice($lowInventoryProducts, $maxToShow);

            /** PENDING APPROVALS **/
            $pendingApprovals = $this->StockMovements->find()
                ->select([
                    'StockMovements.id',
                    'StockMovements.movement_type',
                    'StockMovements.verify_status',
                    'StockMovements.created',
                    'StockMovements.referenceID',
                    'Warehouses.name',

                    // For Incoming
                    'Suppliers.name',
                    'SupplierPurchaseOrders.bill_no',

                    // For Outgoing
                    'FromUser.id',
                    'FromUser.first_name',
                    'FromUser.last_name'
                ])
                ->contain(['Warehouses']) // Existing warehouse association

                // JOIN StockRequests to link Incoming Stock
                ->leftJoin(
                    ['StockRequests' => 'stock_requests'],
                    'StockMovements.referenceID = StockRequests.id'
                )

                // Supplier for Incoming
                ->leftJoin(
                    ['Suppliers' => 'suppliers'],
                    'Suppliers.id = StockRequests.supplier_id'
                )

                // Purchase order (optional)
                ->leftJoin(
                    ['SupplierPurchaseOrders' => 'supplier_purchase_orders'],
                    'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
                )

                // Join Users table to fetch person name (for Outgoing)
                ->leftJoin(
                    ['FromUser' => 'users'],
                    'FromUser.id = StockRequests.requested_by' // Adjust here based on your actual column
                )

                ->where([
                    'StockMovements.verify_status' => 'Pending',
                    'StockMovements.warehouse_id' => $warehouseId
                ])
                ->order(['StockMovements.id' => 'DESC'])
                ->toArray();

            /** INVENTORY CATEGORIZATION **/
            $productStocksCategorization = $this->ProductStocks->find()
                ->select([
                    'ProductStocks.id',
                    'ProductStocks.product_attribute_id',
                    'ProductStocks.quantity',
                    'ProductStocks.reserved_stock',
                    'ProductStocks.defective_stock',
                    'ProductStocks.service_center_stock',
                    'ProductStocks.purchased_stock',
                    'ShowroomWarehouse' => $this->ProductStocks->find()->func()->coalesce(['Showrooms.name', 'Warehouses.name']),
                    'Products.name',
                    'Products.reference_name',
                    'ProductVariants.variant_name',
                    'SKU' => $this->ProductStocks->find()->func()->coalesce([
                        new \Cake\Database\Expression\IdentifierExpression('ProductVariants.sku'),
                        new \Cake\Database\Expression\IdentifierExpression('Products.sku')
                    ])
                ])
                ->contain([
                    'Showrooms' => ['fields' => ['Showrooms.id', 'Showrooms.name']],
                    'Warehouses' => ['fields' => ['Warehouses.id', 'Warehouses.name']],
                    'Products' => ['fields' => ['Products.id', 'Products.name', 'Products.purchase_price', 'Products.sku']],
                    'ProductVariants' => ['fields' => ['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.purchase_price', 'ProductVariants.sku']],
                ])
                ->where(['ProductStocks.warehouse_id' => $warehouseId])
                ->order(['ProductStocks.id' => 'DESC'])
                ->toArray();

            foreach ($productStocksCategorization as &$stock) {
                // Fetch supplier_price
                $productId = $stock->product->id ?? null;
                $productVariantId = $stock->product_variant_id ?? null;

                if ($productVariantId) {
                    $supplierProduct = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where(['product_variant_id' => $productVariantId])
                        ->first();
                    if ($supplierProduct) {
                        $stock->supplier_price = $supplierProduct->supplier_price;
                    } else {
                        $fallback = $this->SupplierProducts->find()
                            ->select(['supplier_price'])
                            ->where(['product_id' => $productId])
                            ->first();
                        $stock->supplier_price = $fallback ? $fallback->supplier_price : 0;
                    }
                } else {
                    $fallback = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where(['product_id' => $productId])
                        ->first();
                    $stock->supplier_price = $fallback ? $fallback->supplier_price : 0;
                }
            }

            // Aggregate summary by stock type
            $summary = [
                'In Stock' => ['count' => 0, 'value' => 0],
                'Reserved' => ['count' => 0, 'value' => 0],
                'Damaged' => ['count' => 0, 'value' => 0],
            ];

            foreach ($productStocksCategorization as $stock_data) {

                $inStockQty = max(0, $stock_data->quantity - ($stock_data->reserved_stock ?? 0) - ($stock_data->defective_stock ?? 0));
                $reservedQty = $stock_data->reserved_stock ?? 0;
                $damagedQty = $stock_data->defective_stock ?? 0;
                $supplierPrice = $stock_data->supplier_price ?? 0;

                $summary['In Stock']['count'] += $inStockQty;
                $summary['In Stock']['value'] += $inStockQty * $supplierPrice;

                $summary['Reserved']['count'] += $reservedQty;
                $summary['Reserved']['value'] += $reservedQty * $supplierPrice;

                $summary['Damaged']['count'] += $damagedQty;
                $summary['Damaged']['value'] += $damagedQty * $supplierPrice;
            }  

            $percentageStocks = $totalProducts > 0 ? (count($lowInventoryProducts) / $totalProducts) * 100 : 0;

        }
        else
        {
            $noActiveWarehouseMessage = __('No warehouse are assigned to you.');
            $totalStocks = 0;
            // $lowStockCount = 0;
            $totalProducts = 0;
            $percentageStocks = 0;
            $pendingRequests = 0;
            $completedRequests = 0;
            $totalRequests = 0;
            $percentageCompleted = 0;
            $totalDispatchedItems = 0;
            $dispatchPercentage = 0;
            $totalDispatchedMovements = 0;
            $supplier_payment_pendings = [];
            $supplier_payment = [];
            $incomingStockGraphData = [];
            $outgoingStockGraphData = [];
            $todays_shipments = [];
            $lowInventoryProducts = [];
            $initialLowInventory = [];
            $remainingLowInventory = [];
            $pendingApprovals = [];
            $summary = [];
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';   
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('warehouse_detail', 'noActiveWarehouseMessage', 'totalStocks', 'totalProducts', 'percentageStocks', 'pendingRequests', 'completedRequests', 'totalRequests', 'percentageCompleted', 'totalDispatchedItems', 'dispatchPercentage', 'totalDispatchedMovements', 'supplier_payment_pendings', 'supplier_payment', 'incomingStockGraphData', 'outgoingStockGraphData', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'todays_shipments', 'lowInventoryProducts', 'initialLowInventory', 'remainingLowInventory', 'pendingApprovals', 'summary'));
    }


    // public function filterWarehouseDashboardGraphData()
    // {
    //     $this->request->allowMethod(['post']);

    //     $startDate = $this->request->getData('startDate');
    //     $endDate = $this->request->getData('endDate');
    //     $months = $this->request->getData('months');  
    //     $warehouse_id = $this->request->getData('warehouse_id');  

    //     // Get the number of months specified (3 or 6)
    //     $numberOfMonths = count($months);

    //     // Fetch incoming stock data from stock_movements table
    //     $stockMovementsQuery = $this->StockMovements->find()
    //         ->select([
    //             'month' => 'MONTH(StockMovements.created)',
    //             'year' => 'YEAR(StockMovements.created)',
    //             'total_quantity' => $this->StockMovements->find()->func()->sum('StockMovementItems.quantity')
    //         ])
    //         ->where([
    //             'StockMovements.movement_type' => 'Incoming',
    //             'StockMovements.warehouse_id' => $warehouse_id,
    //             'DATE(StockMovements.created) >=' => $startDate,
    //             'DATE(StockMovements.created) <=' => $endDate
    //         ])
    //         ->innerJoinWith('StockMovementItems')
    //         ->group(['year', 'month'])
    //         ->order(['year' => 'ASC', 'month' => 'ASC']);

    //     $stockMovements = $stockMovementsQuery->toArray();

    //     // Prepare data for the graph
    //     $stockData = [];
    //     $monthLabels = [];
    //     $currentDate = new \DateTime();
    //     $monthLabels[] = $currentDate->format('M');
    //     for ($i = 1; $i < $numberOfMonths; $i++) {
    //         $currentDate->modify('-1 month');
    //         $monthLabels[] = $currentDate->format('M');
    //     }
    //     $monthLabels = array_reverse($monthLabels);

    //     $stockCounts = array_fill(0, count($monthLabels), 0);

    //     foreach ($stockMovements as $movement) {
    //         $movementMonthAbbr = date('M', mktime(0, 0, 0, $movement['month'], 1));
    //         $movementMonthIndex = array_search($movementMonthAbbr, $monthLabels);

    //         if ($movementMonthIndex !== false) {
    //             $stockCounts[$movementMonthIndex] += $movement['total_quantity'];
    //         }
    //     }

    //     // Prepare the final graph data
    //     $stockTrendsgraphData = [
    //         'name' => 'Stocks',
    //         'data' => $stockCounts
    //     ];

    //     return $this->response->withType('application/json')->withStringBody(json_encode([
    //         'graph_data' => [$stockTrendsgraphData],
    //         'month_labels' => $monthLabels
    //     ]));
    // }


    public function filterWarehouseDashboardGraphData()
    {
        $this->request->allowMethod(['post']);

        $startDate = $this->request->getData('startDate');
        $endDate = $this->request->getData('endDate');
        $months = $this->request->getData('months');  
        $warehouse_id = $this->request->getData('warehouse_id');  

        // Get the number of months specified (3 or 6)
        $numberOfMonths = count($months);

        // Fetch incoming stock data from stock_movements table
        $incomingStockMovementsQuery = $this->StockMovements->find()
            ->select([
                'month' => 'MONTH(StockMovements.created)',
                'year' => 'YEAR(StockMovements.created)',
                'total_quantity' => $this->StockMovements->find()->func()->sum('StockMovementItems.quantity')
            ])
            ->where([
                'StockMovements.movement_type' => 'Incoming',
                'StockMovements.warehouse_id' => $warehouse_id,
                'StockMovements.verify_status' => 'Approved',
                'DATE(StockMovements.created) >=' => $startDate,
                'DATE(StockMovements.created) <=' => $endDate
            ])
            ->innerJoinWith('StockMovementItems')
            ->group(['year', 'month'])
            ->order(['year' => 'ASC', 'month' => 'ASC']);

        $outgoingStockMovementsQuery = $this->StockMovements->find()
            ->select([
                'month' => 'MONTH(StockMovements.created)',
                'year' => 'YEAR(StockMovements.created)',
                'total_quantity' => $this->StockMovements->find()->func()->sum('StockMovementItems.quantity')
            ])
            ->where([
                'StockMovements.movement_type' => 'Outgoing',
                'StockMovements.warehouse_id' => $warehouse_id,
                'StockMovements.verify_status' => 'Approved',
                'DATE(StockMovements.created) >=' => $startDate,
                'DATE(StockMovements.created) <=' => $endDate
            ])
            ->innerJoinWith('StockMovementItems')
            ->group(['year', 'month'])
            ->order(['year' => 'ASC', 'month' => 'ASC']);

        $incomingStockMovements = $incomingStockMovementsQuery->toArray();
        $outgoingStockMovements = $outgoingStockMovementsQuery->toArray();

        // Prepare data for the graph
        $stockData = [];
        $monthLabels = [];
        $currentDate = new \DateTime();
        $monthLabels[] = $currentDate->format('M');
        for ($i = 1; $i < $numberOfMonths; $i++) {
            $currentDate->modify('-1 month');
            $monthLabels[] = $currentDate->format('M');
        }
        $monthLabels = array_reverse($monthLabels);

        $incomingStockCounts = array_fill(0, count($monthLabels), 0);
        $outgoingStockCounts = array_fill(0, count($monthLabels), 0);

        foreach ($incomingStockMovements as $movement) {
            $movementMonthAbbr = date('M', mktime(0, 0, 0, $movement['month'], 1));
            $movementMonthIndex = array_search($movementMonthAbbr, $monthLabels);

            if ($movementMonthIndex !== false) {
                $incomingStockCounts[$movementMonthIndex] += $movement['total_quantity'];
            }
        }

        foreach ($outgoingStockMovements as $movement) {
            $movementMonthAbbr = date('M', mktime(0, 0, 0, $movement['month'], 1));
            $movementMonthIndex = array_search($movementMonthAbbr, $monthLabels);

            if ($movementMonthIndex !== false) {
                $outgoingStockCounts[$movementMonthIndex] += $movement['total_quantity'];
            }
        }

        // Prepare the final graph data
        $incomingStockGraphData = [
            'name' => 'Stocks',
            'data' => $incomingStockCounts
        ];

        $outgoingStockGraphData = [
            'name' => 'Stocks',
            'data' => $outgoingStockCounts
        ];

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'incoming_stock_graph_data' => [$incomingStockGraphData],
            'outgoing_stock_graph_data' => [$outgoingStockGraphData],
            'month_labels' => $monthLabels
        ]));
    }


    public function filterDashboardCard()
    {
        $this->request->allowMethod(['get']);
        
        $dateRange = $this->request->getQuery('dateRange');

        // Get the date ranges based on the selected option
        switch ($dateRange) {
            case 'current_month':
                $startDate = FrozenTime::now()->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'last_3_months':
                $startDate = FrozenTime::now()->subMonths(3)->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'last_6_months':
                $startDate = FrozenTime::now()->subMonths(6)->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'current_year':
                $startDate = FrozenTime::now()->startOfYear();
                $endDate = FrozenTime::now()->endOfYear();
                break;
            default:
                return $this->response->withStatus(400, 'Invalid date range');
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $totalOrdersQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate
            ]);

        // Online Orders
        $onlineOrders = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled',
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.order_type' => 'Online'
            ])->count();

        // Showroom Orders
        $showroomOrders = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled',
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.order_type' => 'Showroom'
            ])->count();

        // Get the total number of orders for the current month
        $totalOrders = $totalOrdersQuery->count();

        // Total sales amount for the current month
        // Calculate total sales amount for the current month
        $totalSalesAmountQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude canceled orders
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate
            ])
            ->select([
                'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
            ])
            ->first();

        $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 0, '', $thousandSeparator) . ' ' . $currencySymbol : 0;

        // Get the total number of all-time orders (excluding Cancelled orders)
        $allTimeOrdersQuery = $this->Orders->find()
            ->where(['Orders.status !=' => 'Cancelled']);

        $totalAllTimeOrders = $allTimeOrdersQuery->count();

        // Calculate the percentage of orders in the current month
        $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

        // Normalize role names to lowercase for comparison
        $callCenterRoleNames = ['call center supervisor', 'call center agent'];

        $callCenterRoles = $this->Roles->find()
            ->select(['id'])
            ->where(function (QueryExpression $exp, Query $q) use ($callCenterRoleNames) {
                return $exp->in('LOWER(Roles.name)', $callCenterRoleNames);
            })
            ->all()
            ->extract('id')
            ->toList();

        $callCenterOrders = 0;
        if (!empty($callCenterRoles)) {
            $callCenterOrders = $this->Orders->find()
                ->where([
                    'Orders.status !=' => 'Cancelled', // Exclude canceled orders
                    'Orders.order_date >=' => $startDate,
                    'Orders.order_date <=' => $endDate,
                    'Orders.created_by_role IN' => $callCenterRoles
                ])
                ->count();
        }

        // Base query for active users
        $userQuery = $this->Users->find()
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->order(['Users.first_name' => 'ASC']);

        // Filter by role if provided
        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $userQuery->where(['Users.role_id' => $roleId]);
        }

        // Get the total count of active users
        $totalUsers = $userQuery->count();

        $newUsers = 0;
        if ($startDate && $endDate) {
            $users = $userQuery->all(); // Retrieve all users
            // echo "<pre>";print_r($users);die;
            foreach ($users as $user) {
                $createdDate = $user->created->format('Y-m-d');
                // echo "<pre>";print_r($startDate->format('Y-m-d'));
                if ($createdDate >= $startDate->format('Y-m-d')) {
                    $newUsers++;
                }
            }
        } else {
            // If no date range is provided, new users count is zero
            $newUsers = 0;
        }

        // Calculate the percentage of new users
        $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;      

        // Fetch total active products
        $totalActiveProducts = $this->Products->find()
            ->where([
                'status' => 'A'
                // 'created >=' => $startDate,
                // 'created <=' => $endDate
            ])
            ->count();

        // Fetch number of new products added in the current month
        $newProducts = $this->Products->find()
            ->where([
                'status' => 'A',
                'created >=' => $startDate,
                'created <=' => $endDate
            ])
            ->count();

        // Calculate the percentage of new products
        $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;         

        // Get the total number of active showrooms
        $totalShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A' 
                // 'created >=' => $startDate,
                // 'created <=' => $endDate
            ])
            ->count(); 

        $newShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A', 
                'created >=' => $startDate,
                'created <=' => $endDate
            ])
            ->count();          

        // Calculate the percentage of new showrooms
        $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

        // Return data as JSON
        $this->set([
            'totalOrders' => $totalOrders,
            'onlineOrders' => $onlineOrders,
            'showroomOrders' => $showroomOrders,
            'callCenterOrders' => $callCenterOrders,
            'totalSalesAmount' => $totalSalesAmount,
            'percentageOrders' => $percentageOrders,
            'totalUsers' => $totalUsers,
            'newUsers' => $newUsers,
            'newUsersPercentage' => $newUsersPercentage,
            'totalActiveProducts' => $totalActiveProducts,
            'newProducts' => $newProducts,
            'newProductsPercentage' => $newProductsPercentage,
            'totalShowrooms' => $totalShowrooms,
            'newShowrooms' => $newShowrooms,
            'newShowroomsPercentage' => $newShowroomsPercentage,
            '_serialize' => ['totalOrders', 'onlineOrders', 'showroomOrders', 'callCenterOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage']
        ]);

        return $this->response->withType('application/json')
                    ->withStringBody(json_encode([

                        'totalOrders' => $totalOrders,
                        'onlineOrders' => $onlineOrders,
                        'showroomOrders' => $showroomOrders,
                        'callCenterOrders' => $callCenterOrders,
                        'totalSalesAmount' => $totalSalesAmount,
                        'percentageOrders' => $percentageOrders,
                        'totalUsers' => $totalUsers,
                        'newUsers' => $newUsers,
                        'newUsersPercentage' => $newUsersPercentage,
                        'totalActiveProducts' => $totalActiveProducts,
                        'newProducts' => $newProducts,
                        'newProductsPercentage' => $newProductsPercentage,
                        'totalShowrooms' => $totalShowrooms,
                        'newShowrooms' => $newShowrooms,
                        'newShowroomsPercentage' => $newShowroomsPercentage

                    ]));

    }

    public function filterShowroomDashboardCard()
    {
        $this->request->allowMethod(['get']);
        
        $dateRange = $this->request->getQuery('dateRange');

        // Get the date ranges based on the selected option
        switch ($dateRange) {
            case 'current_month':
                $startDate = FrozenTime::now()->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'last_3_months':
                $startDate = FrozenTime::now()->subMonths(3)->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'last_6_months':
                $startDate = FrozenTime::now()->subMonths(6)->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'current_year':
                $startDate = FrozenTime::now()->startOfYear();
                $endDate = FrozenTime::now()->endOfYear();
                break;
            default:
                return $this->response->withStatus(400, 'Invalid date range');
        }

        $user = $this->Authentication->getIdentity();

        // Get user role
        $userWithRole = $this->Users->get($user->id, [
            'contain' => ['Roles']
        ]);
        $userRole = $userWithRole->role->name;

        // Initialize variable
        $showroom_detail = null;

        if ($userRole === 'Showroom Manager') {
            $showroom_detail = $this->Showrooms->find()
                ->where(['Showrooms.showroom_manager' => $user->id])
                ->first();
        } elseif ($userRole === 'Sales Person') {
            $showroomUser = $this->ShowroomUsers->find()
                ->where(['ShowroomUsers.user_id' => $user->id, 'ShowroomUsers.status' => 'A'])
                ->contain(['Showrooms'])
                ->first();
            if ($showroomUser) {
                $showroom_detail = $showroomUser->showroom;
            }
        }

        /** GET SHOWROOM DETAILS **/
        // $showroom_detail = $this->Showrooms->find()
        //     ->where(['Showrooms.showroom_manager' => $user->id])
        //     ->first();

        $showroomId = $showroom_detail->id;    

        $totalOrdersQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate,
                'Orders.showroom_id' => $showroomId,
                'Orders.order_type' => 'Showroom'
            ]);

        // Get the total number of orders for the current month
        $totalOrders = $totalOrdersQuery->count();

        // Total sales amount for the current month
        // Calculate total sales amount for the current month
        $totalSalesAmountQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude canceled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate,
                'Orders.showroom_id' => $showroomId,
                'Orders.order_type' => 'Showroom'
            ])
            ->select([
                'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
            ])
            ->first();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 0, '', $thousandSeparator) . ' ' . $currencySymbol : 0;

        // Get the total number of all-time orders (excluding Cancelled orders)
        $allTimeOrdersQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled',
                'Orders.showroom_id' => $showroomId
            ]);

        $totalAllTimeOrders = $allTimeOrdersQuery->count();
  
        // Calculate the percentage of orders in the current month
        $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

        $userQuery = $this->Users->find()
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->order(['Users.first_name' => 'ASC']);

        // Filter by role if provided
        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $userQuery->where(['Users.role_id' => $roleId]);
        }

        // Get the total count of active users
        $totalUsers = $userQuery->count();

        $newUsers = 0;
        if ($startDate && $endDate) {
            $users = $userQuery->all(); // Retrieve all users
            // echo "<pre>";print_r($users);die;
            foreach ($users as $user) {
                $createdDate = $user->created->format('Y-m-d');
                // echo "<pre>";print_r($createdDate);
                // echo "<pre>";print_r($startDate->format('Y-m-d'));
                if ($createdDate >= $startDate->format('Y-m-d')) {
                    $newUsers++;
                }
            }
        } else {
            // If no date range is provided, new users count is zero
            $newUsers = 0;
        }

        // Calculate the percentage of new users
        $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;      

        // Fetch total active products
        $totalActiveProducts = $this->ProductStocks->find()
            ->contain(['Products'])
            ->where([
                'ProductStocks.showroom_id' => $showroom_detail->id,
                'Products.status' => 'A'
            ])
            ->count();

         // echo "<pre>";print_r($startOfMonth);die;  
        $newProducts = $this->ProductStocks->find()
            ->contain(['Products'])
            ->where([
                'ProductStocks.showroom_id' => $showroom_detail->id,
                'Products.status' => 'A', 
                'Products.created >=' => $startDate,
                'Products.created <=' => $endDate
            ])
            ->count();

        // Calculate the percentage of new products
        $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;         

        // Return data as JSON
        $this->set([
            'totalOrders' => $totalOrders,
            'totalSalesAmount' => $totalSalesAmount,
            'percentageOrders' => $percentageOrders,
            'totalUsers' => $totalUsers,
            'newUsers' => $newUsers,
            'newUsersPercentage' => $newUsersPercentage,
            'totalActiveProducts' => $totalActiveProducts,
            'newProducts' => $newProducts,
            'newProductsPercentage' => $newProductsPercentage,
            '_serialize' => ['totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage']
        ]);

        return $this->response->withType('application/json')
                    ->withStringBody(json_encode([

                        'totalOrders' => $totalOrders,
                        'totalSalesAmount' => $totalSalesAmount,
                        'percentageOrders' => $percentageOrders,
                        'totalUsers' => $totalUsers,
                        'newUsers' => $newUsers,
                        'newUsersPercentage' => $newUsersPercentage,
                        'totalActiveProducts' => $totalActiveProducts,
                        'newProducts' => $newProducts,
                        'newProductsPercentage' => $newProductsPercentage

                    ]));

    }

    public function filterSupervisorDashboardCard()
    {
        $this->request->allowMethod(['get']);

        $user = $this->Authentication->getIdentity();

        $supervisorId = $user->id;
        
        $dateRange = $this->request->getQuery('dateRange');

        // Get the date ranges based on the selected option
        switch ($dateRange) {
            case 'current_month':
                $startDate = FrozenTime::now()->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'last_3_months':
                $startDate = FrozenTime::now()->subMonths(3)->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'last_6_months':
                $startDate = FrozenTime::now()->subMonths(6)->startOfMonth();
                $endDate = FrozenTime::now()->endOfMonth();
                break;
            case 'current_year':
                $startDate = FrozenTime::now()->startOfYear();
                $endDate = FrozenTime::now()->endOfYear();
                break;
            default:
                return $this->response->withStatus(400, 'Invalid date range');
        }

        // Fetch the active zones managed by the supervisor
        $activeShowrooms = $this->Showrooms->find()
            ->select(['id'])
            ->where(['showroom_supervisor' => $supervisorId])
            ->toArray();

        // Extract zone IDs
        $showroomIds = Hash::extract($activeShowrooms, '{n}.id');

        // Query for total orders and total sales amount for the current month
        $totalOrdersQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate,
                'Orders.order_type' => 'Showroom',
                'Orders.showroom_id IN' => $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.id IN' => $showroomIds])
            ]);

        // Get the total number of orders for the current month
        $totalOrders = $totalOrdersQuery->count();

        // Total sales amount for the current month
        $totalSalesAmountQuery = $this->Orders->find()
            ->select([
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
            ])
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude canceled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate,
                'Orders.order_type' => 'Showroom',
                'Orders.showroom_id IN' => $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.id IN' => $showroomIds])
            ])
            ->first();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 0, '', $thousandSeparator) . ' ' . $currencySymbol : 0;    

        // Get the total number of all-time orders (excluding Cancelled orders)
        $allTimeOrdersQuery = $this->Orders->find()
            ->where(['Orders.status !=' => 'Cancelled', 'Orders.showroom_id IN' => $this->Showrooms->find()
                ->select(['id'])
                ->where(['Showrooms.id IN' => $showroomIds])
            ]);


        $totalAllTimeOrders = $allTimeOrdersQuery->count();

        // Calculate the percentage of orders in the current month
        $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

        // Base query for active users
        $userQuery = $this->Users->find()
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->order(['Users.first_name' => 'ASC']);

        // Filter by role if provided
        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $userQuery->where(['Users.role_id' => $roleId]);
        }

        // Get the total count of active users
        $totalUsers = $userQuery->count();

        $newUsers = 0;
        if ($startDate && $endDate) {
            $users = $userQuery->all(); // Retrieve all users
            // echo "<pre>";print_r($users);die;
            foreach ($users as $user) {
                $createdDate = $user->created->format('Y-m-d');
                // echo "<pre>";print_r($startDate->format('Y-m-d'));
                if ($createdDate >= $startDate->format('Y-m-d')) {
                    $newUsers++;
                }
            }
        } else {
            // If no date range is provided, new users count is zero
            $newUsers = 0;
        }

        // Calculate the percentage of new users
        $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;      

        // Fetch total active products
        $totalActiveProducts = $this->Products->find()
            ->where([
                'status' => 'A'
                // 'created >=' => $startDate,
                // 'created <=' => $endDate
            ])
            ->count();

        // Fetch number of new products added in the current month
        $newProducts = $this->Products->find()
            ->where([
                'status' => 'A',
                'created >=' => $startDate,
                'created <=' => $endDate
            ])
            ->count();

        // Calculate the percentage of new products
        $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;         

        // Get the total number of active showrooms
        $totalShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A' 
                // 'created >=' => $startDate,
                // 'created <=' => $endDate
            ])
            ->count(); 

        $newShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A', 
                'created >=' => $startDate,
                'created <=' => $endDate
            ])
            ->count();          

        // Calculate the percentage of new showrooms
        $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

        // Return data as JSON
        $this->set([
            'totalOrders' => $totalOrders,
            'totalSalesAmount' => $totalSalesAmount,
            'percentageOrders' => $percentageOrders,
            'totalUsers' => $totalUsers,
            'newUsers' => $newUsers,
            'newUsersPercentage' => $newUsersPercentage,
            'totalActiveProducts' => $totalActiveProducts,
            'newProducts' => $newProducts,
            'newProductsPercentage' => $newProductsPercentage,
            'totalShowrooms' => $totalShowrooms,
            'newShowrooms' => $newShowrooms,
            'newShowroomsPercentage' => $newShowroomsPercentage,
            '_serialize' => ['totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage']
        ]);

        return $this->response->withType('application/json')
                    ->withStringBody(json_encode([

                        'totalOrders' => $totalOrders,
                        'totalSalesAmount' => $totalSalesAmount,
                        'percentageOrders' => $percentageOrders,
                        'totalUsers' => $totalUsers,
                        'newUsers' => $newUsers,
                        'newUsersPercentage' => $newUsersPercentage,
                        'totalActiveProducts' => $totalActiveProducts,
                        'newProducts' => $newProducts,
                        'newProductsPercentage' => $newProductsPercentage,
                        'totalShowrooms' => $totalShowrooms,
                        'newShowrooms' => $newShowrooms,
                        'newShowroomsPercentage' => $newShowroomsPercentage

                    ]));

    }

    public function filterDashboardCardByDate()
    {
        $this->request->allowMethod(['get']);
        
        // Get the fromDate and toDate from the query parameters
        $fromDate = $this->request->getQuery('fromDate');
        $toDate = $this->request->getQuery('toDate');

        // Validate date inputs
        if (!$fromDate || !$toDate) {
            return $this->response->withStatus(400, 'Both dates are required.');
        }

        // Convert dates to FrozenTime objects for comparison
        $startDate = FrozenTime::parse($fromDate);
        $endDate = FrozenTime::parse($toDate);

        $totalOrdersQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate
            ]);

        // Get the total number of orders for the current month
        $totalOrders = $totalOrdersQuery->count();

        // Online Orders
        $onlineOrders = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled',
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.order_type' => 'Online'
            ])->count();

        // Showroom Orders
        $showroomOrders = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled',
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
                'Orders.order_type' => 'Showroom'
            ])->count();

        // Total sales amount for the current month
        // Calculate total sales amount for the current month
        $totalSalesAmountQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude canceled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate
            ])
            ->select([
                'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
            ])
            ->first();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 0, '', $thousandSeparator) . ' ' . $currencySymbol : 0;

        // Get the total number of all-time orders (excluding Cancelled orders)
        $allTimeOrdersQuery = $this->Orders->find()
            ->where(['Orders.status !=' => 'Cancelled']);

        $totalAllTimeOrders = $allTimeOrdersQuery->count();

        // Calculate the percentage of orders in the current month
        $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

        $userQuery = $this->Users->find()
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->order(['Users.first_name' => 'ASC']);

        // Filter by role if provided
        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $userQuery->where(['Users.role_id' => $roleId]);
        }

        // Get the total count of active users
        $totalUsers = $userQuery->count();

        $newUsers = 0;
        if ($startDate && $endDate) {
            $users = $userQuery->all(); // Retrieve all users

            foreach ($users as $user) {
                $createdDate = $user->created->format('Y-m-d');

                if ($createdDate >= $startDate->format('Y-m-d')) {
                    $newUsers++;
                }
            }
        } else {
            // If no date range is provided, new users count is zero
            $newUsers = 0;
        }

        // Calculate the percentage of new users
        $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;      

        // Fetch total active products
        $totalActiveProducts = $this->Products->find()
            ->where([
                'status' => 'A'
                // 'created >=' => $startDate,
                // 'created <=' => $endDate
            ])
            ->count();

        // Fetch number of new products added in the current month
        $newProducts = $this->Products->find()
            ->where([
                'status' => 'A',
                'created >=' => $startDate,
                'created <=' => $endDate
            ])
            ->count();

        // Calculate the percentage of new products
        $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;         

        // Get the total number of active showrooms
        $totalShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A'
                // 'created >=' => $startDate,
                // 'created <=' => $endDate
            ])
            ->count(); 

        $newShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A', 
                'created >=' => $startDate,
                'created <=' => $endDate
            ])
            ->count();          

        // Calculate the percentage of new showrooms
        $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

        // Return data as JSON
        $this->set([
            'totalOrders' => $totalOrders,
            'onlineOrders' => $onlineOrders,
            'showroomOrders' => $showroomOrders,
            'totalSalesAmount' => $totalSalesAmount,
            'percentageOrders' => $percentageOrders,
            'totalUsers' => $totalUsers,
            'newUsers' => $newUsers,
            'newUsersPercentage' => $newUsersPercentage,
            'totalActiveProducts' => $totalActiveProducts,
            'newProducts' => $newProducts,
            'newProductsPercentage' => $newProductsPercentage,
            'totalShowrooms' => $totalShowrooms,
            'newShowrooms' => $newShowrooms,
            'newShowroomsPercentage' => $newShowroomsPercentage,
            '_serialize' => ['totalOrders', 'onlineOrders', 'showroomOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage']
        ]);

        return $this->response->withType('application/json')
                    ->withStringBody(json_encode([

                        'totalOrders' => $totalOrders,
                        'onlineOrders' => $onlineOrders,
                        'showroomOrders' => $showroomOrders,
                        'totalSalesAmount' => $totalSalesAmount,
                        'percentageOrders' => $percentageOrders,
                        'totalUsers' => $totalUsers,
                        'newUsers' => $newUsers,
                        'newUsersPercentage' => $newUsersPercentage,
                        'totalActiveProducts' => $totalActiveProducts,
                        'newProducts' => $newProducts,
                        'newProductsPercentage' => $newProductsPercentage,
                        'totalShowrooms' => $totalShowrooms,
                        'newShowrooms' => $newShowrooms,
                        'newShowroomsPercentage' => $newShowroomsPercentage

                    ]));

    }

    public function filterShowroomDashboardCardByDate()
    {
        $this->request->allowMethod(['get']);
        
        $user = $this->Authentication->getIdentity();

        /** GET SHOWROOM DETAILS **/
        $showroom_detail = $this->Showrooms->find()
            ->where(['Showrooms.showroom_manager' => $user->id])
            ->first();

        $showroomId = $showroom_detail->id;

        // Get the fromDate and toDate from the query parameters
        $fromDate = $this->request->getQuery('fromDate');
        $toDate = $this->request->getQuery('toDate');

        // Validate date inputs
        if (!$fromDate || !$toDate) {
            return $this->response->withStatus(400, 'Both dates are required.');
        }

        // Convert dates to FrozenTime objects for comparison
        $startDate = FrozenTime::parse($fromDate);
        $endDate = FrozenTime::parse($toDate);

        $totalOrdersQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate,
                'Orders.showroom_id' => $showroomId,
                'Orders.order_type' => 'Showroom'
            ]);

        // Get the total number of orders for the current month
        $totalOrders = $totalOrdersQuery->count();

        // Total sales amount for the current month
        // Calculate total sales amount for the current month
        $totalSalesAmountQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude canceled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate,
                'Orders.showroom_id' => $showroomId,
                'Orders.order_type' => 'Showroom'
            ])
            ->select([
                'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
            ])
            ->first();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 0, '', $thousandSeparator) . ' ' . $currencySymbol : 0;

        // Get the total number of all-time orders (excluding Cancelled orders)
        $allTimeOrdersQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled',
                'Orders.showroom_id' => $showroomId
            ]);

        $totalAllTimeOrders = $allTimeOrdersQuery->count();

        // Calculate the percentage of orders in the current month
        $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

        $userQuery = $this->Users->find()
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->order(['Users.first_name' => 'ASC']);

        // Filter by role if provided
        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $userQuery->where(['Users.role_id' => $roleId]);
        }

        // Get the total count of active users
        $totalUsers = $userQuery->count();

        $newUsers = 0;
        if ($startDate && $endDate) {
            $users = $userQuery->all(); // Retrieve all users

            foreach ($users as $user) {
                $createdDate = $user->created->format('Y-m-d');

                if ($createdDate >= $startDate->format('Y-m-d')) {
                    $newUsers++;
                }
            }
        } else {
            // If no date range is provided, new users count is zero
            $newUsers = 0;
        }

        // Calculate the percentage of new users
        $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;      

        // Fetch total active products
        // Fetch total active products
        $totalActiveProducts = $this->ProductStocks->find()
            ->contain(['Products'])
            ->where([
                'ProductStocks.showroom_id' => $showroomId,
                'Products.status' => 'A'
            ])
            ->count();
 
        $newProducts = $this->ProductStocks->find()
            ->contain(['Products'])
            ->where([
                'ProductStocks.showroom_id' => $showroomId,
                'Products.status' => 'A', 
                'Products.created >=' => $startDate,
                'Products.created <=' => $endDate
            ])
            ->count();

        // Calculate the percentage of new products
        $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;         

        // Return data as JSON
        $this->set([
            'totalOrders' => $totalOrders,
            'totalSalesAmount' => $totalSalesAmount,
            'percentageOrders' => $percentageOrders,
            'totalUsers' => $totalUsers,
            'newUsers' => $newUsers,
            'newUsersPercentage' => $newUsersPercentage,
            'totalActiveProducts' => $totalActiveProducts,
            'newProducts' => $newProducts,
            'newProductsPercentage' => $newProductsPercentage,
            '_serialize' => ['totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage']
        ]);

        return $this->response->withType('application/json')
                    ->withStringBody(json_encode([

                        'totalOrders' => $totalOrders,
                        'totalSalesAmount' => $totalSalesAmount,
                        'percentageOrders' => $percentageOrders,
                        'totalUsers' => $totalUsers,
                        'newUsers' => $newUsers,
                        'newUsersPercentage' => $newUsersPercentage,
                        'totalActiveProducts' => $totalActiveProducts,
                        'newProducts' => $newProducts,
                        'newProductsPercentage' => $newProductsPercentage

                    ]));

    }

    public function filterSupervisorDashboardCardByDate()
    {
        $this->request->allowMethod(['get']);
        
        $user = $this->Authentication->getIdentity();

        $supervisorId = $user->id;
        
        // Get the fromDate and toDate from the query parameters
        $fromDate = $this->request->getQuery('fromDate');
        $toDate = $this->request->getQuery('toDate');

        // Validate date inputs
        if (!$fromDate || !$toDate) {
            return $this->response->withStatus(400, 'Both dates are required.');
        }

        // Convert dates to FrozenTime objects for comparison
        $startDate = FrozenTime::parse($fromDate);
        $endDate = FrozenTime::parse($toDate);

        // Fetch the active zones managed by the supervisor
        $activeShowrooms = $this->Showrooms->find()
            ->select(['id'])
            ->where(['showroom_supervisor' => $supervisorId])
            ->toArray();

        // Extract zone IDs
        $showroomIds = Hash::extract($activeShowrooms, '{n}.id');

        // Query for total orders and total sales amount for the current month
        $totalOrdersQuery = $this->Orders->find()
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate,
                'Orders.order_type' => 'Showroom',
                'Orders.showroom_id IN' => $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.id IN' => $showroomIds])
            ]);

        // Get the total number of orders for the current month
        $totalOrders = $totalOrdersQuery->count();

        // Total sales amount for the current month
        $totalSalesAmountQuery = $this->Orders->find()
            ->select([
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
            ])
            ->where([
                'Orders.status !=' => 'Cancelled', // Exclude canceled orders
                'Orders.created >=' => $startDate,
                'Orders.created <=' => $endDate,
                'Orders.order_type' => 'Showroom',
                'Orders.showroom_id IN' => $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['Showrooms.id IN' => $showroomIds])
            ])
            ->first();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 0, '', $thousandSeparator) . ' ' . $currencySymbol : 0;

        // Get the total number of all-time orders (excluding Cancelled orders)
        $allTimeOrdersQuery = $this->Orders->find()
            ->where(['Orders.status !=' => 'Cancelled', 'Orders.showroom_id IN' => $this->Showrooms->find()
                ->select(['id'])
                ->where(['Showrooms.id IN' => $showroomIds])
            ]);

        $totalAllTimeOrders = $allTimeOrdersQuery->count();

        // Calculate the percentage of orders in the current month
        $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

        $userQuery = $this->Users->find()
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->order(['Users.first_name' => 'ASC']);

        // Filter by role if provided
        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $userQuery->where(['Users.role_id' => $roleId]);
        }

        // Get the total count of active users
        $totalUsers = $userQuery->count();

        $newUsers = 0;
        if ($startDate && $endDate) {
            $users = $userQuery->all(); // Retrieve all users
            
            foreach ($users as $user) {
                $createdDate = $user->created->format('Y-m-d');

                if ($createdDate >= $startDate->format('Y-m-d')) {
                    $newUsers++;
                }
            }
        } else {
            // If no date range is provided, new users count is zero
            $newUsers = 0;
        }

        // Calculate the percentage of new users
        $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;      

        // Fetch total active products
        $totalActiveProducts = $this->Products->find()
            ->where([
                'status' => 'A'
                // 'created >=' => $startDate,
                // 'created <=' => $endDate
            ])
            ->count();

        // Fetch number of new products added in the current month
        $newProducts = $this->Products->find()
            ->where([
                'status' => 'A',
                'created >=' => $startDate,
                'created <=' => $endDate
            ])
            ->count();

        // Calculate the percentage of new products
        $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;         

        // Get the total number of active showrooms
        $totalShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A'
                // 'created >=' => $startDate,
                // 'created <=' => $endDate
            ])
            ->count(); 

        $newShowrooms = $this->Showrooms->find()
            ->where([
                'status' => 'A', 
                'created >=' => $startDate,
                'created <=' => $endDate
            ])
            ->count();          

        // Calculate the percentage of new showrooms
        $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

        // Return data as JSON
        $this->set([
            'totalOrders' => $totalOrders,
            'totalSalesAmount' => $totalSalesAmount,
            'percentageOrders' => $percentageOrders,
            'totalUsers' => $totalUsers,
            'newUsers' => $newUsers,
            'newUsersPercentage' => $newUsersPercentage,
            'totalActiveProducts' => $totalActiveProducts,
            'newProducts' => $newProducts,
            'newProductsPercentage' => $newProductsPercentage,
            'totalShowrooms' => $totalShowrooms,
            'newShowrooms' => $newShowrooms,
            'newShowroomsPercentage' => $newShowroomsPercentage,
            '_serialize' => ['totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage']
        ]);

        return $this->response->withType('application/json')
                    ->withStringBody(json_encode([

                        'totalOrders' => $totalOrders,
                        'totalSalesAmount' => $totalSalesAmount,
                        'percentageOrders' => $percentageOrders,
                        'totalUsers' => $totalUsers,
                        'newUsers' => $newUsers,
                        'newUsersPercentage' => $newUsersPercentage,
                        'totalActiveProducts' => $totalActiveProducts,
                        'newProducts' => $newProducts,
                        'newProductsPercentage' => $newProductsPercentage,
                        'totalShowrooms' => $totalShowrooms,
                        'newShowrooms' => $newShowrooms,
                        'newShowroomsPercentage' => $newShowroomsPercentage

                    ]));

    }

    public function noaccess()
    {
        $noaccess = '';
        $this->set(compact('noaccess'));
    }
}
