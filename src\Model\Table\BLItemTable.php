<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Log\Log;

/**
 * StockMovements Model
 *
 * @property \App\Model\Table\WarehousesTable&\Cake\ORM\Association\BelongsTo $Warehouses
 * @property \App\Model\Table\ShowroomsTable&\Cake\ORM\Association\BelongsTo $Showrooms
 * @property \App\Model\Table\StockDeliveriesTable&\Cake\ORM\Association\HasMany $StockDeliveries
 * @property \App\Model\Table\StockMovementItemsTable&\Cake\ORM\Association\HasMany $StockMovementItems
 * @property \App\Model\Table\StockReturnsTable&\Cake\ORM\Association\HasMany $StockReturns
 *
 * @method \App\Model\Entity\StockMovement newEmptyEntity()
 * @method \App\Model\Entity\StockMovement newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\StockMovement> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\StockMovement get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\StockMovement findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\StockMovement patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\StockMovement> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\StockMovement|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\StockMovement saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\StockMovement>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\StockMovement>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\StockMovement>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\StockMovement> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\StockMovement>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\StockMovement>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\StockMovement>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\StockMovement> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class BLItemTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('bl_items');
        $this->setPrimaryKey('id');
        $this->setDisplayField('id');

        $this->addBehavior('Timestamp');

        
        $this->hasMany('StockMovementItems', [
            'foreignKey' => 'stock_movement_id',
        ]);
        $this->hasMany('StockReturns', [
            'foreignKey' => 'stock_movement_id',
        ]);
        $this->belongsTo('Bls', [
            'foreignKey' => 'bl_id',
            'className' => 'BL', // optional if conventional
        ]);
        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('ProductVariants', [
            'foreignKey' => 'product_variant_id',
        ]);
        $this->belongsTo('ProductAttributes', [
            'foreignKey' => 'product_attribute_id',
        ]);
       

    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('bl_id');

        $validator
            ->nonNegativeInteger('product_id');
        
        $validator
            ->nonNegativeInteger('product_variant_id');
        
        $validator
            ->nonNegativeInteger('product_attribute_id');
        
        $validator
            ->nonNegativeInteger('requested_quantity');
        
        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        // $rules->add($rules->existsIn(['warehouse_id'], 'Warehouses'), ['errorField' => 'warehouse_id']);
        // $rules->add($rules->existsIn(['showroom_id'], 'Showrooms'), ['errorField' => 'showroom_id']);

        return $rules;
    }

    //S Add Stock
    public function addBLItems($id, $data) {
        $saveBLItem = null;
        $saveBLItem = $this->newEmptyEntity(); 

        // $blItem = $this->patchEntity($saveBLItem, $data, [
        //     'fields' => [
        //         'product_id', 
        //         'product_variant_id', 
        //         'product_attribute_id', 
        //         'requested_quantity', 
        //         'damaged_quantity', 
        //         'defective_quantity'
        //     ],
        //     'validate' => false
        // ]);

        // // $blItem = $this->patchEntity($saveBLItem, $data);
        // Log::write('debug', 'Saving BLItem with data: ' . print_r($blItem   , true));

        // Log::write('debug', 'Saving BLItem with data: ' . print_r($saveBLItem   , true));

        // $this->save($blItem, ['validate' => false]);

        
        // echo "hi";
        // exit;
        $saveBLItem->bl_id  = $id;
        $saveBLItem->product_id = $data['product_id'];
        if($data['product_variant_id'] !== null && $data['product_variant_id'] !== 'null')
        {
            $saveBLItem->product_variant_id = $data['product_variant_id'];
        } 
        if($data['product_attribute_id'] !== null && $data['product_attribute_id'] !== 'null')
        {    
            $saveBLItem->product_attribute_id = $data['product_attribute_id'];
        }        
        $saveBLItem->requested_quantity = $data['requested_quantity'];
        $saveBLItem->remaining_quantity = $data['remaining_quantity'] ?? 0; // Assuming remaining_quantity is optional 
        // $saveBLItem->damaged_quantity = (int)($data['damaged_quantity'] ?? 0);
        // $saveBLItem->defective_quantity = (int)($data['defective_quantity'] ?? 0);

        
        if ($obj = $this->save($saveBLItem)) {
            return $obj->id;
        } else {
            return false;
        }
    }

    //Get BLItems by BL id
    public function getBLItemsByBLId($blId)
    {
        return $this->find()
            ->where(['bl_id' => $blId])
            // ->contain(['Products']) // Optional: if you want to join related tables
            ->all()
            ->toArray();
    }

    public function getBLItemById($id)
    {
        return $this->get($id); // Fetches one record by primary key (id)
    }
}
