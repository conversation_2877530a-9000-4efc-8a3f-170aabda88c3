<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Zone> $zones
 */
?>

<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #toRemovePadding {
        margin-bottom: 0px !important;
        padding-bottom: 0px !important;
    }

    #remove-border {
        border: none;
        background-color: transparent;
    }

    .modal-backdrop {
        background-color : transparent !important;
        position: relative !important;
    }

    label {
    width: 115px !important;
    }

    #filter-body-refund-container {
        display: none;
        /* Hidden by default */
        opacity: 0;
        /* Initial state for animation */
        transform: translateX(20px);
        /* Initial state for animation */
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #filter-body-refund-container.show {
        display: block;
        opacity: 1;
        transform: translateX(0);
    }

    #filter-body-refund-container {
        display: none;
        opacity: 0;
        transform: translateX(20px);
    }

    #filter-body-refund-container.showing {
        display: block;
        animation: slideFadeIn 0.5s ease forwards;
    }

    #filter-body-refund-container.hiding {
        animation: slideFadeOut 0.5s ease forwards;
    }

    #filter-body-refund-container .input-group .btn {
        box-shadow: unset !important;
    }

    #filter-body-refund-container .btn:focus,
    #filter-body-refund-container .btn:hover,
    #filter-body-refund-container .btn.active {
        background-color: #f77f00 !important;
    }

    #filter-body-return-container {
        display: none;
        /* Hidden by default */
        opacity: 0;
        /* Initial state for animation */
        transform: translateX(20px);
        /* Initial state for animation */
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #filter-body-return-container.show {
        display: block;
        opacity: 1;
        transform: translateX(0);
    }

    #filter-body-return-container {
        display: none;
        opacity: 0;
        transform: translateX(20px);
    }

    #filter-body-return-container.showing {
        display: block;
        animation: slideFadeIn 0.5s ease forwards;
    }

    #filter-body-return-container.hiding {
        animation: slideFadeOut 0.5s ease forwards;
    }

    #filter-body-return-container .input-group .btn {
        box-shadow: unset !important;
    }

    #filter-body-return-container .btn:focus,
    #filter-body-return-container .btn:hover,
    #filter-body-return-container .btn.active {
        background-color: #f77f00 !important;
    }

</style>
<?php $this->end(); ?>

<section class="section">
    <div
        class="section-header d-flex justify-content-between align-items-center mb-3"
    >
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item active">
                <?= __('Returns / Cancellations and Refunds') ?>
            </li>
        </ul>
    </div>
    <div class="section-body1">
        <div class="container-fluid">
            <?= $this->Flash->render() ?>
        </div>
    </div>
    <div class="card" id="toRemovePadding">
            <div class="card-header p-0">
                    <h4><?= __('Refunds Pending') ?></h4>
                    <div class="card-header-form">
                            <div class="input-group">
                                <input
                                    type="text"
                                    class="form-control search-control"
                                    placeholder="Search"
                                    id="customSearchBoxRefundsPending"
                                />
                                <div class="input-group-btn">
                                    <button class="btn">
                                        <i
                                            class="fas fa-search"
                                        ></i>
                                    </button>
                                </div>
                                <button
                                    class="btn refund-menu-toggle fw-bold"
                                    type="submit"
                                    style="height:31px"
                                >
                                    <i
                                        class="fas fa-filter"
                                    ></i>
                                    <?= __('Filter') ?>
                                </button>
                            </div>
                    </div>
                </div>
                <div id="filter-body-refund-container">
                    <div
                        class="input-group m-l-10"
                    >
                        <form
                            method="get"
                            accept-charset="utf-8"
                            class="form-inline filter-rating attribute d-flex"
                            id="filter-search"
                            action=""
                        >
                            <div class="form-group m-r-20">
                                <label class="m-r-5" for="status" style="width: max-content;"><?= __('Order Status:') ?></label>
                                <?php echo $this->Form->control('filterRefundOrderStatus', [
                                    'type' => 'select',
                                    'options' => $orderstatuses,
                                    'id' => 'filterRefundOrderStatus',
                                    'class' => 'form-control form-select p-10',
                                    'label' => false,
                                    'empty' => __('Order Status'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                                ]) ?>
                            </div>

                            <div class="form-group m-r-20">
                                <label class="m-r-5" for="status" style="width: max-content;"><?= __('Refund Status:') ?></label>
                                <select name="status" id="filterRefundStatus" class="form-select form-control" style="width:150px;">
                                    <option value=""><?= __('Filter By Refund Status') ?></option>
                                    <option value="Refund Pending"><?= __('Refund Pending') ?></option>
                                    <option value="Refunded"><?= __('Refunded') ?></option>
                                </select>
                            </div>

                            <div class="form-group d-flex m-t-10">
                                <button class="btn btn-primary refund_filter" id="refund_filter">
                                    <i class="fa fa-filter" aria-hidden="true"></i>
                                </button>
                                <button type="reset" class="btn btn-primary reset_refund_filter"><i
                                        class="fas fa-redo-alt"></i></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

    <div class="section-body">
        <div class="container-fluid">
            <div class="card-body">
                <div class="table-responsive">
                    <div
                        id="table-3_wrapper"
                        class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer"
                    >
                        <table
                            class="table table-striped dataTable no-footer"
                            id="refundsPendingTable"
                            role="grid"
                            aria-describedby="table-3_info"
                        >
                            <thead>
                                <tr role="row">
                                    <th
                                    class="sorting_asc"
                                    tabindex="0"
                                    aria-controls="table-3"
                                    rowspan="1"
                                    colspan="1"
                                    aria-sort="ascending"
                                    aria-label="Request ID: activate to sort column descending"
                                    style="width: 100px"
                                >
                                    <?= __('Request ID') ?></th>               
                                    <th
                                        class="sorting_asc"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        aria-sort="ascending"
                                        style="width: 100px"
                                    >
                                        <?= __('Customer Details') ?>
                                    </th>
                                    <th
                                        class="sorting_asc"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        aria-sort="ascending"
                                        style="width: 100px"
                                    >
                                        <?= __('Order ID') ?>
                                    </th>
                                    <th
                                        class="sorting_asc"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        aria-sort="ascending"
                                        style="width: 100px"
                                    >
                                        <?= __('Order Number') ?>
                                    </th>
                                    <th
                                        class="sorting_asc"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        aria-sort="ascending"
                                        style="width: 100px"
                                    >
                                        <?= __('Order Date ') ?>
                                    </th>
                                    <th
                                        class="sorting"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        style="width: 200px"
                                    >
                                        <?= __('Requested Date') ?>
                                    </th>
                                    <th
                                        class="sorting"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        style="width: 200px"
                                    >
                                        <?= __('Request Type') ?>
                                    </th>
                                    <th
                                        class="sorting"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        style="width: 200px"
                                    >
                                        <?= __('Status') ?>
                                    </th>
                                    <th
                                        class="sorting"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        style="width: 200px"
                                    >
                                        <?= __('Refund Status') ?>
                                    </th>
                                    <!-- <th
                                        class="sorting"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        style="width: 200px"
                                    >
                                        < ?= __('Pickup Status') ?>
                                    </th> -->
                                    <th
                                        class="sorting_disabled"
                                        rowspan="1"
                                        colspan="1"
                                        aria-label="Actions"
                                        style="width: 150px"
                                    >
                                        <?= __('Actions') ?>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderRefunds as $return): ?>
                                    <tr>
                                        <td><?= isset($return['id']) ? h($return['id']) : __('N/A') ?></td>
                                        <td>
                                            <?php
                                                $user = $return['order']['customer']['user'] ?? null;

                                                $name = ($user && !empty($user['first_name']) && !empty($user['last_name'])) 
                                                    ? h($user['first_name'] . ' ' . $user['last_name']) 
                                                    : 'N/A';

                                                $email = ($user && !empty($user['email'])) ? h($user['email']) : 'N/A';
                                                $phone = ($user && !empty($user['mobile_no'])) ? h($user['mobile_no']) : 'N/A';
                                            ?>
                                            <strong><?= __('Name') ?>:</strong> <?= h($name) ?><br>
                                            <strong><?= __('Email') ?>:</strong> <?= h($email) ?><br>
                                            <strong><?= __('Phone') ?>:</strong> <?= h($phone) ?>
                                        </td>
                                        <td><?= isset($return['order_id']) ? h($return['order_id']) : __('N/A') ?></td>
                                        <td><?= isset($return['order']) ? h($return['order']['order_number']) : __('N/A') ?></td>
                                        <td><?= isset($return['order']) ? h($return['order']['order_date']->format('Y-m-d')) : __('N/A') ?></td>
                                        <td><?= isset($return['requested_at']) ? h($return['requested_at']->format('Y-m-d')) : __('N/A') ?></td>
                                        <td><?= isset($return['request_type']) ? $return['request_type'] : __('Return') ?></td>
                                        <td>
                                            <strong><?= __('Order Status') ?>:</strong> 
                                            <?= isset($return['order']['status']) ? h($return['order']['status']) : __('N/A') ?><br>
                                            
                                            <strong><?= __('Item Status') ?>:</strong> 
                                            <?= isset($return['order_item']['status']) ? h($return['order_item']['status']) : __('N/A') ?>
                                        </td>
                                        <td><?= isset($return['status']) ? h($return['status']) : __('N/A') ?></td>
                                        <td>
                                            <?php if ($canView): ?>
                                            <a href="<?= $this->Url->build(['controller' => 'Refunds', 'action' => 'view', $return['id']]) ?>"
                                                data-bs-toggle="tooltip"
                                                title=""
                                                data-original-title="View"
                                                aria-label="View"
                                                ><i
                                                    class="fas fa-eye"
                                                ></i
                                            ></a>
                                            <?php endif; ?>
                                            <?php if ($canApprove && $return['status'] == 'Refund Pending' && strtolower($roleName) !== 'showroom supervisor'): ?>
                                            <a href="javascript:void(0);"
                                                onclick="openRefundModal(<?= h($return['id']) ?>)"
                                                data-pickup-charge="<?= h($return['pickup_charge']) ?>"
                                                data-return-amount="<?= h($return['return_amount']) ?>"
                                                data-delivery-charge="<?= isset($return['order']['delivery_charge']) ? h($return['order']['delivery_charge']) : __(0) ?>"
                                                data-bs-toggle="tooltip"
                                                title=""
                                                data-original-title="Process Refund"
                                                aria-label="Process Refund">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card" id="toRemovePadding">
            <div class="card-header p-0">
                    <h4><?= __('Return and Cancellation Requests') ?></h4>
                    <div class="card-header-form">
                            <div class="input-group">
                                <input
                                    type="text"
                                    class="form-control search-control"
                                    placeholder="Search"
                                    id="customSearchBoxReturnCancellation"
                                />
                                <div class="input-group-btn">
                                    <button class="btn">
                                        <i
                                            class="fas fa-search"
                                        ></i>
                                    </button>
                                </div>
                                <?php if ($canAdd): ?>
                                    <a href="<?= $this->Url->build(['controller' => 'ReturnsCancellation', 'action' => 'add']) ?>" class="btn m-r-20 open-modal" style="height:31px">
                                        <i class="fas fa-plus"></i> <?= __('Add Return') ?>
                                    </a>
                                    <a href="<?= $this->Url->build(['controller' => 'ReturnsCancellation', 'action' => 'addCancellation']) ?>" class="btn m-r-20 open-modal" style="height:31px">
                                        <i class="fas fa-plus"></i> <?= __('Add Cancellation') ?>
                                    </a>
                                <?php endif; ?>
                                <button
                                    class="btn return-menu-toggle fw-bold"
                                    type="submit"
                                    style="height:31px"
                                >
                                    <i
                                        class="fas fa-filter"
                                    ></i>
                                    <?= __('Filter') ?>
                                </button>
                            </div>
                    </div>
                </div>
                <div id="filter-body-return-container">
                    <div
                        class="input-group m-l-10"
                    >
                        <form
                            method="get"
                            accept-charset="utf-8"
                            class="form-inline filter-rating attribute d-flex"
                            id="filter-search"
                            action=""
                        >

                            <div class="form-group m-r-20">
                                <label class="m-r-5" for="status" style="width: max-content;"><?= __('Order Status:') ?></label>
                                <?php echo $this->Form->control('filterReturnOrderStatus', [
                                    'type' => 'select',
                                    'options' => $orderstatuses,
                                    'id' => 'filterReturnOrderStatus',
                                    'class' => 'form-control form-select p-10',
                                    'label' => false,
                                    'empty' => __('Order Status'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                                ]) ?>
                            </div>

                            <div class="form-group m-r-20">
                                <label class="m-r-5" for="status" style="width: max-content;"><?= __('Status:') ?></label>
                                <select name="status" id="filterReturnStatus" class="form-select form-control" style="width:150px;">
                                    <option value=""><?= __('Filter By Status') ?></option>
                                    <option value="Pending"><?= __('Pending') ?></option>
                                    <option value="Approved"><?= __('Approved') ?></option>
                                    <option value="Refund Pending"><?= __('Refund Pending') ?></option>
                                    <option value="Refunded"><?= __('Refunded') ?></option>
                                    <option value="Rejected"><?= __('Rejected') ?></option>
                                </select>
                            </div>

                            <div class="form-group m-r-20">
                                <label class="m-r-5" for="request_type" style="width: max-content;"><?= __('Request Type:') ?></label>
                                <select name="request_type" id="filterRequestType" class="form-select form-control" style="width:150px;">
                                    <option value=""><?= __('Filter By Request Type') ?></option>
                                    <option value="Return"><?= __('Return') ?></option>
                                    <option value="Cancellation"><?= __('Cancellation') ?></option>
                                </select>
                            </div>

                            <div class="form-group d-flex m-t-10">
                                <button class="btn btn-primary return_filter" id="return_filter">
                                    <i class="fa fa-filter" aria-hidden="true"></i>
                                </button>
                                <button type="reset" class="btn btn-primary reset_return_filter"><i
                                        class="fas fa-redo-alt"></i></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        <div class="section-body">
            <div class="container-fluid">
                <div class="card-body">
                    <div class="table-responsive">
                        <div
                            id="table-3_wrapper"
                            class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer"
                        >
                            <table
                                class="table table-striped dataTable no-footer"
                                id="returnCancellationTable"
                                role="grid"
                                aria-describedby="table-3_info"
                            >
                                <thead>
                                    <tr role="row">
                                        <th
                                        class="sorting_asc"
                                        tabindex="0"
                                        aria-controls="table-3"
                                        rowspan="1"
                                        colspan="1"
                                        aria-sort="ascending"
                                        aria-label="Request ID: activate to sort column descending"
                                        style="width: 100px"
                                    >
                                        <?= __('Request ID') ?></th>               
                                        <th
                                            class="sorting_asc"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            aria-sort="ascending"
                                            style="width: 100px"
                                        >
                                            <?= __('Customer Details') ?>
                                        </th>
                                        <th
                                            class="sorting_asc"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            aria-sort="ascending"
                                            style="width: 100px"
                                        >
                                            <?= __('Order ID') ?>
                                        </th>
                                        <th
                                            class="sorting_asc"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            aria-sort="ascending"
                                            style="width: 100px"
                                        >
                                            <?= __('Order Number') ?>
                                        </th>
                                        <th
                                            class="sorting_asc"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            aria-sort="ascending"
                                            style="width: 100px"
                                        >
                                            <?= __('Order Date ') ?>
                                        </th>
                                        <th
                                            class="sorting"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            style="width: 200px"
                                        >
                                            <?= __('Requested Date') ?>
                                        </th>
                                        <!-- <th
                                            class="sorting"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            style="width: 200px"
                                        >
                                            < ?= __('Completion Date') ?>
                                        </th> -->
                                        <th
                                            class="sorting"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            style="width: 200px"
                                        >
                                            <?= __('Request Type') ?>
                                        </th>
                                        <th
                                            class="sorting"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            style="width: 200px"
                                        >
                                            <?= __('Order Status') ?>
                                        </th>
                                        <th
                                            class="sorting"
                                            tabindex="0"
                                            aria-controls="table-3"
                                            rowspan="1"
                                            colspan="1"
                                            style="width: 200px"
                                        >
                                            <?= __('Status') ?>
                                        </th>
                                        <th
                                            class="sorting_disabled"
                                            rowspan="1"
                                            colspan="1"
                                            aria-label="Actions"
                                            style="width: 150px"
                                        >
                                            <?= __('Actions') ?>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orderReturns as $return): ?>
                                    <tr>
                                        <td><?= isset($return['id']) ? h($return['id']) : __('N/A') ?></td>
                                        <td>
                                            <?php
                                                $user = $return['order']['customer']['user'] ?? null;

                                                $name = ($user && !empty($user['first_name']) && !empty($user['last_name'])) 
                                                    ? h($user['first_name'] . ' ' . $user['last_name']) 
                                                    : 'N/A';

                                                $email = ($user && !empty($user['email'])) ? h($user['email']) : 'N/A';
                                                $phone = ($user && !empty($user['mobile_no'])) ? h($user['mobile_no']) : 'N/A';
                                            ?>
                                            <strong><?= __('Name') ?>:</strong> <?= h($name) ?><br>
                                            <strong><?= __('Email') ?>:</strong> <?= h($email) ?><br>
                                            <strong><?= __('Phone') ?>:</strong> <?= h($phone) ?>
                                        </td>
                                        <td><?= isset($return['order_id']) ? h($return['order_id']) : __('N/A') ?></td>
                                        <td><?= isset($return['order']) ? h($return['order']['order_number']) : __('N/A') ?></td>
                                        <td><?= isset($return['order']) ? h($return['order']['order_date']->format('Y-m-d')) : __('N/A') ?></td>
                                        <td><?= isset($return['requested_at']) ? h($return['requested_at']->format('Y-m-d')) : __('N/A') ?></td>
                                        <!-- <td>< ?= isset($return['processed_at']) ? h($return['processed_at']->format('Y-m-d')) : __('N/A') ?></td> -->
                                        <td><?= isset($return['request_type']) ? h($return['request_type']) : __('N/A') ?></td>
                                        <td>
                                            <strong><?= __('Order Status') ?>:</strong> 
                                            <?= isset($return['order']['status']) ? h($return['order']['status']) : __('N/A') ?><br>
                                            
                                            <strong><?= __('Item Status') ?>:</strong> 
                                            <?= isset($return['order_item']['status']) ? h($return['order_item']['status']) : __('N/A') ?>
                                        </td>
                                        <td><?= isset($return['status']) ? h($return['status']) : __('N/A') ?></td>
                                        <td>
                                            <?php if ($canView): ?>
                                            <a href="<?= $this->Url->build(['controller' => 'ReturnsCancellation', 'action' => 'view', $return['id']]) ?>"
                                                data-bs-toggle="tooltip"
                                                title=""
                                                data-original-title="View"
                                                aria-label="View"
                                                ><i
                                                    class="fas fa-eye"
                                                ></i
                                            ></a>
                                            <?php endif; ?>
                                            <!-- < ?php if ($canApprove && $return['status'] == 'Pending'): ?>
                                            <a href="javascript:void(0);"
                                                onclick="< ?= $return['request_type'] === 'Cancellation'
                                                   ? "approveCancellation(" . h($return['id']) . ")"
                                                   : "approveReturn(" . h($return['id']) . ")" ?>"
                                                data-pickup-charge="< ?= h($return['pickup_charge']) ?>"
                                                data-pickup-required="< ?= h($return['pickup_required']) ?>"
                                                data-bs-toggle="tooltip"
                                                title=""
                                                data-original-title="Approve"
                                                aria-label="Approve">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            < ?php endif; ?>
                                            < ?php if ($canApprove && $return['status'] == 'Pending'): ?>
                                            <a href="javascript:void(0);"
                                                onclick="< ?= $return['request_type'] === 'Cancellation' 
                                                   ? "rejectCancellation(" . h($return['id']) . ")" 
                                                   : "rejectReturn(" . h($return['id']) . ")" ?>"
                                                data-bs-toggle="tooltip"
                                                title=""
                                                data-original-title="Reject"
                                                aria-label="Reject">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                            < ?php endif; ?> -->

                                            <?php if ($canApprove && $return['status'] == 'Pending' && $roleName !== 'showroom manager'): ?>
                                                <a href="javascript:void(0);"
                                                    onclick="<?= $return['request_type'] === 'Cancellation'
                                                       ? "approveCancellation(" . h($return['id']) . ")"
                                                       : "approveReturn(" . h($return['id']) . ")" ?>"
                                                    data-pickup-charge="<?= h($return['pickup_charge']) ?>"
                                                    data-pickup-required="<?= h($return['pickup_required']) ?>"
                                                    data-bs-toggle="tooltip"
                                                    title=""
                                                    data-original-title="Approve"
                                                    aria-label="Approve">
                                                    <i class="fas fa-check"></i>
                                                </a>

                                                <a href="javascript:void(0);"
                                                    onclick="<?= $return['request_type'] === 'Cancellation' 
                                                       ? "rejectCancellation(" . h($return['id']) . ")" 
                                                       : "rejectReturn(" . h($return['id']) . ")" ?>"
                                                    data-bs-toggle="tooltip"
                                                    title=""
                                                    data-original-title="Reject"
                                                    aria-label="Reject">
                                                    <i class="fas fa-ban"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if ($canApprove && $return['status'] == 'Approved' && $roleName !== 'showroom supervisor'): ?>
                                                
                                                <?php if (
                                                    isset($return['order']['transactions'][0]) &&
                                                    !empty($return['order']['transactions'][0]['payment_status']) &&
                                                    $return['order']['transactions'][0]['payment_status'] === 'Paid'
                                                ): ?>
                                                <a href="javascript:void(0);"
                                                    onclick="processRefund(<?= h($return['id']) ?>)"
                                                    data-bs-toggle="tooltip"
                                                    title=""
                                                    data-original-title="Process"
                                                    aria-label="Process">
                                                    <i class="fas fa-hand-holding-usd"></i>
                                                </a>
                                                <?php endif; ?>
                                                
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</section>

<!-- Refund Modal -->
<div class="modal fade" id="refundModal" tabindex="-1" aria-labelledby="refundModalLabel" aria-hidden="true" style="margin-top: 50px;">
  <div class="modal-dialog">
    <div class="modal-content">
      <form id="refundForm">
        <div class="modal-header">
          <h5 class="modal-title" id="refundModalLabel"><?= __("Process Refund") ?></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">

            <div class="form-group row">
                <label class="col-sm-3 col-form-label fw-bold"><?= __("Refund Method") ?></label>
                <div class="col-sm-9 d-flex main-field">
                    <div class="form-check mr-3">
                        <input class="form-check-input" type="radio" id="refund_wallet" name="refund_method" value="wallet" checked>
                        <label class="form-check-label" for="refund_wallet">
                            <?= __('Wallet') ?>
                        </label>
                    </div>
                    <div class="form-check mr-3">
                        <input class="form-check-input" type="radio" id="refund_cash" name="refund_method" value="cash">
                        <label class="form-check-label" for="refund_cash">
                            <?= __('Cash') ?>
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group row" id="deliveryChargeGroup" style="display:none;">
                <label for="deliveryCharge" class="col-sm-3 col-form-label fw-bold"><?= __("Pickup Charge") ?></label>
                <div class="col-sm-4">
                    <input type="number" class="form-control" id="deliveryCharge" name="delivery_charge" step="0.01" min="0">
                     <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeDeliveryCharge">
                        <label class="form-check-label" for="includeDeliveryCharge"><?= __("Include") ?></label>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-3 col-form-label fw-bold"><?= __("Return Amount") ?></label>
                <div class="col-sm-4">
                    <p class="form-control-plaintext fw-bold text-primary" id="returnAmount"><?= h(number_format(0, 0, $decimalSeparator, $thousandSeparator)) . ' ' . h($currencySymbol) ?></p>
                </div>
            </div>

            <!-- <div class="form-group row" id="returnDeliveryAmountGroup">
                <label class="col-sm-3 col-form-label fw-bold"><?= __("Return Delivery Amount") ?></label>
                <div class="col-sm-4">
                    <p class="form-control-plaintext fw-bold text-primary" id="returnDeliveryAmount">
                        <?= h(number_format(0, 0, $decimalSeparator, $thousandSeparator)) . ' ' . h($currencySymbol) ?>
                    </p>
                </div>
            </div> -->

            <div class="form-group row" id="returnDeliveryAmountGroup">
                <label class="col-sm-3 col-form-label fw-bold"><?= __("Return Delivery Amount") ?></label>
                <div class="col-sm-4">
                    <input type="number" class="form-control fw-bold text-primary" id="returnDeliveryAmount" name="return_delivery_amount" min="0" step="0.01" value="0">
                </div>
            </div>


            <div class="form-group row">
                <label class="col-sm-3 col-form-label fw-bold"><?= __("Total Refund Amount") ?></label>
                <div class="col-sm-4">
                    <p class="form-control-plaintext fw-bold text-success" id="totalRefundAmount"><?= h(number_format(0, 0, $decimalSeparator, $thousandSeparator)) . ' ' . h($currencySymbol) ?></p>
                </div>
            </div>

            <input type="hidden" name="return_id" id="returnId">

        </div>

        <div class="modal-footer">
          <button type="submit" class="btn btn-success"><?= __("Confirm Refund") ?></button>
        </div>

      </form>
    </div>
  </div>
</div>



<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<!-- <script src="< ?= $this->Url->webroot('js/filter.js') ?>"></script> -->
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script>

    const currencySymbol = '<?= h($currencySymbol) ?>';

    // function openRefundModal(returnId) {
    //     const triggerEl = event.currentTarget;

    //     const pickupCharge = triggerEl.getAttribute('data-pickup-charge');
    //     const returnAmountRaw = parseFloat(triggerEl.getAttribute('data-return-amount')) || 0;
    //     const deliveryChargeRaw = parseFloat(triggerEl.getAttribute('data-delivery-charge')) || 0;

    //     const formatCurrency = (amount) => {
    //         return amount.toLocaleString('fr-FR', { maximumFractionDigits: 0 }) + ' ' + currencySymbol;
    //     };

    //     $('#returnId').val(returnId);
    //     $('#returnAmount').text(formatCurrency(returnAmountRaw));
    //     $('#returnDeliveryAmount').text(formatCurrency(deliveryChargeRaw));

    //     if (pickupCharge === 'Yes') {
    //         $('#returnDeliveryAmountGroup').hide();

    //         $('#deliveryCharge').val(deliveryChargeRaw);
    //         $('#includeDeliveryCharge').prop('checked', true);
    //         $('#deliveryChargeGroup').show();

    //         const recalculateTotal = () => {
    //             const isChecked = $('#includeDeliveryCharge').is(':checked');
    //             const deliveryCharge = isChecked ? parseFloat($('#deliveryCharge').val()) || 0 : 0;
    //             const total = Math.max(0, returnAmountRaw - deliveryCharge);
    //             $('#totalRefundAmount').text(formatCurrency(total));
    //         };

    //         $('#deliveryCharge').off('input').on('input', recalculateTotal);
    //         $('#includeDeliveryCharge').off('change').on('change', recalculateTotal);

    //         recalculateTotal();
    //     } else {
    //         $('#returnDeliveryAmountGroup').show();

    //         $('#deliveryCharge').val(deliveryChargeRaw);
    //         $('#deliveryChargeGroup').hide(); // Hide deduction checkbox
    //         const total = returnAmountRaw + deliveryChargeRaw;
    //         $('#totalRefundAmount').text(formatCurrency(total));
    //     }

    //     $('#refundModal').modal('show');
    // }

    function openRefundModal(returnId) {
    const triggerEl = event.currentTarget;

    const pickupCharge = triggerEl.getAttribute('data-pickup-charge');
    const returnAmountRaw = parseFloat(triggerEl.getAttribute('data-return-amount')) || 0;
    const deliveryChargeRaw = parseFloat(triggerEl.getAttribute('data-delivery-charge')) || 0;

    const formatCurrency = (amount) => {
        return amount.toLocaleString('fr-FR', { maximumFractionDigits: 0 }) + ' ' + currencySymbol;
    };

    $('#returnId').val(returnId);
    $('#returnAmount').text(formatCurrency(returnAmountRaw));
    $('#returnDeliveryAmount').val(deliveryChargeRaw);

    if (pickupCharge === 'Yes') {
        $('#returnDeliveryAmountGroup').hide();

        $('#deliveryCharge').val(deliveryChargeRaw);
        $('#includeDeliveryCharge').prop('checked', true);
        $('#deliveryChargeGroup').show();

        const recalculateTotal = () => {
            const isChecked = $('#includeDeliveryCharge').is(':checked');
            const deliveryCharge = isChecked ? parseFloat($('#deliveryCharge').val()) || 0 : 0;
            const total = Math.max(0, returnAmountRaw - deliveryCharge);
            $('#totalRefundAmount').text(formatCurrency(total));
        };

        $('#deliveryCharge').off('input').on('input', recalculateTotal);
        $('#includeDeliveryCharge').off('change').on('change', recalculateTotal);

        recalculateTotal();
    } else {
        $('#returnDeliveryAmountGroup').show();
        $('#deliveryChargeGroup').hide(); // No checkbox for deduction

        const recalculateTotal = () => {
            const returnDeliveryAmount = parseFloat($('#returnDeliveryAmount').val()) || 0;
            const total = returnAmountRaw + returnDeliveryAmount;
            $('#totalRefundAmount').text(formatCurrency(total));
        };

        $('#returnDeliveryAmount').off('input').on('input', recalculateTotal);

        $('#returnDeliveryAmount').val(deliveryChargeRaw);
        recalculateTotal();
    }

    $('#refundModal').modal('show');
}



    $('#refundForm').on('submit', function (e) {

        e.preventDefault();

        const form = $(this);
        const returnId = $('#returnId').val();
        const refundMethod = $('input[name="refund_method"]:checked').val();
        // const deliveryCharge = $('#deliveryCharge').val() || 0;
        
        let deliveryCharge = 0;
        if ($('#includeDeliveryCharge').is(':checked')) {
            deliveryCharge = parseFloat($('#deliveryCharge').val()) || 0;
        }

        // Replace the dynamic currency symbol from totalRefundAmount and parse to float
        const totalRefundText = $('#totalRefundAmount').text().replace(currencySymbol, '').trim();
        const totalRefund = parseFloat(totalRefundText.replace(/\s/g, '').replace(/,/g, '')) || 0;

        swal({
            title: '<?= __("Are you sure?") ?>',
            text: '<?= __("This action will refund amount to the customer.") ?>',
            icon: "warning",
            buttons: true,
            dangerMode: true,
        }).then(function (willReject) {
            if (willReject) {

            $.ajax({
                url: '<?= $this->Url->build(["controller" => "Refunds", "action" => "processRefund"]) ?>',
                method: 'POST',
                data: {
                    return_id: returnId,
                    refund_method: refundMethod,
                    pickup_charge: deliveryCharge,
                    total_refund: totalRefund
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                beforeSend: function () {
                    $('button[type="submit"]').prop('disabled', true);
                },
                success: function (res) {
                    $('button[type="submit"]').prop('disabled', false);
                    if (res.success) {
                        swal('<?= __("Success") ?>', res.message, 'success').then(() => {
                            $('#refundModal').modal('hide');
                            location.reload();
                        });
                    } else {
                        swal('<?= __("Error") ?>', res.message, 'error');
                    }
                },
                error: function () {
                    $('button[type="submit"]').prop('disabled', false);
                    swal('<?= __("Error") ?>', '<?= __("Something went wrong. Please try again.") ?>', 'error');
                }
            });
        }
        });
    });

    document.addEventListener('DOMContentLoaded', function () {
        const filterRefundButton = document.querySelector('.btn.refund-menu-toggle');
        const filterRefundBodyContainer = document.getElementById('filter-body-refund-container');

        filterRefundButton.addEventListener('click', function (event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (filterRefundBodyContainer.classList.contains('showing')) {
                // If currently showing, trigger hiding animation
                filterRefundBodyContainer.classList.remove('showing');
                filterRefundBodyContainer.classList.add('hiding');
                filterRefundBodyContainer.addEventListener('animationend', function () {
                    filterRefundBodyContainer.classList.remove('hiding');
                    filterRefundBodyContainer.style.display = 'none'; // Ensure it's hidden after animation
                }, { once: true });
            } else {
                // If currently hidden, trigger showing animation
                filterRefundBodyContainer.style.display = 'block'; // Ensure it's visible before animation
                filterRefundBodyContainer.classList.add('showing');
            }
        });
    });

    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#refundsPendingTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": [
            { "data": "id" },
            { "data": "customer_name" },
            { "data": "order_id" },
            { "data": "order_number" },
            { "data": "order_date" },
            { "data": "requested_date" },
            { "data": "requested_type" },
            { "data": "status" },
            { "data": "refund_status" },
            // { "data": "pickup_status" },
            {
                "data": "actions",
                "render": function (data, type, row) {
                    return data;
                }
            }
        ]
    });

    $('#customSearchBoxRefundsPending').on('keyup', function () {
        table.search(this.value).draw();
    });

    $('.refund_filter').on('click', function (e) {
        e.preventDefault();

        var status = $("#filterRefundStatus option:selected").val();
        var order_status = $("#filterRefundOrderStatus option:selected").val();

        table.search('').columns().search('');

        if (order_status) {
            table.column(7).search(order_status, true, false, false).draw();
        }
        if (status) {
            table.column(8).search(status, true, false, false).draw();
        }
    });

    $('.reset_refund_filter').on('click', function (e) {
        e.preventDefault();
        table.search('').columns().search('').draw();
    });


    /** RETURNS CANCELLATION TABLE **/

    document.addEventListener('DOMContentLoaded', function () {
        const filterReturnButton = document.querySelector('.btn.return-menu-toggle');
        const filterReturnBodyContainer = document.getElementById('filter-body-return-container');

        filterReturnButton.addEventListener('click', function (event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (filterReturnBodyContainer.classList.contains('showing')) {
                // If currently showing, trigger hiding animation
                filterReturnBodyContainer.classList.remove('showing');
                filterReturnBodyContainer.classList.add('hiding');
                filterReturnBodyContainer.addEventListener('animationend', function () {
                    filterReturnBodyContainer.classList.remove('hiding');
                    filterReturnBodyContainer.style.display = 'none'; // Ensure it's hidden after animation
                }, { once: true });
            } else {
                // If currently hidden, trigger showing animation
                filterReturnBodyContainer.style.display = 'block'; // Ensure it's visible before animation
                filterReturnBodyContainer.classList.add('showing');
            }
        });
    });

    var return_cancellation_table = $("#returnCancellationTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        "columns": [
            { "data": "id" },
            { "data": "customer_details" },
            { "data": "order_id" },
            { "data": "order_number" },
            { "data": "order_date" },
            { "data": "return_requested_date" },
            // { "data": "completion_date" },
            { "data": "requested_type" },
            { "data": "order_status" },
            { "data": "status" },
            {
                "data": "actions",
                "render": function (data, type, row) {
                    return data;
                }
            }
        ]
    });

    $('#customSearchBoxReturnCancellation').on('keyup', function () {
        return_cancellation_table.search(this.value).draw();
    });

    $('.return_filter').on('click', function (e) {
        e.preventDefault();

        var status = $("#filterReturnStatus option:selected").val();
        var order_status = $("#filterReturnOrderStatus option:selected").val();
        var request_type = $("#filterRequestType option:selected").val();

        return_cancellation_table.search('').columns().search('');

        if (order_status) {
            return_cancellation_table.column(7).search(order_status, true, false, false).draw();
        }
        if (status) {
            return_cancellation_table.column(8).search(status, true, false, false).draw();
        }
        if (request_type) {
            return_cancellation_table.column(6).search(request_type, true, false, false).draw();
        }
    });

    $('.reset_return_filter').on('click', function (e) {
        e.preventDefault();
        return_cancellation_table.search('').columns().search('').draw();
    });

    /** REJECT REQUEST **/
    function rejectReturn(orderReturnId) {
        swal({
            title: '<?= __("Are you sure?") ?>',
            text: '<?= __("This action will reject the return request.") ?>',
            icon: "warning",
            buttons: true,
            dangerMode: true,
        }).then(function (willReject) {
            if (willReject) {
                $.ajax({
                    url: '<?= $this->Url->build(["controller" => "ReturnsCancellation", "action" => "rejectReturn"]) ?>',
                    type: 'POST',
                    data: {
                        id: orderReturnId
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        if (response.success) {
                            swal("<?= __('Success') ?>", response.message, "success")
                                .then(() => location.reload());
                        } else {
                            swal("<?= __('Error') ?>", response.message || '<?= __("An error occurred.") ?>', "error");
                        }
                    },
                    error: function () {
                        swal("<?= __('Error') ?>", '<?= __("Request failed. Please try again.") ?>', "error");
                    }
                });
            }
        });
    }

    function rejectCancellation(orderReturnId) {
        swal({
            title: '<?= __("Are you sure?") ?>',
            text: '<?= __("This action will reject the cancel request.") ?>',
            icon: "warning",
            buttons: true,
            dangerMode: true,
        }).then(function (willReject) {
            if (willReject) {
                $.ajax({
                    url: '<?= $this->Url->build(["controller" => "ReturnsCancellation", "action" => "rejectCancellation"]) ?>',
                    type: 'POST',
                    data: {
                        id: orderReturnId
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        if (response.success) {
                            swal("<?= __('Success') ?>", response.message, "success")
                                .then(() => location.reload());
                        } else {
                            swal("<?= __('Error') ?>", response.message || '<?= __("An error occurred.") ?>', "error");
                        }
                    },
                    error: function () {
                        swal("<?= __('Error') ?>", '<?= __("Request failed. Please try again.") ?>', "error");
                    }
                });
            }
        });
    }

    /** APPROVE REQUEST **/
    function approveReturn(orderReturnId) {

        // Use event delegation to get the clicked element's data attributes
        const clickedElement = event.currentTarget;

        const pickupRequired = $(clickedElement).data('pickup-required');
        const pickupCharge = $(clickedElement).data('pickup-charge');

        // Validate values before proceeding
        if (!pickupRequired || !pickupCharge) {
            swal(
                "<?= __('Missing Information') ?>",
                "<?= __('Pickup Required or Pickup Charge is missing. You can update it from the View page.') ?>",
                "error"
            );
            return;
        }

        swal({
            title: '<?= __("Are you sure?") ?>',
            text: '<?= __("This action will approve the return request.") ?>',
            icon: "warning",
            buttons: true,
            dangerMode: true,
        }).then(function (willReject) {
            if (willReject) {
                $.ajax({
                    url: '<?= $this->Url->build(["controller" => "ReturnsCancellation", "action" => "approveReturn"]) ?>',
                    type: 'POST',
                    data: {
                        id: orderReturnId
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        if (response.success) {
                            swal("<?= __('Success') ?>", response.message, "success")
                                .then(() => location.reload());
                        } else {
                            swal("<?= __('Error') ?>", response.message || '<?= __("An error occurred.") ?>', "error");
                        }
                    },
                    error: function () {
                        swal("<?= __('Error') ?>", '<?= __("Request failed. Please try again.") ?>', "error");
                    }
                });
            }
        });
    }

    function approveCancellation(orderReturnId) {

        // Use event delegation to get the clicked element's data attributes
        const clickedElement = event.currentTarget;

        // const pickupRequired = $(clickedElement).data('pickup-required');
        // const pickupCharge = $(clickedElement).data('pickup-charge');

        // // Validate values before proceeding
        // if (!pickupRequired || !pickupCharge) {
        //     swal(
        //         "<?= __('Missing Information') ?>",
        //         "<?= __('Pickup Required or Pickup Charge is missing. You can update it from the View page.') ?>",
        //         "error"
        //     );
        //     return;
        // }

        swal({
            title: '<?= __("Are you sure?") ?>',
            text: '<?= __("This action will approve the cancel request.") ?>',
            icon: "warning",
            buttons: true,
            dangerMode: true,
        }).then(function (willReject) {
            if (willReject) {
                $.ajax({
                    url: '<?= $this->Url->build(["controller" => "ReturnsCancellation", "action" => "approveCancellation"]) ?>',
                    type: 'POST',
                    data: {
                        id: orderReturnId
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        if (response.success) {
                            swal("<?= __('Success') ?>", response.message, "success")
                                .then(() => location.reload());
                        } else {
                            swal("<?= __('Error') ?>", response.message || '<?= __("An error occurred.") ?>', "error");
                        }
                    },
                    error: function () {
                        swal("<?= __('Error') ?>", '<?= __("Request failed. Please try again.") ?>', "error");
                    }
                });
            }
        });
    }

    /** PROCESS REFUNDS PENDING **/
    function processRefund(orderReturnId) {
        swal({
            title: '<?= __("Are you sure?") ?>',
            text: '<?= __("This action will process to refund pending.") ?>',
            icon: "warning",
            buttons: true,
            dangerMode: true,
        }).then(function (willReject) {
            if (willReject) {
                $.ajax({
                    url: '<?= $this->Url->build(["controller" => "ReturnsCancellation", "action" => "processRefund"]) ?>',
                    type: 'POST',
                    data: {
                        id: orderReturnId
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        if (response.success) {
                            swal("<?= __('Success') ?>", response.message, "success")
                                .then(() => location.reload());
                        } else {
                            swal("<?= __('Error') ?>", response.message || '<?= __("An error occurred.") ?>', "error");
                        }
                    },
                    error: function () {
                        swal("<?= __('Error') ?>", '<?= __("Request failed. Please try again.") ?>', "error");
                    }
                });
            }
        });
    }

</script>

<?php $this->end(); ?>