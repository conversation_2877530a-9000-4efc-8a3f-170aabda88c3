<?php
declare(strict_types=1);

//namespace App\Controller;
namespace App\Controller\Api\V10;

//use App\Controller\AppController;
use App\Controller\Api\V10\AppController;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Core\Configure;
use Cake\Core\Configure\Engine\PhpConfig;
use Cake\Core\Exception\Exception;
use Cake\I18n\Time;
//use Cake\Http\Response;
//use Cake\Http\Exception\BadRequestException;
//use Cake\Http\Exception\NotFoundException;
use Cake\View\JsonView;
use Cake\Utility\Security;
use Cake\Routing\Router;
use Cake\I18n\FrozenTime;
use Cake\I18n\FrozenDate;

class DriverApisController extends AppController
{
    // Declare the property
    protected $Users;
    protected $Drivers;
    protected $Shipments;
    protected $ShipmentOrders;
    protected $ShipmentOrderItems;
    protected $ShipmentDeliveryTrackings;
    protected $Orders;
    protected $OrderItems;
    protected $ContactQueryTypes;
    protected $DriverReturnOrders;
    protected $DriverReturnOrderItems;
    protected $ProductImages;
    protected $OrderReturnCategories;
    protected $ContentPages;
    protected $OtpVerifications;
    protected $DriverDeliveryPhotos;
    protected $CashHandovers;
    protected $SiteSettings;
    protected $Notifications;
    protected $ProductStocks;
    protected $Showrooms;
    protected $Warehouses;
    protected $ZohoSettings;
    protected $zohoSettings;
    
    public function initialize(): void
    {
        parent::initialize(); 

        $this->Users = $this->fetchTable('Users');
        $this->Drivers = $this->fetchTable('Drivers'); 
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->ShipmentOrderItems = $this->fetchTable('ShipmentOrderItems');

        $this->ShipmentDeliveryTrackings = $this->fetchTable('ShipmentDeliveryTrackings');
        $this->Orders = $this->fetchTable('Orders');   
        $this->OrderItems = $this->fetchTable('OrderItems');      
        $this->ContactQueryTypes = $this->fetchTable('ContactQueryTypes');  

        $this->DriverReturnOrders = $this->fetchTable('DriverReturnOrders'); 
        $this->DriverReturnOrderItems = $this->fetchTable('DriverReturnOrderItems'); 
        $this->ProductImages = $this->fetchTable('ProductImages');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories'); 
        $this->ContentPages = $this->fetchTable('ContentPages');    
        $this->OtpVerifications = $this->fetchTable('OtpVerifications');   
        $this->DriverDeliveryPhotos = $this->fetchTable('DriverDeliveryPhotos');  
        $this->CashHandovers = $this->fetchTable('CashHandovers'); 
        $this->SiteSettings = $this->fetchTable('SiteSettings');  
        $this->Notifications = $this->fetchTable('Notifications');            
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->ZohoSettings = $this->fetchTable('ZohoSettings');
        $this->zohoSettings = $this->fetchTable('zohoSettings');
        
        $this->loadComponent('Global');      
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('Zoho');

        $this->Authentication->addUnauthenticatedActions(['test', 'login', 'forgotPassword', 'resetPassword', 'viewContentPage', 'viewAboutUs', 'viewTermsConditions']);
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        //$this->response = $this->response->withType('application/json');
    }

    public function viewClasses(): array
    {
        return [JsonView::class];
    }

    //S
    public function test() {

        //$identity = $this->request->getAttribute('identity');
        //echo "<pre>"; print_r($identity); exit; 

        // For Products module
        $fieldId = $this->Zoho->createCustomField("Contacts", "FCM_Token");

        if ($fieldId) {
            echo "Field created successfully. Zoho Field ID: " . $fieldId;
        } else {
            echo "Failed to create field.";
        }
        exit;
    }

    //S
    public function settings()
    {        
        // Get the user info from the logged-in user's identity
        $identity = $this->request->getAttribute('identity');
        if($identity) {
            $roleId   = $identity->get('role_id');
            $roleName = $identity->get('_matchingData')['Roles']['name'];
            $usertype = $identity->get('user_type');
            $userId   = $identity->get('id');
            $driverId = $identity->get('_matchingData')['Drivers']['id'];                
        }
        $data = $this->request->getQuery();
        //update fcm_token
        $fcmToken = $data['fcm_token'] ?? null;
        if (!empty($fcmToken)) {            
            $fcm_token = trim($data['fcm_token']);
            $tokenUpdate = $this->Users->get($userId);           
            $tokenUpdate->fcm_token = $fcm_token;
            $this->Users->save($tokenUpdate);
        } 

        $city_abidjan = Configure::read('Constants.ABIDJAN_CITY_ID');
        $orderStatuses = Configure::read('Constants.ORDER_STATUSES');
        $orderstatuses = array_keys($orderStatuses);
        $paymentStatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $paymentstatuses = array_keys($paymentStatuses);
        $orderDeliveryStatuses = Configure::read('Constants.ORDER_DELIVERY_STATUSES');
        $orderdeliverystatuses = array_keys($orderDeliveryStatuses);
        $orderPickupStatuses = Configure::read('Constants.ORDER_PICKUP_STATUSES');
        $orderpickupstatuses = array_keys($orderPickupStatuses);      
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $googlemapkey = Configure::read('Settings.GOOGLE_MAP_API_KEY');
        $setting_data = $this->SiteSettings->getDetails();
        $customer_support_no = $setting_data['customer_support_no'];
        $support_email       = $setting_data['support_email'];

        $data = [
            'currency' => $currencySymbol,
            'google_map_api_key' => $googlemapkey,
            'orderstatuses' => $orderstatuses,
            'paymentstatuses' => $paymentstatuses,
            'orderdeliverystatuses' => $orderdeliverystatuses,
            'orderpickupstatuses' => $orderpickupstatuses,
            'ABIDJAN_CITY_ID' => $city_abidjan, 
            'customer_support_no' => $customer_support_no,
            'support_email' => $support_email
            /*'dateFormat' => $dateFormat,*/
            /*'timeFormat' =>  $timeFormat */           
        ];

        $result = [
            'status' => 'success',
            'code' => 200,
            'data' => $data,
            'message' => __('Setting listing successfully.')
        ];
        $this->response = $this->response->withStatus(200);

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S get all supervisor
    public function listSupervisor()
    {

        if ($this->request->is('get')) {

            $supervisors = $this->Users->listSupervisor();
            if ($supervisors) {
                $result = ['status' => __('success'), 'data' => $supervisors];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => __('success'), 'data' => $supervisors, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function login() {

        if ($this->request->is('post')) {
    
            $data = $this->request->getData();
                
            if ( isset($data['email']) && $data['email'] && isset($data['password']) && $data['password'] ) {
                
                $userQuery = $this->Users->find();     
                $user = $userQuery->where(['email' => $data['email'], 'user_type'=>'Driver', 'status' => 'A'])->first();
    
                if ($user && password_verify($data['password'], $user->password)) {
                    if ($user['status'] == 'A') {                         

                        $user->last_login = FrozenTime::now();
                        $user->token = Security::hash(Security::randomBytes(32));
                        $this->Users->save($user);

                        $driver = $this->Drivers->find()
                            ->where(['user_id' => $user['id']])
                            ->first();

                        if($driver['driver_photo']) {
                           $driver['driver_photo'] = $this->Media->getCloudFrontURL($driver['driver_photo']);
                        }

                        $user_data = array(
                            'user_id' => $user['id'],
                            'driver_id' => $driver['id'],
                            'profile_photo' => $driver['driver_photo'],
                            /*'role_id' => $user['role_id'],*/
                            'token' => $user['token'],
                            'role_name' => 'Driver',                 
                            'first_name' => $user['first_name'],
                            'last_name' => $user['last_name'],
                            'email' => $user['email'],
                            'country_code' => $user['country_code'],
                            'mobile_no' => $user['mobile_no'],
                            'last_login' => $user['last_login']
                        );

                        $result = ['status' => 'success', 'data' => $user_data, 'message' => __('You are logged in successfully!')];
                        $this->response = $this->response->withStatus(200);  
                    } else {
                        //$this->Authentication->logout();
                        $result = ['status' => 'error', 'message' => __('Your account is inactive or deleted')];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = ['status' => 'error', 'message' => __('Email or Password is incorrect')];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = ['status' => 'error', 'message' => __('Invalid parameters')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    } 

    //S
    public function forgotPassword() {

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $email = $data['email'];
            
            if (isset($data['email']) && $data['email']) {
                $user = $this->Users->find()->where(['email' => $data['email'], 'status' => 'A', 'user_type' => 'Driver'])->first();
                if (isset($user->id) && $user->id) {
                    
                    $user_id = $user->id;
                    $date_time = date('d-m-Y H:i:s');
                    
                    $token = Security::hash(Security::randomBytes(25));
                    $user->password_reset_token = $token;
                    $user->token_created_at = date('Y-m-d H:i:s');
                    $saved = $this->Users->save($user);

                    if($saved){

                        $web_url = Configure::read('Settings.SITE_URL');
                        $resetLink = $web_url.'Users/resetPassword/'.$token;

                        $firstname = $user->first_name;
                        $lastname  = $user->last_name;
                        $to        = $user->email;
                        $from      = Configure::read('Settings.ADMIN_EMAIL');
                        $subject   = "Reset Password";
                        $template  = "driver_forgot_password";                     
                        $viewVars = array('resetLink'=>$resetLink, 'token' => $token, 'userId' => $user->id, 'username' => $user->first_name . ' ' . $user->last_name, 'datetime' => date('d-m-Y H:i:s'));

                        $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                        //$sendEmail = 1;
                        if ($sendEmail) {
                            $result = ['status' => 'success', 'message' => __('If there is an account associated with '.$email.', you will receive an email to reset password')];
                        } else {
                            $result = ['status' => 'error', 'message' => __('Something went wrong ! please try after some time')];
                        }
                    } else {
                        $result['status'] = 'error';
                        $result['message'] = __('Unable to send the password reset email. Please try again.');
                    }

                } else {
                    $result['status'] = 'error';
                    $result['message'] = __('Please enter a registered email');
                }
            } else {
                $result['status'] = 'error';
                $result['message'] = __('Invalid parameters');
            }
        } else {
            $result['status'] = 'error';
            $result['message'] = 'Method not allowed';
        }
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function resetPassword() {

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (isset($data['password']) && $data['password'] && isset($data['link']) && $data['link']) {
                $url_array = explode("/", $data['link']);
                $date_time = base64_decode(end($url_array));
                array_pop($url_array);
                $user_id = base64_decode(end($url_array));
                $time_diff = abs(strtotime($date_time) - strtotime(date('d-m-Y H:i:s')));
                $minutes = round($time_diff / 60);
                if ($minutes > 720) {  // expires after 12 hours
                    $result['status'] = 'error';
                    $result['message'] = 'Your password reset link has expired';
                } else {
                    $user = $this->Users->get($user_id);
                    $user = $this->Users->patchEntity($user, ['password' => trim($data['password'])]);
                    if ($this->Users->save($user)) {
                        $result['status'] = 'success';
                        $result['message'] = 'Password reset successfully';
                    } else {
                        $result['status'] = 'error';
                        $result['message'] = 'Something went wrong ! please try after some time';
                    }
                }
            } else {
                $result['status'] = 'error';
                $result['message'] = 'Invalid parameters given';
            }
        } else {
            $result['status'] = 'error';
            $result['message'] = 'Method not allowed';
        }
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function changePassword()
    {
        if ($this->request->is('post')) {

            $data = $this->request->getData();
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(401);
                goto label;
            }

            $userId = $identity->get('id');

            if ($data['new_password'] !== $data['confirm_password']) {
                $result = ['status' => 'error', 'message' => __('New password and confirm password do not match')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $changePasswordResult = $this->Users->changeUserPassword($userId, $data['old_password'], $data['new_password']);

            if ($changePasswordResult['status'] === 'success') {
                $result = ['status' => __('success'), 'message' => __('Password changed successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'error', 'message' => __('Old password is incorrect.')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S logout
    public function logout() {

        if ($this->request->is('get')) {  

            // Get the token from headers
            //$authHeader = $this->request->getHeaderLine('Authorization');
            /*if (empty($authHeader)) {
                return $this->response->withStatus(400)->withStringBody('Token is required');
            }*/

            // Extract the token (e.g., "Bearer <token>")
            //$token = str_replace('Bearer ', '', $authHeader);          

            $identity = $this->request->getAttribute('identity');
            if ($identity) {                
                $userId  = $identity->get('id');
                //$this->Authentication->logout();

                // Find the user by id/token
                $user = $this->Users->find()
                    ->where(['id' => $userId])
                    ->first();

                if (!$user) {
                    $result = ['status' => __('error'), 'message' => __('Invalid or expired token')];
                    $this->response = $this->response->withStatus(401);
                }

                // Nullify the token and optionally update last_logged_out_at
                $user->token = null;

                if ($this->Users->save($user)) {
                    $result = ['status' => __('success'), 'message' => __('You are successfully logged out')];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => __('error'), 'message' => __('Unable to log out')];
                    $this->response = $this->response->withStatus(500);
                }
                
            } else {
                $result = ['status' => __('error'), 'message' => __('Invalid or expired token')];
                $this->response = $this->response->withStatus(401);
            }                     
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function viewProfile()
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
                $driverId = $identity->get('_matchingData')['Drivers']['id'];

                $driver = $this->Drivers->find()
                    ->contain([
                        'Users'
                    ])
                    ->where(['Drivers.id' => $driverId])
                    ->first();

                if ($driver) {

                    $driver['driver_photo'] = $this->Media->getCloudFrontURL($driver['driver_photo']);

                    $result = [
                        'status' => 'success',
                        'data' => $driver
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'error', 'message' => __('Driver not found')];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function editProfile()
    {
        $result = [
            'status' => 'error',
            'code' => 200,
            'message' => __('An unexpected error occurred.')
        ];
        $this->response = $this->response->withStatus(200);

        if ($this->request->is(['post', 'put'])) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {
                $userId = $identity['id'];
                $driverId = $identity->get('_matchingData')['Drivers']['id'];

                $driver = $this->Drivers->find()
                    ->contain(['Users'])
                    ->where(['Drivers.id' => $driverId])
                    ->first();

                $data = $this->request->getData();
                if ($driver) {
                   
                   /* $first_name = $data['first_name'];
                    $last_name  = $data['last_name'];

                    $user_attributes = [
                        'first_name' => $first_name,
                        'last_name' => $last_name,
                        'country_code' => $data['country_code'],
                        'mobile_no' => $data['mobile_no']
                    ];*/

                    $driver_attributes = [];                   

                    // Check if the new mobile number is already used by another user
                   /* $existingMobile = $this->Users->find()
                        ->where(['mobile_no' => $data['mobile_no'], 'id !=' => $userId, 'status' => 'A'])
                        ->first();

                    if ($existingMobile) {
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('This mobile number is already in use by another user.')
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }*/

                    if (isset($data['driver_photo']) && $data['driver_photo']->getError() === UPLOAD_ERR_OK) {
                        
                        $driver_photo = $data['driver_photo'];
                        $fileName = trim($driver_photo->getClientFilename());

                        if (!empty($fileName)) {
                            $imageTmpName = $driver_photo->getStream()->getMetadata('uri');
                            $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $driverFolder = Configure::read('Settings.DRIVER');
                            
                            $folderPath = $uploadFolder.$driverFolder.'profile_photo/';
                            $targetdir = WWW_ROOT . $folderPath;
                            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                            $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                            $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                            if ($uploadResult !== 'Success') {
                                $result = [
                                    'status' => __('error'),
                                    'code' => 200,
                                    'message' => __('Profile picture could not be uploaded. Please, try again.')
                                ];
                                $this->response = $this->response->withStatus(200);
                                goto label;
                            } else {
                                $driver_attributes['driver_photo'] = $folderPath . $imageFile;
                            }
                        }
                    } else {
                        $driver_attributes['driver_photo'] = $driver['driver_photo'];
                    }

                    //$user = $this->Users->update_user_by_id($userId, $user_attributes);

                    /*if ($user) {*/

                        $driver = $this->Drivers->update_driver_by_id($driverId, $driver_attributes);

                        $driver['driver_photo'] = $this->Media->getCloudFrontURL($driver['driver_photo']);

                        if ($driver) {
                            $result = [
                                'status' => 'success',
                                'code' => 200,
                                'data' => $driver,
                                'message' => __('Profile updated successfully')
                            ];
                            $this->response = $this->response->withStatus(200);
                        } else {
                            $result = [
                                'status' => 'error',
                                'code' => 200,
                                'message' => __('Failed to update profile'),
                                // 'errors' => $driver->getErrors()
                            ];
                            $this->response = $this->response->withStatus(200);
                        }
                   /* } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Failed to update user information'),
                            // 'errors' => $user->getErrors()
                        ];
                        $this->response = $this->response->withStatus(200);
                    }*/
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Driver not found')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function contactUsQueryTypes()
    {
        if ($this->request->is('get')) {

            $categoriesQuery = $this->ContactQueryTypes->getAllQueryTypes();
            $categoriesArray = $categoriesQuery->toArray();

            if (empty($categoriesArray)) {
                $result = [
                    'status' => 'error',
                    'message' => __('No query type found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'success',
                    'data' => $categoriesArray
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function contactUs()
    {
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            if (empty($data['name'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Name is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['phone_number'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Phone number is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['email_id']) || !filter_var($data['email_id'], FILTER_VALIDATE_EMAIL)) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Valid email ID is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['query_type'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Query type is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['message'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Message is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $queryType = $this->ContactQueryTypes->find()
                ->select(['name'])
                ->where(['id' => $data['query_type']])
                ->first();

            $type = $queryType ? $queryType->name : __('No Query Type provided');

            $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
            $to = $adminEmails[0];
            $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
            $subject = 'Contact Us Form Submission';
            $template = 'contact_us';

            $viewVars = [
                'name' => $data['name'],
                'phone' => $data['phone_number'],
                'email' => $data['email_id'],
                'query_type' => $type,
                'message' => $data['message'],
            ];

            $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);

            if ($sendEmail) {
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('Your message has been sent successfully.')
                ];
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to send the email. Please try again later.')
                ];
            }

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function dashboard(){

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            }           

            $data = $this->request->getData();
            $today_date = date('Y-m-d');

            //total shipment assigned today
            $totalShipmentQuery = $this->Shipments->find()
                ->where(['Shipments.driver_id' => $driverId,
                'Shipments.delivery_type' => 'Driver',
                'Shipments.status' => 'A'
               //, 'ShipmentOrders.expected_delivery_date' => $today_date
            ]);
            $totalShipment = $totalShipmentQuery->count();
            $shipment_count['total'] = $totalShipment;

            //total deliveries today
            $totalDeliveryQuery = $this->ShipmentOrders->find()
                ->where(['ShipmentOrders.driver_id' => $driverId /*, 'ShipmentOrders.expected_delivery_date' => $today_date*/]);
            $totalDelivery = $totalDeliveryQuery->count();
            $delivery_count['total'] = $totalDelivery;

            //delivered today
            $deliveredCountQuery = $this->ShipmentOrders->find()
                ->where(['ShipmentOrders.order_delivery_status' => 'Delivered', 'ShipmentOrders.driver_id' => $driverId/*, 'ShipmentOrders.delivery_status_date' => $today_date*/]);
            $deliveredCount = $deliveredCountQuery->count();
            $delivery_count['delivered'] = $deliveredCount;

            //remaining
            $remainingCount = $totalDelivery - $deliveredCount;
            $delivery_count['remaining'] = $remainingCount;

            //list all deliveries schedule for the day
            $assigned_orders = $this->Shipments->listAssignedOrder($driverId, $today_date);

            // Filter out 'Return Pickup' shipments already present in driver_return_orders
            $shipmentIdsToRemove = [];

            foreach ($assigned_orders as $shipment) {
                if (
                    isset($shipment['delivery_status']) &&
                    $shipment['delivery_status'] === 'Return Pickup' &&
                    $this->DriverReturnOrders->exists(['shipment_id' => $shipment['id']])
                ) {
                    $shipmentIdsToRemove[] = $shipment['id'];
                }
            }

            // Remove the shipments from assigned_orders
            if (!empty($shipmentIdsToRemove)) {
                $assigned_orders = array_values(array_filter($assigned_orders, function ($shipment) use ($shipmentIdsToRemove) {
                    return !in_array($shipment['id'], $shipmentIdsToRemove);
                }));
            }

            $result = [
                    'status' => 'success',
                    'data' => ['shipment_count'=>$shipment_count, 'delivery_count'=>$delivery_count, 'assigned_orders'=>$assigned_orders]
                ];
            $this->response = $this->response->withStatus(200);

        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function filterDeliveryHistory() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            }           

            $data = $this->request->getData();
            $filter_date = $data['date'];
            //total deliveries
            $totalDeliveryQuery = $this->ShipmentOrders->find()
                ->where(['ShipmentOrders.driver_id' => $driverId, 'ShipmentOrders.expected_delivery_date' => $filter_date]);
            $totalDelivery = $totalDeliveryQuery->count();
            $delivery_count['total'] = $totalDelivery;

            //delivered
            $deliveredCountQuery = $this->ShipmentOrders->find()
                ->where(['ShipmentOrders.order_delivery_status' => 'Delivered', 'ShipmentOrders.driver_id' => $driverId, 'ShipmentOrders.delivery_status_date' => $filter_date]);
            $deliveredCount = $deliveredCountQuery->count();
            $delivery_count['delivered'] = $deliveredCount;

            //remaining
            $remainingCount = $totalDelivery - $deliveredCount;
            $delivery_count['remaining'] = $remainingCount;

            $result = [
                'status' => 'success',
                'data' => ['delivery_count'=>$delivery_count]
            ];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function filterDeliveryOrder() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            }           

            $data = $this->request->getData();
            $today_date = date('Y-m-d');
            $searchTerm = $data['search_term'];
            //list all deliveries schedule for the day
            //$assigned_orders = $this->ShipmentOrders->listAssignedOrder($driverId, $today_date, $searchTerm);

            $assigned_orders = $this->Shipments->listAssignedOrder($driverId, $today_date, $searchTerm);
            $result = [
                'status' => 'success',
                'data' => ['assigned_orders'=>$assigned_orders]
            ];
            $this->response = $this->response->withStatus(200);

        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S order autocomplete
    public function orderAutocomplete() {

        if ($this->request->is('get')) {

             // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            }

            // Get the search term from the query string
            $queryStr = $this->request->getQuery('q');
            
            $suggestions = $this->ShipmentOrders->orderAutocomplete($queryStr, $driverId);

            if(!empty($suggestions)){
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $suggestions
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => __( 'error'),
                    'code' => 200,
                    'data' => $suggestions,
                    'message' => __('No results found')
                ];
                $this->response = $this->response->withStatus(200);
            }

        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S
    public function viewOrder($shipmentOrderId)
    {
        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(401);
            } else {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];  

                // Fetch the order for the given order ID
                $shipment_order = $this->ShipmentOrders->orderDetail($shipmentOrderId);

                if (!$shipment_order) {
                    $result = [
                        'status' => 'error',
                        'message' => __('Order not found')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                // calculate total amount for the order to be collected by driver from customer
                $shareData = $this->calculateShipmentShare(
                    $shipment_order['order'],
                    $shipment_order['shipment_order_items']
                );                
                
                /*$assignment_details = $this->ShipmentOrders->find()->select()
                 ->where(['ShipmentOrders.id' => $shipmentOrderId])
                 ->first();*/

                 //echo "<pre>"; print_r($assignment_details);exit;

                // Prepare order data
                $orderData = [
                    'shipment_order_id' => $shipment_order->id,
                    'order_id' => $shipment_order->order->id,
                    'order_number' => $shipment_order->order->order_number,
                    'order_date' => $shipment_order->order->order_date,
                    /*'subtotal_amount' => $shipment_order->order->subtotal_amount,*/
                    /*'total_amount' => $shipment_order->order->total_amount,*/                    
                    /*'delivery_charge' => $shipment_order->order->delivery_charge,*/
                    /*'discount_amount' => $shipment_order->order->discount_amount,*/
                    /*'offer_amount' => $shipment_order->order->offer_amount,*/

                    // Add share data right here
                    'subtotal_amount' => $shareData['sub_total_amount'],
                    'total_amount' => $shareData['total_amount'],
                    'delivery_charge' => $shareData['shipment_delivery_charge'],
                    'offer_amount' => $shareData['shipment_discount'],
                    'loyalty_redeem_amount' => $shareData['shipment_loyalty'],
                    'wallet_redeem_amount' => $shareData['shipment_wallet_redeem'],

                    'delivery_mode' => $shipment_order->order->delivery_mode,
                    'order_type' => $shipment_order->order->order_type,
                    'status' => $shipment_order->order->status,
                    'status_date' => $shipment_order->order->status_date,
                    'payment_method' => $shipment_order->order->payment_method,
                    'shipping_method' => $shipment_order->order->shipping_method,
                    'delivery_mode_type' => $shipment_order->order->delivery_mode_type,
                    'delivery_address' => $shipment_order->order->customer_address ? [                        
                        'house_no' => $shipment_order->order->customer_address->house_no,
                        'address_line_1' => $shipment_order->order->customer_address->address_line1,
                        'address_line_2' => $shipment_order->order->customer_address->address_line2,
                        'city' => $shipment_order->order->customer_address->city->city_name,
                        'municipality' => $shipment_order->order->customer_address->municipality->name,
                        'zipcode' => $shipment_order->order->customer_address->zipcode,
                        'name' => $shipment_order->order->customer_address->name,
                        'type' => $shipment_order->order->customer_address->type,
                        'landmark' => $shipment_order->order->customer_address->landmark,
                    ] : null,
                    'expected_delivery_date' => $shipment_order->order->delivery_date,
                    'pending_items' => [],
                    'delivered_items' => [],
                    'return_items' => []
                ];



                //echo "<pre>"; print_r($order); exit;            
                $itemsQuery =  $shipment_order['shipment_order_items'];
                // Format items data
                foreach ($itemsQuery as $item) {

                    $product_image = null;
                    $image = $this->ProductImages->getDefaultProductImage($item->order_item->product->id);
                    if ($image) {
                        $product_image = $this->Media->getCloudFrontURL($image);
                    }                    

                    //$latestTrackingStatus = end($item->order_tracking_histories);

                    /*if ($latestTrackingStatus) {
                        $estimatedDeliveryDate = $latestTrackingStatus->status == 'Shipped'
                            ? date('Y-m-d', strtotime("{$latestTrackingStatus->updated} +5 days"))
                            : null;
                    }
                    */
                    //$totalPrice = ($item->order_item->total_price > 0) ? $item->order_item->total_price : ($item->order_item->quantity * $item->order_item->price);
                    $totalPrice = $item->quantity * $item->order_item->price;

                    if($item->item_delivery_status == 'Pending') {
                        $orderData['pending_items'][] = [
                            'id' => $item->order_item->id,
                            'product_id' => $item->order_item->product_id,
                            'product_name' => $item->order_item->product->name,
                            'reference_name' => $item->order_item->product->product_reference,
                            'product_image' => $product_image,
                            //'quantity' => $item->order_item->quantity,                           
                            'quantity' => $item->quantity,
                            'price' => $item->order_item->price,
                            'total_price' => $totalPrice,
                            'latest_status' => $item->item_delivery_status,
                            'status_updated_at' => $item->delivery_status_date,
                            'product_variant' => $item->order_item->product_variant,
                            /*'estimated_delivery_date' => $estimatedDeliveryDate,*/                            
                            /*'tracking_history' => array_map(function ($tracking) {
                                return [
                                    'status' => $tracking->status,
                                    'comment' => $tracking->comment,
                                    'updated_at' => $tracking->updated,
                                ];
                            }, $item->order_tracking_histories)*/
                        ];
                    } elseif($item->item_delivery_status == 'Delivered') {
                        $orderData['delivered_items'][] = [
                            'id' => $item->order_item->id,
                            'product_id' => $item->order_item->product_id,
                            'product_name' => $item->order_item->product->name,
                            'reference_name' => $item->order_item->product->product_reference,
                            'product_image' => $product_image,
                            //'quantity' => $item->order_item->quantity,                            
                            'quantity' => $item->quantity,
                            'price' => $item->order_item->price,
                            'total_price' => $totalPrice,
                            'latest_status' => $item->item_delivery_status,
                            'status_updated_at' => $item->delivery_status_date,
                            'product_variant' => $item->order_item->product_variant,
                            /*'estimated_delivery_date' => $estimatedDeliveryDate,*/
                           /* 'return_replace_eligibility_date' => $returnEligibilityDate,*/
                            /*'tracking_history' => array_map(function ($tracking) {
                                return [
                                    'status' => $tracking->status,
                                    'comment' => $tracking->comment,
                                    'updated_at' => $tracking->updated,
                                ];
                            }, $item->order_tracking_histories)*/
                        ];
                    } elseif($item->item_delivery_status == 'Return Pickup') {
                        $orderData['return_items'][] = [
                            'id' => $item->order_item->id,
                            'product_id' => $item->order_item->product_id,
                            'product_name' => $item->order_item->product->name,
                            'reference_name' => $item->order_item->product->product_reference,
                            'product_image' => $product_image,
                            //'quantity' => $item->order_item->quantity,
                            'quantity' => $item->quantity,
                            'price' => $item->order_item->price,
                            'total_price' => $totalPrice,
                            'latest_status' => $item->item_delivery_status,
                            'status_updated_at' => $item->delivery_status_date,
                            'product_variant' => $item->order_item->product_variant,
                            /*'estimated_delivery_date' => $estimatedDeliveryDate,*/
                           /* 'return_replace_eligibility_date' => $returnEligibilityDate,*/
                            /*'tracking_history' => array_map(function ($tracking) {
                                return [
                                    'status' => $tracking->status,
                                    'comment' => $tracking->comment,
                                    'updated_at' => $tracking->updated,
                                ];
                            }, $item->order_tracking_histories)*/
                        ];
                    }                    
                }
                $orderData['order_qty'] = count($shipment_order['shipment_order_items']);
                $orderData['schedule_delivery_date'] = $shipment_order['expected_delivery_date'];
                $orderData['shipment_order_id'] = $shipment_order['id'];
                $orderData['shipment_id'] = $shipment_order['shipment_id'];
                //$orderData['schedule_delivery_time'] = $shipment_order['expected_delivery_date'];
                $orderData['pending_item_count'] = count($orderData['pending_items']);
                $orderData['delivered_item_count'] = count($orderData['delivered_items']);
                $orderData['return_item_count'] = count($orderData['return_items']);
                /*$orderData['order_tracking_histories'] = $order['order_tracking_histories'];*/

                if($shipment_order['order']['customer']['profile_photo']) {
                  $shipment_order['order']['customer']['profile_photo'] = $this->Media->getCloudFrontURL($shipment_order['order']['customer']['profile_photo']);  
                }
                $orderData['customer'] = $shipment_order['order']['customer'];
                /*$orderData['showroom'] = $shipment_order['order']['showroom'];
                $orderData['offer'] = $shipment_order['order']['offer'];
                $orderData['transactions'] = $shipment_order['order']['transactions'][0];*/

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $orderData,
                    'message' => __('Order details retrieved successfully')
                ];

                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S
    public function viewOrder_OLD($orderId)
    {
        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(401);
            } else {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];  

                // Fetch the order for the given order ID
                $order = $this->Orders->orderDetail($orderId);

                if (!$order) {
                    $result = [
                        'status' => 'error',
                        'message' => __('Order not found')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                //
                $assignment_details = $this->ShipmentOrders->find()->select()
                 ->where(['ShipmentOrders.order_id' => $order->id])
                 ->first();

                 //echo "<pre>"; print_r($assignment_details);exit;

                // Prepare order data
                $orderData = [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'order_date' => $order->order_date,
                    'total_amount' => $order->total_amount,
                    'subtotal_amount' => $order->subtotal_amount,
                    'delivery_charge' => $order->delivery_charge,
                    'discount_amount' => $order->discount_amount,
                    'offer_amount'   => $order->offer_amount,
                    'delivery_mode' => $order->delivery_mode,
                    'order_type' => $order->order_type,
                    'status' => $order->status,
                    'status_date' => $order->status_date,
                    'payment_method' => $order->payment_method,
                    'shipping_method' => $order->shipping_method,
                    'delivery_mode_type' => $order->delivery_mode_type,
                    /*'delivery_address' => $order->customer_address ? [                        
                        'house_no' => $order->customer_address->house_no,
                        'address_line_1' => $order->customer_address->address_line1,
                        'address_line_2' => $order->customer_address->address_line2,
                        'city' => $order->customer_address->city->city_name,
                        'municipality' => $order->customer_address->municipality->name,
                        'zipcode' => $order->customer_address->zipcode,
                        'name' => $order->customer_address->name,
                        'type' => $order->customer_address->type,
                        'landmark' => $order->customer_address->landmark,
                    ] : null,*/
                    'expected_delivery_date' => $order->delivery_date,
                    'pending_items' => [],
                    'delivered_items' => []
                ];   

                //echo "<pre>"; print_r($order); exit;            
                $itemsQuery =  $order['order_items'];
                // Format items data
                foreach ($itemsQuery as $item) {

                    $product_image = null;
                    $image = $this->ProductImages->getDefaultProductImage($item->product->id);
                    if ($image) {
                        $product_image = $this->Media->getCloudFrontURL($image);
                    }                    

                    $latestTrackingStatus = end($item->order_tracking_histories);

                    /*if ($latestTrackingStatus) {
                        $estimatedDeliveryDate = $latestTrackingStatus->status == 'Shipped'
                            ? date('Y-m-d', strtotime("{$latestTrackingStatus->updated} +5 days"))
                            : null;
                    }
                    */
                    $totalPrice = ($item->total_price > 0) ? $item->total_price : ($item->quantity * $item->price);

                    if($item->delivery_status == 'pending') {
                        $orderData['pending_items'][] = [
                            'id' => $item->id,
                            'product_id' => $item->product_id,
                            'product_name' => $item->product->name,
                            'reference_name' => $item->reference_name,
                            'product_image' => $product_image,
                            'quantity' => $item->quantity,
                            'price' => $item->price,
                            'total_price' => $totalPrice,
                            'latest_status' => $latestTrackingStatus->status ?? $item->status,
                            'status_updated_at' => $latestTrackingStatus->updated ?? $order->updated,
                            'product_variant' => $item->product_variant,
                            /*'estimated_delivery_date' => $estimatedDeliveryDate,*/                            
                            /*'tracking_history' => array_map(function ($tracking) {
                                return [
                                    'status' => $tracking->status,
                                    'comment' => $tracking->comment,
                                    'updated_at' => $tracking->updated,
                                ];
                            }, $item->order_tracking_histories)*/
                        ];
                    } elseif($item->delivery_status == 'delivered') {
                        $orderData['delivered_items'][] = [
                            'id' => $item->id,
                            'product_id' => $item->product_id,
                            'product_name' => $item->product->name,
                            'reference_name' => $item->reference_name,
                            'product_image' => $product_image,
                            'quantity' => $item->quantity,
                            'price' => $item->price,
                            'total_price' => $totalPrice,
                            'latest_status' => $latestTrackingStatus->status ?? $item->status,
                            'status_updated_at' => $latestTrackingStatus->updated ?? $order->updated,
                            'product_variant' => $item->product_variant,
                            /*'estimated_delivery_date' => $estimatedDeliveryDate,*/
                           /* 'return_replace_eligibility_date' => $returnEligibilityDate,*/
                            /*'tracking_history' => array_map(function ($tracking) {
                                return [
                                    'status' => $tracking->status,
                                    'comment' => $tracking->comment,
                                    'updated_at' => $tracking->updated,
                                ];
                            }, $item->order_tracking_histories)*/
                        ];
                    }                    
                }
                $orderData['order_qty'] = count($order['order_items']);
                $orderData['schedule_delivery_date'] = $assignment_details['expected_delivery_date'];
                $orderData['shipment_order_id'] = $assignment_details['id'];
                $orderData['shipment_id'] = $assignment_details['shipment_id'];
                $orderData['schedule_delivery_time'] = $assignment_details['expected_delivery_date'];
                $orderData['pending_item_count'] = count($orderData['pending_items']);
                $orderData['delivered_item_count'] = count($orderData['delivered_items']);
                /*$orderData['order_tracking_histories'] = $order['order_tracking_histories'];*/

                if($order['customer']['profile_photo']) {
                  $order['customer']['profile_photo'] = $this->Media->getCloudFrontURL($order['customer']['profile_photo']);  
                }
                $orderData['customer'] = $order['customer'];
                /*$orderData['showroom'] = $order['showroom'];
                $orderData['offer'] = $order['offer'];
                $orderData['transactions'] = $order['transactions'][0];*/

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $orderData,
                    'message' => __('Order details retrieved successfully')
                ];

                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S
    public function orderProgress(){
        
    }

    //S
    public function updateShipmentStatus() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            }

            $data = $this->request->getData();
            $shipment_id = $data['shipment_id'];
            $delivery_status = $data['delivery_status'];
            $order_delivery_status = 'Out for Delivery';
            $delivery_status_date = date('Y-m-d H:i:s');

            //
            $all_order_details = $this->ShipmentOrders->detailByShipmentId($shipment_id); 
            //echo "<pre>"; print_r($all_order_details); exit;                      

            // Update Shipment Status

            // 1. Fetch the shipment entity
            $shipment = $this->Shipments->get($shipment_id);
            // 2. Update its delivery status
            $shipment->delivery_status = $delivery_status;

            if($delivery_status == 'Pickup Failed') {
                $shipment->reason = $data['reason'];
            }
            // 3. Save it
            $this->Shipments->saveOrFail($shipment);

            if($delivery_status == 'Picked up') {
                
                // 2. Bulk update all ShipmentOrders
                $this->ShipmentOrders->updateAll(
                    [
                        'order_delivery_status' => $order_delivery_status,
                        'delivery_status_date' => $delivery_status_date
                    ],
                    ['shipment_id' => $shipment_id]
                );            
                
                /*$trackingData = [
                        'shipment_id' => $shipment_id,
                        'shipment_order_id' => $shipment_order_id,
                        'delivery_status' => $data['delivery_status'],
                        'comment' => $data['comment'] ?? null,
                        'updated_date' => date('Y-m-d H:i:s') 
                    ];
                $tracking = $this->ShipmentDeliveryTrackings->newEntity($trackingData);
                $this->ShipmentDeliveryTrackings->save($tracking);*/

                //send email 
                foreach ($all_order_details as $key => $details) {

                    $to = $details['customer']['user']['email'];
                    $subject = 'Order : Out for Delivery';                    
                    $template = 'order_delivery_email';
                    $viewVars = [
                        'delivery_status' => 'Out for Delivery',
                        'order_number' => $details['order']['order_number'],
                        'order_date' => $details['order']['order_date'],
                        'payment_method' => $details['order']['payment_method'],
                        'customer_name' => $details['customer']['user']['first_name'] . ' ' . $details['customer']['user']['last_name'],
                        'customer_email' => $details['customer']['user']['email']
                    ];

                    $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars);

                    //Push
                    $deviceToken = $details['customer']['fcm_token'];
                    $title = 'Out for Delivery - #' . $details['order']['order_number'];
                    $body  = 'Your order is Out for Delivery.';
                    $customData = [
                        "notification_type" => "order",
                        "id" => (string)$details['order']['id'],
                        "category" => "order"
                    ];
                    $response = $this->Global->sendNotification(
                        [$deviceToken],
                        $title,
                        $body,
                        $customData
                    );

                    // shipment_order_items
                    foreach ($details['shipment_order_items'] as $item) {
                        $conditions = [
                            'showroom_id IS' => $details['order']['showroom_id'],
                            'product_id' => $item['order_item']['product_id'],
                            'product_variant_id IS' => $item['order_item']['product_variant_id'],
                            'product_attribute_id IS' => $item['order_item']['product_attribute_id'],
                        ];

                        $stock = $this->ProductStocks->find()
                            ->where($conditions)
                            ->first();

                        if ($stock) {
                            $stock->quantity -= $item['quantity'];
                            $stock->reserved_stock -= $item['quantity'];
                            $this->ProductStocks->save($stock);

                            /*if ($this->ProductStocks->save($stock)) {
                                // Now update order status
                                $order->status = 'Delivered';
                                if (!$this->Orders->save($order)) {
                                    $response = 'Stock updated, but failed to update order status.';
                                    return $response;
                                }
                            }*/ 
                        }
                    }
                }
            } 
            elseif($delivery_status == 'Pickup Failed') {

              /*  $to = $details['customer']['user']['email'];
                $subject = 'Pickup Failed';                    
                $template = 'shipment_pickup_failed';
                $viewVars = [
                    'delivery_status' => 'Pickup Failed',
                    'order_number' => $details['order']['order_number'],
                    'order_date' => $details['order']['order_date'],
                    'payment_method' => $details['order']['payment_method'],
                    'customer_name' => $details['customer']['user']['first_name'] . ' ' . $details['customer']['user']['last_name'],
                    'customer_email' => $details['customer']['user']['email']
                ];

                $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars);*/
            } else {

            }
            
            $result = ['status' => 'success', 'message' => __('Status changed successfully.')];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }    

    //S
    public function updateDeliveryStatus() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            }

            $data = $this->request->getData();
            $shipment_id = $data['shipment_id'];
            $shipment_order_id = $data['shipment_order_id']; 
            $data['delivery_status_date'] = date('Y-m-d H:i:s');
            if($data['delivery_status'] == 'Delivered') {
                $data['actual_delivery_date'] = date('Y-m-d');
            } else {
                $data['actual_delivery_date'] = '';
            }

            //
            $details = $this->ShipmentOrders->shipmentOrderDetail($shipment_order_id); 
            //echo "<pre>"; print_r($details); exit;                      

            //status update
            $changeStatus = $this->ShipmentOrders->changeDeliveryStatus($shipment_order_id, $data);
            
            $trackingData = [
                    'shipment_id' => $shipment_id,
                    'shipment_order_id' => $shipment_order_id,
                    'delivery_status' => $data['delivery_status'],
                    'comment' => $data['comment'] ?? null,
                    'updated_date' => date('Y-m-d H:i:s') 
                ];
            $tracking = $this->ShipmentDeliveryTrackings->newEntity($trackingData);
            $this->ShipmentDeliveryTrackings->save($tracking);
            
            if ($changeStatus) {

                // 1. Update all shipment order items first
                $this->ShipmentOrderItems->updateAll(
                    ['item_delivery_status' =>  $data['delivery_status']], 
                    ['shipment_order_id' => $shipment_order_id]
                );

                // 2. Update related order items
                $orderId = $details['order_id'];
                foreach ($details['shipment_order_items'] as $item) {
                    $this->OrderItems->updateAll(['status' => $data['delivery_status']], ['id' => $item['order_item_id']]);
                }

                // 3. Check if all orders under this shipment are delivered
                $remainingShipmentOrders = $this->ShipmentOrders->find()
                    ->where(['shipment_id' => $shipment_id, 'order_delivery_status IS NOT' => 'Delivered'])
                    ->count();

                if ($remainingShipmentOrders == 0) {
                    $this->Shipments->updateAll(['delivery_status' => $data['delivery_status']], ['id' => $shipment_id]);
                }

                // 4. Check if all order items for this order are delivered
                $remainingOrderItems = $this->OrderItems->find()
                    ->where(['order_id' => $orderId, 'status IS NOT' => 'Delivered'])
                    ->count();

                if ($remainingOrderItems == 0) {
                    $this->Orders->updateAll(['status' => 'Delivered'], ['id' => $orderId]);
                }

                $uploadImages = !empty($data['photos']) ? $this->deliveryImageUpload($shipment_id, $shipment_order_id) : [];

                //send email                
                $to = $details['customer']['user']['email'];
                if($data['delivery_status'] == 'Out for Delivery'){
                   $subject = 'Order : Out for Delivery';
                } elseif ($data['delivery_status'] == 'Delivered') {
                   $subject = 'Order : Delivered'; 
                }
                
                $template = 'order_delivery_email';
                $viewVars = [

                    'delivery_status' => $data['delivery_status'],
                    'order_number' => $details['order']['order_number'],
                    'order_date' => $details['order']['order_date'],
                    'payment_method' => $details['order']['payment_method'],
                    'customer_name' => $details['customer']['user']['first_name'] . ' ' . $details['customer']['user']['last_name'],
                    'customer_email' => $details['customer']['user']['email']
                ];

                $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars);

                //Push
                $deviceToken = $details['customer']['fcm_token'];
                $title = 'Delivered - #' . $details['order']['order_number'];
                $body  = 'Your order is Delivered.';
                $customData = [
                    "notification_type" => "order",
                    "id" => (string)$details['order']['id'],
                    "category" => "order"
                ];
                $response = $this->Global->sendNotification(
                    [$deviceToken],
                    $title,
                    $body,
                    $customData
                );                     
                
                $result = ['status' => 'success', 'message' => __('Status changed successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => 'error', 'message' => __('Unable to change status. Please try again.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S
    private function deliveryImageUpload($shipment_id, $shipment_order_id)
    {
        $files = $this->request->getData('photos');        
        $uploadedImages = [];
        $i = 0;
        foreach ($files as $file) {
            if ($file->getError() === UPLOAD_ERR_OK) {
                $fileName = trim($file->getClientFilename());
                if (!empty($fileName)) {
                    $imageTmpName = $file->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.DELIVERY_PHOTOS');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                    } else {
                        $itemPhoto = $this->DriverDeliveryPhotos->newEmptyEntity(); 
                        $itemPhoto->shipment_id = $shipment_id;  
                        $itemPhoto->shipment_order_id = $shipment_order_id;                     
                        $itemPhoto->image = $folderPath . $imageFile;
                        $i++;
                        if ($this->DriverDeliveryPhotos->save($itemPhoto)) {
                            $uploadedImages[] = $folderPath . $imageFile; // Collecting the image paths
                        } else {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                        }
                    }
                }
            }
        }
        return $uploadedImages;
    }

    //S list and search/filter    
    public function myDeliveries() {

       if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            }

            $data = $this->request->getQuery();
            
            if($data['page']){
                $page = $data['page'];
            }else{
                $page = 1;
            }
            if($data['limit']){
                $limit = $data['limit'];
            }else{
                $limit = 10;
            }

            $filter_delivery_status = $data['delivery_status'];
            $filter_payment_status = $data['payment_status'];
            $filter_sdate = $data['sdate'];
            //$filter_edate = $data['edate'];
            //$filter_month = $data['month'];

             // Change this to 'month', 'quarter', or 'year' based on your requirement.
            if($data['time_period']){
                $time_period = $data['time_period'];
            } else {
                $time_period = '';
                $startDate  = '';
                $endDate = '';
            }
            if( $time_period) {
                switch ($time_period) {
                    case 'month':
                        // Start and end of the current month
                        $startDate = (new \DateTime('first day of this month'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of this month'))->format('Y-m-d 23:59:59');
                        break;

                    case 'quarter':
                        // Determine the current quarter
                        $quarter = ceil(date('n') / 3);
                        $firstMonthOfQuarter = ($quarter - 1) * 3 + 1;
                        $lastMonthOfQuarter = $quarter * 3;

                        // Start and end of the current quarter
                        $startDate = (new \DateTime('first day of ' . $firstMonthOfQuarter . ' month'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of ' . $lastMonthOfQuarter . ' month'))->format('Y-m-d 23:59:59');
                        break;

                    case 'year':
                        // Start and end of the current year
                        $startDate = (new \DateTime('first day of January'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of December'))->format('Y-m-d 23:59:59');
                        break;

                    default:
                        $this->response = $this->response->withStatus(400);
                        $result = [
                            'status' => __('error'),
                            'code' => 400,
                            'message' => __('Invalid time period')
                        ];
                        goto label;
                }
            }

            $search_str = $data['search_str'];

            $orders = $this->ShipmentOrders->listDeliveredOrder($driverId, $filter_delivery_status, $filter_payment_status, $filter_sdate, $time_period, $startDate, $endDate, $search_str, $page, $limit); 

            //echo "<pre>"; print_r($orders); exit;

            foreach ($orders as $kk=>$vv) {

                // Prepare order data
                $orderData = [
                    'order_id' => $vv['order']['id'],
                    'order_number' => $vv['order']['order_number'],                    
                    'total_amount' => $vv['order']['total_amount'],                    
                    'status' => $vv['order']['status'],
                    'payment_method' => $vv['order']['payment_method'],
                    'delivery_mode_type' => $vv['order']['delivery_mode_type'],
                    'created' => $vv['order']['created'],                    
                    'transactions' => $vv['order']['transactions'],
                    'order_items' => []
                ];  

                $itemsQuery =  $vv['shipment_order_items'];
                // Format items data
                foreach ($itemsQuery as $item) {

                    $product_image = null;
                    $image = $this->ProductImages->getDefaultProductImage($item['order_item']['product']['id']);
                    if ($image) {
                        $product_image = $this->Media->getCloudFrontURL($image);
                    } 
                    $totalPrice = ($item['order_item']['total_price'] > 0) ? $item['order_item']['total_price'] : ($item['order_item']['quantity'] * $item['order_item']['price']);
                    $orderData['order_items'][] = [
                        'id' => $item['order_item']['id'],
                        'shipment_order_item_id' => $item['id'],
                        'quantity' => $item['order_item']['quantity'],
                        'item_status' => $item['item_delivery_status'],
                        'product_id' => $item['order_item']['product']['id'],
                        'product_name' => $item['order_item']['product']['name'],
                        'reference_name' =>$item['order_item']['product']['reference_name'],
                        'product_image' => $product_image,
                        'total_price' => $totalPrice,
                        'product_variant' => $item['order_item']['product_variant'],
                        /*'estimated_delivery_date' => $estimatedDeliveryDate,*/                            
                        /*'tracking_history' => array_map(function ($tracking) {
                            return [
                                'status' => $tracking->status,
                                'comment' => $tracking->comment,
                                'updated_at' => $tracking->updated,
                            ];
                        }, $item->order_tracking_histories)*/
                    ];
                }

                $orders[$kk]['order'] = $orderData;
            }      
            
            if ($orders) {
                $result = ['status' => __('success'), 'data' => $orders];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $orders, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    // public function returnOrder(){

    //     if ($this->request->is('post')) {  

    //         // Get the user info from the logged-in user's identity
    //         $identity = $this->request->getAttribute('identity');
    //         if($identity) {
    //             $roleId   = $identity->get('role_id');
    //             $roleName = $identity->get('_matchingData')['Roles']['name'];
    //             $usertype = $identity->get('user_type');
    //             $userId   = $identity->get('id');
    //             $driverId = $identity->get('_matchingData')['Drivers']['id'];                
    //         } 

    //         $data = $this->request->getData();
    //         $data['driver_id'] = $driverId;
    //         $returnOrder = $this->DriverReturnOrders->newEmptyEntity();
    //         $returnOrder = $this->DriverReturnOrders->patchEntity($returnOrder, $data, [
    //             'associated' => ['DriverReturnOrderItems']
    //         ]);
    //         $save_return_order = $this->DriverReturnOrders->save($returnOrder); 
            
    //         if ($save_return_order) {
    //             $result = ['status' => __('success'), 'message' => __('Order Item returned to warehouse.')];
    //             $this->response = $this->response->withStatus(200);
    //         } else {            
    //             $result = ['status' => __('error'), 'message' => __('Unable to return, Please try again.')];
    //         }                       
    //     } else {
    //         $result = [
    //             'status' => __('error'),
    //             'code' => 405,
    //             'message' => __('Method not allowed')
    //         ];
    //         $this->response = $this->response->withStatus(405);
    //     }
    //     label:
    //     $this->set(['result' => $result]);
    //     $this->viewBuilder()->setOption('serialize', ['result']);
    // }

    //Z
    public function returnOrder()
    {
        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');
            $result = [];

            if ($identity) {
                $driverId = $identity->get('_matchingData')['Drivers']['id'] ?? null;
            }

            $data = $this->request->getData();
            $data['driver_id'] = $driverId;

            $returnOrder = $this->DriverReturnOrders->newEmptyEntity();
            $returnOrder = $this->DriverReturnOrders->patchEntity($returnOrder, $data, [
                'associated' => ['DriverReturnOrderItems']
            ]);

            $saveReturnOrder = $this->DriverReturnOrders->save($returnOrder);

            if ($saveReturnOrder) {
                $this->sendDriverReturnEmail($saveReturnOrder); // <- Send notification
                $result = ['status' => __('success'), 'message' => __('Order Item return.')];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => __('error'), 'message' => __('Unable to return, Please try again.')];
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    private function sendDriverReturnEmail($returnOrder)
    {

        $toEmails = [];
        $toLocation = '';
        $greeting = '';

        if ($returnOrder->return_to === 'Showroom' && !empty($returnOrder->return_to_id)) {
            $showroom = $this->Showrooms->get($returnOrder->return_to_id, [
                'contain' => ['ShowroomManager']
            ]);

            $managerEmail = $showroom->manager->email ?? null;
            $managerName = $showroom->manager->first_name ?? 'Manager';

            $toEmails = array_filter([$managerEmail]);
            $toLocation = "Showroom: " . ($showroom->name ?? 'Unknown Showroom');
            $greeting = "Dear {$managerName},";

        } elseif ($returnOrder->return_to === 'Warehouse' && !empty($returnOrder->return_to_id)) {
            $warehouse = $this->Warehouses->get($returnOrder->return_to_id, [
                'contain' => ['Managers']
            ]);

            $managerEmail = $warehouse->manager->email ?? null;
            $managerName = trim(($warehouse->manager->first_name ?? '') . ' ' . ($warehouse->manager->last_name ?? '')) ?: 'Manager';

            $toEmails = array_filter([$managerEmail]);
            $toLocation = "Warehouse: " . ($warehouse->name ?? 'Unknown Warehouse');
            $greeting = "Dear {$managerName},";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for driver return order ID: " . $returnOrder->id);
            return;
        }

        $formattedDate = $returnOrder->created ? $returnOrder->created->format('d-m-Y') : 'N/A';

        $emailData = [
            'return_order_id' => $returnOrder->id,
            'order_id' => $returnOrder->order_id,
            'shipment_id' => $returnOrder->shipment_id,
            'return_reason' => $returnOrder->description,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'return_date' => $formattedDate,
        ];

        $subject = "Return Request #{$returnOrder->id} from Driver";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'driver_return_notification', // Template file needed
            $emailData
        );
    }

    //S
    public function orderReturnCategoriesList() {

        if ($this->request->is('get')) {

            $categoriesQuery = $this->OrderReturnCategories->getAllCategories();
            $categoriesArray = $categoriesQuery->toArray();

            if (empty($categoriesArray)) {
                $result = [
                    'status' => __('error'),
                    'message' => __('No return categories found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => __('success'),
                    'data' => $categoriesArray
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S
    public function viewContentPage($identifier = null)
    {
        if ($this->request->is('get')) {
            if (empty($identifier)) {
                $this->response = $this->response->withStatus(200);
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('identifier is required')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $content = $this->ContentPages->find()
                ->where(['content_category_identifier' => $identifier, 'content_category' => 'Website Pages'])
                ->contain(['ContentImages'])
                ->first();

            if (!$content) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Content page not found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                if ($content->image != null || $content->image != '') {
                    $content->image = $this->Media->getCloudFrontURL($content->image);
                }

                if (!empty($content->content_images)) {
                    foreach ($content->content_images as &$image) {
                        $image->image = $this->Media->getCloudFrontURL($image->image);
                    }
                }

                $result = [
                    'status' => __('success'),
                    'code' => 200,
                    'data' => $content
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S
    public function viewTermsConditions()
    {
        if ($this->request->is('get')) {

            $content = $this->ContentPages->find()
                ->where(['content_category_identifier' => 'terms-conditions', 'content_category' => 'Website Pages'])
                ->contain(['ContentImages'])
                ->first();

            if (!$content) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Content page not found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                if ($content->image != null || $content->image != '') {
                    $content->image = $this->Media->getCloudFrontURL($content->image);
                }

                if (!empty($content->content_images)) {
                    foreach ($content->content_images as &$image) {
                        $image->image = $this->Media->getCloudFrontURL($image->image);
                    }
                }

                $result = [
                    'status' => __('success'),
                    'code' => 200,
                    'data' => $content
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S
    public function viewAboutUs()
    {
        if ($this->request->is('get')) {

            $content = $this->ContentPages->find()
                ->where(['content_category_identifier' => 'about-us', 'content_category' => 'Website Pages'])
                ->contain(['ContentImages'])
                ->first();

            if (!$content) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Content page not found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {

                if ($content->image != null || $content->image != '') {
                    $content->image = $this->Media->getCloudFrontURL($content->image);
                }

                if (!empty($content->content_images)) {
                    foreach ($content->content_images as &$image) {
                        $image->image = $this->Media->getCloudFrontURL($image->image);
                    }
                }

                $result = [
                    'status' => __('success'),
                    'code' => 200,
                    'data' => $content
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S
    public function sendOtp()
    {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            } 

            try {
                $data = $this->request->getData();
                if (isset($data['country_code']) && !empty($data['country_code'])) {
                    // Remove the '+' sign from the 'country_code'
                    $data['country_code'] = trim($data['country_code'], '+');
                }

                $data['user_type'] = 'Customer';

                if (empty($data['country_code']) && empty($data['mobile_no'])) {
                    $result = ['status' => __('error'), 'message' => __('Mobile number is required')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                if (!empty($data['mobile_no']) && !empty($data['country_code'])) {

                    if (!preg_match('/^[0-9]{1,4}\s?[0-9]{10}$/', $data['country_code'] . $data['mobile_no'])) {
                        $result = ['status' => __('error'), 'message' => __('Enter a valid mobile number with a country code')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }

                    $user_identifier = $data['country_code'] . $data['mobile_no'];
                    $otp_type = 'mobile';

                    $user = $this->Users->find()->where(['mobile_no' => $data['mobile_no'], 'country_code' => $data['country_code'], 'status' => 'A'])->first();                   
                }

                $otp = $this->generateOTP();
                $otp_saved = $this->OtpVerifications->save_otp($user_identifier, $otp, $otp_type);

                if ($otp_saved) {

                    if (!empty($data['mobile_no']) && !empty($data['country_code'])) {

                        $otpResult = $this->Global->sendOtp($data['country_code'] . $data['mobile_no'], $otp);
                        if ($otpResult['success']) {
                            $result = [
                                'status' => 'success',
                                'code' => 200,
                                /*'data' => [
                                    'otp' => $otp
                                ],*/
                                'message' => __('OTP has been successfully sent!')
                            ];
                            $this->response = $this->response->withStatus(200);
                        } else {
                            $result = [
                                'status' => __('error'),
                                'code' => 200,
                                'message' => _('OTP could not sent, Please try again!')
                            ];
                            $this->response = $this->response->withStatus(200);
                        }
                    } else {
                        
                    }
                } else {
                    $result = ['status' => __('error'), 'message' => 'Please try again!'];
                    $this->response = $this->response->withStatus(200);
                }
            } catch (\Exception $e) {
                $result = ['status' => __('error'), 'message' => __('Parameters went wrong!')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function verifyOtp()
    {
        if ($this->request->is('post')) {
            try {
                $data = $this->request->getData();
                if (isset($data['country_code']) && !empty($data['country_code'])) {
                    // Remove the '+' sign from the 'country_code'
                    $data['country_code'] = trim($data['country_code'], '+');
                }

                if (empty($data['otp'])) {
                    $result = ['status' => __('error'), 'message' => _('OTP is required')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                if (empty($data['country_code']) && empty($data['mobile_no'])) {
                    $result = ['status' => __('error'), 'message' => __('Mobile number is required')];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                if (!empty($data['mobile_no']) && !empty($data['country_code'])) {

                    if (!preg_match('/^[0-9]{1,4}\s?[0-9]{10}$/', $data['country_code'] . $data['mobile_no'])) {
                        $result = ['status' => __('error'), 'message' => __('Enter a valid mobile number with a country code')];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }
                    $user_identifier = $data['country_code'] . $data['mobile_no'];
                    $otp_type = 'mobile';
                }

                $otp = $data['otp'];
                $otp_verified = $this->OtpVerifications->verify_otp($user_identifier, $otp, $otp_type);

                if ($otp_verified == 1) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => [
                            $otp_type => $user_identifier
                        ],
                        'message' => __('OTP verified successfully!')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else if ($otp_verified == 2) {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'data' => [
                            $otp_type => $user_identifier
                        ],
                        'message' => __('Your OTP has expired. Please resend.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else if ($otp_verified == 3) {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'data' => [
                            $otp_type => $user_identifier
                        ],
                        'message' => __('Invalid OTP!')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'data' => [
                            $otp_type => $user_identifier
                        ],
                        'message' => __('Invalid OTP!')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            } catch (\Exception $e) {
                $result = ['status' => __('error'), 'message' => __('Parameters went wrong!')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S cash desk list current date delivered order which is cod payment
    public function cashOrders() {

       if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            }

            $data = $this->request->getQuery();
            
            if($data['page']){
                $page = $data['page'];
            }else{
                $page = 1;
            }
            if($data['limit']){
                $limit = $data['limit'];
            }else{
                $limit = 50;
            } 
            
            $finalTotalAmount = 0;
            $orders = $this->ShipmentOrders->cashDeliveredOrder($driverId, $page, $limit); 

            //echo "<pre>"; print_r($orders); exit;
            foreach ($orders as $kk=>$vv) {                            

                // Prepare order data
                $orderData = [
                    'order_id' => $vv['order']['id'],
                    'order_number' => $vv['order']['order_number'],                    
                    'total_amount' => $vv['cash_collected_amount'],                                    
                    'status' => $vv['order']['status'],
                    'payment_method' => $vv['order']['payment_method'],
                    'delivery_mode_type' => $vv['order']['delivery_mode_type'],
                    'created' => $vv['order']['created'],                    
                    'transactions' => $vv['order']['transactions'],
                    'order_items' => []
                ];  

                $itemsQuery =  $vv['shipment_order_items'];

                // Format items data
                foreach ($itemsQuery as $item) {

                    $product_image = null;
                    $image = $this->ProductImages->getDefaultProductImage($item['order_item']['product']['id']);
                    if ($image) {
                        $product_image = $this->Media->getCloudFrontURL($image);
                    } 

                    //$totalPrice = ($item['order_item']['total_price'] > 0) ? $item['order_item']['total_price'] : ($item['order_item']['quantity'] * $item['order_item']['price']);

                    $totalPrice = $item['quantity'] * $item['order_item']['price'];

                    $itemsSubTotal += $totalPrice;

                    $orderData['order_items'][] = [
                        'id' => $item['order_item']['id'],
                        'shipment_order_item_id' => $item['id'],
                        //'quantity' => $item['order_item']['quantity'],
                        'quantity' => $item['quantity'],
                        'item_status' => $item['item_delivery_status'],
                        'product_id' => $item['order_item']['product']['id'],
                        'product_name' => $item['order_item']['product']['name'],
                        'reference_name' =>$item['order_item']['product']['product_reference'],
                        'product_image' => $product_image,
                        'total_price' => $totalPrice,
                        'product_variant' => $item['order_item']['product_variant'],
                        /*'estimated_delivery_date' => $estimatedDeliveryDate,*/                            
                        /*'tracking_history' => array_map(function ($tracking) {
                            return [
                                'status' => $tracking->status,
                                'comment' => $tracking->comment,
                                'updated_at' => $tracking->updated,
                            ];
                        }, $item->order_tracking_histories)*/
                    ];
                }                

                $finalTotalAmount += $vv['cash_collected_amount'];
                $orders[$kk]['order'] = $orderData;
            }

            //$totalAmount = $this->ShipmentOrders->cashOnHand($driverId);
            //$todaytotalamount = $totalAmount->total;
            
            $todaytotalamount = $finalTotalAmount;
            $todayHandover = $this->CashHandovers->cashHandoverToday($driverId);
            $todayhandoveramount = $todayHandover->total;
            $cashonhand = $todaytotalamount - $todayhandoveramount;
            if($cashonhand < 0) {
                $cashonhand = 0;
            }

            $cash_on_hand = number_format($cashonhand ?? 0, 2, '.', ''); // Format to 2 decimal places      
            
            $cash_desk_close = $this->CashHandovers->find()->select(['handover_date'])
            ->where([
                /*'showroom_id' => $showroom_id,*/
                'driver_id' => $driverId
            ])->order(['handover_date' => 'DESC'])->first()->toArray();
            $last_cash_desk_handover = $cash_desk_close['handover_date'];

            $data = [
                'last_cash_desk_handover' => $last_cash_desk_handover,
                'cash_on_hand' => $cash_on_hand,
                'cash_order' => $orders ?: [] // if no orders, send empty array
            ];

            $result = [
                'status' => __('success'),
                'data' => $data
            ];

            // Add a message only if no orders
            if (empty($orders)) {
                $result['message'] = __('No data found');
            }

            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S cash desk - cash handover to supervisor
    public function cashHandover(){

        if ($this->request->is('post')) {  

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];                
            } 
            $driver = $this->Drivers->find()
                ->contain(['Users'])
                ->where(['Drivers.id' => $driverId])
                ->first();

            $data = $this->request->getData();
                       
           /* $totalAmount = $this->ShipmentOrders->cashOnHand($driverId);
            $todaytotalamount = $totalAmount->total;

            $todayHandover = $this->CashHandovers->cashHandoverToday($driverId);
            $todayhandoveramount = $todayHandover->total;

            $cashonhand = $todaytotalamount - $todayhandoveramount;

            $cash_on_hand = number_format($cashonhand ?? 0, 2, '.', ''); // Format to 2 decimal places

            if($cash_on_hand != $data['cash_amount']){
                $result = ['status' => __('error'), 'message' => __('Cash is not matched.')];
                goto label;
            }*/
            
            $cash_handover = $this->CashHandovers->newEmptyEntity();
            $cash_handover['driver_id'] = $driverId;
            $cash_handover['handed_to_supervisor'] = $data['supervisor_id'];
            //$cash_handover['handed_to_supervisor'] = $driver['supervisorId'];
            $cash_handover['amount'] = $data['cash_amount'];
            $cash_handover['handover_date'] = date('Y-m-d H:i:s');
            //$cash_handover['remarks'] = $data['remarks'];
            $save_cash_handover = $this->CashHandovers->save($cash_handover); 
            
            if ($save_cash_handover) {
                $result = ['status' => __('success'), 'message' => __('Cash handed over to supervisor')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('error'), 'message' => __('Unable to handover, Please try again.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function notificationSetting()
    {
        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');
            
            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);  
                goto label;
            } else {

                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id']; 

                $emailNotifications = $this->request->getData('email_notification');
                $smsNotifications = $this->request->getData('sms_notification');
                $pushNotifications = $this->request->getData('push_notification');
                //$appNotifications = $this->request->getData('app_notification');
                // Find the user
                $user = $this->Users->get($userId);
                
                $user_attributes = [
                    'email_notifications' => $emailNotifications,
                    'sms_notification' => $smsNotifications,
                    'push_notification' => $pushNotifications,
                    //'app_notifications' => $appNotifications
                ];
                $user = $this->Users->update_user_by_id($userId, $user_attributes);
                if ($user) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => $user,
                        'message' => __('Notification preferences updated successfully'),
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to update notification preferences'),
                    ];
                    $this->response = $this->response->withStatus(500);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed'),
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function listNotifications()
    {
        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
               
                $roleId   = $identity->get('role_id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
                $usertype = $identity->get('user_type');
                $userId   = $identity->get('id');
                $driverId = $identity->get('_matchingData')['Drivers']['id'];

                $data = $this->request->getData();
                //flag 0:unread, 1:read 2:all
                $flag = $data['flag'];
                if (empty($flag) && $flag !== "0") {
                    $flag = 2;
                }
                //$notification_count = $this->Notifications->notificationCount ($userId);
                $notifications = $this->Notifications->userNotifications ($userId, $flag);
                
                if ($notifications) {                    
                    $result = [
                        'status' => 'success',
                        'data' => $notifications
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'success', 'data' => $notifications, 'message' => __('Notification not found')];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    // calculate shipment share amount
    private function calculateShipmentShare($order, $shipmentItems)
    {
        // ---- calculate shipment qty ----
        $shipmentQty = 0;
        foreach ($shipmentItems as $sItem) {
            $shipmentQty += $sItem['quantity'];
        }

        // ---- calculate total order qty ----
        $orderTotalQty = 0;
        foreach ($order['order_items'] as $oi) {
            $orderTotalQty += $oi['quantity'];
        }

        // ---- shipment share ----
        $shipmentShare = ($orderTotalQty > 0) ? ($shipmentQty / $orderTotalQty) : 0;

        // ---- distribute order level charges ----
        $shipmentDeliveryCharge = round(($order['delivery_charge'] ?? 0) * $shipmentShare, 2);
        $shipmentDiscount       = round(($order['offer_amount'] ?? 0) * $shipmentShare, 2);
        $shipmentLoyalty        = round(($order['loyalty_amount'] ?? 0) * $shipmentShare, 2);
        $shipmentWalletRedeem   = round(($order['wallet_redeem_amount'] ?? 0) * $shipmentShare, 2);

        // ---- calculate subtotal ----
        $itemsSubTotal = 0;
        foreach ($shipmentItems as $item) {
            $itemsSubTotal += $item['quantity'] * $item['order_item']['price'];
        }

        // ---- final total ----
        $totalAmount = $itemsSubTotal + $shipmentDeliveryCharge - $shipmentDiscount - $shipmentLoyalty - $shipmentWalletRedeem;

        return [
            'sub_total_amount'        => $itemsSubTotal,
            'total_amount'            => $totalAmount,
            'shipment_delivery_charge'=> $shipmentDeliveryCharge,
            'shipment_discount'       => $shipmentDiscount,
            'shipment_loyalty'        => $shipmentLoyalty,
            'shipment_wallet_redeem'  => $shipmentWalletRedeem,
        ];
    }


}