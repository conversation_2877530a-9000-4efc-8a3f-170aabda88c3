<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Order $order
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>
    .details {
        display: none;
        /* Hide details initially */
    }

    .qr-code-section {
        display: none;
        margin-top: 10px;
    }

    .label {
        width: max-content !important;
    }

    .bg-green-light {
        background-color: rgb(173 253 173 / 61%);
        /* A light green color */
    }

    .fist-table-header {
        border-top-left-radius: 15px;
        border-bottom-left-radius: 15px;
    }

    .end-table-header {
        border-top-right-radius: 15px;
        border-bottom-right-radius: 15px;
    }

    .table:not(.table-sm) thead th {
        background-color: rgba(246, 184, 49, 0.614) !important;
        color: black !important;
    }

    .card,
    .card-body {
        border-radius: 10px !important;
    }

    input {
        padding: 0px !important;
    }

    .small {
        font-size: 10px !important;
        font-weight: 200 !important;
    }

    .payment-info-card {
        margin-top: -180px !important;
    }

    .bg-light-purple {
        background-color: #d1b3e0;
        /* A lighter purple */
    }

    .card {
        background-color: #fff !important;
        box-shadow: 0 .46875rem 2.1875rem rgba(90, 97, 105, .1), 0 .9375rem 1.40625rem rgba(90, 97, 105, .1), 0 .25rem .53125rem rgba(90, 97, 105, .12), 0 .125rem .1875rem rgba(90, 97, 105, .1) !important;
    }

    .main-wrapper-1 .section .section-header {
        height: max-content;
    }

    @media (max-width: 500px) {
        .payment-info-card {
            margin-top: 0px;
        }
    }

    .widthStyle {
        width: 100% !important
    }

    .is-invalid-select {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }

    .is-invalid-ckeditor {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }
</style>
<?php $this->end(); ?>


<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Dashboard', 'action' => 'index']) ?>">
                    <h4 class="page-title m-b-0">Dashboard</h4>
                </a>
            </li>
            <li class="breadcrumb-item">
                <?php if ($isMerchantOrder) { ?>    
                    <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'sellerIndex']) ?>">
                        Orders
                    </a>
                <?php } else { ?>
                    <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'index']) ?>">
                        Orders
                    </a>
                <?php } ?>
            </li>
            <li class="breadcrumb-item active">
                View
            </li>
        </ul>
        <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
            <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
        </a>
        <!-- <button class="d-flex align-items-center" id="back-button-mo">
                        <span class="rotate me-2">➥</span><small style="font-weight: bold">BACK</small>
                    </button> -->
    </div>

    <div
        class="section-header d-flex justify-content-between align-items-center mb-3 mt-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0">Order Details</h4>
            </li>
    </div>

    <div
        class="section-header d-flex justify-content-between align-items-center mt-3">
        <ul class="breadcrumb breadcrumb-style mb-0">

            <li class="form-group mb-0 p-10 m-r-10" style="padding-left: 0px;">
                <img src="../../img/cart.png" />
            </li>

            <li class="form-group mb-0 m-r-15">
                <label style="color: #F77F00; width: 100px;" for="order-date">Order ID</label><br />
                <strong><?= h($order->order_number) ?></strong>
            </li>

            <li class="form-group mb-0">
                <label style="color: #F77F00; width: 110px;" for="order-amount">Order Placed</label><br />
                <strong><?= h($order->order_date->i18nFormat('dd MMM yyyy')) ?></strong>
            </li>

            <?php if (!empty($orderPlacedBy)): ?>
                <li class="form-group mb-0">
                    <label style="color: #F77F00; width: 110px;" for="order-placed-by">Order Placed By</label><br />
                    <strong><?= h($orderPlacedBy) ?></strong>
                </li>
            <?php endif; ?>

            <?php if (!empty($salesPersonName)): ?>
                <li class="form-group mb-0">
                    <label style="color: #F77F00; width: 110px;" for="order-placed-by">Sales Person</label><br />
                    <strong><?= h($salesPersonName) ?></strong>
                </li>
            <?php endif; ?>

            <li class="form-group mb-0">
                <label style="color: #F77F00; width: 110px;" for="total-amount">Order Value</label><br />
                <strong><?= isset($order->total_amount)
                            ? h(number_format(round($order->total_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                            : '-'; ?></strong>
            </li>

            <li class="form-group mb-0">
                <label style="color: #F77F00; width: 100px;" for="order-status">No of items</label><br />
                <strong><?= h($orderItemCount) ?></strong>
            </li>
            <?php if (!$isMerchantOrder) { ?>
            <li class="form-group mb-0">
                <label style="color: #F77F00; width: 110px;" for="payment-status">Delivery Address</label><br />
                <strong>
                    <?php if (!empty($order->customer_address_id)): ?>
                        <p style="max-width:180px">
                            <?= h($order->customer_address->house_no) ?>,
                            <?= h($order->customer_address->address_line1) ?>,
                            <?= h($order->customer_address->address_line2) ?>,
                            <?= !empty($order->customer_address->city_id) ? h($order->customer_address->city->city_name) : '' ?>
                            <?= !empty($order->customer_address->municipality_id) ? h($order->customer_address->municipality->name) : '' ?> -
                            <?= h($order->customer_address->zipcode) ?>
                        </p>
                    <?php else: ?>
                        <p>No address available</p>
                    <?php endif; ?>

                </strong>
            </li>
            <?php } ?>
            <li class="form-group mb-0" style="margin: 10px; margin-left: 15px;">
                <?php
                $status = $order->status;
                $statusLabel = isset($orderStatusMap[$status]) ? $orderStatusMap[$status]['label'] : 'Unknown';
                $statusClass = isset($orderStatusMap[$status]) ? $orderStatusMap[$status]['class'] : 'col-purple';
                ?>

                <?php if ($status === 'Pending Cancellation') { ?>
                    <a href="#" onclick="approveCancellation(this, <?= h($order->id) ?>);" class="badge <?= h($statusClass) ?> badge-outline fw-bold" style="padding: 12px;">
                        <?= h($statusLabel) ?>
                    </a>
                <?php } else if ($status === 'Pending Return') { ?>
                    <a href="#" onclick="approveReturn(this, <?= h($order->id) ?>);" class="badge <?= h($statusClass) ?> badge-outline fw-bold" style="padding: 12px;">
                        <?= h($statusLabel) ?>
                    </a>
                <?php } else { ?>
                    <strong class="badge <?= h($statusClass) ?> badge-outline fw-bold" style="padding: 12px;">
                        <?= h($statusLabel) ?>
                    </strong>
                <?php } ?>
            </li>


            <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'downloadInvoice', $order->id]) ?>" target="_blank">
                <li class="form-group mb-0" style="margin: 10px;">
                    <strong class="badge bg-light-purple text-dark badge-outline bg-success-subtle  text-light fw-bold">
                        <img src="../../img/document-download.png" />
                        Download Invoice</strong>
                </li>
            </a>
    </div>
    <div class="section-body" id="view-order-section-body">

        <div class="row">
            <div class="col-12 col-md-6 col-lg-12">

                <div class="card p-0 m-t-20">
                    <div class="card-header">
                        <h4 style="color: black !important;">Progress</h4>
                        <div class="card-header-form d-flex align-items-center">
                            <form class="d-flex align-items-center m-l-10">
                                <div class="input-group me-2">
                                    <!-- <button class="fw-bold" type="submit" style="
                                                        position: relative;
                                                        left: 26px;
                                                        border-radius: 10px;
                                                        background-color: rgba(246, 184, 49, 0.614);
                                                        border: 1px solid orange;

                                                    ">
                                        <img src="../../img/refund.png">
                                        Refund
                                    </button> -->

                                    <?php if (!$isMerchantOrder) { ?>
                                    <a href="<?= $this->Url->build(['controller' => 'ReturnsCancellation', 'action' => 'addCancellation']) ?>"
                                        class="btn fw-bold"
                                        style="position: relative;
                                        left: 26px;
                                        border-radius: 10px !important;
                                        background-color: rgba(246, 184, 49, 0.614) !important;
                                        border: 1px solid orange;
                                        color: black;">
                                        <i class="fas fa-times-circle"></i> <?= __('Cancel') ?>
                                    </a>
                                    <?php } ?>
                                    <?php if ($order->status !== 'Pending Return' && $order->status !== 'Returned' && !$isMerchantOrder) { ?>
                                        <!-- <button class="fw-bold" type="submit" style="
                                                        position: relative;
                                                        left: 26px;
                                                        border-radius: 10px;
                                                        background-color: rgba(246, 184, 49, 0.614);
                                                        border: 1px solid orange;
                                                    ">
                                            <img src="../../img/return.png" />
                                            Return
                                        </button> -->

                                        <a href="<?= $this->Url->build(['controller' => 'ReturnsCancellation', 'action' => 'add']) ?>"
                                           class="btn fw-bold"
                                           style="
                                                position: relative;
                                                left: 26px;
                                                border-radius: 10px !important;
                                                background-color: rgba(246, 184, 49, 0.614) !important;
                                                border: 1px solid orange;
                                                color: black;
                                            ">
                                            <i class="fas fa-undo-alt"></i> <?= __('Return') ?>
                                        </a>


                                    <?php } ?>
                                    <?php if (!$isMerchantOrder) { ?>
                                    <button class="btn fw-bold" type="submit" style="
                                                        position: relative;
                                                        left: 26px;
                                                        border-radius: 10px;">
                                        Edit Order
                                    </button>
                                    <?php } ?>
                                    <!-- < ?php if ($order->status == 'Approved' && $order->transactions[0]->payment_status == 'Paid' && $order->delivery_mode == 'pickup') { ?>
                                        <button id="mark-outgoing-btn" class="btn fw-bold" type="button" style="border-radius: 10px;    margin-left: 20px;    background-color: rgba(246, 184, 49, 0.614);">
                                            Mark as Outgoing order
                                        </button>
                                    < ?php } ?> -->

                                    <?php if (
                                        $order->status == 'Approved' &&
                                        $order->transactions[0]->payment_status == 'Paid' &&
                                        $order->delivery_mode == 'pickup' &&
                                        isset($order->showroom->showroom_manager) &&
                                        $order->showroom->showroom_manager == $loggedInUserId &&
                                        !$isMerchantOrder
                                    ) { ?>
                                        <button id="mark-outgoing-btn" class="btn fw-bold" type="button" style="border-radius: 10px; margin-left: 20px; background-color: rgba(246, 184, 49, 0.614);">
                                            Mark as Outgoing order
                                        </button>
                                    <?php } ?>


                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row m-l-15 m-r-15 justify-content-between">
                        <div class="col-xl-2 col-lg-6">
                            <?php
                            $status = $order->status;
                            $color = $orderStatusProgress[$status] ?? 'l-bg-orange';
                            $progress = $orderStatusProgressBar[$status] ?? '0%';
                            ?>

                            <div class="card-content">
                                <p class="fw-bold m-b-0 text-dark">Order Progress</p>
                                <div class="progress mt-1 mb-1" data-height="8" style="height: 8px;">
                                    <div class="progress-bar <?= h($color) ?>" role="progressbar" style="width: <?= h($progress) ?>;"
                                        aria-valuenow="<?= rtrim(h($progress), '%')  ?>" data-width="<?= h($progress)  ?>" aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="text-nowrap <?= h($color) ?> text-white px-2 rounded" style="font-size: small" ;><?= h($status) ?></span>
                                </p>
                            </div>


                            <!-- <div class="card-content">
                                <p class="fw-bold m-b-0" style="color: black;">Order Status</p>
                                <div class="progress mt-1 mb-1" data-height="8" style="height: 8px;">
                                    <div class="progress-bar l-bg-orange" role="progressbar" data-width="25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="width: 25%;"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="text-nowrap text-warning">Status</span>
                                </p>
                            </div> -->
                        </div>
                        <div class="col-xl-2 col-lg-6">
                            <?php
                            $status = $order->transactions[0]->payment_status;
                            $color = $paymentStatusProgress[$status] ?? 'l-bg-orange';
                            $progress = $paymentStatusProgressBar[$status] ?? '0%';
                            ?>
                            <div class="card-content">
                                <p class="fw-bold m-b-0" style="color: black;">Payment Status</p>
                                <div class="progress mt-1 mb-1" data-height="8" style="height: 8px;">
                                    <div class="progress-bar <?= h($color) ?>" role="progressbar" style="width: <?= h($progress) ?>;"
                                        aria-valuenow="<?= rtrim(h($progress), '%')  ?>" data-width="<?= h($progress)  ?>" aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="text-nowrap <?= h($color) ?> text-white px-2 rounded" style="font-size: small" ;><?= h($status) ?></span>
                                </p>
                            </div>
                        </div>
                        <!-- <div class="col-xl-2 col-lg-6">
                            <div class="card-content">
                                <p class="fw-bold m-b-0" style="color: black;">Processing</p>
                                <div class="progress mt-1 mb-1" data-height="8" style="height: 8px;">
                                    <div class="progress-bar l-bg-orange" role="progressbar" data-width="25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="width: 25%;"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="text-nowrap text-warning">Status</span>
                                </p>
                            </div>
                        </div> -->
                        <?php if ($order->delivery_mode == 'delivery') { ?>
                            <div class="col-xl-2 col-lg-6">
                                <?php
                                $status = $OrderShipment->shipment_status ?? 'Pending';
                                $color = $shippedStatusProgress[$status] ?? 'l-bg-orange';
                                $progress = $shippedStatusProgressBar[$status] ?? '0%';
                                ?>
                                <div class="card-content">
                                    <p class="fw-bold m-b-0" style="color: black;">
                                    <?= h('Shipment Status') ?>
                                    </p>
                                    <div class="progress mt-1 mb-1" data-height="8" style="height: 8px;">
                                        <div class="progress-bar <?= h($color) ?>" role="progressbar" style="width: <?= h($progress) ?>;"
                                            aria-valuenow="<?= rtrim(h($progress), '%')  ?>" data-width="<?= h($progress)  ?>" aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                    <p class="mb-0 text-sm">
                                        <span class="text-nowrap <?= h($color) ?> text-white px-2 rounded" style="font-size: small" ;><?= h($OrderShipment['shipment']['delivery_status'] ?? 'Pending') ?></span>
                                    </p>
                                </div>
                            </div>
                            <div class="col-xl-2 col-lg-6">
                                <?php
                                $status = $OrderShipment->delivery_status ?? 'Not_Started';
                                $color = $deliveryStatusProgress[$status] ?? 'l-bg-orange';
                                $progress = $deliveryStatusProgressBar[$status] ?? '0%';
                                ?>
                                <div class="card-content">
                                    <p class="fw-bold m-b-0" style="color: black;">Delivered</p>
                                    <div class="progress mt-1 mb-1" data-height="8" style="height: 8px;">
                                        <div class="progress-bar <?= h($color) ?>" role="progressbar" style="width: <?= h($progress) ?>;"
                                            aria-valuenow="<?= rtrim(h($progress), '%')  ?>" data-width="<?= h($progress)  ?>" aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                    <p class="mb-0 text-sm">
                                        <span class="text-nowrap <?= h($color) ?> text-white px-2 rounded" style="font-size: small" ;><?= h($status) ?></span>
                                    </p>
                                </div>
                            </div>
                        <?php } ?>

                    </div>

                    <div class="card-header d-flex justify-content-between">
                        <?php if ($order->delivery_mode === 'delivery') { ?>
                            <div class="text-success">
                                <img src="../../img/truck.png" class="p-r-10" />
                                Estimated <?= $order->delivery_mode_type == 'express' ? 'Express' : 'Standard' ?> Delivery:
                                <?= $order->delivery_date ? h($order->delivery_date->i18nFormat('dd MMM yyyy')) : '' ?>
                            </div>

                            <div class="card-header-form align-items-center">
                                <form class="d-flex align-items-center">
                                    <div class="">

                                    </div>
                                </form>
                            </div>
                        <?php } ?>
                    </div>


                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-8">
                <div class="card p-0 pb-20">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/product-list.png" style="width: 25px;" class="m-r-20" />Product Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="height: 300px;
                                    overflow: scroll;">
                            <div id="table-1_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                                <table class="table table-striped" id="table-1">
                                    <thead>
                                        <tr>
                                            <th><?= __("Product Image") ?></th>
                                            <th><?= __("Product Name & Size") ?></th>
                                            <th><?= __("Warranty") ?></th>
                                            <th><?= __("Price") ?></th>
                                            <th><?= __("Quantity") ?></th>
                                            <th><?= __("Status") ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($order->order_items as $item): ?>
                                            <tr>
                                                <td class="mb-0 font-13 table-img">
                                                    <?php
                                                    if ($item->product_variant_id && !empty($item->product_variant->product_variant_images)) {
                                                        $image = $item->product_variant->product_variant_images[0]->image;
                                                    } elseif (!empty($item->product->product_images)) {
                                                        $image = $item->product->product_images[0]->image;
                                                    } else {
                                                        $image = $this->Url->webroot('img/user.png');
                                                    }
                                                    ?>
                                                    <img src="<?= h($image) ?>" alt="Product Image" style="width: 25px; height: auto;" />
                                                </td>
                                                <td class="mb-0 font-13">
                                                    <div>
                                                        <?= h($item->product->name) ?>
                                                        <?php if ($item->product_variant_id): ?>
                                                            (<?= h($item->product_variant->variant_name) ?>)
                                                        <?php endif; ?>
                                                        <br>
                                                        <small>
                                                            <?= h($item->product_attribute->attribute->name ?? '') ?>:
                                                            <i><?= h($item->product_attribute->attribute_value->value ?? '') ?></i>
                                                        </small>
                                                        <?php if (!$item->product_variant_id): ?>
                                                            <br><small><i><?= h($item->product->product_size) ?></i></small>
                                                        <?php else: ?>
                                                            <br><small><i><?= h($item->product_variant->variant_size) ?></i></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td class="mb-0 font-13">
                                                    <?= h($item->product->warranty ? $item->product->warranty . ' ' . __('Month(s)') : '-') ?>
                                                </td>
                                                <td class="mb-0 font-13"><?= isset($item->price)
                                                                                ? h(number_format(round($item->price), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                                                                : '-'; ?>
                                                </td>
                                                <td class="mb-0 font-13"><?= h($item->quantity) ?> Nos</td>
                                                <td>
                                                    <?php
                                                    $statusval = $orderStatusMap[$item->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                    ?>
                                                    <div class="badge-outline <?= h($statusval['class']) ?>">
                                                        <?= h($statusval['label']) ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($order->delivery_mode === 'pickup' && !empty($order->pickup_notes)): ?>
                    <div class="card p-0 m-t-5">
                        <div class="card-header">
                            <h4 style="color: black !important;">
                                <i class="fas fa-sticky-note m-r-10"></i><?= __('Pickup Notes') ?>
                            </h4>
                        </div>
                        <div class="card-body">
                            <p><?= h($order->pickup_notes) ?></p>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="card p-0 m-t-5">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/shipping.png" style="width: 25px;" class="m-r-20" />Shipping Details</h4>
                    </div>
                    <div class="card-body">
                        <div>
                            <div style="float: left;" class="m-b-10">
                                <div>
                                    <p class="fw-bold m-b-0" style="color: black;">Pickup Type</p>
                                </div>
                                <?php if ($order->delivery_mode === 'delivery'): ?>
                                    <h6 class="mb-1">Deliver to Address</h6>
                                <?php elseif ($order->delivery_mode === 'pickup'): ?>
                                    <h6 class="mb-1">Pickup from Showroom</h6>
                                <?php endif; ?>

                            </div>


                            <div style="float: right; margin-right: 150px;">
                                <div>
                                    <?php if ($order->delivery_mode === 'delivery'): ?>
                                        <p class="fw-bold m-b-0" style="color: black !important;">Shipping Address</p>
                                    <?php elseif ($order->delivery_mode === 'pickup'): ?>
                                        <p class="fw-bold m-b-0" style="color: black !important;">Showroom Address</p>
                                    <?php endif; ?>

                                </div>

                                <?php if ($order->delivery_mode === 'delivery'): ?>
                                    <?php if (!empty($order->customer_address_id) && !$isMerchantOrder): ?>
                                        <h6>
                                            <?= h($order->customer_address->house_no) ?>,
                                            <?= h($order->customer_address->address_line1) ?>,
                                            <?= h($order->customer_address->address_line2) ?>,
                                            <?= !empty($order->customer_address->city) ? h($order->customer_address->city->city_name) : '' ?>
                                            <?= !empty($order->customer_address->municipality) ? h($order->customer_address->municipality->name) : '' ?> -
                                            <?= h($order->customer_address->zipcode) ?>
                                        </h6>
                                    <?php elseif($isMerchantOrder): ?>
                                        <p>XXXXXXXXXX</p>
                                    <?php else: ?>
                                        <p>No address available</p>
                                    <?php endif; ?>
                                <?php elseif ($order->delivery_mode === 'pickup'): ?>
                                    <?php if (!empty($order->showroom_id) ): ?>
                                        <h6>
                                            <?= h($order->showroom->name) ?>,
                                            <?= h($order->showroom->address) ?>,
                                            <?= !empty($order->showroom->city_id) ? h($order->showroom->city->name) : '' ?>
                                        </h6>
                                    <?php else: ?>
                                        <p>No address available</p>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                        </div>

                    </div>
                </div>

                <?php if (!$orderReturn->isEmpty()): ?>
                    <div class="card p-0 m-t-5">
                        <div class="card-header">
                            <h4 style="color: black !important;">
                                <img src="../../img/shipping.png" style="width: 25px;" class="m-r-20" />
                                <?= __('Return and Cancel History') ?>
                            </h4>
                        </div>
                        <div class="card-body">
                            <?php foreach ($orderReturn as $return): ?>
                                <div class="border p-3 mb-3 rounded shadow-sm">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <p class="fw-bold m-b-0" style="color: black;"><?= __('Type:') ?></p>
                                            <p class="m-0"><?= h($return->request_type) ?></p>
                                        </div>
                                        <div>
                                            <p class="fw-bold m-b-0" style="color: black;"><?= __('Reason:') ?></p>
                                            <p class="m-0"><?= h($return->order_return_category->name ?? '-') ?></p>
                                        </div>
                                        <div>
                                            <p class="fw-bold m-b-0" style="color: black;"><?= __('Status:') ?></p>
                                            <p class="m-0"><?= h($return->status) ?></p>
                                        </div>
                                        <div>
                                            <p class="fw-bold m-b-0" style="color: black;"><?= __('Note:') ?></p>
                                            <p class="m-0"><?= h($return->note ?? '-') ?></p>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <p class="fw-bold m-b-0" style="color: black;"><?= __('Products:') ?></p>
                                        <ul class="mb-0">
                                            <?php if (!empty($return->order_item)): ?>
                                                <li>
                                                    <?= h($return->order_item->product->name ?? 'N/A') ?>
                                                    <?php if (!empty($return->order_item->product_variant)): ?>
                                                        - <?= h($return->order_item->product_variant->variant_name) ?>
                                                    <?php endif; ?>
                                                    <?= __('(Qty: {0})', h($return->return_quantity)) ?>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>

                                    <div class="mt-3">
                                        <p class="fw-bold m-b-0" style="color: black;"><?= __('Customer:') ?></p>
                                        <?php if (!$isMerchantOrder) { ?>
                                            <p class="m-0"><?= h($return->order->customer->user->first_name . ' ' . $return->order->customer->user->last_name) ?></p>
                                        <?php } else { ?>
                                            <p class="m-0">XXXXXXXXXX</p>
                                        <?php } ?>
                                    </div>

                                    <?php if (!empty($return->refund_transactions)): ?>
                                        <div class="mt-3">
                                            <p class="fw-bold m-b-0" style="color: black;"><?= __('Refund History:') ?></p>
                                            <?php $refund = $return->refund_transactions[0]; ?>
                                            <p class="m-0">
                                                <?= __('Method: ') . h(ucfirst($refund->method)) ?><br>
                                                <?php if (strtolower($return->request_type) !== 'cancellation'): ?>
                                                    <?= __('Pickup Charge: ') . h(number_format($refund->pickup_charge, 0,'', $thousandSeparator)).' '.$currencySymbol ?><br>
                                                <?php endif; ?>
                                                <?= __('Product Refund: ') . h(number_format($refund->product_refund, 0,'', $thousandSeparator)).' '.$currencySymbol ?><br>

                                                <?php if($return->pickup_charge == 'No'): ?>

                                                    <?php if (!empty($return->pickup_charge) && $return->pickup_charge === 'No' && !empty($return->order) && isset($return->order->delivery_charge)): ?>
                                                        <?= __('Delivery Charge Refund: ') . h(number_format((float)$return->order->delivery_charge, 0, '', $thousandSeparator)) . ' ' . h($currencySymbol) ?><br>
                                                    <?php endif; ?>
                                                <?php endif; ?>

                                                <?= __('Total Refund: ') . h(number_format($refund->total_refund, 0,'', $thousandSeparator)).' '.$currencySymbol ?><br>
                                                <?= __('Note: ') . h($refund->note ?? '-') ?><br>
                                                <?= __('Date: ') . $refund->created_at ? h($refund->created_at->format('Y-m-d H:i')) : '-' ?>
                                            </p>
                                        </div>
                                    <?php endif; ?>

                                    <div class="mt-3">
                                        <p class="fw-bold m-b-0" style="color: black;"><?= __('Verified By:') ?></p>
                                        <p class="m-0"><?= h($return->verified_by_user->first_name ?? '-') . ' ' . h($return->verified_by_user->last_name ?? '') ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>


                <?php if (isset($order->transactions[0]) && $order->transactions[0]->payment_collected === 'No'): ?>
                    <div class="card p-0" id="payment-info-card">
                        <div class="card-header">
                            <h4 style="color: black !important;">
                                <img src="../../img/bank.png" style="width: 25px;" class="m-r-20" />
                                Payment Information
                            </h4>
                        </div>
                        <div class="card-body" style="border: 2px solid #f780006b; margin: 15px; margin-top: 0px;">
                            <p class="fw-bold text-danger" style="margin: 0;">Payment not collected</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card p-0" id="payment-info-card">
                        <div class="card-header">
                            <h4 style="color: black !important;">
                                <img src="../../img/bank.png" style="width: 25px;" class="m-r-20" />
                                Payment Information
                            </h4>
                        </div>
                        <div class="card-body" style="border: 2px solid #f780006b; margin: 15px;margin-top: 0px;">

                            <div style="float: left;">
                                <div class="form-group mb-0">
                                    <label for="payment-type">Payment Method</label>
                                    <div>
                                        <div class="d-flex">
                                            <p class="fw-bold m-b-0" style="color: black;"><?= h($order->transactions[0]->payment_method) ?></p>
                                        </div>
                                        <div class="d-flex">
                                            <p class="fw-bold m-b-0" style="color: black;">Transaction Date : </p>
                                            <p class="fw-bold m-b-0" style="color: black;"><?= h($order->transactions[0]->transaction_date->i18nFormat('dd MMM yyyy')) ?> </p>
                                        </div>
                                        <div class="form-group mb-0">
                                            <label for="order-date" style="width: max-content !important;">
                                                Transaction ID : <?= h($order->transactions[0]->transaction_number) ?>
                                            </label>
                                            <div class="form-group mb-0">
                                                <label for="order-date" style="width: max-content !important;">
                                                    Customer Name : <?= $isMerchantOrder ? 'XXXXXXXXXX' : h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div style="float: right;">
                                <?php
                                $status = $order->transactions[0]->payment_status;
                                $color = $paymentStatusProgress[$status] ?? 'l-bg-orange';
                                $progress = $paymentStatusProgressBar[$status] ?? '0%';
                                ?>
                                <p class="mb-0 text-sm">
                                    <span class="text-nowrap <?= h($color) ?> text-white px-2 rounded" style="font-size: small;">
                                        <?= h($status) ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>


                <!-- <div class="card p-0" id="payment-info-card">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/bank.png" style="width: 25px;" class="m-r-20" />Payment Information</h4>
                    </div>
                    <div class="card-body" style="border: 2px solid #f780006b; margin: 15px;margin-top: 0px;">

                        <div style="float: left;">

                            <div class="form-group mb-0">
                                <label for="payment-type">Payment Method</label>
                                <div>
                                    <div class="d-flex">
                                        <p class="fw-bold m-b-0" style="color: black;"><?= h($order->transactions[0]->payment_method) ?></p>
                                    </div>
                                    <div class="d-flex">
                                        <p class="fw-bold m-b-0" style="color: black;">Transaction Date : </p>
                                        <p class="fw-bold m-b-0" style="color: black;"><?= h($order->transactions[0]->transaction_date->i18nFormat('dd MMM yyyy')) ?> </p>
                                    </div>
                                    <div class="form-group mb-0">
                                        <label for="order-date" style="width: max-content !important;">Transaction ID : <?= h($order->transactions[0]->transaction_number) ?></label>
                                        <div>
                                            <div class="form-group mb-0">
                                                <label for="order-date" style="width: max-content !important;">Customer Name : <?= h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?>
                                                </label>
                                                <div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div style="float: right;">
                            <?php
                            $status = $order->transactions[0]->payment_status;
                            $color = $paymentStatusProgress[$status] ?? 'l-bg-orange';
                            $progress = $paymentStatusProgressBar[$status] ?? '0%';
                            ?>
                            <p class="mb-0 text-sm">
                                <span class="text-nowrap <?= h($color) ?> text-white px-2 rounded" style="font-size: small" ;><?= h($status) ?></span>
                            </p>
                        </div>


                    </div>
                </div> -->
            </div>


            <div class="col-12 col-md-6 col-lg-4">
                <div class="card p-0">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/profile-circle.png" style="width: 25px;" class="m-r-20" />Customer Details</h4>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center gap-2 p-b-20" style="border-bottom: 2px solid #f780006b;">
                            <img src="assets/images/users/avatar-1.jpg" alt="" class="avatar rounded-3 border border-light border-3">
                            <div>
                                <h6 class="mb-1"><?= $isMerchantOrder ? 'XXXXXXXXXX' : h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?> </h6>
                                <a href="#!" style="color: #F77F00;" class="link-primary fw-medium"><?= $isMerchantOrder ? 'XXXXXXXXXX' : h($order->customer->user->email) ?>
                                </a>
                            </div>
                        </div>

                        <div class="">
                            <div class="m-b-10">
                                <div style="float: left;">
                                    <div class="m-t-10">
                                        <p class="fw-bold m-b-0" style="color: black;">Contact Number</p>
                                    </div>
                                    <p style="font-size: 12px;" class="mb-1"><?= $isMerchantOrder ? 'XXXXXXXXXX' : h('(' . $order->customer->user->country_code . ') ' . $order->customer->user->mobile_no) ?>
                                    </p>
                                </div>

                                <div style="float: right;">
                                    <div class="m-t-10">
                                        <p class="fw-bold m-b-0" style="color: black;">Alternate Number</p>
                                    </div>
                                    <p style="font-size: 12px;" class="mb-1"></p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="card m-t-5 p-0">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/order-summary.png" style="width: 25px;" class="m-r-20" />Order Summary</h4>
                    </div>
                    <div class="card-body pt-0 pb-0 ">
                        <div class="d-flex" style="color: black; border-bottom: 1px solid #F77F00;">
                            <p class="fw-bold m-b-0 p-b-10 p-t-10" style="color: black;margin-right: auto;">Price (<?= h($orderItemCount) ?> item(s)) : </p>
                            <p class="fw-bold m-b-0" style="color: black !important;"><?= isset($order->subtotal_amount)
                                                                                            ? h(number_format(round($order->subtotal_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                                                                            : '-'; ?></p>
                        </div>
                    </div>
                    <div class="card-body p-t-5 pb-0">
                        <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Discount :</p>
                            <p class="fw-bold m-b-0"><?= isset($order->discount_amount)
                                                            ? h(number_format(round($order->discount_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                                            : '-'; ?></p>
                        </div>

                        <div class="d-flex">
                            <div style="margin-right: auto;">
                                <p class="fw-bold m-b-0" ;>
                                    Coupons <small class="small"><?= !empty($order->offer) ? '(' . h($order->offer->offer_code) . ')' : '' ?>
                                    </small>
                                    <!-- <a href=""><small class="small" style="color:red; text-decoration: underline;">(Remove)</small></a> -->
                                </p>
                            </div>
                            <p class="fw-bold m-b-0"><?= isset($order->offer_amount)
                                                            ? h(number_format(round($order->offer_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                                            : '-'; ?></p>
                        </div>

                        <div class="d-flex justify-content-between">

                            <!-- <p class="fw-bold m-b-0" id="view-order-radeem-loyalty-points">Redeem Loyalty Points</p> -->
                            <a>
                                <p class="fw-bold m-b-0" id="view-order-radeem-loyalty-point" data-bs-toggle="modal" data-bs-target="#redeemPointsModal">
                                    Redeemed Loyalty Points (in <?= $currencySymbol ?>)
                                </p>
                            </a>
                            <p class="fw-bold m-b-0 txt-right"><?= isset($order->loyalty_amount)
                                                                    ? h(number_format(round($order->loyalty_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                                                    : '-'; ?></p>
                        </div>

                        <?php if ($order->delivery_mode === 'delivery'): ?>
                        <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Delivery Charges :</p>
                            <p class="fw-bold m-b-0"><?= isset($order->delivery_charge)
                                                            ? h(number_format(round($order->delivery_charge), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                                            : '-'; ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($order->wallet_redeem_amount)): ?>
                        <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Wallet Amount :</p>
                            <p class="fw-bold m-b-0"><?= isset($order->wallet_redeem_amount)
                                ? h(number_format(round($order->wallet_redeem_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                : '-'; ?></p>
                        </div>
                        <?php endif; ?>

                        <!-- <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Shipping :</p>
                            <p class="fw-bold m-b-0">Free</p>
                        </div> -->


                    </div>
                    <div class="card-body">
                        <div class="d-flex" style="color: black; border-bottom: 1px solid #F77F00; border-top: 1px solid #F77F00;">
                            <p class="fw-bold m-b-0 p-b-10 p-t-10" style="color: black;margin-right: auto;">Total Price (<?= h($orderItemCount) ?> item(s)) : </p>
                            <p class="fw-bold m-b-0  p-t-10" style="color: black !important;"><?= isset($order->total_amount)
                                                                                                    ? h(number_format(round($order->total_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                                                                                    : '-'; ?></p>
                        </div>
                    </div>
                </div>
            </div>




        </div>

    </div>
    </div>
    <div class="modal fade" id="approveCancellModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="approveCancellModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approveCancellModalLabel"><?= __('Approve Cancellation') ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetCancellationForm()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?= $this->Form->create(null, [
                    'id' => 'approveCancellForm',
                    'novalidate' => true,
                    'type' => 'post',
                    'enctype' => 'multipart/form-data'
                ]); ?>
                <div class="modal-body">
                    <input type="hidden" name="order_id" id="order-id" value="<?= h($order->id) ?>">

                    <div class="form-group">
                        <label for="order_cancellation_category_id"><?= __('Cancellation Category') ?> <sup class="text-danger font-11">*</sup></label>
                        <?= $this->Form->control('order_cancellation_category_id', [
                            'type' => 'select',
                            'options' => $orderCancellationCategories,
                            'id' => 'order_cancellation_category_id',
                            'class' => 'form-control select2 widthStyle',
                            'label' => false,
                            'empty' => __('Select a Cancellation Category'),
                            'required' => true
                        ]); ?>
                    </div>

                    <div class="form-group">
                        <label for="reason"><?= __('Reason') ?> <sup class="text-danger font-11">*</sup></label>
                        <?= $this->Form->control('reason', [
                            'type' => 'textarea',
                            'class' => 'form-control ckeditor-textarea',
                            'id' => 'ckeditor',
                            'label' => false,
                            'required' => true
                        ]); ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetCancellationForm()"><?= __('Close') ?></button>
                    <button type="submit" class="btn btn-primary" id="btnSaveOrderCancellation"><?= __('Save') ?></button>
                </div>
                <?= $this->Form->end(); ?>
            </div>
        </div>
    </div>

    <?php $this->append('script'); ?>
    <script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
    <script
        src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
    <script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                minimumResultsForSearch: 0
            });

            $('#approveCancellModal').on('shown.bs.modal', function() {
                $('#order_cancellation_category_id').select2({
                    dropdownParent: $('#approveCancellModal')
                });
            });
            $(function() {
                CKEDITOR.replace("ckeditor");
                CKEDITOR.config.height = 300;

                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].on('blur', function() {
                        CKEDITOR.instances[this.name].updateElement();
                    });
                }
            });

            $('#btnSaveOrderCancellation').on('click', function(e) {
                event.preventDefault();
                let isValid = true;

                $('#approveCancellForm').find('input[required], select[required]:visible, #ckeditor').each(function() {
                    let isSelect2 = $(this).hasClass('select2');
                    let isckEditor = $(this).hasClass('ckeditor-textarea');

                    if (isckEditor) {
                        value = CKEDITOR.instances[$(this).attr('id')].getData().trim();
                    } else if (isSelect2) {
                        if (isSelect2) {
                            value = $(this).val();
                        } else {
                            value = $(this).val() ? $(this).val().trim() : '';
                        }
                    } else {
                        value = $(this).val().trim();
                    }

                    if ((value.length === 0) && value === '') {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                            $(this).closest('.form-group').find('.select2-selection--multiple').addClass('is-invalid-select');
                        }
                        if (isckEditor) {
                            $(this).closest('.form-group').find('.cke').addClass('is-invalid-ckeditor');
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                            $(this).closest('.form-group').find('.select2-selection--multiple').removeClass('is-invalid-select');
                        }
                        if (isckEditor) {
                            $(this).closest('.form-group').find('.cke').removeClass('is-invalid-ckeditor');
                        }
                    }
                });
                if (isValid) {
                    var orderId = $('#order-id').val();
                    for (instance in CKEDITOR.instances) {
                        CKEDITOR.instances[instance].updateElement();
                    }
                    var order_cancellation_category_id = $('#order_cancellation_category_id').val();
                    var reason = $('#ckeditor').val();
                    const swalApproveConfirmation = "<?= __("Are you sure you want to approve this cancellation request?") ?>";
                    const swalApproveWarning = "<?= __("Once approved, the order will be permanently cancelled.") ?>";

                    swal({
                        title: swalApproveConfirmation,
                        text: swalApproveWarning,
                        icon: "warning",
                        buttons: true,
                        dangerMode: true,
                    }).then((willApprove) => {
                        if (willApprove) {
                            $.ajax({
                                url: '<?= $this->Url->build(['controller' => 'Orders', 'action' => 'approveCancellation']) ?>',
                                type: 'POST',
                                data: {
                                    id: orderId,
                                    reason: reason,
                                    order_cancellation_category_id: order_cancellation_category_id
                                },
                                headers: {
                                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                                },
                                success: function(response) {
                                    if (response.status === 'success') {
                                        swal("Cancelled!", response.message, "success").then(function() {
                                            location.reload();
                                        });
                                    } else {
                                        swal("Error!", response.message || "There was an error cancelling the order.", "error");
                                    }
                                },
                                error: function(xhr, status, error) {
                                    swal("Error!", "There was an error approving the product.", "error");
                                }
                            });
                        } else {
                            swal("Your order is not cancelled.");
                        }
                    });

                }

            });
        });

        function approveCancellation(element, orderId) {
            const swalApproveConfirmation = "<?= __("Are you sure you want to approve this cancellation request?") ?>";
            const swalApproveWarning = "<?= __("Once approved, the order will be permanently cancelled.") ?>";

            swal({
                title: swalApproveConfirmation,
                text: swalApproveWarning,
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Reject",
                        value: false,
                        visible: true,
                        className: "btn btn-danger",
                    },
                    confirm: {
                        text: "Approve",
                        value: true,
                        visible: true,
                        className: "btn btn-success",
                    }
                },
                dangerMode: true,
            }).then((willApprove) => {
                if (willApprove) {
                    condition = 'Approved';
                } else {
                    condition = 'Rejected';
                }
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Orders', 'action' => 'approveCancellation']) ?>',
                    type: 'POST',
                    data: {
                        id: orderId,
                        condition: condition
                        // reason: reason,
                        // order_cancellation_category_id: order_cancellation_category_id
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            if (condition == 'Approved') {
                                swal("Cancellation Approved!", response.message, "success").then(function() {
                                    location.reload();
                                });
                            } else {
                                swal("Cancellation Rejected!", response.message, "success").then(function() {
                                    location.reload();
                                });
                            }
                        } else {
                            swal("Error!", response.message || "There was an error cancelling the order.", "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        swal("Error!", "There was an error cancelling the order.", "error");
                    }
                });
            });
        }

        function approveReturn(element, orderId) {
            const swalApproveConfirmation = "<?= __("Are you sure you want to approve this return request?") ?>";
            const swalApproveWarning = "<?= __("Once approved, the order will be permanently in return status.") ?>";

            swal({
                title: swalApproveConfirmation,
                text: swalApproveWarning,
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Reject",
                        value: false,
                        visible: true,
                        className: "btn btn-danger",
                    },
                    confirm: {
                        text: "Approve",
                        value: true,
                        visible: true,
                        className: "btn btn-success",
                    }
                },
                dangerMode: true,
            }).then((willApprove) => {
                if (willApprove) {
                    condition = 'Approved';
                } else {
                    condition = 'Rejected';
                }
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Orders', 'action' => 'approveReturn']) ?>',
                    type: 'POST',
                    data: {
                        id: orderId,
                        condition: condition
                        // reason: reason,
                        // order_cancellation_category_id: order_cancellation_category_id
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            if (condition == 'Approved') {
                                swal("Return Approved!", response.message, "success").then(function() {
                                    location.reload();
                                });
                            } else {
                                swal("Return Rejected!", response.message, "success").then(function() {
                                    location.reload();
                                });
                            }
                        } else {
                            swal("Error!", response.message || "There was an error returning the order.", "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        swal("Error!", "There was an error returning the order.", "error");
                    }
                });
            });
        }

        // $('#mark-outgoing-btn').on('click', function(e) {
        //     e.preventDefault();
        //     var orderId = $('#order-id').val();

        //     $.ajax({
        //         url: '<?= $this->Url->build(['controller' => 'ProductStocks', 'action' => 'outgoingStock']) ?>',
        //         type: 'POST',
        //         headers: {
        //             'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
        //         },
        //         data: {
        //             orderId: orderId
        //         },
        //         dataType: 'json',
        //         success: function(response) {
        //             if (response.status === 'success') {
        //                 swal("Outgoing marked successfully", response.message, "success").then(function() {
        //                     location.reload();
        //                 });
        //             } else {
        //                 swal('Error', response.message, 'error');
        //             }
        //         },
        //         error: function() {
        //             swal('Error', 'Failed to fetch stock information.', 'error');
        //         }
        //     });
        // });

        $('#mark-outgoing-btn').on('click', function(e) {
            e.preventDefault();

            var orderId = $('#order-id').val();

            // Ask for confirmation first
            swal({
                title: "<?= __('Are you sure?') ?>",
                text: "<?= __('Do you really want to mark this order as outgoing?') ?>",
                icon: "warning",
                buttons: ["<?= __('Cancel') ?>", "<?= __('Yes, mark it!') ?>"],
                dangerMode: true
            }).then(function(confirmed) {
                if (!confirmed) {
                    return; // do nothing if cancelled
                }

                // Proceed with AJAX
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'ProductStocks', 'action' => 'outgoingStock']) ?>',
                    type: 'POST',
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    data: {
                        orderId: orderId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            swal("Outgoing marked successfully", response.message, "success").then(function() {
                                location.reload();
                            });
                        } else {
                            swal('Error', response.message, 'error');
                        }
                    },
                    error: function() {
                        swal('Error', 'Failed to fetch stock information.', 'error');
                    }
                });
            });
        });


    </script>
    <?php $this->end(); ?>