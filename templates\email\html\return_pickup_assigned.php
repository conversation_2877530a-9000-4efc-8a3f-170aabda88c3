<!DOCTYPE html>
<html>
<head>
    <title><?= __('Return Pickup Assigned') ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        h2 {
            color: #d9534f;
            text-align: center;
        }
        .details {
            padding: 15px;
            background: #ffecec;
            border-radius: 4px;
            border-left: 5px solid #d9534f;
            margin-bottom: 20px;
        }
        .driver {
            padding: 15px;
            background: #e6f7ff;
            border-radius: 4px;
            border-left: 5px solid #17a2b8;
            margin-bottom: 20px;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="https://babiken.com360degree.com/webroot/img/logo.png" alt="Marketplace Logo" width="150">
        </div>
        <h2><?= __('Return Pickup Scheduled') ?></h2>

        <p><?= h($greeting) ?></p>
        <p><?= __('We have assigned a driver to collect your return. Please find the details below:') ?></p>

        <div class="details">
            <p><strong><?= __('Order ID:') ?></strong> <?= h($order_id) ?></p>
            <p><strong><?= __('Order Number:') ?></strong> <?= h($order_number) ?></p>
            <?php if (!empty($products)): ?>
                <p><strong><?= __('Products in Return:') ?></strong></p>
                <ul>
                    <?php foreach ($products as $p): ?>
                        <li><?= h($p) ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>

        <div class="driver">
            <p><strong><?= __('Driver Name:') ?></strong> <?= h($driver_name) ?></p>
            <p><strong><?= __('Scheduled Pickup Date:') ?></strong> <?= h($pickup_date) ?></p>
        </div>

        <p><?= __('Please keep your items ready for pickup. The driver will contact you if needed.') ?></p>

        <div class="footer">
            <p><?= __('Regards,') ?><br><strong><?= __('Marketplace Team') ?></strong></p>
        </div>
    </div>
</body>
</html>
