// image automatic slider

// document.addEventListener("DOMContentLoaded", function () {
//   const slider = document.querySelector(".slider");
//   const images = document.querySelectorAll(".slider img");
//   const totalImages = images.length;
//   let index = 0;

//   function slideImages() {
//     index++;
//     if (index >= totalImages) {
//       index = 0;
//     }
//     slider.style.transform = `translateX(${-index * 830}px)`;
//   }

//   setInterval(slideImages, 2000);
// });

// document.addEventListener("DOMContentLoaded", function () {
//   const slider = document.querySelector(".slider");
//   const images = document.querySelectorAll(".slider img");
//   const totalImages = images.length;
//   let index = 0;

//   function updateSliderWidth() {
//       const imageWidth = images[0].clientWidth; // Get the current width of the images
//       slider.style.width = `${imageWidth * totalImages}px`; // Set the total width of the slider
//   }

//   function slideImages() {
//       index++;
//       if (index >= totalImages) {
//           index = 0;
//       }
//       const imageWidth = images[0].clientWidth; // Get the current width of the images
//       slider.style.transform = `translateX(${-index * imageWidth}px)`; // Adjust translation based on current width
//   }

//   updateSliderWidth(); // Set initial width
//   setInterval(slideImages, 2000);

//   window.addEventListener('resize', updateSliderWidth); // Update width on window resize
// });


// image automatic slider ends

//slider 1
// let currentCardIndex = 0;
// const itemsPerView = 3;

// function moveLeftCarousel() {
//   const smallCardsInner = document.querySelector(".carousel-inner");
//   const cards = document.querySelectorAll(".carousel-inner .card");
//   if (currentCardIndex > 0) {
//     currentCardIndex--;
//   }
//   updateSmallCardsCarousel();
// }

// function moveRightCarousel() {
//   const smallCardsInner = document.querySelector(".carousel-inner");
//   const cards = document.querySelectorAll(".carousel-inner .card");
//   if (currentCardIndex < cards.length - itemsPerView) {
//     currentCardIndex++;
//   } 
//   updateSmallCardsCarousel();
// }

// function updateSmallCardsCarousel() {
//   const smallCardsInner = document.querySelector(".carousel-inner");
//   const cardWidth = document.querySelector(".card").offsetWidth + 20;
//   smallCardsInner.style.transform = `translateX(-${
//     currentCardIndex * cardWidth
//   }px)`;
// }

//slider 2
// let customSlideIndex = 0;

// function moveLeftCustomCarousel() {
//   const customCarouselInner = document.querySelector(".custom-carousel-inner");
//   const customCards = document.querySelectorAll(
//     ".custom-carousel-inner .custom-card"
//   );
//   if (customSlideIndex > 0) {
//     customSlideIndex--;
//   } else {
//     customSlideIndex = customCards.length - 1;
//   }
//   updateCustomCarousel();
// }

// function moveRightCustomCarousel() {
//   const customCarouselInner = document.querySelector(".custom-carousel-inner");
//   const customCards = document.querySelectorAll(
//     ".custom-carousel-inner .custom-card"
//   );
//   if (customSlideIndex > customCards.length - 1) {
//     customSlideIndex++;
//   } else {
//     customSlideIndex = 0;
//   }
//   updateCustomCarousel();
// }

// function updateCustomCarousel() {
//   const smallCardsInner = document.querySelector(".custom-carousel-inner");
//   const cardWidth = document.querySelector(".custom-card").offsetWidth + 20;
//   smallCardsInner.style.transform = `translateX(-${
//     customSlideIndex * cardWidth
//   }px)`;
// }

//slider 3

// const scrollSmoothly = (element, direction, distance, step) => {
//   let scrolled = 0;
//   const interval = setInterval(() => {
//     if (scrolled >= distance) {
//       clearInterval(interval);
//     } else {
//       element.scrollBy({ left: direction * step, behavior: "smooth" });
//       scrolled += step;
//     }
//   }, 10);
// };

// const moveLeftMyCarouselMy = () => {
//   const carousel = document.querySelector(".my-carousel");
//   const cardWidth = document.querySelector(".custom-card-div").offsetWidth;
//   scrollSmoothly(carousel, -1, cardWidth, 250);
// };

// const moveRightMyCarouselMy = () => {
//   const carousel = document.querySelector(".my-carousel");
//   const cardWidth = document.querySelector(".custom-card-div").offsetWidth;
//   scrollSmoothly(carousel, 1, cardWidth, 250);
// };

//slider 4


//slider 5


//profile slider

// let mySlideIndexs = 0;

// function slideLeftMyCarousel() {
//   const myCarouselInner = document.querySelector(".my-carousel-inner-s");
//   const myCards = document.querySelectorAll(".my-carousel-inner-s .my-card-s");
//   if (mySlideIndexs > 0) {
//     mySlideIndexs--;
//   } else {
//     mySlideIndexs = myCards.length - 1;
//   }
//   updateMyCarousel();
// }

// function moveRightMyCarousel() {
//   const myCarouselInner = document.querySelector(".my-carousel-inner-s");
//   const myCards = document.querySelectorAll(".my-carousel-inner-s .my-card-s");
//   if (mySlideIndexs < myCards.length - 1) {
//     mySlideIndexs++;
//   } else {
//     mySlideIndexs = 0;
//   }
//   updateMyCarousel();
// }

// function updateMyCarousel() {
//   const myCarouselInner = document.querySelector(".my-carousel-inner-s");
//   const myCardWidth = document.querySelector(".my-card-s").offsetWidth + 20;
//   myCarouselInner.style.transform = `translateX(-${
//     mySlideIndexs * myCardWidth
//   }px)`;
// }

// // Recently viewed carousel
// let myCarouselIndexs = 0;
// function slideLeftCustomCarousel() {
//   const myCarouselInner = document.querySelector(".custom-carousel-inner");
//   const myCards = document.querySelectorAll(".custom-carousel-inner .custom-card-recent");
//   if (myCarouselIndexs > 0) {
//     myCarouselIndexs--;
//   } else {
//     myCarouselIndexs = myCards.length - 1;
//   }
//   slideupdateCustomCarousel();
// }

// function slideRightCustomCarousel() {
//   const myCarouselInner = document.querySelector(".custom-carousel-inner");
//   const myCards = document.querySelectorAll(".custom-carousel-inner .custom-card-recent");
//   if (myCarouselIndexs < myCards.length - 1) {
//     myCarouselIndexs++;
//   } else {
//     myCarouselIndexs = 0;
//   }
//   slideupdateCustomCarousel();
// }

// function slideupdateCustomCarousel() {
//   const myCarouselInner = document.querySelector(".custom-carousel-inner");
//   const myCardWidth = document.querySelector(".custom-card-recent").offsetWidth + 20;
//   myCarouselInner.style.transform = `translateX(-${
//     myCarouselIndexs * myCardWidth
//   }px)`;
// }

// Top Selling Items carousel
// let myFeaturedIndexs = 0;
// const myFeatureditemsPerView = getItemsPerView();
// function getItemsPerView() {
//   if (window.innerWidth < 768) {
//     return 1;   // Mobile
//   } else if (window.innerWidth < 992) {
//     return 2;   // Tablet
//   } else {
//     return 4;   // Desktop
//   }
// }
// function moveLeftCustomCarouselagain() {
//   // Get the first .custom-carousel-inner-class element (Top Selling Items)
//   const myCarouselInner = document.querySelectorAll(".carousel-inner-class")[0];
//   const myCards = myCarouselInner.querySelectorAll(".card");
//   if (myFeaturedIndexs > 0) {
//     myFeaturedIndexs--;
//   } 
//   // else {
//   //   myFeaturedIndexs = myCards.length - 1;
//   // }
//   updateFeaturedCarousel(myCarouselInner);
// }

// function moveRightCustomCarouselagain() {
//   // Get the first .custom-carousel-inner-class element (Top Selling Items)
//   const myCarouselInner = document.querySelectorAll(".carousel-inner-class")[0];
//   const myCards = myCarouselInner.querySelectorAll(".card");
//   if (myFeaturedIndexs < myCards.length - myFeatureditemsPerView) {
//     myFeaturedIndexs++;
//   } 
//   // else {
//   //   myFeaturedIndexs = 0;
//   // }
//   updateFeaturedCarousel(myCarouselInner);
// }

// function updateFeaturedCarousel(carouselInner) {
//   const myCardWidth = document.querySelector(".card").offsetWidth + 20;
//   carouselInner.style.transform = `translateX(-${
//     myFeaturedIndexs * myCardWidth
//   }px)`;
// }

// // Featured Categories carousel
// let myFeaturedIndexs2 = 0;
// function moveLeftCustomCarouselagain2() {
//   // Get the second .custom-carousel-inner-class element (Featured Categories)
//   const myCarouselInner = document.querySelectorAll(".custom-carousel-inner-class")[1];
//   const myCards = myCarouselInner.querySelectorAll(".custom-card");
//   if (myFeaturedIndexs2 > 0) {
//     myFeaturedIndexs2--;
//   } else {
//     myFeaturedIndexs2 = myCards.length - 1;
//   }
//   updateFeaturedCarousel2(myCarouselInner);
// }

// function moveRightCustomCarouselagain2() {
//   // Get the second .custom-carousel-inner-class element (Featured Categories)
//   const myCarouselInner = document.querySelectorAll(".custom-carousel-inner-class")[1];
//   const myCards = myCarouselInner.querySelectorAll(".custom-card");
//   if (myFeaturedIndexs2 < myCards.length - 1) {
//     myFeaturedIndexs2++;
//   } else {
//     myFeaturedIndexs2 = 0;
//   }
//   updateFeaturedCarousel2(myCarouselInner);
// }

// function updateFeaturedCarousel2(carouselInner) {
//   const myCardWidth = document.querySelector(".custom-card").offsetWidth + 20;
//   carouselInner.style.transform = `translateX(-${
//     myFeaturedIndexs2 * myCardWidth
//   }px)`;
// }

// // Recently viewed products
// let recentIndexs = 0;
// const recentitemsPerView = 4;
// function slideLeftRecentlyViewed() {
//   // Get the first .custom-carousel-inner-class element (Top Selling Items)
//   const myCarouselInner = document.querySelectorAll(".custom-carousel-inner")[0];
//   const myCards = myCarouselInner.querySelectorAll(".custom-card");
//   if (recentIndexs > 0) {
//     recentIndexs--;
//   } 
//   // else {
//   //   myFeaturedIndexs = myCards.length - 1;
//   // }
//   updateRecentCarousel(myCarouselInner);
// }

// function slideRightRecentlyViewed() {
//   // Get the first .custom-carousel-inner-class element (Top Selling Items)
//   const myCarouselInner = document.querySelectorAll(".custom-carousel-inner")[0];
//   const myCards = myCarouselInner.querySelectorAll(".custom-card");
//   if (recentIndexs < myCards.length - recentitemsPerView) {
//     recentIndexs++;
//   } 
//   // else {
//   //   myFeaturedIndexs = 0;
//   // }
//   updateRecentCarousel(myCarouselInner);
// }

// function updateRecentCarousel(carouselInner) {
//   const myCardWidth = document.querySelector(".custom-card").offsetWidth + 20;
//   carouselInner.style.transform = `translateX(-${
//     recentIndexs * myCardWidth
//   }px)`;
// }

// landscape slider

// let slideIndex = 0;

// function plusSlides(n) {
//   showSlides((slideIndex += n));
// }

// function showSlides(n) {
//   const slides = document.querySelectorAll(".landscape-images img");
//   const container = document.querySelector(".landscape-images");
//   const slideWidth = container.offsetWidth; // Get the current container width dynamically

//   if (n >= slides.length) {
//     slideIndex = 0;
//   }
//   if (n < 0) {
//     slideIndex = slides.length - 1;
//   }

//   const offset = -slideIndex * slideWidth; // Calculate offset dynamically
//   container.style.transform = `translateX(${offset}px)`;
// }

// window.addEventListener("resize", () => {
//   // Recalculate slide position on screen resize
//   showSlides(slideIndex);
// });

// showSlides(slideIndex);

// landscape slider

// landscape slider

// let slideIndex = 0;

// function plusSlides(n) {
//   showSlides((slideIndex += n));
// }

// function showSlides(n) {
//   const slides = document.querySelectorAll(".landscape-images img");
//   if (n >= slides.length) {
//     slideIndex = 0;
//   }
//   if (n < 0) {
//     slideIndex = slides.length - 1;
//   }
//   const offset = -slideIndex * 1244;
//   document.querySelector(
//     ".landscape-images"
//   ).style.transform = `translateX(${offset}px)`;
// }

// showSlides(slideIndex);

// Top Deal slider
function dealCarousel(carouselId) {
  let currentIndex = 0;

  const wrapper = document.getElementById(carouselId);
  const inner = wrapper.querySelector(".carousel-inner-class");
  const cards = inner.querySelectorAll(".card");
  const leftBtn = document.getElementById(carouselId + "-left");
  const rightBtn = document.getElementById(carouselId + "-right");

  function getItemsPerView() {
    if (window.innerWidth >= 1200) return 4;
    if (window.innerWidth >= 992) return 3;
    if (window.innerWidth >= 576) return 2;
    return 1;
  }

  function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 20; // 20 = margin gap
    const itemsPerView = getItemsPerView();

    if (currentIndex > cards.length - itemsPerView) {
      currentIndex = cards.length - itemsPerView;
    }
    if (currentIndex < 0) currentIndex = 0;

    inner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

    // disable buttons
    leftBtn.disabled = currentIndex === 0;
    rightBtn.disabled = currentIndex >= cards.length - itemsPerView;
  }

  leftBtn.addEventListener("click", () => {
    currentIndex--;
    updateCarousel();
  });

  rightBtn.addEventListener("click", () => {
    currentIndex++;
    updateCarousel();
  });

  window.addEventListener("resize", updateCarousel);
  updateCarousel();
}

// Init carousel
dealCarousel("topDealCarousel");

//Top selling slider
function initCarousel(carouselId) {
  let currentIndex = 0;

  const wrapper = document.getElementById(carouselId);
  if (!wrapper) return;
  const inner = wrapper.querySelector(".carousel-inner-class");
  const cards = inner.querySelectorAll(".card");
  const leftBtn = document.getElementById(carouselId + "-left");
  const rightBtn = document.getElementById(carouselId + "-right");

  function getItemsPerView() {
    if (window.innerWidth >= 1200) return 4;
    if (window.innerWidth >= 992) return 3;
    if (window.innerWidth >= 576) return 2;
    return 1;
  }

  function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 20; // 20 = margin gap
    const itemsPerView = getItemsPerView();

    if (currentIndex > cards.length - itemsPerView) {
      currentIndex = cards.length - itemsPerView;
    }
    if (currentIndex < 0) currentIndex = 0;

    inner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

    // disable buttons
    leftBtn.disabled = currentIndex === 0;
    rightBtn.disabled = currentIndex >= cards.length - itemsPerView;
  }

  leftBtn.addEventListener("click", () => {
    currentIndex--;
    updateCarousel();
  });

  rightBtn.addEventListener("click", () => {
    currentIndex++;
    updateCarousel();
  });

  window.addEventListener("resize", updateCarousel);
  updateCarousel();
}

// Init carousel
// initCarousel("topSellingCarousel");
document.addEventListener("DOMContentLoaded", () => {
  initCarousel("topSellingCarousel");
});

//New Arrival slider
function arrivalCarousel(carouselId) {
  let currentIndex = 0;

  const wrapper = document.getElementById(carouselId);
  const inner = wrapper.querySelector(".carousel-inner-class");
  const cards = inner.querySelectorAll(".card");
  const leftBtn = document.getElementById(carouselId + "-left");
  const rightBtn = document.getElementById(carouselId + "-right");

  function getItemsPerView() {
    if (window.innerWidth >= 1200) return 4;
    if (window.innerWidth >= 992) return 3;
    if (window.innerWidth >= 576) return 2;
    return 1;
  }

  function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 20; // 20 = margin gap
    const itemsPerView = getItemsPerView();

    if (currentIndex > cards.length - itemsPerView) {
      currentIndex = cards.length - itemsPerView;
    }
    if (currentIndex < 0) currentIndex = 0;

    inner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

    // disable buttons
    leftBtn.disabled = currentIndex === 0;
    rightBtn.disabled = currentIndex >= cards.length - itemsPerView;
  }

  leftBtn.addEventListener("click", () => {
    currentIndex--;
    updateCarousel();
  });

  rightBtn.addEventListener("click", () => {
    currentIndex++;
    updateCarousel();
  });

  window.addEventListener("resize", updateCarousel);
  updateCarousel();
}

// Init carousel
arrivalCarousel("newArrivalCarousel");

//Deals of the day slider for mobile view
function dealDayCarousel(carouselId) {
  let currentIndex = 0;

  const wrapper = document.getElementById(carouselId);
  const inner = wrapper.querySelector(".carousel-inner-class");
  const cards = inner.querySelectorAll(".card");
  const leftBtn = document.getElementById(carouselId + "-left");
  const rightBtn = document.getElementById(carouselId + "-right");

  function getItemsPerView() {
    if (window.innerWidth >= 576) return 1;
    return 1;
  }

  function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 20; // 20 = margin gap
    const itemsPerView = getItemsPerView();

    if (currentIndex > cards.length - itemsPerView) {
      currentIndex = cards.length - itemsPerView;
    }
    if (currentIndex < 0) currentIndex = 0;

    inner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

    // disable buttons
    leftBtn.disabled = currentIndex === 0;
    rightBtn.disabled = currentIndex >= cards.length - itemsPerView;
  }

  leftBtn.addEventListener("click", () => {
    currentIndex--;
    updateCarousel();
  });

  rightBtn.addEventListener("click", () => {
    currentIndex++;
    updateCarousel();
  });

  window.addEventListener("resize", updateCarousel);
  updateCarousel();
}

// Init carousel
dealDayCarousel("dealDayCarousel");

// Featured Categories slider
function featuredCategoryCarousel(carouselId) {
  let currentIndex = 0;

  const wrapper = document.getElementById(carouselId);
  const inner = wrapper.querySelector(".carousel-inner-class");
  const cards = inner.querySelectorAll(".card");
  const leftBtn = document.getElementById(carouselId + "-left");
  const rightBtn = document.getElementById(carouselId + "-right");

  function getItemsPerView() {
    if (window.innerWidth >= 1200) return 4;
    if (window.innerWidth >= 992) return 3;
    if (window.innerWidth >= 576) return 2;
    return 1;
  }

  function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 20; // 20 = margin gap
    const itemsPerView = getItemsPerView();

    if (currentIndex > cards.length - itemsPerView) {
      currentIndex = cards.length - itemsPerView;
    }
    if (currentIndex < 0) currentIndex = 0;

    inner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

    // disable buttons
    leftBtn.disabled = currentIndex === 0;
    rightBtn.disabled = currentIndex >= cards.length - itemsPerView;
  }

  leftBtn.addEventListener("click", () => {
    currentIndex--;
    updateCarousel();
  });

  rightBtn.addEventListener("click", () => {
    currentIndex++;
    updateCarousel();
  });

  window.addEventListener("resize", updateCarousel);
  updateCarousel();
}

// Init carousel
featuredCategoryCarousel("featureCategoryCarousel");

// custom widget slider
function widgetCarousel(carouselId) {
  let currentIndex = 0;

  const wrapper = document.getElementById(carouselId);
  const inner = wrapper.querySelector(".carousel-inner-class");
  const cards = inner.querySelectorAll(".card");
  const leftBtn = document.getElementById(carouselId + "-left");
  const rightBtn = document.getElementById(carouselId + "-right");

  function getItemsPerView() {
    if (window.innerWidth >= 1200) return 4;
    if (window.innerWidth >= 992) return 3;
    if (window.innerWidth >= 576) return 2;
    return 1;
  }

  function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 20; // 20 = margin gap
    const itemsPerView = getItemsPerView();

    if (currentIndex > cards.length - itemsPerView) {
      currentIndex = cards.length - itemsPerView;
    }
    if (currentIndex < 0) currentIndex = 0;

    inner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

    // disable buttons
    leftBtn.disabled = currentIndex === 0;
    rightBtn.disabled = currentIndex >= cards.length - itemsPerView;
  }

  leftBtn.addEventListener("click", () => {
    currentIndex--;
    updateCarousel();
  });

  rightBtn.addEventListener("click", () => {
    currentIndex++;
    updateCarousel();
  });

  window.addEventListener("resize", updateCarousel);
  updateCarousel();
}

widgetCarousel("customWidgetCarousel");

//Our Brands Slider
function brandCarousel(carouselId) {
  let currentIndex = 0;

  const wrapper = document.getElementById(carouselId);
  const inner = wrapper.querySelector(".carousel-inner-class");
  const cards = inner.querySelectorAll(".brand-list");
  const leftBtn = document.getElementById(carouselId + "-left");
  const rightBtn = document.getElementById(carouselId + "-right");

  function getItemsPerView() {
    if (window.innerWidth >= 1200) return 5;
    if (window.innerWidth >= 992) return 4;
    if (window.innerWidth >= 576) return 3;
    return 3;
  }

  function updateCarousel() {
    // const cardWidth = cards[0].offsetWidth + 20; // 20 = margin gap
    // const cardWidth = cards[0].getBoundingClientRect().width;
    const itemsPerView = getItemsPerView();
    // Dynamically set each card width so they fit perfectly
    const wrapperWidth = wrapper.offsetWidth;
    const cardWidth = wrapperWidth / itemsPerView;

    cards.forEach(card => {
      card.style.minWidth = `${cardWidth}px`;
    });

    if (currentIndex > cards.length - itemsPerView) {
      currentIndex = cards.length - itemsPerView;
    }
    if (currentIndex < 0) currentIndex = 0;

    inner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

    // disable buttons
    leftBtn.disabled = currentIndex === 0;
    rightBtn.disabled = currentIndex >= cards.length - itemsPerView;
  }

  leftBtn.addEventListener("click", () => {
    currentIndex--;
    updateCarousel();
  });

  rightBtn.addEventListener("click", () => {
    currentIndex++;
    updateCarousel();
  });

  window.addEventListener("resize", updateCarousel);
  updateCarousel();
}

brandCarousel("brandCarousel");

// Client Says slider
function clientSaysCarousel(carouselId) {
  let currentIndex = 0;

  const wrapper = document.getElementById(carouselId);
  const inner = wrapper.querySelector(".carousel-inner-class");
  const cards = inner.querySelectorAll(".card");
  const leftBtn = document.getElementById(carouselId + "-left");
  const rightBtn = document.getElementById(carouselId + "-right");

  function getItemsPerView() {
    if (window.innerWidth >= 1200) return 4;
    if (window.innerWidth >= 992) return 3;
    if (window.innerWidth >= 576) return 2;
    return 1;
  }

  function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 20; // 20 = margin gap
    const itemsPerView = getItemsPerView();

    if (currentIndex > cards.length - itemsPerView) {
      currentIndex = cards.length - itemsPerView;
    }
    if (currentIndex < 0) currentIndex = 0;

    inner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;

    // disable buttons
    leftBtn.disabled = currentIndex === 0;
    rightBtn.disabled = currentIndex >= cards.length - itemsPerView;
  }

  leftBtn.addEventListener("click", () => {
    currentIndex--;
    updateCarousel();
  });

  rightBtn.addEventListener("click", () => {
    currentIndex++;
    updateCarousel();
  });

  window.addEventListener("resize", updateCarousel);
  updateCarousel();
}
document.addEventListener("DOMContentLoaded", () => {
  clientSaysCarousel("clientFeedbackCarousel");
});


// Question section
document.addEventListener("DOMContentLoaded", function () {
  const faqItems = document.querySelectorAll(".faq-item");
  faqItems.forEach(item => {
    const question = item.querySelector(".bab-ques-q");
    const content = item.querySelector(".faq-content");
    content.style.display = "none";
    question.addEventListener("click", function () {
        item.classList.toggle("active");

        if (item.classList.contains("active")) {
            content.style.display = "block";
            content.style.maxHeight = content.scrollHeight + "px";
        } else {
            content.style.maxHeight = null;
            content.style.display = "none";
        }

        const plus = question.querySelector(".plus");
        const symbol = plus.querySelector(".symbol")
        if (item.classList.contains("active")) {
            symbol.textContent = "-";
        } else {
            symbol.textContent = "+";
        }
    });
  });
});

// Middle banner slider
// 1. Declare first
const bannerIndexes = {}; // store index per carousel

// 2. Define function
function plusSlides(n, carouselId) {
  if (!(carouselId in bannerIndexes)) bannerIndexes[carouselId] = 0;

  const inner = document.querySelector(`#${carouselId} .carousel-inner-class`);
  if (!inner) return;

  const slides = inner.querySelectorAll(".landscape-images");
  if (!slides.length) return;

  const len = slides.length;

  bannerIndexes[carouselId] = (bannerIndexes[carouselId] + n + len) % len;

  inner.style.transform = `translateX(-${bannerIndexes[carouselId] * 100}%)`;

  // 3. Run AFTER DOM is ready
  document.addEventListener("DOMContentLoaded", () => {
    setInterval(() => plusSlides(1, "middleBannerCarousel"), 5000);
  });
}

