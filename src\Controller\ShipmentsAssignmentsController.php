<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\I18n\FrozenDate;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ShipmentsAssignmentsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Cities;
    protected $Zones;
    protected $Orders;
    protected $OrderItems;
    protected $Users;
    protected $Drivers;
    protected $DeliveryPartners;
    protected $ProductStocks;
    protected $Shipments;
    protected $ShipmentItems;
    protected $Products;
    protected $ProductVariants;
    protected $ZoneMunicipalities;
    protected $Showrooms;
    protected $Warehouses;
    protected $Municipalities;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Cities = $this->fetchTable('Cities');
        $this->Zones = $this->fetchTable('Zones');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Users = $this->fetchTable('Users');
        $this->Drivers = $this->fetchTable('Drivers');
        $this->DeliveryPartners = $this->fetchTable('DeliveryPartners');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentItems = $this->fetchTable('ShipmentItems');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ZoneMunicipalities = $this->fetchTable('ZoneMunicipalities');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Municipalities = $this->fetchTable('Municipalities');
    }
    
    public function index()
    {
        $shipmentsQuery = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A'])
                             ->contain(['Cities', 'Zones', 'Municipalities', 'Orders']);
                },
                'Drivers' => function ($q) {
                    return $q->leftJoinWith('Users')
                        ->select([
                            'Drivers.id',
                            'Users.id',
                            'Users.first_name',
                            'Users.last_name',
                            'full_name' => $q->func()->concat([
                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                            ])
                        ]);
                },
                'DeliveryPartners'
            ])
            ->select([
                'Shipments.id',
                'Shipments.sender_type',
                'Shipments.senderID',
                'Shipments.created',
                'Shipments.delivery_status',
                'Shipments.driver_id',
                'sender_name' => $this->Shipments->find()
                    ->newExpr()
                    ->add("(CASE 
                            WHEN Shipments.sender_type = 'showroom' 
                            THEN (SELECT name FROM showrooms WHERE showrooms.id = Shipments.senderID) 
                            WHEN Shipments.sender_type = 'warehouse' 
                            THEN (SELECT name FROM warehouses WHERE warehouses.id = Shipments.senderID) 
                            ELSE 'N/A' 
                        END)"),
                'partner_name' => $this->Shipments->find()
                ->newExpr()
                ->add("(CASE 
                        WHEN Shipments.delivery_partner_id IS NOT NULL 
                        THEN DeliveryPartners.partner_name 
                        ELSE NULL 
                    END)")
            ])
            ->where([
                'Shipments.delivery_status NOT IN' => ['Cancelled'],
                'Shipments.driver_id IS' => null,
                'Shipments.delivery_partner_id IS' => null,
                'Shipments.status' => 'A'
            ])
            ->order(['Shipments.created' => 'DESC']);

        $shipments = $shipmentsQuery->toArray();

        $cities = $this->Cities->find()
            ->order(['Cities.city_name' => 'ASC'])
            ->toArray();

        // Query the Drivers table and join with Users to get driver names
        $drivers = $this->Drivers->find()
            ->select(['Drivers.id', 'Users.first_name', 'Users.last_name'])
            ->where(['Users.status' => 'A', 'Users.user_type' => 'Driver'])
            ->contain(['Users'])->toArray();

        $delivery_partners = $this->DeliveryPartners->find('all')
            ->where(['DeliveryPartners.status' => 'A'])->toArray();

        $warehouses = $this->Warehouses->find()
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC'])
            ->toArray();

        $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

        $zones = $this->Zones->find()
            ->where(['Zones.status' => 'A'])
            ->order(['Zones.name' => 'ASC'])
            ->toArray();

        $municipalities = $this->Municipalities->find()
            ->where(['Municipalities.status' => 'A'])
            ->order(['Municipalities.name' => 'ASC'])
            ->toArray();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('shipments', 'cities', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'drivers', 'delivery_partners', 'showrooms', 'warehouses', 'zones', 'municipalities'));
    }

    public function assignShipment()
    {
        $this->request->allowMethod(['post']);

        try {
            $data = $this->request->getData();

            if ($this->request->is('json')) {
                $data = json_decode(file_get_contents('php://input'), true);
            }

            $deliveryType = $data['delivery_type'] ?? null;
            $deliveryId = $data['deliveryId'] ?? null;
            $shipmentIds = $data['shipment_ids'] ?? [];

            if (empty($deliveryType) || empty($deliveryId) || empty($shipmentIds)) {
                return $this->response->withStatus(400)->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Missing delivery type, delivery ID, or shipment IDs.')
                ]));
            }

            // VALIDATION: Check for order ID conflicts across drivers/delivery partners
            $conflictingOrders = $this->Shipments->ShipmentOrders->find()
                ->select(['ShipmentOrders.order_id', 'Shipments.driver_id', 'Shipments.delivery_partner_id', 'ShipmentOrders.shipment_id'])
                ->contain(['Shipments'])
                ->where([
                    'ShipmentOrders.order_id IS NOT' => null,
                    'ShipmentOrders.shipment_id NOT IN' => $shipmentIds
                ])
                ->all()
                ->combine('order_id', function ($row) {
                    return [
                        'driver_id' => $row->shipment->driver_id ?? null,
                        'delivery_partner_id' => $row->shipment->delivery_partner_id ?? null,
                        'shipment_id' => $row->shipment_id
                    ];
                })
                ->toArray();

            $currentOrders = $this->Shipments->ShipmentOrders->find()
                ->select(['order_id'])
                ->where(['shipment_id IN' => $shipmentIds])
                ->distinct(['order_id'])
                ->all()
                ->extract('order_id')
                ->toArray();

            $conflictList = [];

            foreach ($currentOrders as $orderId) {
                if (isset($conflictingOrders[$orderId])) {
                    $conflict = $conflictingOrders[$orderId];

                    // Skip if the other shipment is not yet assigned
                    if (empty($conflict['driver_id']) && empty($conflict['delivery_partner_id'])) {
                        continue;
                    }

                    // Check for conflict based on current delivery type
                    $isConflict = false;

                    if ($deliveryType === 'Driver') {
                        if ((int)$conflict['driver_id'] !== (int)$deliveryId) {
                            $isConflict = true;
                        }
                    } elseif ($deliveryType === 'Delivery Partner') {
                        if ((int)$conflict['delivery_partner_id'] !== (int)$deliveryId) {
                            $isConflict = true;
                        }
                    }

                    if ($isConflict) {
                        $conflictList[] = [
                            'order_id' => $orderId,
                            'shipment_id' => $conflict['shipment_id'],
                            'assigned_driver_id' => $conflict['driver_id'],
                            'assigned_delivery_partner_id' => $conflict['delivery_partner_id']
                        ];
                    }
                }
            }

            if (!empty($conflictList)) {
                return $this->response->withStatus(409)->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Some orders are already assigned to other shipments with different drivers or delivery partners.'),
                    'conflicts' => $conflictList
                ]));
            }

            $updatedCount = 0;
            $failedShipments = [];

            foreach ($shipmentIds as $shipmentId) {
                try {
                    $shipment = $this->Shipments->get($shipmentId);

                    $shipment->delivery_type = $deliveryType;

                    if ($deliveryType === 'Driver') {
                        $shipment->driver_id = $deliveryId;
                        $shipment->delivery_partner_id = null;
                    } else {
                        $shipment->delivery_partner_id = $deliveryId;
                        $shipment->driver_id = null;
                    }

                    if ($this->Shipments->save($shipment)) {
                        $updatedCount++;

                        // If assigned to driver, send shipment details notification
                        if ($deliveryType === 'Driver') {

                            // Update related shipment_orders
                            $shipmentOrders = $this->Shipments->ShipmentOrders->find()
                                ->where(['shipment_id' => $shipmentId])
                                ->all();

                            foreach ($shipmentOrders as $shipmentOrder) {
                                $shipmentOrder->driver_id = $deliveryId;
                                $shipmentOrder->expected_delivery_date = date('Y-m-d');
                                $this->Shipments->ShipmentOrders->save($shipmentOrder);

                                // Skip if it's a Return Pickup
                                if ($shipmentOrder->order_delivery_status !== 'Return Pickup') {

                                    // Update order status
                                    if (!empty($shipmentOrder->order_id)) {
                                        $this->Shipments->ShipmentOrders->Orders->updateAll(
                                            ['status' => 'Shipped'],
                                            ['id' => $shipmentOrder->order_id]
                                        );
                                    }

                                    // Update order_items using shipment_order_items.order_item_id
                                    $shipmentOrderItems = $this->Shipments->ShipmentOrders->ShipmentOrderItems->find()
                                        ->where(['shipment_order_id' => $shipmentOrder->id])
                                        ->all();

                                    foreach ($shipmentOrderItems as $item) {
                                        if (!empty($item->order_item_id)) {
                                            $this->Shipments->ShipmentOrders->Orders->OrderItems->updateAll(
                                                ['status' => 'Shipped'],
                                                ['id' => $item->order_item_id]
                                            );
                                        }
                                    }
                                }

                                if ($shipmentOrder->order_delivery_status === 'Return Pickup') {
                                    // Send email & push notification to customer

                                    $shipmentData = $this->getShipmentDetails($shipmentOrder->shipment_id);

                                    if (!empty($shipmentData) && !empty($shipmentData->shipment_orders)) {
                                        foreach ($shipmentData->shipment_orders as $shipmentOrderRow) {
                                            if ($shipmentOrderRow->id == $shipmentOrder->id) {

                                                // ✅ Get order from first order_item of this shipment order
                                                $firstOrderItem = $shipmentOrderRow->shipment_order_items[0]->order_item ?? null;
                                                $orderData      = $firstOrderItem?->order;
                                                $customerData   = $orderData?->customer;
                                                $customerUser   = $customerData?->user;

                                                if (!$orderData || !$customerUser) {
                                                    continue;
                                                }

                                                $customerName = trim($customerUser->first_name . ' ' . $customerUser->last_name);

                                                $subject = __('Return Pickup Scheduled - Order #{0}', $orderData->order_number ?? $orderData->id);

                                                // ✅ pickup date
                                                $pickupDate = !empty($shipmentOrderRow->expected_delivery_date)
                                                    ? (is_object($shipmentOrderRow->expected_delivery_date)
                                                        ? $shipmentOrderRow->expected_delivery_date->format('d-m-Y')
                                                        : date('d-m-Y', strtotime($shipmentOrderRow->expected_delivery_date)))
                                                    : date('d-m-Y');

                                                // ✅ driver
                                                $driverName = !empty($shipmentData->driver) && !empty($shipmentData->driver->user)
                                                    ? $shipmentData->driver->user->first_name . ' ' . $shipmentData->driver->user->last_name
                                                    : __('Not Assigned');

                                                // ✅ collect products
                                                $productList = [];
                                                foreach ($shipmentOrderRow->shipment_order_items as $shipmentOrderItem) {
                                                    $orderItem = $shipmentOrderItem->order_item;
                                                    if (!$orderItem) {
                                                        continue;
                                                    }

                                                    $productName = $orderItem->product->name ?? 'N/A';

                                                    $variantInfo = [];
                                                    if (!empty($orderItem->product_variant->variant_name)) {
                                                        $variantInfo[] = $orderItem->product_variant->variant_name;
                                                    }
                                                    if (!empty($orderItem->product_variant->attributes)) {
                                                        $variantInfo[] = $orderItem->product_variant->attributes;
                                                    }

                                                    if (!empty($variantInfo)) {
                                                        $productName .= ' (' . implode(', ', $variantInfo) . ')';
                                                    }

                                                    $productList[] = $productName;
                                                }

                                                // ✅ email data
                                                $emailData = [
                                                    'greeting'     => __('Dear {0},', $customerName),
                                                    'order_id'     => $orderData->id,
                                                    'order_number' => $orderData->order_number ?? $orderData->id,
                                                    'pickup_date'  => $pickupDate,
                                                    'driver_name'  => $driverName,
                                                    'products'     => $productList,
                                                ];

                                                // 🔹 Send Email
                                                if (!empty($customerUser->email)) {
                                                    $this->Global->send_email(
                                                        [$customerUser->email],
                                                        null,
                                                        $subject,
                                                        'return_pickup_assigned',
                                                        $emailData
                                                    );
                                                }

                                                // 🔹 Push Notification
                                                if (!empty($customerData->fcm_token)) {
                                                    $tokens = [$customerData->fcm_token];
                                                    $title  = __('Return Pickup Assigned');
                                                    $body   = __('Your return pickup for Order #{0} has been scheduled. Our driver is on the way.', [$orderData->order_number ?? $orderData->id]);

                                                    $customPayload = [
                                                        'type'         => 'return_pickup',
                                                        'action'       => 'assigned',
                                                        'order_id'     => $orderData->id,
                                                        'order_number' => $orderData->order_number ?? $orderData->id,
                                                        'status'       => 'Return Pickup'
                                                    ];

                                                    $this->Global->sendNotification($tokens, $title, $body, $customPayload);
                                                }
                                            }
                                        }
                                    }
                                }

                            }

                            
                            $shipmentDetails = $this->getShipmentDetails($shipmentId);
                            $this->sendDriverShipmentNotification($shipmentDetails);
                        }

                    } else {
                        $failedShipments[] = $shipmentId;
                    }
                } catch (\Exception $e) {
                    $failedShipments[] = $shipmentId;
                }
            }

            if (!empty($failedShipments)) {
                return $this->response->withStatus(207)->withType('application/json')->withStringBody(json_encode([
                    'status' => 'partial',
                    'message' => __('Some shipments could not be updated.'),
                    'failed_ids' => $failedShipments
                ]));
            }

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'success',
                'message' => __('{0} shipments updated successfully.', $updatedCount)
            ]));

        } catch (\Exception $e) {
            return $this->response->withStatus(500)->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ]));
        }
    }

    private function getShipmentDetails($id)
    {
        $shipment = $this->Shipments->find()
            ->contain([
                'ShipmentOrders' => function ($q) {
                    return $q->where(['ShipmentOrders.status' => 'A'])
                        ->contain([
                            'CustomerAddresses',
                            'ShipmentOrderItems' => function ($q) {
                                return $q->where(['ShipmentOrderItems.status' => 'A'])
                                    ->contain([
                                        'OrderItems' => function ($q) {
                                            return $q->contain([
                                                'Products',
                                                'ProductVariants',
                                                'Orders' => [
                                                    'Customers' => function ($q) {
                                                        return $q->contain(['Users'])->select([
                                                            'Customers.id',
                                                            'Users.first_name',
                                                            'Users.last_name',
                                                            'full_name' => $q->func()->concat([
                                                                'Users.first_name' => 'literal', ' ', 'Users.last_name' => 'literal'
                                                            ])
                                                        ]);
                                                    }
                                                ]
                                            ]);
                                        }
                                    ]);
                            }
                        ]);
                },
                'Drivers' => function ($q) {
                    return $q->contain(['Users'])->select([
                        'Drivers.id',
                        'Users.email',
                        'Users.first_name',
                        'Users.last_name',
                    ]);
                }
            ])
            ->where(['Shipments.id' => $id])
            ->first();

        // Resolve sender name
        $senderName = 'N/A';
        if ($shipment->sender_type === 'Showroom') {
            $showroom = $this->Showrooms->find()
                ->select(['name', 'address'])
                ->where(['id' => $shipment->senderID])
                ->first();
            $senderName = $showroom->name ?? 'N/A';
            $senderAddress = $showroom->address ?? 'N/A';
        } elseif ($shipment->sender_type === 'Warehouse') {

            $warehouse = $this->Warehouses->find()
                ->select(['Warehouses.name', 'Warehouses.warehouse_no_area', 'Warehouses.address_line1'])
                ->contain([
                    'Cities' => function ($q) {
                        return $q->select(['Cities.id', 'Cities.city_name']);
                    },
                    'Municipalities' => function ($q) {
                        return $q->select(['Municipalities.id', 'Municipalities.name']);
                    }
                ])
                ->where(['Warehouses.id' => $shipment->senderID])
                ->first();

            $senderName = $warehouse->name ?? 'N/A';

            // Build sender address with city and municipality
            $addressParts = [];
            if (!empty($warehouse->warehouse_no_area)) {
                $addressParts[] = $warehouse->warehouse_no_area;
            }
            if (!empty($warehouse->address_line1)) {
                $addressParts[] = $warehouse->address_line1;
            }
            if (!empty($warehouse->city->city_name)) {
                $addressParts[] = $warehouse->city->city_name;
            }
            if (!empty($warehouse->municipality->name)) {
                $addressParts[] = $warehouse->municipality->name;
            }

            $senderAddress = !empty($addressParts) ? implode(', ', $addressParts) : 'N/A';

        }

        $shipment->sender_name = $senderName;
        $shipment->sender_address = $senderAddress;

        return $shipment;
    }

    private function sendDriverShipmentNotification($shipment)
    {
        if (empty($shipment->driver) || empty($shipment->driver->user->email)) {
            \Cake\Log\Log::warning("Driver email not found for shipment ID: {$shipment->id}");
            return;
        }

        $toEmail = [$shipment->driver->user->email];

        $driverName = $shipment->driver->user->first_name . ' ' . $shipment->driver->user->last_name;

        $ordersData = [];
        foreach ($shipment->shipment_orders as $shipmentOrder) {
            $customerAddress = $shipmentOrder->customer_address ?? null;
            $addressParts = array_filter([
                $customerAddress->address_line1 ?? null,
                $customerAddress->address_line2 ?? null,
                $customerAddress->landmark ?? null,
                $customerAddress->zipcode ?? null
            ]);
            $deliveryAddress = implode(', ', $addressParts);

            foreach ($shipmentOrder->shipment_order_items as $shipmentOrderItem) {
                $orderItem = $shipmentOrderItem->order_item;
                $order = $orderItem->order ?? null;
                $product = $orderItem->product ?? null;
                $productVariant = $orderItem->product_variant ?? null;
                $customer = $order->customer ?? null;
                $user = $customer->user ?? null;

                $ordersData[] = [
                    'order_id' => $order->id ?? 'N/A',
                    'product_name' => $product->name ?? 'N/A',
                    'variant' => $productVariant->variant_name ?? '',
                    'quantity' => $shipmentOrderItem->quantity ?? 0,
                    'delivery_address' => $deliveryAddress ?? 'N/A',
                    'customer_name' => $user ? $user->first_name . ' ' . $user->last_name : 'N/A',
                    'estimated_time' => $shipmentOrder->expected_delivery_date ? $shipmentOrder->expected_delivery_date->format('d-m-Y') : 'N/A',
                ];
            }
        }

        $emailData = [
            'shipment_id' => $shipment->id,
            'driver_name' => $driverName,
            'sender_name' => $shipment->sender_name,
            'sender_address' => $shipment->sender_address,
            'orders' => $ordersData,
        ];

        $subject = "New Shipment Assignment - Shipment #{$shipment->id}";

        $this->Global->send_email(
            $toEmail,
            null,
            $subject,
            'driver_shipment_notification',
            $emailData
        );
    }

}
