<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Order $order
 */
?>
<?php $this->append('style'); ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/logo.png" />
    <title>Invoice Template</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet">

</head>
<style>
    table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 0px;
        margin-bottom: 20px;
        table-layout: auto;
        /* Ensures dynamic width adjustment */
    }

    th,
    td {
        padding: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    th {
        text-align: left;
        color: #666;
        font-weight: bold;
    }

    td {
        color: #333;
        vertical-align: middle;
        /* Ensures proper alignment */
    }

    td:nth-child(2),
    td:nth-child(3),
    th:nth-child(2),
    th:nth-child(3) {
        text-align: center;
    }

    .page-break {
        page-break-before: always;
    }
</style>
<?php $this->end(); ?>

<body style="font-family: Poppins, sans-serif; background-color: #f5f5f5; padding: 20px; color: #333;">
    <div id="invoiceSection">
        <div
            style="max-width: 900px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);">
            <header style="display: flex;justify-content: space-between;">
                <img src="<?= $this->Url->webroot('img/logo.png') ?>" alt="Babiken Logo" style="max-width: 150px;max-height: 100px;" />
            </header>

            <div style="display: flex;align-items: center;justify-content: space-between;max-width: 900px;flex-wrap: wrap;">
                <table>
                    <tr>
                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Customer Care No:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;"><?= h($customer_care_no) ?></p>
                        </td>

                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: -2px; margin-bottom:2px;">Customer Care Email ID:</p>
                            <a style="font-size: 14px; margin:0px; margin-bottom: 10px;" href="mailto:<?= h($customer_care_email) ?>" style="color: #555;"><?= h($customer_care_email) ?></a>
                        </td>

                        <!-- <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Warranty Period:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;">12 Months</p>
                        </td> -->

                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Call Center No:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;"><?= h($call_center_no) ?></p>
                        </td>

                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">WhatsApp No:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;"><?= h($whatsapp_no) ?></p>
                        </td>
                    </tr>
                    <tr>

                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Babiken After Sales Mobile No:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;"><?= h($after_sales_no) ?></p>
                        </td>

                        <?php if (!empty($order->showroom_id)): ?>
                            <td style="margin: 20px;">
                                <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Showroom Mobile No:</p>
                                <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;">
                                    <?= h('+' . $order->showroom->contact_country_code . ' ' . $order->showroom->contact_number) ?>
                                </p>
                            </td>
                        <?php endif; ?>

                    </tr>
                </table>
            </div>

            <div style="background-color: #f0f0f0; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background-color:#f0f0f0">
                        <td colspan="2">
                            <h2 style="margin: 0; font-weight: 600; font-size: 24px;">Invoice</h2>
                        </td>
                        <td style="text-align: right;">
                            <p style="margin: 0; font-weight: 400; color: #111;">Invoice No.</p>
                            <p style="margin: 0; font-weight: bold; font-size: 18px;"><?= h($order->order_number) ?></p>
                        </td>
                    </tr>
                </table>

                <table style="width: 100%; border-collapse: collapse; margin-top: 20px; table-layout: fixed;">
                    <tr style="background-color:#f0f0f0;">
                        <td style="width: 50%; vertical-align: top; padding: 10px;">
                            <h3 style="font-size: 14px; color: #555; margin: 0;">Billed To:</h3>
                            <p style="margin: 5px 0 0; font-weight: bold; font-size: 16px;">
                                <?= $isMerchantOrder ? 'XXXXXXXXXX' : h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?>
                            </p>
                            <?php if (!empty($order->customer_address) && !$isMerchantOrder): ?>
                                <p style="margin: 5px 0; color: #555;">
                                    <?= h($order->customer_address->house_no ?? '') ?>
                                    <?= !empty($order->customer_address->address_line1) ? ', ' . h($order->customer_address->address_line1) : '' ?>
                                    <?= !empty($order->customer_address->address_line2) ? ', ' . h($order->customer_address->address_line2) : '' ?>
                                    <?= !empty($order->customer_address->city) ? ', ' . h($order->customer_address->city->city_name) : '' ?>
                                    <?= !empty($order->customer_address->municipality) ? '- ' . h($order->customer_address->municipality->name) : '' ?>
                                    <?= !empty($order->customer_address->zipcode) ? ', ' . h($order->customer_address->zipcode) : '' ?>
                                </p>
                            <?php elseif($isMerchantOrder): ?>
                                <p style="margin: 5px 0; color: #555;">
                                    XXXXXXXXXX
                                </p>
                            <?php endif; ?>
                        </td>
                        <td style="width: 50%; vertical-align: top; padding: 10px;"> <?php if ($order->delivery_mode === 'delivery'): ?>
                                <h3 style="font-size: 14px; color: #555; margin: 0;">Delivered To:</h3>
                                <p style="margin: 5px 0 0; font-weight: bold; font-size: 16px;">
                                    <?= $isMerchantOrder ? 'XXXXXXXXXX' : h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?>
                                </p>
                                <?php if (!empty($order->customer_address) && !$isMerchantOrder): ?>
                                <p style="margin: 5px 0; color: #555;">
                                    <?= h($order->customer_address->house_no) ?>,
                                    <?= h($order->customer_address->address_line1) ?>,
                                    <?= h($order->customer_address->address_line2) ?>,
                                    <?= !empty($order->customer_address->city) ? h($order->customer_address->city->city_name) : '' ?> -
                                    <?= !empty($order->customer_address->municipality) ? h($order->customer_address->municipality->name) : '' ?>
                                    <?= h($order->customer_address->zipcode) ?>
                                </p>
                                <?php elseif($isMerchantOrder): ?>
                                <p style="margin: 5px 0; color: #555;">
                                    XXXXXXXXXX
                                </p>
                                <?php endif; ?>
                            <?php elseif ($order->delivery_mode === 'pickup'): ?>
                                <h3 style="font-size: 14px; color: #555; margin: 0;">Picked Up from:</h3>
                                <p style="margin: 5px 0 0; font-weight: bold; font-size: 16px;"><?= h($order->showroom->name) ?></p>
                                <p style="margin: 5px 0; color: #555;">
                                    <?= h($order->showroom->address) ?>,
                                    <?= !empty($order->showroom->city_id) ? h($order->showroom->city->name) : '' ?>
                                </p>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>

                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <tr style="background-color:#f0f0f0">
                        <th style="padding: 8px;">Mobile Number</th>
                        <th style="padding: 8px;">E-Mail ID</th>
                        <th style="padding: 8px;">Order Date</th>
                        <th style="padding: 8px;">Order No</th>
                        <th style="padding: 8px;">Delivered Date</th>
                    </tr>
                    <tr style="background-color:#f0f0f0">
                        <td style="padding: 8px;"><?= $isMerchantOrder ? 'XXXXXXXXXX' : h('(' . $order->customer->user->country_code . ') ' . $order->customer->user->mobile_no) ?></td>
                        <td style="padding: 8px;"><?= $isMerchantOrder ? 'XXXXXXXXXX' : h($order->customer->user->email) ?></td>
                        <td style="padding: 8px;"><?= h($order->order_date->i18nFormat('dd MMM yyyy')) ?></td>
                        <td style="padding: 8px;"><?= h($order->order_number) ?></td>
                        <td style="padding: 8px;">
                            <?= $order->delivery_date ? h($order->delivery_date->i18nFormat('dd MMM yyyy')) : '' ?>
                        </td>
                    </tr>
                </table>
            </div>


            <table>
                <thead>
                    <tr>
                        <th>Item</th>
                        <th>Warranty</th>
                        <th>Qty.</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($order->order_items as $item): ?>
                        <tr>
                            <td>
                                <?= h($item->product->name) ?>
                                <?php if ($item->product_variant_id): ?>
                                    (<?= h($item->product_variant->variant_name) ?>)
                                <?php endif; ?>
                                <br>
                                <small>
                                    <?= h($item->product_attribute->attribute->name ?? '') ?>:
                                    <i><?= h($item->product_attribute->attribute_value->value ?? '') ?></i>
                                </small>
                                <?php if (!$item->product_variant_id): ?>
                                    <br><small><i><?= h($item->product->product_size) ?></i></small>
                                <?php else: ?>
                                    <br><small><i><?= h($item->product_variant->variant_size) ?></i></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?= h($item->product->warranty ? $item->product->warranty . ' ' . __('Month(s)') : '-') ?>
                            </td>

                            <td><?= h($item->quantity) ?> Nos</td>
                            <td><?= isset($item->price)
                                    ? h(number_format(round($item->price), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                    : '-'; ?>
                            </td>
                            <td>
                                <?php $total = $item->quantity * $item->price; ?>
                                <?= isset($total)
                                    ? h(number_format(round($total), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                    : '-'; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <div style="margin-bottom: 20px;">
                <div style="width: 400px;padding: 20px; background-color: #f5f5f5; border-radius:20px;margin-left:auto;display:flex;justify-content: end;">
                    <table style="width: 100%; border-radius: 8px; text-align: left;background-color: #f5f5f5;">
                        <tbody style="background-color: #f5f5f5;">
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 16px; font-weight: bold; color: #333;">
                                    Price (<?= h($orderItemCount) ?> item(s))
                                </td>
                                <td style="font-size: 16px; font-weight: bold; text-align: right; color: #333;">
                                    <?= isset($order->subtotal_amount)
                                        ? h(number_format(round($order->subtotal_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                        : '-'; ?>
                                </td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="3" style="padding: 8px 0; border-bottom: 1px solid #ddd;"></td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Discount</td>
                                <td style="text-align: right; color: #333;">
                                    <?= isset($order->discount_amount)
                                        ? h(number_format(round($order->discount_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                        : '-'; ?>
                                </td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">
                                    Coupons <small class="small"><?= !empty($order->offer) ? '(' . h($order->offer->offer_code) . ')' : '' ?></small>
                                </td>
                                <td style="text-align: right; color: #333;">
                                    <?= isset($order->offer_amount)
                                        ? h(number_format(round($order->offer_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                        : '-'; ?>
                                </td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Redeemed Loyalty Points</td>
                                <td style="text-align: right; color: #333;">
                                    <?= isset($order->loyalty_amount)
                                        ? h(number_format(round($order->loyalty_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                        : '-'; ?></td>
                            </tr>
                            <?php if ($order->delivery_mode === 'delivery'): ?>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Delivery Charges</td>
                                <td style="text-align: right; color: red;">
                                    <?= h($order->delivery_charge) > 0
                                        ? h(number_format(round($order->delivery_charge), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                        : 'Free'; ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                            <?php if (!empty($order->wallet_redeem_amount)): ?>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Wallet Amount</td>
                                <td style="text-align: right;">
                                    <?= h($order->wallet_redeem_amount) > 0
                                        ? h(number_format(round($order->wallet_redeem_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                        : '-'; ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="3" style="padding: 8px 0; border-bottom: 1px solid #ddd;"></td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 16px; padding: 8px 0; font-weight: bold; color: #333;">Total (FCFA)</td>
                                <td style="font-size: 18px; font-weight: bold; text-align: right; color: #333;">
                                    <?= isset($order->total_amount)
                                        ? h(number_format(round($order->total_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                        : '-'; ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <a href="javascript:void(0);" id="downloadPdf"
            style="display: inline-block; min-width: 220px; padding: 10px 20px; background-color: #00a0e3; color: #fff; text-decoration: none; border-radius: 8px; margin-top: 20px; font-size: 16px;">
            <?= __('Download') ?>
        </a>
    </div>

    </div>
</body>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

<script>
    $(document).ready(function() {
        $(document).on("click", "#downloadPdf", function() {
            var invoiceHtml = $("#invoiceSection").html();

            if (!invoiceHtml) {
                swal("Error", "Invoice content is empty!", "error");
                return;
            }

            // Create a form dynamically
            var form = $('<form>', {
                method: "GET",
                action: "<?= $this->Url->build(['controller' => 'Orders', 'action' => 'printInvoicePdf' , $order->id]) ?>",
                target: "_blank" // Open PDF in new tab
            });

            // Append the invoice HTML
            // form.append($('<input>', {
            //     type: "hidden",
            //     name: "html",
            //     value: invoiceHtml
            // }));

            // Append the CSRF token
            form.append($('<input>', {
                type: "hidden",
                name: "_csrfToken",
                value: "<?= $this->request->getAttribute('csrfToken') ?>"
            }));

            $("body").append(form);
            form.submit();
            form.remove();
        });
    });
</script>


<?php $this->end(); ?>