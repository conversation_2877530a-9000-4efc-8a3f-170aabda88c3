<?php $this->start('add_css'); ?>
<style>
    .navbar, .search-container, .search-container-agn {
        z-index: 1 !important;
    }
</style>

<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/existingUser.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/myAccountMyOrders.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/personalDetails.css') ?>">

<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkOutPayment.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkoutNewCards.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkOrderSummary.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/cartDeliverToAddress.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkoutDeliverAddress.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/checkout-responsive-fix.css') ?>">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<script src="https://cdn.jsdelivr.net/npm/multi-step-form-js@0.1.1/dist/multi-step-form.min.js"></script>


<style>

    .radeem-btn-c {
        min-width: fit-content;
    }
    #delivery-div {
        max-height: 500px;
        overflow-x: auto;
    }
    .showroom-list-container {
        max-height: 500px;
        overflow-x: auto;
    }

    .select2-container--default .select2-selection--single {
        height: 0 !important;
    }
    .navbar, .search-container, .search-container-agn {
        z-index: 1 !important;
    }
    .pay-button {
       padding: 10px 20% !important;
    }
    .checkout-order-summary {
        max-height: max-content;
    }
    
    /* Hide shipping sections when showroom pickup is selected */
    .shipping-section {
        transition: all 0.3s ease;
    }
    
    /* When hidden we want the section to collapse and free space so the layout reflows.
       Use display:none to fully remove it from flow; keep .disabled for the visual disabled state
       in cases where you want it visible but not interactive. */
    .shipping-section.hidden {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
    }

    /* Keep disabled appearance separate so code can add only .disabled when needed */
    .shipping-section.disabled {
        opacity: 0.9;
        pointer-events: none;
        position: relative;
    }

    .shipping-section.disabled::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1;
    }
    
    .shipping-section.disabled input[type="radio"],
    .shipping-section.disabled label {
        pointer-events: none;
        opacity: 0.4;
        cursor: not-allowed;
    }
    .checkout-deliver-address-row {
        background: unset !important;
        margin: auto !important;
        padding: initial !important;
        border-radius: 0 !important;
        box-shadow: unset !important;
        display: block !important;
    }
    .Order-s-nameandaddress {
        width: auto !important;
    }
    *{
        box-sizing: border-box;
    }
    #sch-icn {
           top: 35%;
    }
    .I-CTA-input {
            margin: 1px 0px !important;
    }
    .ax-popup {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 300px;
        padding: 20px;
        background: white;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        z-index: 9999999; 
        border-radius: 8px;
    }

    #ax-popup-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999998; 
    }

    .ax-popup button {
        margin-top: 10px;
        padding: 8px 16px;
        background: #7059a8;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .ax-popup button:hover {
        background: #5a4291;
    }

    #momo-mobile-input,
    div#momo-mobile-input,
    .form-group#momo-mobile-input,
    body .form-group#momo-mobile-input,
    body div#momo-mobile-input {
        display: none !important; 
    }

    #momo-mobile-input.show-input,
    div#momo-mobile-input.show-input,
    .form-group#momo-mobile-input.show-input,
    body .form-group#momo-mobile-input.show-input,
    body div#momo-mobile-input.show-input {
        display: flex !important; /* Show when class is added */
        flex-direction: column !important;
    }

    /* Override any other CSS rules */
    @media all {
        #momo-mobile-input:not(.show-input) {
            display: none !important;
        }
    }

    /* Enhanced Select2 styling */
    .select2-container {
        z-index: 9999;
    }

    /* Improved dropdown styling */
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #fff;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 42px;
        padding-left: 15px;
        color: #333;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }

    .select2-dropdown {
        border: 1px solid #ddd;

    /* Main overlay for loyalty popup compatibility */
    #overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        cursor: pointer;
        display: none;
    }

    /* Payment method smooth transitions */
    .payment-method-option {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .payment-method-option[style*="display: none"] {
        opacity: 0;
        height: 0;
        margin: 0;
        padding: 0;
    }

    /* Loyalty Points Popup Styling - Enhanced with transitions */
    .loyalty-points-popup {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90%;
        max-width: 500px;
        max-height: 80vh;
        padding: 20px;
        background: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        z-index: 10001;
        border-radius: 8px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
    }

    .loyalty-points-popup.show {
        opacity: 1;
    }

    /* Loyalty popup overlay */
    .loyalty-popup-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
    }

    .loyalty-popup-overlay.show {
        opacity: 1;
    }

    /* Prevent body scroll when loyalty popup is open */
    body.loyalty-popup-open {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
        top: 0 !important;
        left: 0 !important;
    }

    /* Ensure popup content is scrollable */
    .loyalty-points-popup .loyalty-contents,
    .loyalty-points-popup .radeem-btn-c {
        position: relative;
        z-index: 1;
    }

    /* Enhanced close button styling */
    .loyalty-points-popup .close-popup {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 24px;
        cursor: pointer;
        z-index: 10002;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;
        user-select: none;
    }

    .loyalty-points-popup .close-popup:hover {
        background: rgba(255, 255, 255, 1);
        transform: scale(1.1);
    }

    .loyalty-points-popup .close-popup .icon {
        line-height: 1;
        font-weight: bold;
    }

    /* Ensure popup container has proper positioning */
    #popup {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        pointer-events: none;
    }

    #popup.show,
    #popup[style*="block"] {
        pointer-events: auto;
    }

    /* Mobile responsive for loyalty popup */
    @media (max-width: 768px) {
        .loyalty-points-popup {
            width: 95%;
            max-height: 90vh;
            padding: 15px;
            top: 45%;
        }
        
        .loyalty-points-popup .close-popup {
            top: 5px;
            right: 10px;
            width: 35px;
            height: 35px;
            font-size: 20px;
        }
    }
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        background-color: #fff;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #FA9313;
    }

    .select2-results__option {
        padding: 8px 15px;
        font-size: 14px;
    }

    .select2-search--dropdown .select2-search__field {
        padding: 8px;
        border: 1px solid #ddd;
    }

    /* Country code dropdown specific styling */
    .country-code-dropdown {
        width: 100% !important;
        background-color: #fff !important;
    }

    .select2-container--open .select2-dropdown {
        margin-top: 2px;
    }

    /* Additional Select2 styling for improved appearance */
    .select2-dropdown-improved {
        border-color: #ddd !important;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.15) !important;
        border-radius: 5px !important;
        overflow: visible !important;
        margin-top: 3px !important;
        background-color: #fff !important;
    }

    /* Improved country code dropdown styling */
    .country-code-dropdown {
        min-width: 250px !important;
        width: auto !important;
        background-color: #fff !important;
        z-index: 99999 !important;
    }

    .country-code-dropdown .select2-results__option {
        padding: 8px 12px;
    }

    .select2-country-option {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .select2-country-code {
        font-weight: 500;
        font-size: 14px;
        min-width: 45px;
        color: #333;
    }

    .select2-country-name {
        font-size: 13px;
        color: #555;
        margin-left: 10px;
    }

    .country-code-dropdown .select2-results__option--highlighted[aria-selected] {
        background-color: #FA9313 !important;
    }

    .country-code-dropdown .select2-results__option--highlighted[aria-selected] .select2-country-code,
    .country-code-dropdown .select2-results__option--highlighted[aria-selected] .select2-country-name {
        color: white !important;
    }

    /* Ensure the dropdown is wide enough to show country names */
    .select2-container--open .select2-dropdown.country-code-dropdown {
        min-width: 250px !important;
        width: auto !important;
    }

    .select2-container--open .select2-dropdown--below {
        border-top: 1px solid #ddd !important;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #FA9313 !important;
        color: white !important;
    }

    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #fff9f0 !important;
        color: #FA9313 !important;
    }

    .select2-results__option {
        transition: background-color 0.2s ease;
    }

    .country-code-dropdown .select2-results__option {
        display: flex;
        align-items: center;
        padding: 8px 15px;
    }

    /* Mobile responsiveness for Select2 */
    @media (max-width: 576px) {
        .select2-container--default .select2-selection--single {
            height: 38px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            font-size: 13px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-results__option {
            padding: 6px 12px;
            font-size: 13px;
        }

        .select2-dropdown {
            width: auto !important;
            min-width: 200px !important;
        }
    }

    .text-muted {
        font-size: 10px;
        margin: 2px 20px;
    }

    #momo_mobile {
        padding: 5px 12px;
        margin: 4px 19px;
        width: 300px;
        padding-left: 83px;
    }
    .checkout-deliver-address-row {
        width: 100%;
        background: #fff;
        margin: 30px 0 0 0;
        padding: 30px 20px 20px 20px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.07);
        display: block;
    }
    /* Optionally, adjust for mobile responsiveness */
    @media (max-width: 768px) {
        .checkout-deliver-address-row {
            padding: 15px 5px;
        }
    }

    /* Showroom Header Styles */
    .showroom-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .showroom-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    /* Showroom List Grid Styles */
    #showroom-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-top: 15px;
        width: 100%;
        min-height: 100px; /* Ensure there's space for the loading indicator */
    }

    /* Loading indicator styles */
    #load-more-indicator {
        width: 100%;
        text-align: center;
        padding: 15px;
        color: #7059a8;
        font-size: 14px;
    }

    #load-more-indicator .fa-spinner {
        margin-right: 8px;
        animation: spin 1s infinite linear;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Showroom Item Styles */
    .showroom-item {
        border: 2px solid #eee;
        border-radius: 10px;
        background: white;
        transition: all 0.2s ease;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0,0,0,0.03);
        cursor: pointer;
    }

    .showroom-item:hover {
        border-color: #FA9313;
        box-shadow: 0 4px 12px rgba(250,147,19,0.1);
        transform: translateY(-2px);
    }

    .showroom-item.selected {
        border-color: #FA9313;
        box-shadow: 0 4px 12px rgba(250,147,19,0.1);
        transform: translateY(-2px);
    }

    .showroom-item.selected .showroom-item-header {
        background-color: #fffbf4;
        border-bottom-color: #ffecd9;
    }

    .showroom-item.selected .showroom-details {
        background-color: #fffdf9;
    }

    .showroom-item-content {
        padding: 0;
    }

    .showroom-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 12px 12px 12px;
        border-bottom: 1px solid #f5f5f5;
        background-color: #fafafa;
    }

    .showroom-radio-container {
        display: flex;
        align-items: center;
    }

    .showroom-radio-container input[type="radio"] {
        margin-right: 10px;
        accent-color: #FA9313;
        cursor: pointer;
        width: 18px;
        height: 18px;
        flex-shrink: 0;
        position: relative;
        top: 1px;
        z-index: 1;
    }

    .showroom-radio-container .pop-up-name {
        font-weight: 600;
        font-size: 14px;
        color: #333;
        margin-right: 8px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }

    .pop-up-showroom-type {
        padding: 3px 8px;
        border-radius: 12px;
        background: #fff0e1;
        color: #fa9313;
        font-size: 11px;
        font-weight: 600;
        margin-left: 8px;
        display: inline-block;
    }

    .showroom-actions {
        display: flex;
        gap: 8px;
    }

    .showroom-actions a {
        background: transparent;
        border: 1px solid #eee;
        cursor: pointer;
        padding: 5px 10px;
        border-radius: 4px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        font-size: 12px;
        text-decoration: none;
        color: #007bff;
        position: relative;
        z-index: 2;
    }

    .showroom-actions a i {
        margin-right: 5px;
    }

    .view-map-btn:hover {
        background-color: rgba(0, 123, 255, 0.1);
        border-color: #007bff;
    }

    .showroom-details {
        padding: 15px 15px 15px 15px;
        color: #555;
        font-size: 13px;
        line-height: 1.5;
    }

    .showroom-line {
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
    }

    .showroom-icon {
        color: #FA9313;
        width: 16px;
        margin-right: 10px;
        text-align: center;
        font-size: 14px;
        position: relative;
        top: 2px;
    }

    .showroom-phone {
        display: flex;
        align-items: center;
        margin-top: 8px;
    }

    .showroom-phone .showroom-icon {
        font-size: 14px;
    }

    .showroom-phone-link {
        color: #555;
        text-decoration: none;
    }

    .showroom-phone-link:hover {
        color: #FA9313;
        text-decoration: underline;
    }
    /* Mobile styles moved to checkout-responsive-fix.css */

    /* Mobile styles moved to checkout-responsive-fix.css */

    /* Main container styles moved to checkout-responsive-fix.css */

    /* Add smooth scrollbar for better UX */
    .checkout-order-summary::-webkit-scrollbar {
        width: 6px;
    }

    .checkout-order-summary::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .checkout-order-summary::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }

    .checkout-order-summary::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Ensure sticky works on mobile too */
    /* Mobile styles moved to checkout-responsive-fix.css */



    /* Search Container Styles */
    .showroom-search-container {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 20px;
        position: relative;
    }

    #showroom-search {
        padding: 10px 15px 10px 40px;
        border-radius: 25px;
        border: 1px solid #fa9313;
        width: 100%;
        max-width: 355px;
        font-size: 13px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    #showroom-search:focus {
        outline: none;
        box-shadow: 0 2px 10px rgba(250,147,19,0.2);
    }

    #sch-icn {
        position: absolute;
        left: 10px;
        transform: translateY(-50%) scaleX(1);
        color: #fa9313;
        font-size: 16px;
        pointer-events: none;
    }

    .showroom-count {
        color: #666;
        font-size: 0.9em;
        background: #f8f8f8;
        padding: 6px 12px;
        border-radius: 20px;
        white-space: nowrap;
    }

    @media (max-width: 600px) {
        .showroom-search-container {
            flex-direction: column;
            align-items: flex-start;
        }

        #showroom-search {
            max-width: 100%;
        }
    }

    /* Redesigned Delivery Options */

    .pick-up-locatn-checkbox {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin: 25px 20px auto;
        max-width: 600px;
        padding: 0;
        position: relative;
        z-index: 1;
    }

    .delivery-option {
        flex: 1;
        position: relative;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 10px;
        overflow: visible;
        background: white;
        max-width: 280px;
        z-index: 1;
    }

    .delivery-option input[type="radio"] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }



    .delivery-option input[type="radio"]:checked + .delivery-option-content {
        border-color: #FA9313;
        background-color: #fffbf4;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(250, 147, 19, 0.2);
        z-index: 2;
    }

    .delivery-option-icon {
        font-size: 28px;
        color: #008080;
        transition: all 0.3s ease;
        min-width: 40px;
        text-align: center;
    }
.I-CTA-button{
        margin: 0px -55px !important;
}


.pay-button .spinner {
    display: none;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

.pay-button.loading .spinner {
    display: inline-block;
}

.pay-button:disabled {
    background-color: #ccc !important;
    cursor: not-allowed;
}


#add-address-btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
    animation: pulse-address-btn 2s infinite;
}

#add-address-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(250,147,19,0.4);
}

#add-address-btn:active {
    transform: scale(0.95);
}

#add-address-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #FA9313, #ffb347);
    z-index: -1;
    border-radius: 50%;
    transition: all 0.3s ease;
}

#add-address-btn:hover::before {
    transform: rotate(180deg);
}

@keyframes pulse-address-btn {
    0% {
        box-shadow: 0 0 0 0 rgba(250,147,19,0.5);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(250,147,19,0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(250,147,19,0);
    }
}



#add-address-btn::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    pointer-events: none;
}

#add-address-btn:hover::after {
    opacity: 1;
    visibility: visible;
}


.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.address-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

#add-address-btn {
    background-color: #FA9313;
    border-color: #FA9313;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(250,147,19,0.3);
    transition: all 0.3s ease;
}

#add-address-btn i {
    font-size: 16px;
    color: white;
}

#address-list-container {
    display: grid;
    gap: 20px;
    margin-top: 15px;
    width: 100%;
}

.address-item {
    border: 2px solid #eee;
    border-radius: 10px;
    background: white;
    transition: all 0.2s ease;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.03);
    cursor: pointer;
    margin-bottom: 15px;
}

.address-item:hover {
    border-color: #FA9313;
    box-shadow: 0 4px 12px rgba(250,147,19,0.1);
    transform: translateY(-2px);
}

.address-item.selected {
    border-color: #FA9313;
    box-shadow: 0 4px 12px rgba(250,147,19,0.1);
    transform: translateY(-2px);
}

.address-item.selected .address-item-header {
    background-color: #fffbf4;
    border-bottom-color: #ffecd9;
}

.address-item.selected .address-details {
    background-color: #fffdf9;
}

.address-item-content {
    padding: 0;
    width: inherit;
}

.address-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 12px 12px 12px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #fafafa;
}

.address-radio-container {
    display: flex;
    align-items: center;
}

.address-radio-container input[type="radio"] {
    margin-right: 10px;
    accent-color: #FA9313;
    cursor: pointer;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    position: relative;
    top: 1px;
    z-index: 1;
}

.address-radio-container .pop-up-name {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-right: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.address-name {
    display: inline-block;
    margin-right: 5px;
}

.pop-up-work-home {
    padding: 3px 8px;
    border-radius: 12px;
    background: #eef4ff;
    color: #3538cd;
    font-size: 11px;
    font-weight: 600;
    margin-left: 0;
    display: inline-block;
}

.address-actions {
    display: flex;
    gap: 8px;
}
.address-item {
    padding: 0 !important;
}

.address-actions button {
    background: transparent;
    border: 1px solid #eee;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    font-size: 12px;
    position: relative;
    z-index: 2;
}



.action-text {
    display: inline-block;
}

.edit-address-btn {
    color: #007bff;
}

.edit-address-btn:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-color: #007bff;
}

.delete-address-btn {
    color: #dc3545;
}

.delete-address-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
}

.address-details {
    padding: 15px 15px 15px 15px;
    color: #555;
    font-size: 13px;
    line-height: 1.5;
}

.address-line {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
}

.address-icon {
    color: #FA9313;
    width: 16px;
    margin-right: 10px;
    text-align: center;
    font-size: 14px;
    position: relative;
    top: 2px;
}

.address-phone {
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.address-phone .address-icon {
    font-size: 14px;
}

.no-address-message {
    text-align: center;
    padding: 30px 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #ddd;
    color: #666;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.no-address-message p {
    margin: 5px 0;
    font-size: 14px;
}

/* Responsive styles for address list */
@media (max-width: 768px) {
    .address-header {
        flex-direction: row;
        align-items: center;
    }

    .address-section-title {
        font-size: 16px;
    }

    #add-address-btn {
        width: 36px;
        height: 36px;
    }

    #add-address-btn i {
        font-size: 14px;
    }

    #address-list-container {
        grid-template-columns: 1fr;
    }

    .address-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .address-actions {
        margin-top: 10px;
        align-self: flex-end;
        width: 100%;
        justify-content: flex-end;
    }

    .address-details {
        padding-left: 15px;
    }

    .address-icon {
        width: 20px;
        margin-right: 8px;
    }
}

@media (max-width: 480px) {
    .address-header {
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

    .address-section-title {
        font-size: 15px;
    }

    .address-item {
        border-width: 1px;
    }

    .address-radio-container .pop-up-name {
        font-size: 13px;
    }

    .pop-up-work-home {
        font-size: 11px;
        padding: 2px 6px;
    }

    .address-actions {
        flex-direction: row;
        gap: 5px;
    }

    .address-actions button {
        padding: 4px 8px;
        font-size: 11px;
    }

    .action-text {
        display: none;
    }

    .address-actions button i {
        margin-right: 0;
        font-size: 14px;
    }
}
    .delivery-option input[type="radio"]:checked + .delivery-option-content .delivery-option-icon {
        color: #FA9313;
        transform: scale(1.1);
    }

    .delivery-option-title {
        font-weight: 700;
        font-size: 14px;
        color: #333;
        transition: all 0.3s ease;
    }

    /* Redesigned delivery option styles */
    .delivery-option-content {
        position: relative;
        display: flex;
        align-items: center;
        padding: 20px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        overflow: visible;
        text-align: left;
        margin-top: 0;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    }

    .delivery-option-info {
        flex: 1;
        text-align: center;
    }

    .delivery-option-description {
        font-size: 14px;
        color: #666;
        line-height: 1.4;
        margin-top: 5px;
    }

    /* Animation for delivery options */
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    .pulse-animation {
        animation: pulse 0.5s ease-in-out;
    }

    /* Responsive styles for delivery options */
    @media (max-width: 768px) {
        .pick-up-locatn-checkbox {
            flex-direction: column;
            align-items: center;
            gap: 15px;
            margin: 15px auto;
        }

        .delivery-option {
            width: 100%;
            max-width: 300px;
        }

        .delivery-option-content {
            padding: 15px;
        }

        .delivery-option-icon {
            font-size: 24px;
            min-width: 30px;
        }

        .delivery-option-info {
            margin-left: 10px;
        }
    }

    @media (max-width: 480px) {
        .delivery-option-title {
            font-size: 16px;
        }

        .delivery-option-description {
            font-size: 13px;
        }
    }

    /* Wallet Popup Styles */
    #wallet-popup {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border: 1px solid orange;
        z-index: 1001;
        border-radius: 15px;
        max-width: 90%;
        width: auto !important;
    }

    #wallet-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }

    .wallet-input {
        width: 100%;
        max-width: 200px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .wallet-apply-btn {
        background-color: #FA9313;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 600;
        transition: background-color 0.3s ease;
    }

    .wallet-apply-btn:hover {
        background-color: #e8840f;
    }

    .wallet-apply-btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }

    .walletPointsCheck {
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .walletPointsCheck:hover {
        color: #FA9313 !important;
    }

    /* Mobile responsive styles for wallet popup */
    @media (max-width: 768px) {
        #wallet-popup {
            max-width: 95%;
            padding: 15px;
            margin: 10px;
        }

        .wallet-input {
            max-width: 100%;
            font-size: 16px; /* Prevent zoom on iOS */
        }

        .wallet-apply-btn {
            width: 100%;
            padding: 12px;
            font-size: 16px;
        }
    }

    @media (max-width: 480px) {
        #wallet-popup {
            max-width: 98%;
            padding: 10px;
        }

        .loyalty-contents {
            padding: 10px 20px;
        }

        .radeem-btn-c {
            padding: 10px;
        }
    }

    /* Multi-Step Form Modal Styles */
    #multistep-form-modal {
        display: none;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.5);
        z-index: 99999999 !important;
        align-items: center;
        justify-content: center;
    }

    #multistep-form-modal.show {
        display: flex !important;
    }

    /* Form Header Styles */
    #multistep-form-modal .form-header {
        border-bottom: 1px solid #eee;
        background: #f8f9fa;
    }

    #multistep-form-modal .form-header #close-multistep-form-btn:hover {
        background: rgba(0, 0, 0, 0.1);
        color: #333;
    }

    /* Step Indicator Styles */
    #multistep-form-modal .step-indicator {
        transition: all 0.3s ease;
    }

    #multistep-form-modal .step-divider.active {
        background: #28a745 !important;
    }

    /* Form Body Styles */
    #multistep-form-modal .form-body {
        background: white;
    }

    #multistep-form-modal .form-body input:focus {
        outline: none;
        border-color: #FA9313;
        box-shadow: 0 0 0 3px rgba(250, 147, 19, 0.1);
    }

    #multistep-form-modal .form-body input.invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    #multistep-form-modal .form-body input.valid {
        border-color: #28a745;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    }

    #multistep-form-modal .form-body select.invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    #multistep-form-modal .form-body select.valid {
        border-color: #28a745;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    }

    /* Mobile Input Wrapper Styles */
    .mobile-input-wrapper {
        display: flex;
        position: relative;
        border: 2px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
        transition: border-color 0.3s ease;
        background: white;
        align-items: stretch;
        width: 100%;
    }

    .mobile-input-wrapper:focus-within {
        border-color: #FA9313;
        box-shadow: 0 0 0 3px rgba(250, 147, 19, 0.1);
    }

    .mobile-input-wrapper.invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
    }

    .mobile-input-wrapper.valid {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
    }

    .mobile-input-wrapper .select2-container {
        border: none !important;
        background: #f8f9fa;
        min-width: 100px;
        max-width: 120px;
        width: auto !important;
        flex-shrink: 0;
        border-right: 1px solid #ddd !important;
    }

    .mobile-input-wrapper .select2-container .select2-selection--single {
        border: none !important;
        background: #f8f9fa !important;
        height: 48px !important;
        padding: 0 !important;
        border-radius: 0 !important;
    }

    .mobile-input-wrapper .select2-container .select2-selection__rendered {
        padding: 12px !important;
        line-height: 24px !important;
        font-size: 15px !important;
    }

    .mobile-input-wrapper input.ax-phone-input {
        border: none !important;
        padding: 12px !important;
        font-size: 15px !important;
        flex: 1 !important;
        background: white !important;
        outline: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
    }

    .mobile-input-wrapper input.ax-phone-input:focus {
        outline: none !important;
        border: none !important;
        box-shadow: none !important;
    }

    .ax-field-wrapper {
        width: 100%;
    }

    .ax-width-measure {
        position: absolute;
        visibility: hidden;
        height: auto;
        width: auto;
        white-space: nowrap;
    }

    /* Phone field error message positioning */
    #multistep-phone-input-error {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
        display: none;
    }

    /* Form Footer Styles */
    #multistep-form-modal .form-footer {
        border-top: 1px solid #eee;
        background: #f8f9fa;
    }

    #multistep-form-modal .form-footer .previous:hover {
        background-color: #5a6268;
        transform: translateY(-2px);
    }

    #multistep-form-modal .form-footer .next:hover {
        background-color: #e8840f;
        transform: translateY(-2px);
    }

    #multistep-form-modal .form-footer .submit:hover {
        background-color: #218838;
        transform: translateY(-2px);
    }

    #multistep-form-modal .form-footer button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    /* Multi-Step Form Styles */
    #myForm {
        position: relative;
        height: 100%;
    }

    #myForm .tab {
        display: none;
        animation: fadeIn 0.3s ease-in-out;
    }

    #myForm .tab.active {
        display: block;
    }

    /* Step Indicators */
    #myForm .step {
        display: inline-block;
        width: 35px;
        height: 35px;
        background-color: #ddd;
        color: white;
        border-radius: 50%;
        line-height: 35px;
        margin: 0 5px;
        text-align: center;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    #myForm .step.active {
        background-color: #FA9313;
        transform: scale(1.1);
        box-shadow: 0 0 0 3px rgba(250, 147, 19, 0.2);
    }

    #myForm .step.completed {
        background-color: #28a745;
        transform: scale(1.05);
    }

    /* Open Button Styles */
    #open-multistep-form-btn {
        background-color: #FA9313;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 3px 8px rgba(250,147,19,0.3);
    }

    #open-multistep-form-btn:hover {
        background-color: #e8840f;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(250,147,19,0.4);
    }

    /* Animation */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Credit Partner Cards */
    .credit-partner-card:hover {
        border-color: #FA9313 !important;
        box-shadow: 0 4px 15px rgba(250, 147, 19, 0.2) !important;
        transform: translateY(-3px);
    }

    .credit-partner-card input[type="radio"]:checked + * {
        border-color: #FA9313 !important;
    }

    .credit-partner-card:has(input[type="radio"]:checked) {
        border-color: #FA9313 !important;
        background-color: #fffbf4 !important;
        box-shadow: 0 4px 15px rgba(250, 147, 19, 0.2) !important;
    }

    /* EMI Plan Cards */
    .emi-plan-card {
        border: 2px solid #eee;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        background: white;
    }

    .emi-plan-card:hover {
        border-color: #FA9313 !important;
        box-shadow: 0 4px 15px rgba(250, 147, 19, 0.2) !important;
        transform: translateY(-3px);
    }

    .emi-plan-card:has(input[type="radio"]:checked) {
        border-color: #FA9313 !important;
        background-color: #fffbf4 !important;
        box-shadow: 0 4px 15px rgba(250, 147, 19, 0.2) !important;
    }

    .emi-highlight {
        background: linear-gradient(135deg, #FA9313, #e8840f);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 11px;
        font-weight: 600;
        position: absolute;
        top: -8px;
        right: 15px;
    }

    /* Form Sections */
    .form-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .form-section h4 {
        margin: 0 0 15px;
        color: #333;
        font-size: 16px;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 2px solid #FA9313;
        display: inline-block;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
        #multistep-form-modal > div {
            width: 95%;
            max-width: 95%;
        }

        #multistep-form-modal .form-header {
            padding: 15px 20px 10px;
        }

        #multistep-form-modal .form-header h2 {
            font-size: 18px;
        }

        #multistep-form-modal .form-header p {
            font-size: 12px;
        }

        #multistep-form-modal .form-body {
            flex-direction: column !important;
            padding: 0 !important;
        }

        #multistep-form-modal .form-body > div:first-child {
            padding: 20px 15px !important;
        }

        #multistep-form-modal .form-body > div:last-child {
            width: 100% !important;
            border-left: none !important;
            border-top: 1px solid #eee !important;
            padding: 15px 20px !important;
        }

        #multistep-form-modal .form-footer {
            padding: 12px 20px;
        }

        #multistep-form-modal .form-body input {
            padding: 10px;
            font-size: 16px; /* Prevent zoom on iOS */
        }

        #multistep-form-modal .step-indicator {
            gap: 5px !important;
        }

        #multistep-form-modal .step-indicator span:not(.step) {
            font-size: 10px !important;
            margin-left: 3px !important;
        }

        #multistep-form-modal .step-divider {
            width: 20px !important;
        }

        #multistep-form-modal .form-footer > div {
            flex-direction: column;
            gap: 15px;
        }

        #multistep-form-modal .form-footer > div > div {
            flex: none;
            text-align: center;
        }

        #multistep-form-modal .form-footer button {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        #multistep-form-modal .form-body {
            padding: 15px;
        }

        #multistep-form-modal .form-body input {
            padding: 10px;
        }

        #multistep-form-modal .form-header h2 {
            font-size: 18px;
        }

        #multistep-form-modal .form-header p {
            font-size: 13px;
        }

        #multistep-form-modal .form-body h3 {
            font-size: 16px;
        }

        #multistep-form-modal .form-body > div {
            grid-template-columns: 1fr;
        }
    }

/* Multi-step Form Field Styles */
#myForm input[type="text"], 
#myForm input[type="email"], 
#myForm input[type="tel"] {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

#myForm input[type="text"]:focus, 
#myForm input[type="email"]:focus, 
#myForm input[type="tel"]:focus {
    border-color: #FA9313 !important;
    box-shadow: 0 0 0 3px rgba(250, 147, 19, 0.1) !important;
    outline: none;
}

/* Invalid state styling */
#myForm input[type="text"].invalid, 
#myForm input[type="email"].invalid, 
#myForm input[type="tel"].invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
    background-color: #fff5f5;
}

/* Valid state styling */
#myForm input[type="text"].valid, 
#myForm input[type="email"].valid, 
#myForm input[type="tel"].valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
    background-color: #f8fff8;
}

/* Add checkmark icon for valid fields */
#myForm input[type="text"].valid, 
#myForm input[type="email"].valid, 
#myForm input[type="tel"].valid {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%2328a745' d='M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px 16px;
    padding-right: 40px;
}

/* Add warning icon for invalid fields */
#myForm input[type="text"].invalid, 
#myForm input[type="email"].invalid, 
#myForm input[type="tel"].invalid {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%23dc3545' d='M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z'/%3e%3cpath fill='%23dc3545' d='M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px 16px;
    padding-right: 40px;
}

/* Error message styling */
.field-error {
    animation: slideInError 0.3s ease-out;
    font-weight: 500;
}

@keyframes slideInError {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Label styling for required fields */
#myForm label[for] {
    position: relative;
}

#myForm label[for]:after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* Enhanced focus states */
#myForm input:focus + .field-error {
    display: none;
}

/* Form grid responsiveness */
@media (max-width: 768px) {
    #myForm .tab > div > div > div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
    
    /* Adjust padding for mobile */
    #myForm input[type="text"].valid, 
    #myForm input[type="email"].valid, 
    #myForm input[type="tel"].valid,
    #myForm input[type="text"].invalid, 
    #myForm input[type="email"].invalid, 
    #myForm input[type="tel"].invalid {
        padding-right: 35px;
        background-size: 14px 14px;
        background-position: right 10px center;
    }
}

/* Success message styling */
.field-success {
    color: #28a745;
    font-size: 12px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    animation: slideInSuccess 0.3s ease-out;
}

@keyframes slideInSuccess {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced checkbox styling for terms */
#terms_agreement {
    width: 20px;
    height: 20px;
    accent-color: #FA9313;
    cursor: pointer;
}

#terms_agreement:focus {
    box-shadow: 0 0 0 3px rgba(250, 147, 19, 0.2);
    outline: none;
}

/* Credit partner and EMI plan selection validation */
.credit-partner-card.selected {
    border-color: #FA9313 !important;
    background-color: #fffbf4;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(250, 147, 19, 0.2);
}

.emi-plan-card.selected {
    border-color: #FA9313 !important;
    background-color: #fffbf4;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(250, 147, 19, 0.2);
}

/* Cart Items Table Responsive Styles */
.cart-items-table-container {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: auto;
}

.cart-items-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 700px; /* Minimum width to prevent cramping */
}

.cart-items-table thead th {
    background: #FA9313;
    color: white;
    padding: 12px 8px;
    font-weight: 600;
    font-size: 13px;
    border-bottom: 2px solid #e8840f;
    position: sticky;
    top: 0;
    z-index: 10;
}

.cart-items-table tbody td {
    padding: 12px 8px;
    border-bottom: 1px solid #e9ecef;
    font-size: 13px;
}

.cart-items-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.cart-items-table tbody tr:nth-child(odd) {
    background: white;
}

.cart-items-table tbody tr:hover {
    background: #fff3cd !important;
}

/* Mobile Responsive Styles for Cart Table */
@media (max-width: 768px) {
    .cart-items-table-container {
        max-height: 350px;
    }

    .cart-items-table {
        min-width: 600px; /* Reduced minimum width for mobile */
    }

    .cart-items-table thead th,
    .cart-items-table tbody td {
        padding: 8px 6px;
        font-size: 12px;
    }

    .cart-items-table thead th {
        font-size: 11px;
    }

    /* Hide reference column on mobile to save space */
    .cart-items-table .hide-mobile {
        display: none;
    }
}

@media (max-width: 480px) {
    .cart-items-table-container {
        max-height: 300px;
    }

    .cart-items-table {
        min-width: 500px; /* Further reduced for very small screens */
    }

    .cart-items-table thead th,
    .cart-items-table tbody td {
        padding: 6px 4px;
        font-size: 11px;
    }

    .cart-items-table thead th {
        font-size: 10px;
    }
}
</style>

<?php $this->end(); ?>

<div class="productCategoryListingC">



    <div class="productCategoryListing">
        <img src="../assets/icons8-home-100.png" class="productCategoryListing-home-icn">
        <span class="productCategoryListing-home-span"><?= $this->Html->link(__('Home'), ['controller' => 'Website', 'action' => 'home', 'prefix' => false]) ?></span>
        <span class="productCategoryListing-home-span">&gt;</span>
        <span class="productCategoryListing-home-span"><?= __('Checkout') ?></span>
    </div>
</div>
<div style="text-align:center;">
            <?= $this->Flash->render() ?>
</div>
<div class="productCategoryListingC back-transparent">
    <div class="productCategoryListing">
        <div class="checkout-payment-title"><?= __('CART') ?> <span>(<?= $total_items ?>)</span></div>
    </div>
</div>


<div class="check-out-container">

        <div class="p-v-p-item-description-container">

            <div class="show-order-summary-container">
                <div class="show-order-summary">
                    <?= __('Order Summary') ?>
                </div>
                <?php foreach ($cartItems as $item): ?>
                    <div class="my-order-item-details">
                        <a href="/product/<?= $item['url_key'] ?>"><img src="<?= $item['product_image'] ?>" class="my-order-item-icon"></a>
                        <div class="my-order-items-details-tpc">
                            <div class="my-order-item-details-title-container">
                                <div class="my-order-item-details-title"><a href="/product/<?= $item['url_key'] ?>"><?= $item['product_name'] ?></a></div>
                            </div>
                            <div class="my-order-item-details-price"><?= $this->Price->setPriceFormat($item['price']) ?>
                            <input type="hidden" value="<?= $item['price'] ?>" class='cart_items_list_price'>
                            <input type="hidden" value="<?= $item['product_id'] ?>" class='cart_items_list_id'>
                            <input type="hidden" value="<?= $item['category_name'] ?>" class='cart_items_list_category_name'>
                            <input type="hidden" value="<?= $item['brand_name'] ?>" class='cart_items_list_brand_name'>
                            </div>
                            <div class="my-order-item-details-price"><span class="strikethrough-text" style="margin-left:0;"><?= $this->Price->setPriceFormat($item['sale']) ?></span></div>
                            <div>
                                <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $item['discount'] ?></span>% 0ff</span>
                            </div>
                            <div class="my-order-item-details-price"><?= __("Reference: "). $item['reference_name'] ?> </div>
                            <div class="units-left"><?= $item['get_available_status'] ?></div>
                        
                            <div class="credit-available emi-option text-green-600 text-sm flex items-center space-x-2 mt-2">
                                <i class="fas fa-credit-card"></i>
                                <?php if ($item['avl_on_credit'] == 1): ?>
                                    <span class="credit-available-text"><?= __('Available on EMI') ?></span>
                                <?php else: ?>
                                    <span class="credit-not-available-text"><?= __('Not Available On Credit') ?></span>
                                <?php endif; ?>
                            </div>

                        </div>
                        <div class="add-to-cart-ctnr">
                            <div class="counter">
                                <button class="counter-btn updateCartItem" data-item-id="<?= $item['cart_item_id'] ?>"
                                        data-item-type="decrease">-
                                </button>
                                <div class="counter-value cartItemQty" id="value"
                                    value="<?= $item['quantity'] ?>"><?= $item['quantity'] ?>
                                </div>
                                <button class="counter-btn updateCartItem" data-item-id="<?= $item['cart_item_id'] ?>"
                                        data-item-type="increase">+
                                </button>
                            </div>
                            <div class="p-v-p-item-description-add-to-wishlist-share">

                                <?php if ($item['whishlist']): ?>
                                    <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                        data-product-id="<?= $item['product_id'] ?>"><span
                                            class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                                src="/assets/heart-background.png" class="wishlist"> </span>
                                    </div>
                                <?php else: ?>
                                        <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                            data-product-id="<?= $item['product_id'] ?>"><span
                                                class="p-v-p-item-description-add-to-wishlist-heart"> <img
                                                    src="/assets/heart-nobackgrounddark.png" class="wishlist"> </span>
                                        </div>
                                <?php endif; ?>
                            </div>
                            <button class="close-btn-btn closeCartItem" data-item-id="<?= $item['cart_item_id'] ?>">✖
                            </button>
                        </div>

                    </div>
                <?php endforeach; ?>

                <div class="show-order-summary">
                        <?= __('Payment Method') ?>
                </div>
                <div class="my-order-item-details">
                    <div class="card-details">
                    
                        <div class="pay-with-card-i-l">
                            <?php foreach ($methods as $k => $val): ?>
                            <?php 
                                // Hide Wave payment method if total price is 0
                                $hideWave = ($val['name'] === 'Wave' && $totalPrice <= 0);
                            ?>
                            <?php if (!$hideWave): ?>
                                    <div class="payment-method-option" data-method="<?= strtolower($val['name']) ?>">
                                        <input type="radio" id="method_<?= $val['name'] ?>"
                                            name="method" <?php if ($k == 0): echo 'checked';  endif; ?>
                                            value="<?= $val['name'] ?>" class="Pay-With-Credit-Card-input">
                                        <label for="method_<?= $val['name'] ?>"><?= $val['name'] ?></label>
                                    </div>
                            <?php endif; ?>
                                
                            <?php endforeach; ?>
                        </div>

                        <!-- Mobile Number Input for MTN MoMo - Only visible when MTN MoMo is selected -->
                        <div id="momo-mobile-input" class="form-group mt-3" style="display: none !important;">
                            <label for="momo_mobile" class="Billing-Address"><?= __('Mobile Number (required for MTN MoMo)') ?></label>
                            <small class="text-muted"><?= __('Format: Country code + Mobile number (e.g., +237XXXXXXXXX)') ?></small>
                            <div id="mobile-container" class="toggle-input">
                                <div class="ax-field-wrapper">
                                    <div class="ax-input-box mobile-input-wrapper">
                                        <select id="ax-country-select" name="country_code" class="ax-country-select2 searchable-select">
                                            <option value="1">+1 <small>(USA, Canada)</small></option>
                                                                    <option value="7">+7 <small>(Russia, Kazakhstan)</small></option>
                                                                    <option value="20">+20 <small>(Egypt)</small></option>
                                                                    <option value="27">+27 <small>(South Africa)</small></option>
                                                                    <option value="30">+30 <small>(Greece)</small></option>
                                                                    <option value="31">+31 <small>(Netherlands)</small></option>
                                                                    <option value="32">+32 <small>(Belgium)</small></option>
                                                                    <option value="33">+33 <small>(France)</small></option>
                                                                    <option value="34">+34 <small>(Spain)</small></option>
                                                                    <option value="36">+36 <small>(Hungary)</small></option>
                                                                    <option value="39">+39 <small>(Italy)</small></option>
                                                                    <option value="40">+40 <small>(Romania)</small></option>
                                                                    <option value="41">+41 <small>(Switzerland)</small></option>
                                                                    <option value="43">+43 <small>(Austria)</small></option>
                                                                    <option value="44">+44 <small>(United Kingdom)</small></option>
                                                                    <option value="45">+45 <small>(Denmark)</small></option>
                                                                    <option value="46">+46 <small>(Sweden)</small></option>
                                                                    <option value="47">+47 <small>(Norway)</small></option>
                                                                    <option value="48">+48 <small>(Poland)</small></option>
                                                                    <option value="49">+49 <small>(Germany)</small></option>
                                                                    <option value="51">+51 <small>(Peru)</small></option>
                                                                    <option value="52">+52 <small>(Mexico)</small></option>
                                                                    <option value="53">+53 <small>(Cuba)</small></option>
                                                                    <option value="54">+54 <small>(Argentina)</small></option>
                                                                    <option value="55">+55 <small>(Brazil)</small></option>
                                                                    <option value="56">+56 <small>(Chile)</small></option>
                                                                    <option value="57">+57 <small>(Colombia)</small></option>
                                                                    <option value="58">+58 <small>(Venezuela)</small></option>
                                                                    <option value="60">+60 <small>(Malaysia)</small></option>
                                                                    <option value="61">+61 <small>(Australia)</small></option>
                                                                    <option value="62">+62 <small>(Indonesia)</small></option>
                                                                    <option value="63">+63 <small>(Philippines)</small></option>
                                                                    <option value="64">+64 <small>(New Zealand)</small></option>
                                                                    <option value="65">+65 <small>(Singapore)</small></option>
                                                                    <option value="66">+66 <small>(Thailand)</small></option>
                                                                    <option value="81">+81 <small>(Japan)</small></option>
                                                                    <option value="82">+82 <small>(South Korea)</small></option>
                                                                    <option value="84">+84 <small>(Vietnam)</small></option>
                                                                    <option value="86">+86 <small>(China)</small></option>
                                                                    <option value="90">+90 <small>(Turkey)</small></option>
                                                                    <option value="91">+91 <small>(India)</small></option>
                                                                    <option value="92">+92 <small>(Pakistan)</small></option>
                                                                    <option value="93">+93 <small>(Afghanistan)</small></option>
                                                                    <option value="94">+94 <small>(Sri Lanka)</small></option>
                                                                    <option value="95">+95 <small>(Myanmar)</small></option>
                                                                    <option value="98">+98 <small>(Iran)</small></option>
                                                                    <option value="211">+211 <small>(South Sudan)</small></option>
                                                                    <option value="212">+212 <small>(Morocco)</small></option>
                                                                    <option value="213">+213 <small>(Algeria)</small></option>
                                                                    <option value="216">+216 <small>(Tunisia)</small></option>
                                                                    <option value="218">+218 <small>(Libya)</small></option>
                                                                    <option value="220">+220 <small>(Gambia)</small></option>
                                                                    <option value="221">+221 <small>(Senegal)</small></option>
                                                                    <option value="222">+222 <small>(Mauritania)</small></option>
                                                                    <option value="223">+223 <small>(Mali)</small></option>
                                                                    <option value="224">+224 <small>(Guinea)</small></option>
                                                                    <option value="225">+225 <small>(Ivory Coast)</small></option>
                                                                    <option value="226">+226 <small>(Burkina Faso)</small></option>
                                                                    <option value="227">+227 <small>(Niger)</small></option>
                                                                    <option value="228">+228 <small>(Togo)</small></option>
                                                                    <option value="229">+229 <small>(Benin)</small></option>
                                                                    <option value="230">+230 <small>(Mauritius)</small></option>
                                                                    <option value="231">+231 <small>(Liberia)</small></option>
                                                                    <option value="232">+232 <small>(Sierra Leone)</small></option>
                                                                    <option value="233">+233 <small>(Ghana)</small></option>
                                                                    <option value="234">+234 <small>(Nigeria)</small></option>
                                                                    <option value="235">+235 <small>(Chad)</small></option>
                                                                    <option value="236">+236 <small>(Central African Republic)</small></option>
                                                                    <option value="237">+237 <small>(Cameroon)</small></option>
                                                                    <option value="238">+238 <small>(Cape Verde)</small></option>
                                                                    <option value="239">+239 <small>(Sao Tome and Principe)</small></option>
                                                                    <option value="240">+240 <small>(Equatorial Guinea)</small></option>
                                                                    <option value="241">+241 <small>(Gabon)</small></option>
                                                                    <option value="242">+242 <small>(Congo - Brazzaville)</small></option>
                                                                    <option value="243">+243 <small>(Congo - Kinshasa)</small></option>
                                                                    <option value="244">+244 <small>(Angola)</small></option>
                                                                    <option value="245">+245 <small>(Guinea-Bissau)</small></option>
                                                                    <option value="246">+246 <small>(British Indian Ocean Territory)</small></option>
                                                                    <option value="248">+248 <small>(Seychelles)</small></option>
                                                                    <option value="249">+249 <small>(Sudan)</small></option>
                                                                    <option value="250">+250 <small>(Rwanda)</small></option>
                                                                    <option value="251">+251 <small>(Ethiopia)</small></option>
                                                                    <option value="252">+252 <small>(Somalia)</small></option>
                                                                    <option value="253">+253 <small>(Djibouti)</small></option>
                                                                    <option value="254">+254 <small>(Kenya)</small></option>
                                                                    <option value="256">+256 <small>(Uganda)</small></option>
                                                                    <option value="257">+257 <small>(Burundi)</small></option>
                                                                    <option value="258">+258 <small>(Mozambique)</small></option>
                                                                    <option value="260">+260 <small>(Zambia)</small></option>
                                                                    <option value="261">+261 <small>(Madagascar)</small></option>
                                                                    <option value="262">+262 <small>(Réunion, Mayotte)</small></option>
                                                                    <option value="263">+263 <small>(Zimbabwe)</small></option>
                                                                    <option value="264">+264 <small>(Namibia)</small></option>
                                                                    <option value="265">+265 <small>(Malawi)</small></option>
                                                                    <option value="266">+266 <small>(Lesotho)</small></option>
                                                                    <option value="267">+267 <small>(Botswana)</small></option>
                                                                    <option value="268">+268 <small>(Eswatini)</small></option>
                                                                    <option value="269">+269 <small>(Comoros)</small></option>
                                                                    <option value="290">+290 <small>(Saint Helena)</small></option>
                                                                    <option value="291">+291 <small>(Eritrea)</small></option>
                                                                    <option value="297">+297 <small>(Aruba)</small></option>
                                                                    <option value="298">+298 <small>(Faroe Islands)</small></option>
                                                                    <option value="299">+299 <small>(Greenland)</small></option>
                                        </select>
                                        <input type="tel" name="momo_mobile" id="ax-mobile-input" class="ax-phone-input" placeholder="<?= __('Enter mobile number') ?>" value="<?= isset($mobile) ? $mobile : '' ?>" required>
                                        <span id="ax-width-measure" class="ax-width-measure"></span>
                                    </div>
                                </div>
                                <label id="ax-mobile-input-error" class="error" for="ax-mobile-input" style="display: none;"></label>
                            </div>
                        </div>

                    </div>
                </div>





            </div>

        </div>

        <div class="checkout-order-summary">
    <div class="checkout-deliver-address-row">
        <div class="Order-s-nameandaddress">
            <div class="checkout-order-summary-title">
            <?= __('PRICE DETAILS') ?>
            </div>

            <div class="checkout-order-summary-price">
                <div><?= __('Price') ?> <span>(<?= $total_items ?> <?= __('Items') ?>)</span> : </div>
                <div><?= $this->Price->setPriceFormat($totalPrice) ?></div>
            </div>
            <hr class="checkout-order-summary-hr">

            <div class="for-padding">

                <div class="checkout-order-discount-price-label">
                    <div>Discount</div>
                    <div style="text-decoration: line-through;"><?= $this->Price->setPriceFormat($totalDiscountedPrice) ?></div>
                </div>

                <div class="checkout-order-discount-price-label">
                    <div>
                        <?= __('Coupon') ?> <span class="font-size"><?= __('Code') ?></span>
                        <span class="coupon-status">
                            <span class="coupon-applied" style="display: none;">
                                <span class="font-size" style="font-size: 10px;">(<span class="appliedCoupon"></span>)</span>
                                <span class="font-size" style="font-size: 10px; color: red; cursor: pointer;">
                                    (<span class="removeCoupon"><?= __('Remove') ?></span>)
                                </span>
                            </span>
                            <span class="no-coupon" style="font-size: 10px;"><?= __('No coupon applied') ?></span>
                        </span>
                    </div>
                    <div class="offerApplied">0</div>
                </div>

                <div class="checkout-order-discount-price-label">
                    <div>
                        <span id="loyaltyPoints" class="loyaltyPointsCheck"
                            style="color: teal; text-decoration: underline; cursor: pointer;font-weight:600"><?= __('Redeem Loyalty Points') ?>
                        </span>
                        <span class="loyalty-status">
                            <span class="loyalty-applied" style="display: none;">
                                <span class="font-size" style="font-size: 10px; color: red; cursor: pointer;">
                                    (<span class="removeLoyalty"><?= __('Remove') ?></span>)
                                </span>
                            </span>
                            <span class="no-loyalty" style="font-size: 10px;"><?= __('No loyalty points applied') ?></span>
                        </span>
                    </div>
                    <div id="loyalty-points"><span class="lyt-reedem-point">0</span> FCFA</div>
                    <input type="hidden" name="lytPoints" class="lytPoints">
                </div>
                <!-- <div class="checkout-order-discount-price-label">
                    <div>
                        <label>
                            <input name="wallet" type="checkbox" id="wallet-checkbox" <?= ($wallet > 0) ? '' : 'disabled' ?>>
                            <?= __('Wallet') ?>
                        </label>
                    </div>
                    <div id="wallet-remaining"><?= $this->Price->setPriceFormat($wallet) ?></div>
                </div> -->
                <div class="checkout-order-discount-price-label">
                    <div>
                        <span id="walletPoints" class="walletPointsCheck"
                            style="color: teal; text-decoration: underline; cursor: pointer;font-weight:600"><?= __('Wallet') ?>
                        </span>
                        <span class="wallet-status">
                            <span class="wallet-applied" style="display: none;">
                                <span class="font-size" style="font-size: 10px; color: red; cursor: pointer;">
                                    (<span class="removeWallet"><?= __('Remove') ?></span>)
                                </span>
                            </span>
                            <span class="no-wallet" style="font-size: 10px;"><?= __('No wallet amount applied') ?></span>
                        </span>
                    </div>
                    <div id="wallet-points"><span class="wallet-used-amount">0</span> FCFA</div>
                    <input type="hidden" name="walletAmount" class="walletAmount">
                </div>
                <!-- Pop-up Modal -->
                <div id="popup" style="display: none;">
                    <div class="loyalty-popup-overlay"></div>
                    <div class="loyalty-points-popup">
                        <span onclick="closePopup()" class="close-popup"><span class="icon">×</span></span>

                        <div class="loyalty-contents">
                            <p class="emoji">🏅</p>
                            <p class="loyalty-points"><?= __('Your Loyalty Points') ?></p>
                            <p class="date"><?= __('Valid untill') ?> <span class="setDateAjax"></span></p>
                            <div class="background-orange">
                                <p><?= __('Total Loyalty Points') ?></p>
                                <p class="loyalty-price">0</p>

                            </div>
                            <div class="background-orange">
                                <p><?= __('Points Worth') ?></p>
                                <p class="loyalty-price">0</p>
                            </div>
                        </div>


                        <div class="radeem-btn-c">
                            <input type="text" placeholder="<?= __('Enter points to redeem') ?>" class="radeem-input">
                            <p class="color-blue"><?= __('Points worth') ?> : <span> 0 </span></p>
                            <p class="color-blue loyalty-max-info" style="font-size: 12px; color: #666; margin: 5px 0;"><?= __('Maximum based on order total') ?>: <span class="max-redeemable-points">0</span> <?= __('points') ?></p>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <button class="radeem-btn" style="flex: 1;"><?= __('Redeem Points') ?></button>
                                <button class="use-max-loyalty-btn" style="background: #f8f9fa; color: #6c757d; border: 1px solid #dee2e6; padding: 8px 12px; border-radius: 4px; font-size: 12px; white-space: nowrap;"><?= __('Use Max') ?></button>
                            </div>
                        </div>

                    </div>
                </div>
                <div id="overlay" style="display: none;"></div>

                <!-- Wallet Pop-up Modal -->
                <div id="wallet-popup" style="display: none;">
                    <span onclick="closeWalletPopup()" class="close-popup"><span class="icon">×</span></span>
                    <div class="loyalty-points-popup">

                        <div class="loyalty-contents">
                            <p class="emoji">💰</p>
                            <p class="loyalty-points"><?= __('Your Wallet') ?></p>
                            <p class="date"><?= __('Available Balance') ?></p>
                            <div class="background-orange">
                                <p><?= __('Total Wallet Amount') ?></p>
                                <p class="loyalty-price"><?= $this->Price->setPriceFormat($wallet) ?></p>
                            </div>
                            <div class="background-orange">
                                <p><?= __('Amount to Use') ?></p>
                                <p class="loyalty-price"><span class="wallet-amount-to-use">0</span> FCFA</p>
                            </div>
                        </div>

                        <div class="radeem-btn-c">
                            <input type="number" placeholder="<?= __('Enter amount to use') ?>" class="wallet-input" min="0" max="<?= $wallet ?>" step="0.01">
                            <p class="color-blue"><?= __('Available Balance') ?>: <span><?= $this->Price->setPriceFormat($wallet) ?></span></p>
                            <p class="color-blue wallet-max-info" style="font-size: 12px; color: #666;"><?= __('Maximum usable') ?>: <span class="max-usable-amount"><?= $this->Price->setPriceFormat($wallet) ?></span></p>
                            <button class="wallet-apply-btn"><?= __('Apply Wallet Amount') ?></button>
                        </div>

                    </div>
                </div>
                <div id="wallet-overlay" style="display: none;"></div>





                <div class="checkout-order-discount-price-label shipping-section" id="shipping-charges-section">
                    <div><?= __('Shipping Charges') ?> :</div>
                    <div style="" class="delivery-state">-</div>
                </div>


                <div class="checkout-order-discount-price-label shipping-section" id="delivery-mode-section">
                    <div><?= __('Delivery Mode') ?></div>
                    <div>
                        <!-- <select id="delivery_mode_type" name="delivery_mode_type">
                            <option value="standard">Standard</option>
                            <option value="express">Express</option>
                        </select> -->

                        <input type='radio' id="standard" class="delivery_mode_type delivery_mode_type2" name="delivery_mode_type" value="standard" checked> <label for="standard"><?= __('Standard') ?></label>
                        <input type='radio' id="express" class="delivery_mode_type delivery_mode_type2" name="delivery_mode_type" value="express"> <label for="express"><?= __('Express') ?></label>

                    </div>
                </div>

            </div>

            <hr class="checkout-order-summary-hr">

            <div class="checkout-order-summary-price">
                <div><?= __('Total') ?></div>
                <div><span class="totalAmount"></span> FCFA</div>
            </div>

            <div class="I-CTA-Btn">
                <input placeholder="<?= __('Enter Code') ?>" maxlength="12" class="I-CTA-input couponName">
                <button class="I-CTA-button checkCouponCode"><?= __('Apply Coupon') ?></button>
            </div>


            <hr class="checkout-order-summary-hr">
            
    
            <button id="payment-confirm" class="pay-button">
                <span class="spinner"></span><?= __('Pay') ?> <?= $this->Price->setPriceFormat($totalPrice) ?>
            </button>

            <!-- Credit Application Button (Hidden by default) -->
            <button id="apply-credit-button" class="pay-button" style="display: none;">
                <span class="spinner"></span><?= __('Apply for Credit') ?>
            </button>

        <hr class="checkout-order-summary-hr">

                    <div class="checkout-order-summary-title"><i class="fa fa-truck"></i> &nbsp; <?= __("Deliver To Address") ?></div>

    </div>
</div>
        <!-- NEW FULL WIDTH ROW FOR DELIVER TO ADDRESS -->
        <div class="checkout-deliver-address-row">


            <div class="delivery-options">
                <div class="pick-up-locatn-checkbox">
                    <div class="delivery-option">
                        <input type="radio" name="delivery_option" checked value="pickup" tooltip="Pick up your order from one of our showrooms" id="pickup-from-showroom">
                        <label class="delivery-option-content" for="pickup-from-showroom">
                            <i class="fas fa-store delivery-option-icon"></i>
                            <div class="delivery-option-info">
                                <div class="delivery-option-title"><?= __('Showroom') ?></div>
                            </div>
                        </label>
                    </div>
                    <div class="delivery-option">
                        <input type="radio" name="delivery_option" value="delivery" tooltip="Get your order delivered to your address" id="deliver-to-address">
                        <label class="delivery-option-content" for="deliver-to-address">
                            <i class="fas fa-home delivery-option-icon"></i>
                            <div class="delivery-option-info">
                                <div class="delivery-option-title"><?= __('Home') ?></div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>

            <div class="Order-s-nameandaddress" >
                

                <div id="loading-message" style="display:none;">
                    <i class="fa fa-spinner fa-spin"></i> <?= __('Loading showrooms') ?>...
                </div>
                    <div class="showroom-list-container" id="pickup-div">
                        <div class="showroom-header">
                        <h3 class="showroom-section-title"><?= __('Available Showrooms') ?></h3>
                    </div>

                    <div class="showroom-search-container">
                        <i class="fa fa-search" id="sch-icn"></i>
                        <input type="text" id="showroom-search" maxlength="25" placeholder="<?= __('Search by location or showroom name') ?>">
                    </div>

                    <div id="showroom-list">
                        <!-- Dynamic Showroom List will appear here -->
                    </div>
                </div>
            </div>

            <div class="Order-s-nameandaddress" id="delivery-div" style="display: none;">
                <?php if ($customerId): ?>
                    <div class="address-header">
                        <h3 class="address-section-title"><?= __('My Addresses') ?></h3>
                        <span href="" id="add-address-btn" class="btn btn-sm btn-primary" title="<?= __('Add New Address') ?>">
                            <i class="fa fa-plus"></i>
                        </span>
                    </div>

                    <div id="address-list-container">
                        <?php if ($customerAddress): ?>
                            <?php foreach ($customerAddress as $val): ?>
                                <div class="address-item" data-address-id="<?= $val['id'] ?>">
                                    <div class="address-item-content">
                                        <div class="address-item-header">
                                            <div class="address-radio-container">
                                                <input type="radio" name="address" value="<?= $val['id'] ?>"
                                                    data-city-id="<?= $val['city_id'] ?>" id="address-<?= $val['id'] ?>">
                                                <label class="pop-up-name" for="address-<?= $val['id'] ?>">
                                                    <span class="address-name"><?= $val['name'] ?></span>
                                                    <span class="pop-up-work-home"><?= $val['type'] ?></span>
                                                </label>
                                            </div>
                                            <div class="address-actions">
                                                <button type="button" class="edit-address-btn" data-address-id="<?= $val['id'] ?>" title="<?= __('Edit Address') ?>">
                                                    <i class="fa fa-edit"></i>
                                                </button>
                                                <button type="button" class="delete-address-btn" data-address-id="<?= $val['id'] ?>" title="<?= __('Delete Address') ?>">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="address-details">
                                            <div class="address-line">
                                                <i class="fa fa-home address-icon"></i>
                                                <?= $val['type'] ?> <?= $val['house_no'] ?> <?= $val['address_line1'] ?>
                                            </div>

                                            <div class="address-line">
                                                <i class="fa fa-city address-icon"></i>
                                                <?= $val['city']['city_name'] ?>
                                            </div>
                                            <div class="address-phone">
                                                <i class="fa fa-phone address-icon"></i>
                                                <span><?= $val['phone_no1'] ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-address-message">
                                <i class="fa fa-map-marker-alt" style="font-size: 24px; color: #FA9313; margin-bottom: 10px;"></i>
                                <p><?= __("You don't have any saved addresses.") ?></p>
                                <p><?= __("Please add a new address using the + button above.") ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="Order-s-nameandaddress-internal-ctn">
                        <?= __('Login required!') ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Address Modal Overlay -->
        <div id="address-modal-overlay" style="display: none; position: fixed !important; top: 0 !important; left: 0 !important; width: 100vw !important; height: 100vh !important; background: rgba(0, 0, 0, 0.5); z-index: 9999998 !important;"></div>

        <!-- Address Modal -->
        <div id="address-modal" class="ax-popup" style="display: none; max-width: 90%; width: 550px; z-index: ********** !important; position: fixed !important; top: 50% !important; left: 50% !important; transform: translate(-50%, -50%) !important; border-radius: 8px; box-shadow: 0 5px 30px rgba(0,0,0,0.3) !important;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                <h3 id="address-modal-title" style="font-size: 18px; font-weight: 600; color: #333; margin: 0;"><i class="fa fa-map-marker" style="margin-right: 8px; color: #FA9313;"></i><?= __('Add New Address') ?></h3>
                <button id="close-address-modal" style="background: none; border: none; font-size: 22px; cursor: pointer; padding: 0; margin: 0; line-height: 1; color: #999; transition: color 0.2s ease;">&times;</button>
            </div>
            <form id="address-form" method="post" style="max-height: 70vh; overflow-y: auto; padding-right: 5px;">
                <input type="hidden" id="address_id" name="id" value="">
                <input type="hidden" name="customer_id" value="<?= $customerId ?>">

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="address_name" style="display: block; margin-bottom: 5px; font-weight: 500;"><?= __('Full Name') ?></label>
                        <input type="text" id="address_name" name="name" placeholder="<?= __('Enter your full name') ?>" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>

                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="address_type" style="display: block; margin-bottom: 5px; font-weight: 500;"><?= __('Address Type') ?></label>
                        <select id="address_type" name="type" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="Home"><?= __('Home') ?></option>
                            <option value="Office"><?= __('Office') ?></option>
                        </select>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="house_no" style="display: block; margin-bottom: 5px; font-weight: 500;"><?= __('House/Building Number') ?></label>
                        <input type="text" id="house_no" name="house_no" placeholder="<?= __('Enter house/building number') ?>" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>

                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="zipcode" style="display: block; margin-bottom: 5px; font-weight: 500;"><?= __('Zipcode') ?></label>
                        <input type="text" id="zipcode" name="zipcode" placeholder="<?= __('Enter zipcode') ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label for="address_line1" style="display: block; margin-bottom: 5px; font-weight: 500;"><?= __('Address Line 1') ?></label>
                    <input type="text" id="address_line1" name="address_line1" placeholder="<?= __('Street address, apartment, etc.') ?>" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                </div>

                <div class="form-group" style="margin-bottom: 15px;">
                    <label for="address_line2" style="display: block; margin-bottom: 5px; font-weight: 500;"><?= __('Address Line 2 (Optional)') ?></label>
                    <input type="text" id="address_line2" name="address_line2" placeholder="<?= __('Apartment, suite, unit, building, floor, etc.') ?>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="city_id" style="display: block; margin-bottom: 5px; font-weight: 500;"><?= __('City') ?></label>
                        <select id="city_id" name="city_id" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; ">
                            <option value=""><?= __('Select City') ?></option>
                            <?php foreach ($cities as $city): ?>
                                <option value="<?= $city->id ?>"><?= $city->city_name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group" style="margin-bottom: 15px;">
                        <label for="phone_no1" style="display: block; margin-bottom: 5px; font-weight: 500;"><?= __('Phone Number') ?></label>
                        <div class="ax-field-wrapper">
                            <div class="ax-input-box mobile-input-wrapper">
                                <select id="address-country-select" name="country_code1" class="ax-country-select searchable-select">
                                    <option value="1">+1 <small>(USA, Canada)</small></option>
                                    <option value="7">+7 <small>(Russia, Kazakhstan)</small></option>
                                    <option value="20">+20 <small>(Egypt)</small></option>
                                    <option value="27">+27 <small>(South Africa)</small></option>
                                    <option value="30">+30 <small>(Greece)</small></option>
                                    <option value="31">+31 <small>(Netherlands)</small></option>
                                    <option value="32">+32 <small>(Belgium)</small></option>
                                    <option value="33">+33 <small>(France)</small></option>
                                    <option value="34">+34 <small>(Spain)</small></option>
                                    <option value="36">+36 <small>(Hungary)</small></option>
                                    <option value="39">+39 <small>(Italy)</small></option>
                                    <option value="40">+40 <small>(Romania)</small></option>
                                    <option value="41">+41 <small>(Switzerland)</small></option>
                                    <option value="43">+43 <small>(Austria)</small></option>
                                    <option value="44">+44 <small>(UK)</small></option>
                                    <option value="45">+45 <small>(Denmark)</small></option>
                                    <option value="46">+46 <small>(Sweden)</small></option>
                                    <option value="47">+47 <small>(Norway)</small></option>
                                    <option value="48">+48 <small>(Poland)</small></option>
                                    <option value="49">+49 <small>(Germany)</small></option>
                                    <option value="51">+51 <small>(Peru)</small></option>
                                    <option value="52">+52 <small>(Mexico)</small></option>
                                    <option value="53">+53 <small>(Cuba)</small></option>
                                    <option value="54">+54 <small>(Argentina)</small></option>
                                    <option value="55">+55 <small>(Brazil)</small></option>
                                    <option value="56">+56 <small>(Chile)</small></option>
                                    <option value="57">+57 <small>(Colombia)</small></option>
                                    <option value="58">+58 <small>(Venezuela)</small></option>
                                    <option value="60">+60 <small>(Malaysia)</small></option>
                                    <option value="61">+61 <small>(Australia)</small></option>
                                    <option value="62">+62 <small>(Indonesia)</small></option>
                                    <option value="63">+63 <small>(Philippines)</small></option>
                                    <option value="64">+64 <small>(New Zealand)</small></option>
                                    <option value="65">+65 <small>(Singapore)</small></option>
                                    <option value="66">+66 <small>(Thailand)</small></option>
                                    <option value="81">+81 <small>(Japan)</small></option>
                                    <option value="82">+82 <small>(South Korea)</small></option>
                                    <option value="84">+84 <small>(Vietnam)</small></option>
                                    <option value="86">+86 <small>(China)</small></option>
                                    <option value="90">+90 <small>(Turkey)</small></option>
                                    <option value="91">+91 <small>(India)</small></option>
                                    <option value="92">+92 <small>(Pakistan)</small></option>
                                    <option value="93">+93 <small>(Afghanistan)</small></option>
                                    <option value="94">+94 <small>(Sri Lanka)</small></option>
                                    <option value="95">+95 <small>(Myanmar)</small></option>
                                    <option value="98">+98 <small>(Iran)</small></option>
                                    <option value="212">+212 <small>(Morocco)</small></option>
                                    <option value="213">+213 <small>(Algeria)</small></option>
                                    <option value="216">+216 <small>(Tunisia)</small></option>
                                    <option value="218">+218 <small>(Libya)</small></option>
                                    <option value="220">+220 <small>(Gambia)</small></option>
                                    <option value="221">+221 <small>(Senegal)</small></option>
                                    <option value="222">+222 <small>(Mauritania)</small></option>
                                    <option value="223">+223 <small>(Mali)</small></option>
                                    <option value="224">+224 <small>(Guinea)</small></option>
                                    <option value="225">+225 <small>(Côte d'Ivoire)</small></option>
                                    <option value="226">+226 <small>(Burkina Faso)</small></option>
                                    <option value="227">+227 <small>(Niger)</small></option>
                                    <option value="228">+228 <small>(Togo)</small></option>
                                    <option value="229">+229 <small>(Benin)</small></option>
                                    <option value="230">+230 <small>(Mauritius)</small></option>
                                    <option value="231">+231 <small>(Liberia)</small></option>
                                    <option value="232">+232 <small>(Sierra Leone)</small></option>
                                    <option value="233">+233 <small>(Ghana)</small></option>
                                    <option value="234">+234 <small>(Nigeria)</small></option>
                                    <option value="235">+235 <small>(Chad)</small></option>
                                    <option value="236">+236 <small>(Central African Republic)</small></option>
                                    <option value="237" selected>+237 <small>(Cameroon)</small></option>
                                    <option value="238">+238 <small>(Cape Verde)</small></option>
                                    <option value="239">+239 <small>(São Tomé and Príncipe)</small></option>
                                    <option value="240">+240 <small>(Equatorial Guinea)</small></option>
                                    <option value="241">+241 <small>(Gabon)</small></option>
                                    <option value="242">+242 <small>(Congo)</small></option>
                                    <option value="243">+243 <small>(DR Congo)</small></option>
                                    <option value="244">+244 <small>(Angola)</small></option>
                                    <option value="245">+245 <small>(Guinea-Bissau)</small></option>
                                    <option value="248">+248 <small>(Seychelles)</small></option>
                                    <option value="249">+249 <small>(Sudan)</small></option>
                                    <option value="250">+250 <small>(Rwanda)</small></option>
                                    <option value="251">+251 <small>(Ethiopia)</small></option>
                                    <option value="252">+252 <small>(Somalia)</small></option>
                                    <option value="253">+253 <small>(Djibouti)</small></option>
                                    <option value="254">+254 <small>(Kenya)</small></option>
                                    <option value="255">+255 <small>(Tanzania)</small></option>
                                    <option value="256">+256 <small>(Uganda)</small></option>
                                    <option value="257">+257 <small>(Burundi)</small></option>
                                    <option value="258">+258 <small>(Mozambique)</small></option>
                                    <option value="260">+260 <small>(Zambia)</small></option>
                                    <option value="261">+261 <small>(Madagascar)</small></option>
                                    <option value="263">+263 <small>(Zimbabwe)</small></option>
                                    <option value="264">+264 <small>(Namibia)</small></option>
                                    <option value="265">+265 <small>(Malawi)</small></option>
                                    <option value="266">+266 <small>(Lesotho)</small></option>
                                    <option value="267">+267 <small>(Botswana)</small></option>
                                    <option value="268">+268 <small>(Eswatini)</small></option>
                                    <option value="269">+269 <small>(Comoros)</small></option>
                                    <option value="290">+290 <small>(Saint Helena)</small></option>
                                    <option value="291">+291 <small>(Eritrea)</small></option>
                                    <option value="297">+297 <small>(Aruba)</small></option>
                                    <option value="298">+298 <small>(Faroe Islands)</small></option>
                                    <option value="299">+299 <small>(Greenland)</small></option>
                                </select>
                                <input type="tel" name="phone_no1" id="address-phone-input" class="ax-phone-input" placeholder="<?= __('Enter phone number') ?>" required>
                                <span id="address-width-measure" class="ax-width-measure"></span>
                            </div>
                        </div>
                        <label id="address-phone-input-error" class="error" for="address-phone-input" style="display: none;"></label>
                    </div>
                </div>


                <div style="display: flex; justify-content: space-between; margin-top: 25px;">
                    <button type="button" id="save-address-btn" style="background-color: #FA9313; color: white; border: none; padding: 12px 20px; border-radius: 4px; cursor: pointer; flex: 1; margin-right: 10px; font-weight: 600; transition: all 0.3s ease;">
                        <i class="fa fa-save" style="margin-right: 5px;"></i> <?= __('Save Address') ?>
                    </button>
                    <button type="button" id="cancel-address-btn" style="background-color: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 4px; cursor: pointer; flex: 1; font-weight: 600; transition: all 0.3s ease;">
                        <i class="fa fa-times" style="margin-right: 5px;"></i> <?= __('Cancel') ?>
                    </button>
                </div>
            </form>
        </div>

        <!-- Confirmation Modal for Delete -->
        <div id="delete-confirm-modal" class="ax-popup" style="display: none; max-width: 90%; width: 350px; z-index: ********** !important; position: fixed !important; top: 50% !important; left: 50% !important; transform: translate(-50%, -50%) !important; border-radius: 8px; box-shadow: 0 5px 30px rgba(0,0,0,0.3) !important;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                <h3 style="font-size: 18px; font-weight: 600; color: #333; margin: 0;"><i class="fa fa-trash" style="margin-right: 8px; color: #dc3545;"></i>Confirm Delete</h3>
                <button id="close-delete-modal" style="background: none; border: none; font-size: 22px; cursor: pointer; padding: 0; margin: 0; line-height: 1; color: #999;">&times;</button>
            </div>
            <p style="margin-bottom: 25px; font-size: 15px; color: #555;"><?= __('Are you sure you want to delete this address? This action cannot be undone.') ?></p>
            <input type="hidden" id="delete_address_id" value="">
            <div style="display: flex; justify-content: flex-end;">
                <button type="button" id="confirm-delete-btn" style="background-color: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px; font-weight: 600; transition: all 0.3s ease;">
                    <i class="fa fa-trash" style="margin-right: 5px;"></i> <?= __('Delete') ?>
                </button>
                <button type="button" id="cancel-delete-btn" style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;">
                    <i class="fa fa-times" style="margin-right: 5px;"></i> <?= __('Cancel') ?>
                </button>
            </div>
        </div>

        <!-- END NEW FULL WIDTH ROW -->


        <!-- Credit Application Modal -->
        <div id="multistep-form-modal" style="display: none; position: fixed !important; top: 0 !important; left: 0 !important; width: 100vw !important; height: 100vh !important; background: rgba(0, 0, 0, 0.5); z-index: 99999999 !important; align-items: center; justify-content: center;">
            <div style="background: white; border-radius: 12px; max-width: 800px; width: 95%; max-height: 90vh; box-shadow: 0 10px 30px rgba(0,0,0,0.3); position: relative; margin: auto; display: flex; flex-direction: column; z-index: 99999999 !important;">
                
                <!-- Form Header -->
                <div class="form-header" style="padding: 15px 30px 12px; border-bottom: 1px solid #eee; background: linear-gradient(135deg, #FA9313 0%, #e8840f 100%); border-radius: 12px 12px 0 0; color: white;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="text-align: left;">
                            <h2 style="margin: 0; color: white; font-size: 20px; font-weight: 600;">
                                <i class="fa fa-credit-card" style="margin-right: 8px; font-size: 18px;"></i>
                                <?= __('Credit Application') ?>
                            </h2>
                            <p style="margin: 3px 0 0; color: rgba(255,255,255,0.9); font-size: 13px;"><?= __('Complete your purchase with flexible payment options') ?></p>
                        </div>
                        <div style="text-align: right;">
                            <div style="color: white; font-size: 16px; font-weight: 600; background: rgba(255,255,255,0.2); padding: 6px 12px; border-radius: 15px; display: inline-block;">
                                <?= __('Total') ?>: <span class="totalAmount"></span> FCFA
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step Progress Indicators -->
                    <div style="margin-top: 15px; text-align: center;">
                        <div style="display: flex; justify-content: center; align-items: center; gap: 10px;">
                            <div class="step-indicator" style="display: flex; align-items: center;">
                                <span class="step" style="display: inline-block; width: 32px; height: 32px; background-color: rgba(255,255,255,0.3); color: white; border-radius: 50%; line-height: 32px; text-align: center; font-weight: bold; transition: all 0.3s ease; border: 2px solid transparent; font-size: 13px;">1</span>
                                <span style="margin-left: 6px; font-size: 11px; color: rgba(255,255,255,0.9); font-weight: 500;"><?= __('Partner') ?></span>
                            </div>
                            <div class="step-divider" style="width: 30px; height: 2px; background: rgba(255,255,255,0.3); transition: all 0.3s ease;"></div>
                            <div class="step-indicator" style="display: flex; align-items: center;">
                                <span class="step" style="display: inline-block; width: 32px; height: 32px; background-color: rgba(255,255,255,0.3); color: white; border-radius: 50%; line-height: 32px; text-align: center; font-weight: bold; transition: all 0.3s ease; border: 2px solid transparent; font-size: 13px;">2</span>
                                <span style="margin-left: 6px; font-size: 11px; color: rgba(255,255,255,0.9); font-weight: 500;"><?= __('Plan') ?></span>
                            </div>
                            <div class="step-divider" style="width: 30px; height: 2px; background: rgba(255,255,255,0.3); transition: all 0.3s ease;"></div>
                            <div class="step-indicator" style="display: flex; align-items: center;">
                                <span class="step" style="display: inline-block; width: 32px; height: 32px; background-color: rgba(255,255,255,0.3); color: white; border-radius: 50%; line-height: 32px; text-align: center; font-weight: bold; transition: all 0.3s ease; border: 2px solid transparent; font-size: 13px;">3</span>
                                <span style="margin-left: 6px; font-size: 11px; color: rgba(255,255,255,0.9); font-weight: 500;"><?= __('Details') ?></span>
                            </div>
                            <div class="step-divider" style="width: 30px; height: 2px; background: rgba(255,255,255,0.3); transition: all 0.3s ease;"></div>
                            <div class="step-indicator" style="display: flex; align-items: center;">
                                <span class="step" style="display: inline-block; width: 32px; height: 32px; background-color: rgba(255,255,255,0.3); color: white; border-radius: 50%; line-height: 32px; text-align: center; font-weight: bold; transition: all 0.3s ease; border: 2px solid transparent; font-size: 13px;">4</span>
                                <span style="margin-left: 6px; font-size: 11px; color: rgba(255,255,255,0.9); font-weight: 500;"><?= __('Summary') ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Form Body -->
                <div class="form-body" style="padding: 0; flex-grow: 1; overflow: hidden; min-height: 400px; display: flex;">
                    <!-- Main Content Area -->
                    <div style="flex: 2; padding: 25px 30px; overflow-y: auto; max-height: 500px;">
                        <form id="myForm" action="/">
                        <!-- Step 1: Select Credit Partner -->
                        <div class="tab" style="display: none;">
                            <div style="margin-bottom: 25px;">
                                <h3 style="margin: 0 0 20px; color: #FA9313; font-size: 20px; font-weight: 600;">
                                    <i class="fa fa-handshake" style="margin-right: 10px;"></i>
                                    <?= __('Choose Your Credit Partner') ?>
                                </h3>
                                <p style="color: #666; margin-bottom: 25px;"><?= __('Select a trusted financial partner to proceed with your credit application') ?></p>

                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">
                                    <!-- Debug: Show credit partners count -->
                                    <script>
                                        console.log('PHP DEBUG: Total credit partners available:', <?= count($creditPartnersList) ?>);
                                    </script>
                                    <?php foreach ($creditPartnersList as $partner): ?>
                                    <!-- Partner 1 -->
                                    <div class="credit-partner-card" style="border: 2px solid #eee; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease; position: relative;">
                                        <input type="radio" name="credit_partner" value="<?= $partner->id ?>" data-json-term='<?= json_encode($partner->partner_payment_terms) ?>' id="partner<?= $partner->id ?>" style="position: absolute; top: 15px; right: 15px; width: 20px; height: 20px; accent-color: #FA9313;">
                                        <div style="text-align: center; margin-bottom: 15px;">
                                            <img src="/img/partner1-logo.png" alt="<?= $partner->name ?>" style="width: 80px; height: 60px; object-fit: contain; margin-bottom: 10px;" onerror="this.style.display='none';">
                                            <h4 style="margin: 0; color: #333; font-size: 18px; font-weight: 600;"><?= $partner->business_name ?></h4>
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="margin-bottom: 10px;">
                                                <span style="color: #FA9313; font-weight: 600;"><?= __('Interest Rate') ?>:</span>
                                                <?php if (!empty($partner->partner_payment_terms) && isset($partner->partner_payment_terms[0]->term_commission_percent)): ?>
                                                    <span style="color: #333;"><?= __('From') ?> <?= $partner->partner_payment_terms[0]->term_commission_percent ?>% APR</span>
                                                <?php else: ?>
                                                    <span style="color: #999;"><?= __('N/A') ?></span>
                                                <?php endif; ?>
                                            </div>
                                        
                                            <div style="margin-bottom: 10px;">
                                                <span style="color: #FA9313; font-weight: 600;"><?= __('Processing') ?>:</span>
                                                <span style="color: #333;"><?= __('12-24 hours') ?></span>
                                            </div>
                                            <div style="color: #28a745; font-size: 12px; font-weight: 500;">
                                                ✓ <?= __('Fast approval') ?> ✓ <?= __('Flexible terms') ?> ✓ <?= __('No hidden fees') ?>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Partner 1 End -->
                                    <?php endforeach; ?>

                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Choose EMI Plan -->
                        <div class="tab" style="display: none;">
                            <div style="margin-bottom: 25px;">
                                <h3 style="margin: 0 0 20px; color: #FA9313; font-size: 20px; font-weight: 600;">
                                    <i class="fa fa-calendar-alt" style="margin-right: 10px;"></i>
                                    Select Your Payment Plan
                                </h3>
                                <p style="color: #666; margin-bottom: 25px;">Choose a payment plan that suits your budget</p>
                                
                                <div id="emi-plans-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">
                                    <!-- 3 Months Plan -->
                                    <div class="emi-plan-card" style="border: 2px solid #eee; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease; position: relative; background: white;">
                                        <input type="radio" name="emi_plan" value="3months" id="plan3" style="position: absolute; top: 15px; right: 15px; width: 20px; height: 20px; accent-color: #FA9313;">
                                        <div class="emi-highlight" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 5px 10px; border-radius: 15px; font-size: 11px; font-weight: 600; position: absolute; top: -8px; right: 15px;">
                                            POPULAR
                                        </div>
                                        <div style="text-align: center; margin-bottom: 15px;">
                                            <h4 style="margin: 0; color: #333; font-size: 18px; font-weight: 600;">3 Months</h4>
                                            <p style="color: #666; font-size: 12px; margin: 5px 0;">Short-term financing</p>
                                        </div>
                                        <div style="text-align: center; margin-bottom: 15px;">
                                            <div style="font-size: 24px; font-weight: 700; color: #FA9313; margin-bottom: 5px;">
                                                <span class="monthly-amount-3">0</span> FCFA
                                            </div>
                                            <div style="font-size: 12px; color: #666;">per month</div>
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="margin-bottom: 8px; font-size: 14px;">
                                                <span style="color: #666;">Interest Rate:</span>
                                                <span style="color: #333; font-weight: 600;">8.5% APR</span>
                                            </div>
                                            <div style="margin-bottom: 8px; font-size: 14px;">
                                                <span style="color: #666;">Total Amount:</span>
                                                <span style="color: #333; font-weight: 600;"><span class="total-amount-3">0</span> FCFA</span>
                                            </div>
                                            <div style="color: #28a745; font-size: 12px; font-weight: 500; margin-top: 10px;">
                                                ✓ Quick payoff ✓ Lower total interest ✓ Build credit score
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Application Form -->
                        <div class="tab" style="display: none;">
                            <div style="margin-bottom: 25px;">
                                <h3 style="margin: 0 0 20px; color: #FA9313; font-size: 20px; font-weight: 600;">
                                    <i class="fa fa-user-edit" style="margin-right: 10px;"></i>
                                    <?= __('Application Form') ?>
                                </h3>
                                <p style="color: #666; margin-bottom: 25px;"><?= __('Fill in your details to complete the credit application') ?></p>

                                <div style="display: grid; gap: 25px;">
                                    <!-- Application Information -->
                                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                                        <h4 style="margin: 0 0 20px; color: #333; font-size: 18px; text-align: center;"><?= __('Application Information') ?></h4>
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                            <div>
                                                <label for="full_name" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 15px;"><?= __('Full Name') ?></label>
                                                <input type="text" id="full_name" value="<?= $users->first_name.' '.$users->last_name ?>" name="full_name"  placeholder="<?= __('Enter your full name') ?>" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 15px; transition: border-color 0.3s ease;" required>
                                            </div>
                                            <div>
                                                <label for="email" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 15px;"><?= __('Email Address') ?></label>
                                                <input type="email" id="email" value="<?= $users->email ?>" name="email" placeholder="<?= __('Enter your email address') ?>" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 15px; transition: border-color 0.3s ease;" required>
                                            </div>
                                            <div>
                                                <label for="phone" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 15px;"><?= __('Phone Number') ?></label>
                                                <div class="ax-field-wrapper">
                                                    <div class="ax-input-box mobile-input-wrapper">
                                                        <select id="multistep-country-select" name="country_code" class="ax-country-select searchable-select country-code-dropdown" style="padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 15px; transition: border-color 0.3s ease; background: white; min-width: 120px;" required>
                                                                   <?php 
                            $countryCodes = [
                                "1" => "+1 <small>(USA, Canada)</small>",
                                "7" => "+7 <small>(Russia, Kazakhstan)</small>",
                                "20" => "+20 <small>(Egypt)</small>",
                                "27" => "+27 <small>(South Africa)</small>",
                                "30" => "+30 <small>(Greece)</small>",
                                "31" => "+31 <small>(Netherlands)</small>",
                                "32" => "+32 <small>(Belgium)</small>",
                                "33" => "+33 <small>(France)</small>",
                                "34" => "+34 <small>(Spain)</small>",
                                "36" => "+36 <small>(Hungary)</small>",
                                "39" => "+39 <small>(Italy)</small>",
                                "40" => "+40 <small>(Romania)</small>",
                                "41" => "+41 <small>(Switzerland)</small>",
                                "43" => "+43 <small>(Austria)</small>",
                                "44" => "+44 <small>(United Kingdom)</small>",
                                "45" => "+45 <small>(Denmark)</small>",
                                "46" => "+46 <small>(Sweden)</small>",
                                "47" => "+47 <small>(Norway)</small>",
                                "48" => "+48 <small>(Poland)</small>",
                                "49" => "+49 <small>(Germany)</small>",
                                "51" => "+51 <small>(Peru)</small>",
                                "52" => "+52 <small>(Mexico)</small>",
                                "53" => "+53 <small>(Cuba)</small>",
                                "54" => "+54 <small>(Argentina)</small>",
                                "55" => "+55 <small>(Brazil)</small>",
                                "56" => "+56 <small>(Chile)</small>",
                                "57" => "+57 <small>(Colombia)</small>",
                                "58" => "+58 <small>(Venezuela)</small>",
                                "60" => "+60 <small>(Malaysia)</small>",
                                "61" => "+61 <small>(Australia)</small>",
                                "62" => "+62 <small>(Indonesia)</small>",
                                "63" => "+63 <small>(Philippines)</small>",
                                "64" => "+64 <small>(New Zealand)</small>",
                                "65" => "+65 <small>(Singapore)</small>",
                                "66" => "+66 <small>(Thailand)</small>",
                                "81" => "+81 <small>(Japan)</small>",
                                "82" => "+82 <small>(South Korea)</small>",
                                "84" => "+84 <small>(Vietnam)</small>",
                                "86" => "+86 <small>(China)</small>",
                                "90" => "+90 <small>(Turkey)</small>",
                                "91" => "+91 <small>(India)</small>",
                                "92" => "+92 <small>(Pakistan)</small>",
                                "93" => "+93 <small>(Afghanistan)</small>",
                                "94" => "+94 <small>(Sri Lanka)</small>",
                                "95" => "+95 <small>(Myanmar)</small>",
                                "98" => "+98 <small>(Iran)</small>",
                                "211" => "+211 <small>(South Sudan)</small>",
                                "212" => "+212 <small>(Morocco)</small>",
                                "213" => "+213 <small>(Algeria)</small>",
                                "216" => "+216 <small>(Tunisia)</small>",
                                "218" => "+218 <small>(Libya)</small>",
                                "220" => "+220 <small>(Gambia)</small>",
                                "221" => "+221 <small>(Senegal)</small>",
                                "222" => "+222 <small>(Mauritania)</small>",
                                "223" => "+223 <small>(Mali)</small>",
                                "224" => "+224 <small>(Guinea)</small>",
                                "225" => "+225 <small>(Ivory Coast)</small>",
                                "226" => "+226 <small>(Burkina Faso)</small>",
                                "227" => "+227 <small>(Niger)</small>",
                                "228" => "+228 <small>(Togo)</small>",
                                "229" => "+229 <small>(Benin)</small>",
                                "230" => "+230 <small>(Mauritius)</small>",
                                "231" => "+231 <small>(Liberia)</small>",
                                "232" => "+232 <small>(Sierra Leone)</small>",
                                "233" => "+233 <small>(Ghana)</small>",
                                "234" => "+234 <small>(Nigeria)</small>",
                                "235" => "+235 <small>(Chad)</small>",
                                "236" => "+236 <small>(Central African Republic)</small>",
                                "237" => "+237 <small>(Cameroon)</small>",
                                "238" => "+238 <small>(Cape Verde)</small>",
                                "239" => "+239 <small>(Sao Tome and Principe)</small>",
                                "240" => "+240 <small>(Equatorial Guinea)</small>",
                                "241" => "+241 <small>(Gabon)</small>",
                                "242" => "+242 <small>(Congo - Brazzaville)</small>",
                                "243" => "+243 <small>(Congo - Kinshasa)</small>",
                                "244" => "+244 <small>(Angola)</small>",
                                "245" => "+245 <small>(Guinea-Bissau)</small>",
                                "246" => "+246 <small>(British Indian Ocean Territory)</small>",
                                "248" => "+248 <small>(Seychelles)</small>",
                                "249" => "+249 <small>(Sudan)</small>",
                                "250" => "+250 <small>(Rwanda)</small>",
                                "251" => "+251 <small>(Ethiopia)</small>",
                                "252" => "+252 <small>(Somalia)</small>",
                                "253" => "+253 <small>(Djibouti)</small>",
                                "254" => "+254 <small>(Kenya)</small>",
                                "255" => "+255 <small>(Tanzania)</small>",
                                "256" => "+256 <small>(Uganda)</small>",
                                "257" => "+257 <small>(Burundi)</small>",
                                "258" => "+258 <small>(Mozambique)</small>",
                                "260" => "+260 <small>(Zambia)</small>",
                                "261" => "+261 <small>(Madagascar)</small>",
                                "262" => "+262 <small>(Réunion, Mayotte)</small>",
                                "263" => "+263 <small>(Zimbabwe)</small>",
                                "264" => "+264 <small>(Namibia)</small>",
                                "265" => "+265 <small>(Malawi)</small>",
                                "266" => "+266 <small>(Lesotho)</small>",
                                "267" => "+267 <small>(Botswana)</small>",
                                "268" => "+268 <small>(Eswatini)</small>",
                                "269" => "+269 <small>(Comoros)</small>",
                                "290" => "+290 <small>(Saint Helena)</small>",
                                "291" => "+291 <small>(Eritrea)</small>",
                                "297" => "+297 <small>(Aruba)</small>",
                                "298" => "+298 <small>(Faroe Islands)</small>",
                                "299" => "+299 <small>(Greenland)</small>"
                            ];
                            foreach ($countryCodes as $code => $label) {
                                $selected = ($users->country_code == $code) ? "selected" : "";
                                echo "<option value='$code' $selected>$label</option>";
                            }
                            ?></select>
                                                        <input type="tel" id="multistep-phone-input" value="<?= $users->mobile_no ?>" name="phone" class="ax-phone-input" style="min-width:auto !important;" placeholder="<?= __('Enter phone number') ?>" style="flex: 1; padding: 12px; font-size: 15px; transition: border-color 0.3s ease;" required>
                                                        <span id="multistep-width-measure" class="ax-width-measure"></span>
                                                    </div>
                                                </div>
                                                <label id="multistep-phone-input-error" class="error" for="multistep-phone-input" style="display: none;"></label>
                                            </div>
                                            <div>
                                                <label for="registration_id" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 15px;"><?= __('Registration ID') ?></label>
                                                <input type="text" id="registration_id" value="<?= isset($users->employee_id) ? $users->employee_id : (isset($users->reg_id) ? $users->reg_id : '') ?>" name="registration_id" placeholder="<?= __('Enter your registration ID') ?>" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 15px; transition: border-color 0.3s ease;" required>
                                            </div>
                                        </div>
                                        <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-left: 4px solid #28a745; border-radius: 6px;">
                                            <p style="margin: 0; color: #155724; font-size: 14px; display: flex; align-items: center;">
                                                <i class="fa fa-info-circle" style="margin-right: 8px; color: #28a745;"></i>
                                                <?= __('Please ensure all information is accurate to avoid delays in processing your application.') ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Summary & Confirmation -->
                        <div class="tab" style="display: none;">
                            <div style="margin-bottom: 25px;">
                                <h3 style="margin: 0 0 20px; color: #FA9313; font-size: 20px; font-weight: 600;">
                                    <i class="fa fa-check-circle" style="margin-right: 10px;"></i>
                                    Application Summary
                                </h3>
                                <p style="color: #666; margin-bottom: 25px;">Review your cart items before final submission</p>
                                
                                <div style="display: grid; gap: 20px;">

                                <!-- Cart Items Table -->
                                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
                                    <h4 style="margin: 0 0 15px; color: #333; font-size: 16px; font-weight: 600; display: flex; align-items: center;">
                                        <i class="fa fa-shopping-cart" style="margin-right: 8px; color: #FA9313;"></i>
                                        <?= __('Cart Items') ?> (<?= $total_items ?>)
                                    </h4>

                                    <div class="cart-items-table-container">
                                        <table class="cart-items-table">
                                            <thead>
                                                <tr>
                                                    <th style="text-align: left; min-width: 60px;">
                                                        <?= __('Image') ?>
                                                    </th>
                                                    <th style="text-align: left; min-width: 200px;">
                                                        <?= __('Product Name') ?>
                                                    </th>
                                                    <th style="text-align: center; min-width: 60px;">
                                                        <?= __('Qty') ?>
                                                    </th>
                                                    <th style="text-align: right; min-width: 100px;">
                                                        <?= __('Unit Price') ?>
                                                    </th>
                                                    <th class="hide-mobile" style="text-align: left; min-width: 120px;">
                                                        <?= __('Reference') ?>
                                                    </th>
                                                    <th style="text-align: center; min-width: 80px;">
                                                        <?= __('EMI') ?>
                                                    </th>
                                                    <th style="text-align: right; min-width: 100px;">
                                                        <?= __('Total Price') ?>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($cartItems as $index => $item): ?>
                                                    <tr>
                                                        <!-- Product Image -->
                                                        <td style="text-align: center;">
                                                            <img src="<?= $item['product_image'] ?>"
                                                                 alt="<?= h($item['product_name']) ?>"
                                                                 style="width: 45px; height: 45px; object-fit: cover; border-radius: 4px; border: 1px solid #e9ecef;">
                                                        </td>

                                                        <!-- Product Name -->
                                                        <td>
                                                            <div style="font-weight: 600; color: #333; font-size: 13px; line-height: 1.3; margin-bottom: 4px;">
                                                                <?= h($item['product_name']) ?>
                                                            </div>
                                                            <?php if (!empty($item['sale']) && $item['sale'] != $item['price']): ?>
                                                                <div style="font-size: 11px; color: #666;">
                                                                    <span style="text-decoration: line-through;"><?= $this->Price->setPriceFormat($item['sale']) ?></span>
                                                                    <?php if (!empty($item['discount'])): ?>
                                                                        <span style="color: #28a745; font-weight: 600; margin-left: 5px;"><?= $item['discount'] ?>% <?= __('OFF') ?></span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </td>

                                                        <!-- Quantity -->
                                                        <td style="text-align: center; font-weight: 600; color: #333; font-size: 14px;">
                                                            <?= $item['quantity'] ?>
                                                        </td>

                                                        <!-- Unit Price -->
                                                        <td style="text-align: right; font-weight: 600; color: #333; font-size: 13px;">
                                                            <?= $this->Price->setPriceFormat($item['price']) ?>
                                                        </td>

                                                        <!-- Reference -->
                                                        <td class="hide-mobile" style="font-size: 12px; color: #666;">
                                                            <?= !empty($item['reference_name']) ? h($item['reference_name']) : '-' ?>
                                                        </td>

                                                        <!-- EMI Availability -->
                                                        <td style="text-align: center; font-size: 11px;">
                                                            <?php if ($item['avl_on_credit'] == 1): ?>
                                                                <span style="color: #28a745; display: inline-flex; align-items: center; gap: 3px;">
                                                                    <i class="fas fa-check-circle"></i>
                                                                    <?= __('Yes') ?>
                                                                </span>
                                                            <?php else: ?>
                                                                <span style="color: #dc3545; display: inline-flex; align-items: center; gap: 3px;">
                                                                    <i class="fas fa-times-circle"></i>
                                                                    <?= __('No') ?>
                                                                </span>
                                                            <?php endif; ?>
                                                        </td>

                                                        <!-- Total Price -->
                                                        <td style="text-align: right; font-weight: 600; color: #FA9313; font-size: 14px;">
                                                            <?= $this->Price->setPriceFormat($item['price'] * $item['quantity']) ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Cart Summary -->
                                    <div style="border-top: 2px solid #FA9313; padding-top: 15px; margin-top: 15px; background: white; padding: 15px; border-radius: 6px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; font-weight: 600; color: #333; font-size: 16px;">
                                            <span><?= __('Total Amount') ?>:</span>
                                            <span style="color: #FA9313; font-size: 18px;"><?= $this->Price->setPriceFormat($totalPrice) ?></span>
                                        </div>
                                    </div>
                                </div>

                                    <!-- Terms Agreement -->
                                    <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border: 1px solid #ffeaa7;">
                                        <h4 style="margin: 0 0 15px; color: #856404; font-size: 16px; font-weight: 600; display: flex; align-items: center;">
                                            <i class="fa fa-exclamation-triangle" style="margin-right: 8px;"></i>
                                            Terms & Conditions
                                        </h4>
                                        
                                        <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                            <input type="checkbox" id="terms_agreement" name="terms_agreement" style="margin-right: 10px; width: 18px; height: 18px;" required>
                                            <label for="terms_agreement" style="color: #333; font-weight: 500;">I agree to the terms and conditions and authorize credit processing for the above cart items *</label>
                                        </div>
                                        <div style="font-size: 12px; color: #666; line-height: 1.5;">
                                            By checking this box, I confirm that I want to purchase the items listed above through credit financing. I understand that this application is subject to credit approval and I authorize the selected credit partner to process my application.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
                
                <!-- Form Footer -->
                <div class="form-footer" style="padding: 15px 30px; border-top: 1px solid #eee; background: #f8f9fa; border-radius: 0 0 12px 12px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <!-- Left side: Previous button -->
                        <div style="flex: 1; display: flex; justify-content: flex-start;">
                            <button type="button" class="multistep-previous" style="background-color: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; display: none; align-items: center; font-size: 14px;">
                                <i class="fa fa-arrow-left" style="margin-right: 8px;"></i>
                                Previous
                            </button>
                        </div>
                        
                        <!-- Center: Step info -->
                        <div style="flex: 1; display: flex; justify-content: center; align-items: center;">
                            <span id="step-info" style="color: #666; font-size: 14px; font-weight: 500;">Step 1 of 4</span>
                        </div>
                        
                        <!-- Right side: Next/Submit button -->
                        <div style="flex: 1; display: flex; justify-content: flex-end;">
                            <button type="button" class="multistep-next" style="background-color: #FA9313; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; display: inline-flex; align-items: center; font-size: 14px;">
                                Next
                                <i class="fa fa-arrow-right" style="margin-left: 8px;"></i>
                            </button>
                            <button type="submit" class="multistep-submit" style="background-color: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; display: none; align-items: center; font-size: 14px;">
                                <i class="fa fa-paper-plane" style="margin-right: 8px;"></i>
                                Submit Application
                            </button>

                            <!-- Debug button for testing -->
                            <button type="button" id="debug-form-data" style="background-color: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-left: 10px; display: none;">
                                Debug Form Data
                            </button>

                            <!-- Test button for credit partner selection -->
                            <button type="button" id="test-credit-partner" style="background-color: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-left: 10px; display: none;">
                                Test Select Partner
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>





</div>
</div>

<?php $this->start('add_js'); ?>
<script>
  // Multi-Step Form Modal Management
  let multiStepFormInstance = null;
  
  // Open the multi-step form modal
  $(document).on('click', '#open-multistep-form-btn', function() {
    $('#multistep-form-modal').addClass('show').css('display', 'flex');
    $('body').addClass('modal-open');
    
    // Force all other elements behind the modal
    $('header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky').css({
      'z-index': '1 !important',
      'position': 'relative !important'
    });
    
    // Add a style tag to force all elements behind the modal
    if ($('#force-multistep-behind').length === 0) {
      $('head').append('<style id="force-multistep-behind">header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky, .checkout-order-summary, .check-out-container > * { z-index: 1 !important; } #multistep-form-modal { z-index: 99999999 !important; position: fixed !important; }</style>');
    }
    
    // Reset the form every time it's opened
    resetMultiStepForm();
    
    // Initialize the multi-step form if not already done
    if (!multiStepFormInstance) {
      multiStepFormInstance = $("#myForm").multiStepForm({
        beforeSubmit: function(form) {
          // final validation or data prep here
          console.log("Ready to submit:", form);
          // You can add form submission logic here
          // For now, we'll just close the modal on successful submission
          $('#multistep-form-modal').removeClass('show').fadeOut(300);
          $('body').removeClass('modal-open');
          return false; // Prevent actual form submission for demo
        }
      }).navigateTo(0); // start at the first step
    } else {
      // Reset to first step if form was already initialized
      multiStepFormInstance.navigateTo(0);
    }
  });
  
  // Close modal when clicking outside
  $(document).on('click', '#multistep-form-modal', function(e) {
    if (e.target === this) {
      $('#multistep-form-modal').removeClass('show').fadeOut(300);
      $('body').removeClass('modal-open');
      
      // Remove the force styling and restore normal z-index
      $('#force-multistep-behind').remove();
      $('header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky').css({
        'position': 'relative'
      });
      
      // Reset the form when closing
      resetMultiStepForm();
    }
  });
  
  // Prevent closing when clicking inside the modal content
  $(document).on('click', '#multistep-form-modal > div', function(e) {
    e.stopPropagation();
  });
  
  // Custom multi-step form functionality
  let currentStep = 0;
  const totalSteps = 4;
  
  // Reset form to initial state
  function resetMultiStepForm() {
    // Reset current step to 0
    currentStep = 0;
    
    // Clear form inputs except user data fields (name, email, phone, country code, registration ID)
    $('#myForm input').not('#full_name, #email, #multistep-phone-input, #registration_id').val('');
    $('#myForm select').not('#multistep-country-select').val('');
    
    // Remove any validation classes and states
    $('#myForm input').removeClass('invalid valid');
    $('#myForm select').removeClass('invalid valid');
    
    // Remove all error messages
    $('.field-error').remove();
    
    // Clear card selections
    $('.credit-partner-card, .emi-plan-card').removeClass('selected');
    
    // Uncheck all radio buttons
    $('#myForm input[type="radio"]').prop('checked', false);
    
    // Reset step indicators and button states
    updateStepIndicators();
    updateButtonStates();
    
    // Show first tab, hide others
    $('.tab').hide();
    $('.tab').eq(0).show();
    
    console.log('Multi-step form has been reset (preserving user data)');
  }
  
  // Initialize custom multi-step form
  function initializeCustomMultiStepForm() {
    // Hide all tabs initially
    $('.tab').hide();
    
    // Show first tab
    $('.tab').eq(0).show();
    
    // Update step indicators
    updateStepIndicators();
    
    // Update button states
    updateButtonStates();
  }
  
  // Update step indicators
  function updateStepIndicators() {
    $('.step').each(function(index) {
      $(this).removeClass('active completed');
      if (index < currentStep) {
        $(this).addClass('completed');
      } else if (index === currentStep) {
        $(this).addClass('active');
      }
    });
    
    // Update step dividers
    $('.step-divider').each(function(index) {
      if (index < currentStep) {
        $(this).css('background', '#28a745');
      } else {
        $(this).css('background', '#ddd');
      }
    });
    
    // Update step info in footer
    $('#step-info').text(`Step ${currentStep + 1} of ${totalSteps}`);
  }
  
  // Update button states
  function updateButtonStates() {
    // Show/hide previous button
    if (currentStep === 0) {
      $('.multistep-previous').hide();
    } else {
      $('.multistep-previous').css('display', 'inline-flex').show();
    }
    
    // Show/hide next and submit buttons
    if (currentStep === totalSteps - 1) {
      $('.multistep-next').hide();
      $('.multistep-submit').css('display', 'inline-flex').show();
      $('#debug-form-data').css('display', 'inline-flex').show(); // Show debug button on final step
      $('#test-credit-partner').css('display', 'inline-flex').show(); // Show test button on final step
    } else {
      $('.multistep-next').css('display', 'inline-flex').show();
      $('.multistep-submit').hide();
      $('#debug-form-data').hide(); // Hide debug button on other steps
      $('#test-credit-partner').hide(); // Hide test button on other steps
    }
  }
  
  // Go to next step
  function nextStep() {
    if (currentStep < totalSteps - 1) {
      // Hide current tab
      $('.tab').eq(currentStep).hide();
      
      // Move to next step
      currentStep++;
      
      // Show next tab
      $('.tab').eq(currentStep).show();
      
      // Update indicators and buttons
      updateStepIndicators();
      updateButtonStates();
      
      // If moving to summary step, update summary details
      if (currentStep === 3) { // Step 4 (0-indexed)
        updateSummaryStep();
      }
    }
  }

  // Add step validation function
  function validateCurrentStep() {
      const currentTab = $('.tab').eq(currentStep);
      
      // Step 1: Validate credit partner selection
      if (currentStep === 0) {
          if (!$('input[name="credit_partner"]:checked').length) {
              toastr.error('Please select a credit partner');
              return false;
          }
      }
      
      // Step 2: Validate EMI plan selection
      if (currentStep === 1) {
          if (!$('input[name="emi_plan"]:checked').length) {
              toastr.error('Please select a payment plan');
              return false;
          }
      }
      
      // Step 3: Validate form fields with detailed validation
      if (currentStep === 2) {
          let isValid = true;
          let errorMessages = [];
          
          // Clear previous validation states
          currentTab.find('input').removeClass('invalid valid');
          currentTab.find('.field-error').remove();
          
          // Validate Full Name
          const fullName = $('#full_name').val().trim();
          if (!fullName) {
              $('#full_name').addClass('invalid');
              showFieldError('#full_name', 'Full name is required');
              isValid = false;
          } else if (fullName.length < 2) {
              $('#full_name').addClass('invalid');
              showFieldError('#full_name', 'Full name must be at least 2 characters');
              isValid = false;
          } else if (!/^[a-zA-Z\s\-'\.]+$/.test(fullName)) {
              $('#full_name').addClass('invalid');
              showFieldError('#full_name', 'Full name contains invalid characters');
              isValid = false;
          } else {
              $('#full_name').addClass('valid');
          }
          
          // Validate Email
          const email = $('#email').val().trim();
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!email) {
              $('#email').addClass('invalid');
              showFieldError('#email', 'Email address is required');
              isValid = false;
          } else if (!emailRegex.test(email)) {
              $('#email').addClass('invalid');
              showFieldError('#email', 'Please enter a valid email address');
              isValid = false;
          } else {
              $('#email').addClass('valid');
          }
          
          // Validate Phone Number and Country Code
          const countryCode = $('#multistep-country-select').val();
          const phone = $('#multistep-phone-input').val().trim();
          const phoneRegex = /^[\d\s\-\(\)]{8,15}$/;
          const wrapper = $('#multistep-phone-input').closest('.mobile-input-wrapper');
          
          // Clear previous states
          $('#multistep-country-select').removeClass('invalid valid');
          $('#multistep-phone-input').removeClass('invalid valid');
          wrapper.removeClass('invalid valid');
          
          if (!countryCode) {
              $('#multistep-country-select').addClass('invalid');
              wrapper.addClass('invalid');
              showFieldError('#multistep-country-select', 'Country code is required');
              isValid = false;
          } else {
              $('#multistep-country-select').addClass('valid');
          }
          
          if (!phone) {
              $('#multistep-phone-input').addClass('invalid');
              wrapper.addClass('invalid');
              showFieldError('#multistep-phone-input', 'Phone number is required');
              isValid = false;
          } else if (!phoneRegex.test(phone)) {
              $('#multistep-phone-input').addClass('invalid');
              wrapper.addClass('invalid');
              showFieldError('#multistep-phone-input', 'Please enter a valid phone number (8-15 digits)');
              isValid = false;
          } else {
              $('#multistep-phone-input').addClass('valid');
          }
          
          // Set wrapper state based on both fields
          if (countryCode && phone && phoneRegex.test(phone)) {
              wrapper.addClass('valid');
          }
          
          // Validate Registration ID
          const registrationId = $('#registration_id').val().trim();
          if (!registrationId) {
              $('#registration_id').addClass('invalid');
              showFieldError('#registration_id', 'Registration ID is required');
              isValid = false;
          } else if (registrationId.length < 3) {
              $('#registration_id').addClass('invalid');
              showFieldError('#registration_id', 'Registration ID must be at least 3 characters');
              isValid = false;
          } else if (!/^[a-zA-Z0-9\-_]+$/.test(registrationId)) {
              $('#registration_id').addClass('invalid');
              showFieldError('#registration_id', 'Registration ID can only contain letters, numbers, hyphens, and underscores');
              isValid = false;
          } else {
              $('#registration_id').addClass('valid');
          }
          
          if (!isValid) {
              showValidationSummary();
              return false;
          }
      }
      
      // Step 4: Validate terms agreement
      if (currentStep === 3) {
          if (!$('#terms_agreement').is(':checked')) {
              toastr.error('Please agree to the terms and conditions');
              return false;
          }
      }
      
      return true;
  }
  
  // Helper function to show field-specific errors
  function showFieldError(fieldSelector, message) {
      const field = $(fieldSelector);
      const errorElement = $('<div class="field-error" style="color: #dc3545; font-size: 12px; margin-top: 5px; display: flex; align-items: center;"><i class="fa fa-exclamation-circle" style="margin-right: 5px;"></i>' + message + '</div>');
      
      // Special handling for phone input within mobile wrapper
      if (fieldSelector === '#multistep-phone-input' || fieldSelector === '#multistep-country-select') {
          // Remove existing error for phone fields
          field.closest('.ax-field-wrapper').find('.field-error').remove();
          $('#multistep-phone-input-error').hide();
          
          // Add new error message after the mobile input wrapper
          field.closest('.ax-field-wrapper').append(errorElement);
      } else {
          // Remove existing error for this field
          field.closest('div').find('.field-error').remove();
          
          // Add new error message
          field.closest('div').append(errorElement);
      }
  }
  
  // Real-time validation functions
  function setupRealTimeValidation() {
      // Real-time validation for Full Name
      $('#full_name').on('input blur', function() {
          const value = $(this).val().trim();
          const field = $(this);
          
          field.removeClass('invalid valid');
          field.closest('div').find('.field-error').remove();
          
          if (value && value.length >= 2 && /^[a-zA-Z\s\-'\.]+$/.test(value)) {
              field.addClass('valid');
          } else if (value) {
              field.addClass('invalid');
              if (value.length < 2) {
                  showFieldError('#full_name', 'Full name must be at least 2 characters');
              } else if (!/^[a-zA-Z\s\-'\.]+$/.test(value)) {
                  showFieldError('#full_name', 'Full name contains invalid characters');
              }
          }
      });
      
      // Real-time validation for Email
      $('#email').on('input blur', function() {
          const value = $(this).val().trim();
          const field = $(this);
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          
          field.removeClass('invalid valid');
          field.closest('div').find('.field-error').remove();
          
          if (value && emailRegex.test(value)) {
              field.addClass('valid');
          } else if (value) {
              field.addClass('invalid');
              showFieldError('#email', 'Please enter a valid email address');
          }
      });
      
      // Real-time validation for Country Code
      $('#multistep-country-select').on('change', function() {
          const field = $(this);
          const wrapper = field.closest('.mobile-input-wrapper');
          
          field.removeClass('invalid valid');
          wrapper.removeClass('invalid valid');
          field.closest('.ax-field-wrapper').find('.field-error').remove();
          $('#multistep-phone-input-error').hide();
          
          if ($(this).val()) {
              field.addClass('valid');
              wrapper.addClass('valid');
          } else {
              field.addClass('invalid');
              wrapper.addClass('invalid');
              showFieldError('#multistep-country-select', 'Country code is required');
          }
      });
      
      // Real-time validation for Phone
      $('#multistep-phone-input').on('input blur', function() {
          const value = $(this).val().trim();
          const field = $(this);
          const wrapper = field.closest('.mobile-input-wrapper');
          const phoneRegex = /^[\d\s\-\(\)]{8,15}$/;
          
          field.removeClass('invalid valid');
          wrapper.removeClass('invalid valid');
          field.closest('.ax-field-wrapper').find('.field-error').remove();
          $('#multistep-phone-input-error').hide();
          
          if (value && phoneRegex.test(value)) {
              field.addClass('valid');
              wrapper.addClass('valid');
          } else if (value) {
              field.addClass('invalid');
              wrapper.addClass('invalid');
              showFieldError('#multistep-phone-input', 'Please enter a valid phone number (8-15 digits)');
          }
      });
      
      // Real-time validation for Registration ID
      $('#registration_id').on('input blur', function() {
          const value = $(this).val().trim();
          const field = $(this);
          
          field.removeClass('invalid valid');
          field.closest('div').find('.field-error').remove();
          
          if (value && value.length >= 3 && /^[a-zA-Z0-9\-_]+$/.test(value)) {
              field.addClass('valid');
          } else if (value) {
              field.addClass('invalid');
              if (value.length < 3) {
                  showFieldError('#registration_id', 'Registration ID must be at least 3 characters');
              } else if (!/^[a-zA-Z0-9\-_]+$/.test(value)) {
                  showFieldError('#registration_id', 'Registration ID can only contain letters, numbers, hyphens, and underscores');
              }
          }
      });
      
      // Format phone number as user types
      $('#multistep-phone-input').on('input', function() {
          let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
          
          // Add formatting for common phone number patterns
          if (value.length >= 3) {
              if (value.length <= 3) {
                  value = value;
              } else if (value.length <= 6) {
                  value = value.slice(0, 3) + ' ' + value.slice(3);
              } else if (value.length <= 9) {
                  value = value.slice(0, 3) + ' ' + value.slice(3, 6) + ' ' + value.slice(6);
              } else {
                  value = value.slice(0, 3) + ' ' + value.slice(3, 6) + ' ' + value.slice(6, 10);
              }
          }
          
          $(this).val(value);
      });
  }
  
  // Scroll to first invalid field and focus it
  function scrollToFirstError() {
      const firstInvalidField = $('#myForm input.invalid').first();
      if (firstInvalidField.length) {
          // Scroll the modal content to show the field
          const modalContent = firstInvalidField.closest('.form-body');
          if (modalContent.length) {
              const fieldOffset = firstInvalidField.offset().top - modalContent.offset().top;
              modalContent.scrollTop(modalContent.scrollTop() + fieldOffset - 100);
          }
          
          // Focus the field after a short delay
          setTimeout(() => {
              firstInvalidField.focus();
          }, 300);
      }
  }
  
  // Enhanced validation with better user experience
  function validateFieldsWithFeedback() {
      // Clear any existing validation states
      $('#myForm input').removeClass('invalid valid');
      $('.field-error').remove();
      
      // Run validation
      const isValid = validateCurrentStep();
      
      // If validation failed, scroll to first error
      if (!isValid && currentStep === 2) {
          setTimeout(scrollToFirstError, 100);
      }
      
      return isValid;
  }
  
  // Go to previous step
  function previousStep() {
    if (currentStep > 0) {
      // Hide current tab
      $('.tab').eq(currentStep).hide();
      
      // Move to previous step
      currentStep--;
      
      // Show previous tab
      $('.tab').eq(currentStep).show();
      
      // Update indicators and buttons
      updateStepIndicators();
      updateButtonStates();
    }
  }
  
  // Add form validation before step progression
  $(document).on('click', '.multistep-next', function(e) {
    e.preventDefault();
    
    // Use enhanced validation with user feedback
    if (!validateFieldsWithFeedback()) {
        return false;
    }
    
    nextStep();
  });
  
  // Handle previous button click
  $(document).on('click', '.multistep-previous', function(e) {
    e.preventDefault();
    previousStep();
  });
  
  // Handle submit button click
  $(document).on('click', '.multistep-submit', function(e) {
    e.preventDefault();
    
    // Final validation before submission
    if (!validateFieldsWithFeedback()) {
        return false;
    }
    
    // Check terms agreement specifically
    if (!$('#terms_agreement').is(':checked')) {
        toastr.error('Please agree to the terms and conditions before submitting', 'Terms Required', {
            timeOut: 5000
        });
        $('#terms_agreement').focus();
        return false;
    }
    
    // Show loading state
    $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin" style="margin-right: 8px;"></i>Submitting...');
    
    // Submit credit application and create order
    submitCreditApplicationAndCreateOrder();
  });
  
  // Initialize when modal is opened
  $(document).on('click', '#apply-credit-button', function() {
    
   // Select the checked radio button
        const checkedRadio = $('input[name="method"]:checked');

        if (!checkedRadio.length) { 
            toastr.warning('Please select a payment method', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }
        let delivery_option = $('input[name="delivery_option"]:checked').val();
        if(delivery_option == 'delivery'){
            if($('input[name="address"]:checked').length == 0){
                toastr.warning('Please select a delivery address', '', {
                timeOut: 3000,
                progressBar: true
            });
                return;
            }
        } else if(delivery_option == 'pickup'){
            if($('input[name="showroom"]:checked').length == 0){
                toastr.warning('<?= __('Please select a showroom to pickup from.') ?>', '', {
                timeOut: 3000,
                progressBar: true
            });
                return;
            }
        }
        
    $('#multistep-form-modal').addClass('show');
    $('body').addClass('loyalty-popup-open');

    setTimeout(function() {
      // Form is already reset in the main click handler
      // Just ensure initialization is complete
      initializeCustomMultiStepForm();
      // Setup real-time validation
      setupRealTimeValidation();
      // Initialize select2 for multistep form country select
      initializeMultistepCountrySelect();
      // Calculate EMI amounts based on current total
      calculateEMIAmounts();
    }, 100);
  });
  
  // Function to initialize multistep form country select
  function initializeMultistepCountrySelect() {
      // Destroy existing select2 if it exists
    //   if ($('#multistep-country-select').data('select2')) {
    //       $('#multistep-country-select').select2('destroy');
    //   }
      
      // Get the current selected value before initializing Select2
      const currentValue = $('#multistep-country-select').val();
      
      // Initialize select2
    //   $('#multistep-country-select').select2({
    //       dropdownParent: $('#multistep-form-modal'),
    //       minimumResultsForSearch: 0,
    //       width: 'element'
    //   });
      
      // Preserve the existing selected value or set default to Cameroon
      const valueToSet = currentValue || '237';
      $('#multistep-country-select').val(valueToSet).trigger('change.select2');
  }
  
  // Function to calculate EMI amounts
  function calculateEMIAmounts() {
    // Get total amount - use PHP total as primary source
    let totalAmount = <?= $totalPrice ?>;
    
    // Check if we have a calculated final amount in session storage
    let finalAmount = parseFloat(sessionStorage.getItem('final_amount'));
    if (!isNaN(finalAmount) && finalAmount > 0) {
        totalAmount = finalAmount;
    }
    
    // If totalAmount is still invalid, use the PHP total price as fallback
    if (isNaN(totalAmount) || totalAmount <= 0) {
        totalAmount = <?= $totalPrice ?>;
        console.log('Using fallback total amount:', totalAmount);
    }
    
    console.log('Calculating EMI amounts for total:', totalAmount);
    
    // Calculate for 3 months
    let amount3 = totalAmount;
    let interest3 = amount3 * 0.085 / 12 * 3; // 8.5% APR for 3 months
    let total3 = amount3 + interest3;
    let monthly3 = total3 / 3;
    
    // Calculate for 6 months
    let amount6 = totalAmount;
    let interest6 = amount6 * 0.092 / 12 * 6; // 9.2% APR for 6 months
    let total6 = amount6 + interest6;
    let monthly6 = total6 / 6;
    
    // Calculate for 12 months
    let amount12 = totalAmount;
    let interest12 = amount12 * 0.115 / 12 * 12; // 11.5% APR for 12 months
    let total12 = amount12 + interest12;
    let monthly12 = total12 / 12;
    
    // Update the display
    $('.monthly-amount-3').text(Math.round(monthly3).toLocaleString());
    $('.total-amount-3').text(Math.round(total3).toLocaleString());
    $('.monthly-amount-6').text(Math.round(monthly6).toLocaleString());
    $('.total-amount-6').text(Math.round(total6).toLocaleString());
    $('.monthly-amount-12').text(Math.round(monthly12).toLocaleString());
    $('.total-amount-12').text(Math.round(total12).toLocaleString());
  }
  
  // Update right sidebar when selections change
  function updateSidebarSelections() {
    // Update selected credit partner
    const selectedPartner = $('input[name="credit_partner"]:checked');
    if (selectedPartner.length > 0) {
      const partnerCard = selectedPartner.closest('.credit-partner-card');
      const partnerName = partnerCard.find('h4').text();
      const partnerRate = partnerCard.find('span:contains("Interest Rate:")').next().text();
      const partnerMax = partnerCard.find('span:contains("Max Amount:")').next().text();
      const partnerTime = partnerCard.find('span:contains("Processing:")').next().text();
      
      $('#partner-details').html(`
        <div style="margin-bottom: 10px;">
          <strong style="color: #333; font-size: 16px;">${partnerName}</strong>
        </div>
        <div style="margin-bottom: 8px; font-size: 14px;">
          <span style="color: #666;">Rate:</span> <span style="color: #FA9313; font-weight: 600;">${partnerRate}</span>
        </div>
        <div style="margin-bottom: 8px; font-size: 14px;">
          <span style="color: #666;">Limit:</span> <span style="color: #333;">${partnerMax}</span>
        </div>
        <div style="font-size: 14px;">
          <span style="color: #666;">Processing:</span> <span style="color: #333;">${partnerTime}</span>
        </div>
      `);
      $('#selected-partner-info').show();
    }
    
    // Update selected EMI plan
    const selectedPlan = $('input[name="emi_plan"]:checked');
    if (selectedPlan.length > 0) {
      const planCard = selectedPlan.closest('.emi-plan-card');
      const planName = planCard.find('h4').text();
      const monthlyAmount = planCard.find('.monthly-amount-' + selectedPlan.val().replace('months', '')).text();
      const totalAmount = planCard.find('.total-amount-' + selectedPlan.val().replace('months', '')).text();
      const planRate = planCard.find('span:contains("Interest Rate:")').next().text();
      
      $('#plan-details').html(`
        <div style="margin-bottom: 10px;">
          <strong style="color: #333; font-size: 16px;">${planName} Plan</strong>
        </div>
        <div style="margin-bottom: 8px; font-size: 14px;">
          <span style="color: #666;">Monthly:</span> <span style="color: #007bff; font-weight: 600; font-size: 16px;">${monthlyAmount} FCFA</span>
        </div>
        <div style="margin-bottom: 8px; font-size: 14px;">
          <span style="color: #666;">Total:</span> <span style="color: #333;">${totalAmount} FCFA</span>
        </div>
        <div style="font-size: 14px;">
          <span style="color: #666;">Rate:</span> <span style="color: #FA9313; font-weight: 600;">${planRate}</span>
        </div>
      `);
      $('#selected-plan-info').show();
    }
  }
  
  // Update summary step with comprehensive details
  function updateSummaryStep() {
    // Get selected partner details
    const selectedPartner = $('input[name="credit_partner"]:checked');
    const selectedPlan = $('input[name="emi_plan"]:checked');
    
    if (selectedPartner.length > 0 && selectedPlan.length > 0) {
      const partnerCard = selectedPartner.closest('.credit-partner-card');
      const planCard = selectedPlan.closest('.emi-plan-card');
      
      // Partner details
      const partnerName = partnerCard.find('h4').text();
      const partnerRateElement = partnerCard.find('span:contains("Interest Rate:")').next();
      const partnerRate = partnerRateElement.length > 0 ? partnerRateElement.text() : 'N/A';
      const partnerProcessingElement = partnerCard.find('span:contains("Processing:")').next();
      const partnerTime = partnerProcessingElement.length > 0 ? partnerProcessingElement.text() : '24-48 hours';
      
      // Plan details
      const planName = planCard.find('h4').text();
      const planValue = selectedPlan.val().replace('months', '');
      const monthlyAmountElement = planCard.find('.monthly-amount-' + planValue);
      const monthlyAmount = monthlyAmountElement.length > 0 ? monthlyAmountElement.text() : '0';
      const totalAmountElement = planCard.find('.total-amount-' + planValue);
      const totalAmount = totalAmountElement.length > 0 ? totalAmountElement.text() : '0';
      const planRateElement = planCard.find('span:contains("Interest Rate:")').next();
      const planRate = planRateElement.length > 0 ? planRateElement.text() : '8.5% APR';
      
      // Calculate amounts
      let cartTotal = parseFloat($('.totalAmount').text().replace(/[^0-9.]/g, '')) || 100000;
      let interestRateMatch = planRate.match(/[\d.]+/);
      let interestRate = interestRateMatch ? parseFloat(interestRateMatch[0]) / 100 : 0.085;
      let termMonths = parseInt(planValue) || 3;
      
      let interestAmount = cartTotal * interestRate / 12 * termMonths;
      let totalFinanced = cartTotal + interestAmount;
      let monthlyPayment = totalFinanced / termMonths;
      
      // Update summary overview
      $('.summary-monthly-amount').text(Math.round(monthlyPayment).toLocaleString());
      $('.summary-term-months').text(termMonths);
      const displayRate = interestRateMatch ? interestRateMatch[0] : '8.5';
      $('.summary-interest-rate').text(displayRate);
      $('.summary-interest-amount').text(Math.round(interestAmount).toLocaleString());
      $('.summary-total-amount').text(Math.round(totalFinanced).toLocaleString());
      
      // Update partner details in summary
      $('#summary-partner-details').html(`
        <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
          <span style="color: #666; font-weight: 500;">Partner:</span>
          <span style="font-weight: 600; color: #333;">${partnerName}</span>
        </div>
        <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
          <span style="color: #666; font-weight: 500;">Interest Rate:</span>
          <span style="font-weight: 600; color: #28a745;">${partnerRate}</span>
        </div>
        <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
          <span style="color: #666; font-weight: 500;">Processing Time:</span>
          <span style="font-weight: 600; color: #333;">${partnerTime}</span>
        </div>
        <div style="display: flex; justify-content: space-between; padding: 8px 0;">
          <span style="color: #666; font-weight: 500;">Benefits:</span>
          <span style="font-weight: 600; color: #28a745;">Fast approval • Flexible terms</span>
        </div>
      `);
      
      // Update plan details in summary
      $('#summary-plan-details').html(`
        <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
          <span style="color: #666; font-weight: 500;">Plan:</span>
          <span style="font-weight: 600; color: #333;">${planName}</span>
        </div>
        <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
          <span style="color: #666; font-weight: 500;">Monthly Payment:</span>
          <span style="font-weight: 600; color: #007bff; font-size: 16px;">${Math.round(monthlyPayment).toLocaleString()} FCFA</span>
        </div>
        <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
          <span style="color: #666; font-weight: 500;">Term:</span>
          <span style="font-weight: 600; color: #333;">${termMonths} Months</span>
        </div>
        <div style="display: flex; justify-content: space-between; padding: 8px 0;">
          <span style="color: #666; font-weight: 500;">Total Amount:</span>
          <span style="font-weight: 600; color: #333;">${Math.round(totalFinanced).toLocaleString()} FCFA</span>
        </div>
      `);
      
      // Update personal information
      const fullName = $('#full_name').val() || '-';
      const email = $('#email').val() || '-';
      const countryCode = $('#multistep-country-select').val() || '';
      const phone = $('#multistep-phone-input').val() || '';
      const formattedPhone = countryCode && phone ? `+${countryCode} ${phone}` : (phone || '-');
      const registrationId = $('#registration_id').val() || '-';
      
      $('#summary-full-name').text(fullName);
      $('#summary-email').text(email);
      $('#summary-phone').text(formattedPhone);
      $('#summary-employment').text(registrationId);
      
      // Generate payment schedule preview
      generatePaymentSchedulePreview(totalFinanced, termMonths, interestRate / 12);
    }
  }
     
  
  // Generate payment schedule preview
  function generatePaymentSchedulePreview(totalAmount, termMonths, monthlyRate) {
    const monthlyPayment = totalAmount / termMonths;
    let scheduleHtml = '';
    
    for (let i = 1; i <= Math.min(3, termMonths); i++) {
      const dueDate = new Date();
      dueDate.setMonth(dueDate.getMonth() + i);
      const dueDateString = dueDate.toLocaleDateString('en-GB');
      
      const principalPayment = totalAmount / termMonths;
      const interestPayment = (totalAmount - (principalPayment * (i - 1))) * monthlyRate;
      const totalPayment = principalPayment + interestPayment;
      
      scheduleHtml += `
        <tr style="border-bottom: 1px solid #dee2e6;">
          <td style="padding: 10px; border: 1px solid #dee2e6;">${i}</td>
          <td style="padding: 10px; border: 1px solid #dee2e6;">${dueDateString}</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">${Math.round(principalPayment).toLocaleString()} FCFA</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6;">${Math.round(interestPayment).toLocaleString()} FCFA</td>
          <td style="padding: 10px; text-align: right; border: 1px solid #dee2e6; font-weight: 600;">${Math.round(monthlyPayment).toLocaleString()} FCFA</td>
        </tr>
      `;
    }
    
    $('#payment-schedule-preview').html(scheduleHtml);
  }
  
// Listen for partner selection changes
$(document).on('change', 'input[name="credit_partner"]', function() {
    console.log('Partner selection changed, value:', $(this).val());
    console.log('Partner selection event triggered successfully');

    // Read the data-json-term attribute and parse it as JSON
    var jsonTerm = $(this).attr('data-json-term');
    var termData = [];
    if (jsonTerm) {
        try {
            termData = JSON.parse(jsonTerm);
            console.log('Parsed partner payment terms:', termData);
            
            // Generate EMI plans based on termData
            generateEMIPlans(termData);
            
        } catch (e) {
            console.error('Invalid JSON in data-json-term:', e);
        }
    }

    // Update selected partner display
    const selectedPartner = $('input[name="credit_partner"]:checked');
    if (selectedPartner.length > 0) {
        const partnerCard = selectedPartner.closest('.credit-partner-card');
        const partnerName = partnerCard.find('h4').text();

        if (partnerName && partnerName.trim() !== '') {
            $('#partner-name-display').text(partnerName);
            $('#selected-partner-display').show();
        } else {
            // Fallback: get partner name from the input value
            const partnerId = selectedPartner.val();
            $('#partner-name-display').text('Partner ID: ' + partnerId);
            $('#selected-partner-display').show();
        }
    } else {
        $('#selected-partner-display').hide();
    }
});

// Function to generate EMI plans dynamically
function generateEMIPlans(termData) {
    const emiContainer = $('#emi-plans-container');
    emiContainer.empty(); // Clear existing plans
    
    if (!termData || termData.length === 0) {
        emiContainer.html('<div style="text-align: center; padding: 40px; color: #666;">No payment plans available for this partner.</div>');
        return;
    }
    
    // Get total amount for calculations

    let totalAmount = parseFloat(sessionStorage.getItem('final_amount')) || 1000000;
    
    termData.forEach((term, index) => {
        console.log('Processing term:', JSON.stringify(term));
        // Calculate amounts based on term data
        let termMonths = parseInt(term.no_of_month) || 24;
        let interestRate = parseFloat(term.term_commission_percent) || 18;
        
        let interestAmount = totalAmount * (interestRate / 100) / 12 * termMonths;
        let totalFinanced = totalAmount + interestAmount;
        let monthlyPayment = totalFinanced / termMonths;
        
        // Determine if this plan should be highlighted
        let isPopular = term.is_popular || index === 0; // First plan or marked as popular
        let highlightClass = isPopular ? 'POPULAR' : '';
        let highlightColor = isPopular ? '#28a745' : '#FA9313';
        
        // Create the plan HTML
        let planHtml = `
            <div class="emi-plan-card" style="border: 2px solid #eee; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease; position: relative; background: white;">
                <input type="radio" name="emi_plan" value="${termMonths}months" id="plan${termMonths}" style="position: absolute; top: 15px; right: 15px; width: 20px; height: 20px; accent-color: #FA9313;">
                ${highlightClass ? `<div class="emi-highlight" style="background: linear-gradient(135deg, ${highlightColor}, #20c997); color: white; padding: 5px 10px; border-radius: 15px; font-size: 11px; font-weight: 600; position: absolute; top: -8px; right: 15px;">
                    ${highlightClass}
                </div>` : ''}
                <div style="text-align: center; margin-bottom: 15px;">
                    <h4 style="margin: 0; color: #333; font-size: 18px; font-weight: 600;">${termMonths} Months</h4>
                    <p style="color: #666; font-size: 12px; margin: 5px 0;">${term.description || 'Flexible financing'}</p>
                </div>
                <div style="text-align: center; margin-bottom: 15px;">
                    <div style="font-size: 24px; font-weight: 700; color: #FA9313; margin-bottom: 5px;">
                        <span class="monthly-amount-${termMonths}">${Math.round(monthlyPayment).toLocaleString()}</span> FCFA
                    </div>
                    <div style="font-size: 12px; color: #666;">per month</div>
                </div>
                <div style="text-align: left;">
                    <div style="margin-bottom: 8px; font-size: 14px;">
                        <span style="color: #666;">Interest Rate:</span>
                        <span style="color: #333; font-weight: 600;">${interestRate}% APR</span>
                    </div>
                    <div style="margin-bottom: 8px; font-size: 14px;">
                        <span style="color: #666;">Total Amount:</span>
                        <span style="color: #333; font-weight: 600;"><span class="total-amount-${termMonths}">${Math.round(totalFinanced).toLocaleString()}</span> FCFA</span>
                    </div>
                  
                    <div style="color: #28a745; font-size: 12px; font-weight: 500; margin-top: 10px;">
                        ${term.benefits || '✓ Flexible payment ✓ Quick approval ✓ Build credit score'}
                    </div>
                </div>
            </div>
        `;
        
        emiContainer.append(planHtml);
    });
    
    console.log('Generated', termData.length, 'EMI plans');
}
  // Listen for plan selection changes
  $(document).on('change', 'input[name="emi_plan"]', function() {
    updateSidebarSelections();
    // Update visual selection for EMI plan cards
    $('.emi-plan-card').removeClass('selected');
    $(this).closest('.emi-plan-card').addClass('selected');
  });
  
  // Handle credit partner card selection
  $(document).on('change', 'input[name="credit_partner"]', function() {
    console.log('Credit partner card selection event triggered');
    // Update visual selection for credit partner cards
    $('.credit-partner-card').removeClass('selected');
    $(this).closest('.credit-partner-card').addClass('selected');
    console.log('Credit partner visual selection updated');
  });

  // Add click handler for credit partner cards
  $(document).on('click', '.credit-partner-card', function(e) {
    console.log('Credit partner card clicked');

    // Always select the radio button when card is clicked
    const radio = $(this).find('input[name="credit_partner"]');
    console.log('Found radio button:', radio.length, 'Value:', radio.val());

    if (radio.length > 0) {
      // Uncheck all other credit partner radios first
      $('input[name="credit_partner"]').prop('checked', false);

      // Check this radio button
      radio.prop('checked', true);
      console.log('Radio button checked:', radio.is(':checked'));

      // Trigger change event
      radio.trigger('change');

      // Force visual update
      $('.credit-partner-card').removeClass('selected');
      $(this).addClass('selected');

      console.log('Credit partner selection completed for value:', radio.val());
    } else {
      console.error('No radio button found in credit partner card');
    }
  });

  // Direct click handler for credit partner radio buttons
  $(document).on('click', 'input[name="credit_partner"]', function(e) {
    console.log('Credit partner radio button clicked directly, value:', $(this).val());

    // Ensure this radio is checked
    $(this).prop('checked', true);

    // Update visual selection
    $('.credit-partner-card').removeClass('selected');
    $(this).closest('.credit-partner-card').addClass('selected');

    // Trigger change event
    $(this).trigger('change');
  });

  // Handle EMI plan card clicks
  $(document).on('click', '.emi-plan-card', function(e) {
    if (!$(e.target).is('input[type="radio"]')) {
      $(this).find('input[type="radio"]').prop('checked', true).trigger('change');
    }
  });
  
  // Handle credit partner card clicks
  $(document).on('click', '.credit-partner-card', function(e) {
    if (!$(e.target).is('input[type="radio"]')) {
      $(this).find('input[type="radio"]').prop('checked', true).trigger('change');
    }
  });
  
  // Enhanced form validation messaging
  function showValidationSummary() {
    const errors = [];
    
    // Check each field and collect errors
    if ($('#full_name').hasClass('invalid') || !$('#full_name').val().trim()) {
      errors.push('Full Name is required and must be valid');
    }
    if ($('#email').hasClass('invalid') || !$('#email').val().trim()) {
      errors.push('Valid Email Address is required');
    }
    if ($('#phone').hasClass('invalid') || !$('#phone').val().trim()) {
      errors.push('Valid Phone Number is required');
    }
    if ($('#registration_id').hasClass('invalid') || !$('#registration_id').val().trim()) {
      errors.push('Registration ID is required and must be valid');
    }
    
    if (errors.length > 0) {
      let errorMessage = 'Please fix the following errors:\n\n';
      errors.forEach((error, index) => {
        errorMessage += `${index + 1}. ${error}\n`;
      });
      
      toastr.error(errorMessage, 'Form Validation Error', {
        timeOut: 8000,
        extendedTimeOut: 2000
      });
    }
  }
</script>


<script>
    // Global functions for address modal management
    // Show the popup overlay when a modal is opened
    function showOverlay() {
        $('#address-modal-overlay').show();
        // Add class to body to help with CSS targeting
        $('body').addClass('modal-open');
        // Force navbar elements behind the overlay
        $('header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky').css({
            'z-index': '1 !important',
            'position': 'relative !important'
        });

        // Add a style tag to force navbar elements behind the overlay
        if ($('#force-navbar-behind').length === 0) {
            $('head').append('<style id="force-navbar-behind">header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky { z-index: 1 !important; } #address-modal, #delete-confirm-modal { z-index: ********** !important; position: fixed !important; }</style>');
        }
    }

    // Hide the popup overlay when all modals are closed
    function hideOverlay() {
        $('#address-modal-overlay').hide();
        // Remove class from body
        $('body').removeClass('modal-open');
        // Remove the style tag
        $('#force-navbar-behind').remove();
        // Restore z-index of navbar elements
        $('header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky').css({
            'z-index': '1000',
            'position': 'relative'
        });
    }

    // Address Management Functions
    $(document).ready(function() {
        // Handle window resize to ensure modals stay centered
        $(window).on('resize', function() {
            if ($('#address-modal').is(':visible')) {
                $('#address-modal').css({
                    'top': '50%',
                    'left': '50%',
                    'transform': 'translate(-50%, -50%)'
                });
            }
            if ($('#delete-confirm-modal').is(':visible')) {
                $('#delete-confirm-modal').css({
                    'top': '50%',
                    'left': '50%',
                    'transform': 'translate(-50%, -50%)'
                });
            }
        });

        // Open the address modal for adding a new address
        $(document).on('click', '#add-address-btn', function(e) {
            e.preventDefault();
            console.log('Add address button clicked');
            // Reset the form
            $('#address-form')[0].reset();
            $('#address_id').val('');
            $('#address-modal-title').text('<?= __('Add New Address') ?>');

            // Show the modal and overlay
            $('#address-modal').show();
            showOverlay();

            // Ensure the modal is on top
            $('#address-modal').css({
                'z-index': '**********',
                'display': 'block',
                'position': 'fixed',
                'top': '50%',
                'left': '50%',
                'transform': 'translate(-50%, -50%)',
                'background-color': 'white'
            });

            // Force all other elements behind
            $('body > *:not(#address-modal, #address-modal-overlay, script, style)').css('z-index', '1');

            // Reinitialize Select2 for country code dropdown
            try {
                // Destroy existing Select2 instance if it exists
                if ($('#address-country-select').data('select2')) {
                    $('#address-country-select').select2('destroy');
                }

                // Reinitialize Select2
                $('#address-country-select').select2({
                    minimumResultsForSearch: 0,
                    allowClear: false,
                    placeholder: 'Select',
                    dropdownParent: $('#address-modal'),
                    templateResult: formatCountryCode,
                    templateSelection: formatCountryCode
                });

                // Format country code options
                function formatCountryCode(state) {
                    if (!state.id) return state.text;
                    return $('<span style="font-weight: 500;">+' + state.id + '</span>');
                }

                // Set default country code (237 for Cameroon)
                // First, make sure all options don't have selected attribute
                $('#address-country-select option').prop('selected', false);

                // Then set the selected option
                $('#address-country-select option[value="237"]').prop('selected', true);

                // Finally trigger change for Select2
                $('#address-country-select').val('237').trigger('change.select2');
                console.log('Set default country code to 237 for new address');

                console.log('Reinitialized Select2 for address country code dropdown');
            } catch (e) {
                console.error('Error reinitializing Select2:', e);
            }

            return false;
        });

        // Open the address modal for editing an existing address
        $(document).on('click', '.edit-address-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const addressId = $(this).data('address-id');

            // Fetch the address details
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkoutEditAddress']) ?>/" + addressId,
                type: 'GET',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Show the modal first to ensure Select2 initializes properly
                        $('#address-modal').show();
                        showOverlay();

                        // Ensure the modal is on top
                        $('#address-modal').css({
                            'z-index': '**********',
                            'display': 'block',
                            'position': 'fixed',
                            'top': '50%',
                            'left': '50%',
                            'transform': 'translate(-50%, -50%)',
                            'background-color': 'white'
                        });

                        // Force all other elements behind
                        $('body > *:not(#address-modal, #address-modal-overlay, script, style)').css('z-index', '1');

                        // Reinitialize Select2 for country code dropdown
                        try {
                            // Destroy existing Select2 instance if it exists
                            if ($('#address-country-select').data('select2')) {
                                $('#address-country-select').select2('destroy');
                            }

                            // Reinitialize Select2
                            $('#address-country-select').select2({
                                minimumResultsForSearch: 0,
                                allowClear: false,
                                placeholder: 'Select',
                                dropdownParent: $('#address-modal'),
                                templateResult: formatCountryCode,
                                templateSelection: formatCountryCode
                            });

                            // Format country code options
                            function formatCountryCode(state) {
                                if (!state.id) return state.text;
                                return $('<span style="font-weight: 500;">+' + state.id + '</span>');
                            }

                            console.log('Reinitialized Select2 for address country code dropdown');
                        } catch (e) {
                            console.error('Error reinitializing Select2:', e);
                        }

                        // Populate the form with the address details
                        const address = response.address;
                        $('#address_id').val(address.id);
                        $('#address_name').val(address.name);
                        $('#address_type').val(address.type);
                        $('#house_no').val(address.house_no);
                        $('#address_line1').val(address.address_line1);
                        $('#address_line2').val(address.address_line2);
                        $('#city_id').val(address.city_id);
                        $('#zipcode').val(address.zipcode);

                        // Set country code and trigger change event for Select2
                        if (address.country_code1) {
                            // First, make sure all options don't have selected attribute
                            $('#address-country-select option').prop('selected', false);

                            // Then set the selected option
                            $('#address-country-select option[value="' + address.country_code1 + '"]').prop('selected', true);

                            // Finally trigger change for Select2
                            $('#address-country-select').val(address.country_code1).trigger('change.select2');
                            console.log('Setting country code to: ' + address.country_code1);
                        }

                        $('#address-phone-input').val(address.phone_no1);

                        // Update the modal title
                        $('#address-modal-title').text('Edit Address');
                    } else {
                        toastr.error(response.message || 'Failed to load address details');
                    }
                },
                error: function() {
                    toastr.error('An error occurred while fetching address details');
                }
            });
        });

        // Close the address modal
        $(document).on('click', '#cancel-address-btn, #close-address-modal', function(e) {
            e.preventDefault();
            console.log('Close address modal clicked');
            $('#address-modal').hide();
            hideOverlay();
            return false;
        });

        // Close modal when clicking on overlay
        $(document).on('click', '#address-modal-overlay', function() {
            console.log('Overlay clicked');
            $('#address-modal').hide();
            hideOverlay();
        });

        // Handle input on address phone number field to clear error message
        $(document).on('input', '#address-phone-input', function() {
            $('#address-phone-input-error').hide();
        });

        // Handle address country code change
        $(document).on('change', '#address-country-select', function() {
            $('#address-phone-input-error').hide();
        });

        // Save the address (add or update)
        $(document).on('click', '#save-address-btn', function() {
            console.log('Save address button clicked');
            // Validate the phone number
            const phoneNumber = $('#address-phone-input').val().trim();
            if (phoneNumber && !/^\d{10,15}$/.test(phoneNumber)) {
                $('#address-phone-input-error').text('Please enter a valid phone number (10-15 digits)').show();
                return;
            }

            // Validate the form
            if (!$('#address-form')[0].checkValidity()) {
                $('#address-form')[0].reportValidity();
                return;
            }

            // Disable the button to prevent multiple submissions
            $(this).prop('disabled', true);

            // Determine if this is an add or edit operation
            const addressId = $('#address_id').val();
            const isEdit = addressId !== '';

            // Format the phone number with country code
            const countryCode = $('#address-country-select').val().trim();
            const formattedPhoneNumber = phoneNumber ? '+' + countryCode + phoneNumber : '';

            // Create a hidden input for the formatted phone number
            if ($('#formatted_phone_no').length) {
                $('#formatted_phone_no').val(formattedPhoneNumber);
            } else {
                $('#address-form').append('<input type="hidden" id="formatted_phone_no" name="formatted_phone_no" value="' + formattedPhoneNumber + '">');
            }

            // Prepare the data
            const formData = $('#address-form').serialize();

            // Determine the URL based on the operation
            let url;
            if (isEdit) {
                url = "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkoutEditAddress']) ?>/" + addressId;
            } else {
                url = "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkoutAddAddress']) ?>";
            }

            // Send the AJAX request
            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    // Re-enable the button
                    $('#save-address-btn').prop('disabled', false);

                    if (response.status === 'success') {
                        // Close the modal
                        $('#address-modal').hide();
                        hideOverlay();

                        // Show success message
                        toastr.success(response.message || (isEdit ? 'Address updated successfully' : 'Address added successfully'));

                        // Reload the page to refresh the address list
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message || 'Failed to save address');
                    }
                },
                error: function() {
                    // Re-enable the button
                    $('#save-address-btn').prop('disabled', false);
                    toastr.error('An error occurred while saving the address');
                }
            });
        });

        // Open the delete confirmation modal
        $(document).on('click', '.delete-address-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const addressId = $(this).data('address-id');
            $('#delete_address_id').val(addressId);

            // Show the modal
            $('#delete-confirm-modal').show();
            $('#address-modal-overlay').show();

            // Ensure the modal is on top
            $('#delete-confirm-modal').css({
                'z-index': '**********',
                'display': 'block',
                'position': 'fixed',
                'top': '50%',
                'left': '50%',
                'transform': 'translate(-50%, -50%)',
                'background-color': 'white'
            });

            // Force all other elements behind
            $('body > *:not(#delete-confirm-modal, #address-modal-overlay, script, style)').css('z-index', '1');
            return false;
        });

        // Close the delete confirmation modal
        $(document).on('click', '#cancel-delete-btn, #close-delete-modal', function(e) {
            e.preventDefault();
            console.log('Close delete modal clicked');
            $('#delete-confirm-modal').hide();
            $('#address-modal-overlay').hide();
            return false;
        });

        // Confirm and delete the address
        $(document).on('click', '#confirm-delete-btn', function() {
            console.log('Confirm delete clicked');
            // Disable the button to prevent multiple submissions
            $(this).prop('disabled', true);

            // Get the address ID
            const addressId = $('#delete_address_id').val();

            // Send the AJAX request
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkoutDeleteAddress']) ?>/" + addressId,
                type: 'POST',
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    // Re-enable the button
                    $('#confirm-delete-btn').prop('disabled', false);

                    // Close the modal
                    $('#delete-confirm-modal').hide();
                    $('#address-modal-overlay').hide();

                    if (response.status === 'success') {
                        // Show success message
                        toastr.success(response.message || 'Address deleted successfully');

                        // Remove the address from the DOM
                        $('[data-address-id="' + addressId + '"]').remove();

                        // If no addresses left, show the no-address message
                        if ($('.address-item').length === 0) {
                            $('#address-list-container').html('<div class="no-address-message"><i class="fa fa-map-marker-alt" style="font-size: 24px; color: #FA9313; margin-bottom: 10px;"></i><p>You don\'t have any saved addresses.</p><p>Please add a new address using the + button above.</p></div>');
                        }
                    } else {
                        toastr.error(response.message || 'Failed to delete address');
                    }
                },
                error: function() {
                    // Re-enable the button
                    $('#confirm-delete-btn').prop('disabled', false);
                    toastr.error('An error occurred while deleting the address');
                }
            });
        });

        // Make address item clickable and highlight selection
        $(document).on('click keypress', '.address-item', function(e) {
            // Don't trigger if clicking on edit or delete buttons
            if ($(e.target).closest('.edit-address-btn, .delete-address-btn').length) {
                return;
            }

            if (e.type === 'click' || (e.type === 'keypress' && (e.which === 13 || e.which === 32))) {
                // Remove selection from all
                $('.address-item').removeClass('selected');

                // Add selection to clicked
                $(this).addClass('selected');

                // Set radio checked
                $(this).find('input[name="address"]').prop('checked', true).trigger('change');

                // Store in sessionStorage
                const addressId = $(this).data('address-id');
                const cityId = $(this).find('input[name="address"]').data('city-id');

                sessionStorage.setItem('customer_address_id', addressId);
                sessionStorage.setItem('city_id', cityId);
                sessionStorage.setItem('showroom_id', '');
                sessionStorage.setItem('checkDeliveryTypeCheckedCheckbox', 'delivered');

                // Show a confirmation toast
                if (typeof toastr !== 'undefined') {
                    toastr.success('Address selected successfully', '', {
                        timeOut: 2000,
                        progressBar: true
                    });
                }
            }
        });

        // Handle address selection via radio button change
        $(document).on('change', 'input[name="address"]', function() {
            // Remove selected class from all address items
            $('.address-item').removeClass('selected');

            // Add selected class to the parent of the checked radio button
            $(this).closest('.address-item').addClass('selected');
        });

        // Select the first address by default if available
        if ($('input[name="address"]').length > 0) {
          //  $('input[name="address"]:first').prop('checked', true).trigger('change');
        }
    });

    // Cart Item Functions
  
    function updateToCart(cart_item_id, currentQty) {

        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'updateCartItem']) ?>",
                type: 'POST',
                data: {cart_item_id: cart_item_id, quantity: currentQty},
                success: function (response) {
                    console.log(JSON.stringify(response));
                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });

    }


</script>
<script>
    let lytPoint = 0;
    let isLoyaltyPopupOpen = false; // Track popup state
    
    $(document).on('click', '.loyaltyPointsCheck', function () {
        // Prevent multiple clicks
        if (isLoyaltyPopupOpen) {
            return;
        }
        
        var customerId = <?= json_encode($customerId) ?>;
        if (customerId == null) {

            toastr.warning("<?= __('Login required to add loyalty points') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        } else {
            checkLoyaltyPoint()
                .then((res) => {
                    if (res.status == 'success' || res.status == 200) {
                        toastr.success(res.message, '', {
                            timeOut: 3000,  // 3 seconds before hiding the toastr message
                            progressBar: true,
                            onHidden: function () {
                                showLoyaltyPopup();
                            }
                        });
                    } else {
                        toastr.warning(res.message, '', {
                            timeOut: 3000,  // 3 seconds before hiding the toastr message
                            progressBar: true,
                            onHidden: function () {
                                // Reload the page after the toastr message disappears
                                //    location.reload();
                            }
                        });
                    }
                })
                .catch((error) => {
                    toastr.error(error, 'Error')
                });
        }
    });


    function checkLoyaltyPoint() {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'checkLoyaltyPoint']) ?>",
                type: 'POST',
                success: function (response) {
                    lytPoint = response.data.points_converted;
                    // sessionStorage.setItem('redeemPoint', parseFloat(response.data.points_converted.replace(/,/g, '')));
                    updateTotalAmount();

                    $(".setDateAjax").html(new Date(response.data.validity_end_date.date).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                    }));
                    $(".loyalty-price").html(formatAmount(response.data.points_converted));

                    // Calculate maximum redeemable amount
                    let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
                    let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
                    let walletUsed = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
                    let subAmount = <?= $totalPrice ?>;
                    
                    // Calculate available amount for loyalty redemption
                    let availableAmount = subAmount - (couponAmount + walletUsed) + delivery_charge;
                    
                    // Set the maximum redeemable points as order total (not limited by available points)
                    $(".radeem-input").attr('data-max-order-total', availableAmount);
                    $(".radeem-input").attr('placeholder', `Enter points (max: ${Math.floor(availableAmount)})`);
                    
                    // Update the max redeemable points display (based on order total only)
                    $(".max-redeemable-points").text(Math.floor(availableAmount));

                    if (response.status) {
                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }
    function formatAmount(amount){
        return amount.toString().split('.')[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    }
    $(".radeem-btn").on("click", handleRedeemClick);


    function handleRedeemClick(reload) {

        let inputPoint = parseFloat($(".radeem-input").val());
        final_amount_data = parseFloat(sessionStorage.getItem('final_amount'));
        let newam = final_amount_data - inputPoint;

        $.ajax({
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'loyaltyPointVerify']) ?>",
            type: 'POST',
            data: {
                'redeem_points': inputPoint,
                'final_amount': newam
            },
            success: function (response) {
                if (response.status == 'success') {
                $(".lyt-reedem-point").html(inputPoint);
                $(".lytPoints").val(inputPoint);
                closePopup();
                // Store the input value in sessionStorage
                sessionStorage.setItem('redeemPoint', inputPoint);
                // Update wallet popup if it's open
                updateWalletPopupIfOpen();
                    if(reload == 2){
                        toastr.success(response.message, '', {
                            timeOut: 1000,
                            progressBar: true,
                            onHidden: function () {
                                window.location.reload();
                            }
                        });
                   }


                } else {

                    toastr.warning(response.message, '', {
                        timeOut: 1000,
                        progressBar: true,
                        onHidden: function () {
                            sessionStorage.setItem('redeemPoint', 0);
                            if(reload == 2){
                                window.location.reload();
                            }
                        }
                    });

                }
            },
            error: function (xhr, status, error) {
                reject('An error occurred: ' + error);
            }
        });
    }

    // On page load, retrieve the value from sessionStorage
    $(document).ready(function () {
        let redeemPoint = sessionStorage.getItem('redeemPoint');
        if (redeemPoint) {
            $(".lyt-reedem-point").html(redeemPoint);
            $(".lytPoints").val(redeemPoint);
            $(".radeem-input").val(redeemPoint);
        }
        updateTotalAmount();
    });

    var customerId = <?= json_encode($customerId) ?>;
    if (customerId) {
        document.getElementById('loyaltyPoints').onclick = function () {
            document.getElementById('popup').style.display = 'block';
            document.getElementById('overlay').style.display = 'block';
        };
    }

    function closePopup() {
        hideLoyaltyPopup();
    }

    // Loyalty popup management functions with better error handling and state tracking
    function showLoyaltyPopup() {
        try {
            // Prevent multiple opens
            if (isLoyaltyPopupOpen) {
                return;
            }
            
            // Store current scroll position
            const scrollY = window.scrollY || document.documentElement.scrollTop;
            
            // Get elements
            const popup = document.getElementById('popup');
            const overlay = document.getElementById('overlay');
            const loyaltyPopup = document.querySelector('.loyalty-points-popup');
            const loyaltyOverlay = document.querySelector('.loyalty-popup-overlay');
            
            if (!popup || !loyaltyPopup || !loyaltyOverlay) {
                console.error('Loyalty popup elements not found');
                return;
            }
            
            // Set state
            isLoyaltyPopupOpen = true;
            
            // Store scroll position for restoration
            document.body.setAttribute('data-scroll-y', scrollY);
            
            // Prevent body scroll
            document.body.style.position = 'fixed';
            document.body.style.top = `-${scrollY}px`;
            document.body.style.width = '100%';
            document.body.style.overflow = 'hidden';
            document.body.classList.add('loyalty-popup-open');
            
            // Show popup elements
            popup.style.display = 'block';
            if (overlay) overlay.style.display = 'block'; // Show main overlay
            loyaltyPopup.style.display = 'block';
            loyaltyOverlay.style.display = 'block';
            
            // Add show classes for animations
            setTimeout(() => {
                loyaltyPopup.classList.add('show');
                loyaltyOverlay.classList.add('show');
            }, 10);
            
            // Reset navbar
            resetNavbar();
            
        } catch (error) {
            console.error('Error showing loyalty popup:', error);
            isLoyaltyPopupOpen = false;
        }
    }

    function hideLoyaltyPopup() {
        try {
            // Check if already closed
            if (!isLoyaltyPopupOpen) {
                return;
            }
            
            // Get elements
            const popup = document.getElementById('popup');
            const overlay = document.getElementById('overlay');
            const loyaltyPopup = document.querySelector('.loyalty-points-popup');
            const loyaltyOverlay = document.querySelector('.loyalty-popup-overlay');
            
            if (!popup) {
                console.error('Popup element not found');
                isLoyaltyPopupOpen = false;
                return;
            }
            
            // Set state
            isLoyaltyPopupOpen = false;
            
            // Remove show classes
            if (loyaltyPopup) {
                loyaltyPopup.classList.remove('show');
            }
            if (loyaltyOverlay) {
                loyaltyOverlay.classList.remove('show');
            }
            
            // Hide popup after animation
            setTimeout(() => {
                popup.style.display = 'none';
                if (overlay) overlay.style.display = 'none'; // Hide main overlay
                if (loyaltyPopup) loyaltyPopup.style.display = 'none';
                if (loyaltyOverlay) loyaltyOverlay.style.display = 'none';
            }, 200);
            
            // Restore body scroll
            const scrollY = document.body.getAttribute('data-scroll-y') || '0';
            document.body.classList.remove('loyalty-popup-open');
            document.body.style.position = '';
            document.body.style.top = '';
            document.body.style.width = '';
            document.body.style.overflow = '';
            
            // Restore scroll position
            if (scrollY && scrollY !== '0') {
                window.scrollTo(0, parseInt(scrollY));
            }
            
            // Clean up attribute
            document.body.removeAttribute('data-scroll-y');
            
            // Reset navbar
            resetNavbar();
            
        } catch (error) {
            console.error('Error hiding loyalty popup:', error);
            isLoyaltyPopupOpen = false;
            // Fallback: at least hide the popup and overlays
            const popup = document.getElementById('popup');
            const overlay = document.getElementById('overlay');
            if (popup) popup.style.display = 'none';
            if (overlay) overlay.style.display = 'none';
            // Restore body styles
            document.body.classList.remove('loyalty-popup-open');
            document.body.style.position = '';
            document.body.style.top = '';
            document.body.style.width = '';
            document.body.style.overflow = '';
        }
    }

    function resetNavbar() {
        try {
            const nav = document.querySelector('.navbar.fixed-top');
            const noFixNav = document.querySelector(".navbar");
            
            if (nav) {
                nav.style.removeProperty('position');
                nav.style.removeProperty('z-index');
            }
            if (noFixNav) {
                noFixNav.style.removeProperty('z-index');
            }
        } catch (error) {
            console.error('Error resetting navbar:', error);
        }
    }

    // Enhanced loyalty popup event handlers
    $(document).on('click', '.loyalty-popup-overlay', function(e) {
        e.stopPropagation();
        hideLoyaltyPopup();
    });

    // Also handle clicks on the main overlay
    $(document).on('click', '#overlay', function(e) {
        e.stopPropagation();
        // Only close if it's the loyalty popup that's open
        const popup = document.getElementById('popup');
        if (popup && popup.style.display === 'block' && isLoyaltyPopupOpen) {
            hideLoyaltyPopup();
        }
    });

    // Prevent closing when clicking inside the popup content
    $(document).on('click', '.loyalty-points-popup', function(e) {
        e.stopPropagation();
    });

    // Close popup with escape key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' || e.keyCode === 27) {
            const popup = document.getElementById('popup');
            if (popup && popup.style.display === 'block' && isLoyaltyPopupOpen) {
                hideLoyaltyPopup();
            }
        }
    });

    // Close button click handler
    $(document).on('click', '.close-popup', function(e) {
        e.preventDefault();
        e.stopPropagation();
        hideLoyaltyPopup();
    });

    // Wallet functionality
    let walletAmount = 0;
    let availableWallet = <?= $wallet ?>;

    $(document).on('click', '.walletPointsCheck', function () {
        var customerId = <?= json_encode($customerId) ?>;
        if (customerId == null) {
            toastr.warning("<?= __('Login required to use wallet') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        } else {
            if (availableWallet <= 0) {
                toastr.warning("<?= __('No wallet balance available') ?>", '', {
                    timeOut: 3000,
                    progressBar: true,
                });
            } else {
                // Calculate maximum usable amount before showing popup
                updateMaxUsableAmount();
                document.getElementById('wallet-popup').style.display = 'block';
                document.getElementById('wallet-overlay').style.display = 'block';
            }
        }
    });

    function closeWalletPopup() {
        document.getElementById('wallet-popup').style.display = 'none';
        document.getElementById('wallet-overlay').style.display = 'none';
        //for remove z-index
        let nav = document.querySelector('.navbar.fixed-top');
        let noFixNav = document.querySelector(".navbar");
        let body = document.querySelector("body");
        if (nav) {
            nav.style.removeProperty('position', 'fixed');
            nav.style.removeProperty('z-index', '11', 'important');
            noFixNav.style.removeProperty('z-index', '1', 'important');
            body.style.removeProperty('overflow','auto');
        }
    }

    function updateMaxUsableAmount() {
        // Calculate the maximum amount that can be used from wallet
        let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
        let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
        let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
        let subAmount = <?= $totalPrice ?>;
        let totalBeforeWallet = subAmount - (couponAmount + redeemPoint) + delivery_charge;

        let maxUsable = Math.min(availableWallet, Math.max(0, totalBeforeWallet));
        $('.max-usable-amount').text(formatAmount(maxUsable));

        // Update the input max attribute
        $('.wallet-input').attr('max', maxUsable);
    }

    // Handle wallet input validation
    $(document).on('input', '.wallet-input', function() {
        // Remove any non-numeric characters except decimal point
        let value = $(this).val().replace(/[^0-9.]/g, '');

        // Ensure only one decimal point
        let parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }

        // Limit to 2 decimal places
        if (parts[1] && parts[1].length > 2) {
            value = parts[0] + '.' + parts[1].substring(0, 2);
        }

        $(this).val(value);

        let inputAmount = parseFloat(value) || 0;
        let maxAmount = availableWallet;

        // Get current total amount to limit wallet usage
        let currentTotal = parseFloat(sessionStorage.getItem('final_amount')) || <?= $totalPrice ?>;
        let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
        let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
        let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
        let subAmount = <?= $totalPrice ?>;
        let totalBeforeWallet = subAmount - (couponAmount + redeemPoint) + delivery_charge;

        // Limit wallet amount to available balance and total amount
        maxAmount = Math.min(availableWallet, Math.max(0, totalBeforeWallet));

        // Update the max usable amount display in real-time
        $('.max-usable-amount').text(formatAmount(maxAmount));
        $('.wallet-input').attr('max', maxAmount);

        if (inputAmount > maxAmount) {
            $(this).val(maxAmount);
            inputAmount = maxAmount;
        }

        if (inputAmount < 0) {
            $(this).val(0);
            inputAmount = 0;
        }

        // Update the display
        $('.wallet-amount-to-use').text(formatAmount(inputAmount));

        // Enable/disable apply button
        if (inputAmount > 0 && inputAmount <= maxAmount) {
            $('.wallet-apply-btn').prop('disabled', false);
        } else {
            $('.wallet-apply-btn').prop('disabled', true);
        }
    });

    // Prevent non-numeric input on keypress
    $(document).on('keypress', '.wallet-input', function(e) {
        // Allow: backspace, delete, tab, escape, enter
        if ($.inArray(e.keyCode, [46, 8, 9, 27, 13]) !== -1 ||
            // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
            (e.keyCode === 65 && e.ctrlKey === true) ||
            (e.keyCode === 67 && e.ctrlKey === true) ||
            (e.keyCode === 86 && e.ctrlKey === true) ||
            (e.keyCode === 88 && e.ctrlKey === true)) {
            return;
        }
        // Ensure that it is a number and stop the keypress
        if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105) && e.keyCode !== 190) {
            e.preventDefault();
        }
    });

    // Handle wallet apply button
    $(document).on('click', '.wallet-apply-btn', function() {
        let inputAmount = parseFloat($('.wallet-input').val()) || 0;

        if (inputAmount > 0 && inputAmount <= availableWallet) {
            walletAmount = inputAmount;
            $('.wallet-used-amount').text(formatAmount(inputAmount));
            $('.walletAmount').val(inputAmount);

            // Store in sessionStorage
            sessionStorage.setItem('walletAmount', inputAmount);

            // Update wallet status display
            $('.wallet-applied').show();
            $('.no-wallet').hide();

            closeWalletPopup();
            
            // Use optimized calculation system
            debouncedOptimizedCalculation();

            toastr.success("<?= __('Wallet amount applied successfully') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        } else {
            toastr.error("<?= __('Invalid wallet amount') ?>", '', {
                timeOut: 3000,
                progressBar: true,
            });
        }
    });

    // Handle remove wallet
    $(document).on('click', '.removeWallet', function() {
        walletAmount = 0;
        $('.wallet-used-amount').text('0');
        $('.walletAmount').val('0');
        $('.wallet-input').val('');

        // Remove from sessionStorage
        sessionStorage.removeItem('walletAmount');

        // Update wallet status display
        $('.wallet-applied').hide();
        $('.no-wallet').show();

        // Debug logging
        console.log('Wallet removed, sessionStorage walletAmount:', sessionStorage.getItem('walletAmount'));
        console.log('Current loyalty points:', storage.get('redeemPoint', 0));

        // Use optimized calculation system
        debouncedOptimizedCalculation();

        toastr.info("<?= __('Wallet amount removed') ?>", '', {
            timeOut: 3000,
            progressBar: true,
        });
    });

    // z-index
    // Removed duplicate loyaltyPointsCheck handler that was causing navbar issues
    
    $(document).on('click', '.walletPointsCheck', function () {
        let nav = document.querySelector('.navbar.fixed-top');
        let noFixNav = document.querySelector(".navbar");
        let body = document.querySelector("body");
        if (nav) {
            nav.style.setProperty('position', 'relative', 'important');
            nav.style.setProperty('z-index', '0', 'important');
            noFixNav.style.setProperty('z-index', '0', 'important');
            body.style.setProperty('overflow','hidden');
        }
    });
    // Load wallet amount from sessionStorage on page load
    $(document).ready(function() {
        let storedWalletAmount = sessionStorage.getItem('walletAmount');
        if (storedWalletAmount && parseFloat(storedWalletAmount) > 0) {
            walletAmount = parseFloat(storedWalletAmount);
            $('.wallet-used-amount').text(formatAmount(walletAmount));
            $('.walletAmount').val(walletAmount);
            $('.wallet-input').val(walletAmount);

            // Update wallet status display
            $('.wallet-applied').show();
            $('.no-wallet').hide();
        } else {
            // Show no wallet applied status
            $('.wallet-applied').hide();
            $('.no-wallet').show();
        }
        
        // Initialize Wave payment method visibility based on current total
        setTimeout(function() {
            updateWavePaymentVisibility();
        }, 100);
    });

    // Close wallet popup when clicking on overlay
    $(document).on('click', '#wallet-overlay', function() {
        closeWalletPopup();
    });

    // Prevent closing when clicking inside the popup
    $(document).on('click', '#wallet-popup', function(e) {
        e.stopPropagation();
    });

    // Update wallet popup when loyalty points or coupons change
    function updateWalletPopupIfOpen() {
        if ($('#wallet-popup').is(':visible')) {
            updateMaxUsableAmount();
            // Trigger input validation to update the display
            $('.wallet-input').trigger('input');
        }

        // Also check if current wallet amount is still valid
        let currentWalletAmount = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
        if (currentWalletAmount > 0) {
            let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
            let redeemPoint = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
            let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
            let subAmount = <?= $totalPrice ?>;
            let totalBeforeWallet = subAmount - (couponAmount + redeemPoint) + delivery_charge;
            let maxUsable = Math.min(availableWallet, Math.max(0, totalBeforeWallet));

            // If current wallet amount exceeds the new maximum, adjust it
            if (currentWalletAmount > maxUsable) {
                if (maxUsable > 0) {
                    // Adjust to maximum usable amount
                    sessionStorage.setItem('walletAmount', maxUsable);
                    $('.wallet-used-amount').text(formatAmount(maxUsable));
                    $('.walletAmount').val(maxUsable);
                    $('.wallet-input').val(maxUsable);
                    walletAmount = maxUsable;

                    toastr.info("<?= __('Wallet amount adjusted due to other discounts') ?>", '', {
                        timeOut: 3000,
                        progressBar: true,
                    });
                } else {
                    // Remove wallet amount completely
                    sessionStorage.removeItem('walletAmount');
                    $('.wallet-used-amount').text('0');
                    $('.walletAmount').val('0');
                    $('.wallet-input').val('');
                    $('.wallet-applied').hide();
                    $('.no-wallet').show();
                    walletAmount = 0;

                    toastr.info("<?= __('Wallet amount removed due to other discounts covering full amount') ?>", '', {
                        timeOut: 3000,
                        progressBar: true,
                    });
                }
                updateTotalAmount();
            }
        }
    }

    // Smart function to adjust wallet amount for optimal discount application
    function adjustWalletForOptimalDiscounts() {
        let currentWalletAmount = parseFloat(sessionStorage.getItem('walletAmount')) || 0;

        if (currentWalletAmount > 0) {
            // Get discount amounts from correct storage locations
            let couponAmount = parseFloat(storage.get('coupon_amount', 0)) || 0;
            let redeemPoint = parseFloat(storage.get('redeemPoint', 0)) || 0;
            let delivery_charge = parseFloat(storage.get('delivery_charge', 0)) || 0;
            let subAmount = <?= $totalPrice ?>;

            console.log('adjustWalletForOptimalDiscounts values:', {
                currentWalletAmount, couponAmount, redeemPoint, delivery_charge, subAmount
            });

            // Calculate what the total would be without wallet
            let totalWithoutWallet = subAmount - (couponAmount + redeemPoint) + delivery_charge;

            // If there are other discounts (coupon or loyalty points), prioritize them
            if (couponAmount > 0 || redeemPoint > 0) {
                // Calculate optimal wallet amount: use wallet only for the remaining amount
                let optimalWalletAmount = Math.min(availableWallet, Math.max(0, totalWithoutWallet));

                console.log('Wallet optimization:', {
                    totalWithoutWallet, optimalWalletAmount, availableWallet
                });

                // Only adjust if the current wallet amount is different from optimal
                if (Math.abs(currentWalletAmount - optimalWalletAmount) > 0.01) { // Use small epsilon for comparison
                    if (optimalWalletAmount > 0) {
                        // Adjust wallet to optimal amount
                        sessionStorage.setItem('walletAmount', optimalWalletAmount.toString());
                        $('.wallet-used-amount').text(formatAmount(optimalWalletAmount));
                        $('.walletAmount').val(optimalWalletAmount);
                        $('.wallet-input').val(optimalWalletAmount);
                        walletAmount = optimalWalletAmount;

                        // Show appropriate message based on what happened
                        if (optimalWalletAmount < currentWalletAmount) {
                            let savedAmount = currentWalletAmount - optimalWalletAmount;
                            toastr.info("<?= __('Wallet adjusted to ') ?>" + formatAmount(optimalWalletAmount) + " <?= __(' FCFA to allow coupon discount. Saved ') ?>" + formatAmount(savedAmount) + " <?= __(' FCFA in your wallet!') ?>", '', {
                                timeOut: 4000,
                                progressBar: true,
                            });
                        } else {
                            toastr.info("<?= __('Wallet amount increased to ') ?>" + formatAmount(optimalWalletAmount) + " <?= __(' FCFA') ?>", '', {
                                timeOut: 3000,
                                progressBar: true,
                            });
                        }
                    } else {
                        // Remove wallet amount completely if other discounts cover everything
                        sessionStorage.removeItem('walletAmount');
                        $('.wallet-used-amount').text('0');
                        $('.walletAmount').val('0');
                        $('.wallet-input').val('');
                        $('.wallet-applied').hide();
                        $('.no-wallet').show();
                        walletAmount = 0;

                        toastr.info("<?= __('Wallet amount removed as coupon covers the full amount. Your wallet balance is preserved!') ?>", '', {
                            timeOut: 4000,
                            progressBar: true,
                        });
                    }
                    // Use debounced update to prevent multiple rapid calls
                    debouncedUpdateTotalAmount();
                    
                    // Force payment button update after adjustment
                    setTimeout(forceUpdatePaymentButton, 200);
                }
            }
        }
    }

    // Hook into existing loyalty points and coupon functions
    // Override the existing updateTotalAmount to also update wallet popup
    const originalUpdateTotalAmount = window.updateTotalAmount;
    window.updateTotalAmount = function() {
        if (originalUpdateTotalAmount) {
            originalUpdateTotalAmount();
        }
        updateWalletPopupIfOpen();
        
        // Force update the payment button with latest total after a small delay
        setTimeout(function() {
            let finalAmount = storage.get('final_amount', 0);
            $('#payment-confirm').html("Pay " + formatAmount(finalAmount) + " FCFA");
            
            // Update Wave payment visibility when payment button is updated
            setTimeout(function() {
                updateWavePaymentVisibility();
            }, 50);
            console.log('Payment button updated with final amount:', finalAmount);
        }, 50);
    };
</script>
<script>

    $(document).on('.checkCouponCode', function () {
        let couponName = $(".couponName").val();

        applyOffers(couponName, <?= $totalPrice ?>)
            .then((res) => {
                if (res.status == 'success' || res.status == 200) {
                    $(".appliedCoupon").html(couponName);
                    $(".offerApplied").html(res.data.coupon_amount);

                    sessionStorage.setItem('coupon_amount', res.data.coupon_amount);
                    sessionStorage.setItem('appliedCoupon', couponName);
                    updateTotalAmount();
                    // Update wallet popup if it's open
                    updateWalletPopupIfOpen();
                    toastr.success(res.message, '', {
                        timeOut: 3000,
                        progressBar: true,
                        onHidden: function () {
                        }
                    });
                } else {
                    toastr.warning(res.message, '', {
                        timeOut: 3000,
                        progressBar: true,
                        onHidden: function () {
                            sessionStorage.removeItem('coupon_amount');
                            sessionStorage.removeItem('appliedCoupon');
                            updateTotalAmount();
                            window.location.reload();
                        }
                    });
                }
            })
            .catch((error) => {
                toastr.error(error, 'Error')
            });
    });

    function applyOffers(couponName, amt) {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'applyoffer']) ?>",
                type: 'POST',
                data: {'coupon_code': couponName, 'subTotal': amt},
                success: function (response) {
                    if (response.status) {


                        resolve(response);
                    } else {
                        reject(response);
                    }
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    $(document).ready(function () {
        // Check sessionStorage for appliedCoupon and coupon_amount
        let appliedCoupon = sessionStorage.getItem('appliedCoupon');
        let couponAmount = sessionStorage.getItem('coupon_amount');

        if (appliedCoupon && couponAmount) {
            // Update UI with stored values
            $(".appliedCoupon").html(appliedCoupon);
            $(".couponName").val(appliedCoupon);
            $(".offerApplied").html(couponAmount);
        } else {
            // Reset UI if no values in sessionStorage
            $(".appliedCoupon").html('');
            $(".offerApplied").html('0');
        }
    });
</script>
<script>
    $(document).ready(function () {
        updateTotalAmount();
    });

    // This function is defined later in the file - see the consolidated version below

</script>
<script>
    $(document).ready(function () {
        // When the radio button for "Pickup from showroom" is selected
        $('#pickup-from-showroom').on('change', function () {
            if ($(this).prop('checked')) {
                $('#pickup-div').fadeIn(300);  // Show Pickup div with fade effect
                $('#delivery-div').hide();  // Hide Delivery div
                
                // Hide shipping sections for pickup
                $('.shipping-section').addClass('hidden disabled');
                
                // Reset delivery charge to 0 for pickup
                sessionStorage.setItem('delivery_charge', 0);
                $(".delivery-state").text('Free');
                updateTotalAmount();

                // Add pulse animation to the selected option
                const label = $(this).next('.delivery-option-content');
                label.addClass('pulse-animation');
                setTimeout(() => {
                    label.removeClass('pulse-animation');
                }, 500);
            }
        });

        // When the radio button for "Deliver to address" is selected
        $('#deliver-to-address').on('change', function () {
            if ($(this).prop('checked')) {
                $('#delivery-div').fadeIn(300);  // Show Delivery div with fade effect
                $('#pickup-div').hide();  // Hide Pickup div
                
                // Show shipping sections for home delivery
                $('.shipping-section').removeClass('hidden disabled');

                // Add pulse animation to the selected option
                const label = $(this).next('.delivery-option-content');
                label.addClass('pulse-animation');
                setTimeout(() => {
                    label.removeClass('pulse-animation');
                }, 500);
            }
        });

        // Make sure clicking on the label selects the radio button
        $('.delivery-option-content').on('click', function(e) {
            // Select the radio button
            const radio = $(this).prev('input[type="radio"]');
            radio.prop('checked', true).trigger('change');
        });

        // Set the proper initial state without triggering change events
        let deliveryOption = $('input[name="delivery_option"]:checked').val();
        if (deliveryOption === 'pickup') {
            $('#pickup-div').show();
            $('#delivery-div').hide();
            // Hide shipping sections for pickup
            $('.shipping-section').addClass('hidden disabled');
        } else {
            $('#delivery-div').show();
            $('#pickup-div').hide();
            // Show shipping sections for home delivery
            $('.shipping-section').removeClass('hidden disabled');
        }


    });

    // Client-side showroom data handling with infinite scroll
    let allShowrooms = []; // Store all showrooms data
    let filteredShowrooms = [];
    let displayedCount = 0;
    const itemsPerBatch = 9; // Number of showrooms to load per batch
    let isLoading = false;
    let allLoaded = false;

    // Load all showroom data at once
    $(document).ready(function() {
        // Show loading indicator with animation
        $('#loading-message').show();

        // Fetch all showroom data at once
        $.ajax({
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'getCartShowRoomAddress']) ?>",
            method: 'post',
            data: {
                search_str: '',
                city_name: ''
            },
            dataType: 'json',
            success: function(response) {
                $('#loading-message').hide();

                if (response.status === 'success') {
                    // Store all showrooms for client-side filtering
                    allShowrooms = response.data;
                    filteredShowrooms = [...allShowrooms];

                    // Initial render
                    filterShowrooms('');

                    // Setup scroll event for infinite scrolling
                    setupInfiniteScroll();
                } else {
                    $('#showroom-list').html('<div style="padding:20px; text-align:center;">No showrooms found.</div>');
                }
            },
            error: function() {
                $('#loading-message').hide();
                $('#showroom-list').html('<div style="padding:20px; text-align:center; color:red;">Failed to load showrooms. Please try again.</div>');
            }
        });

        // Focus on search input when clicking the search container
        $('.showroom-search-container').on('click', function() {
            $('#showroom-search').focus();
        });
    });

    // Setup infinite scroll
    function setupInfiniteScroll() {
        // Add scroll event listener to the parent container
        $('#pickup-div').on('scroll', function() {
            const scrollHeight = $(this)[0].scrollHeight;
            const scrollTop = $(this).scrollTop();
            const clientHeight = $(this).height();

            // If we're near the bottom (within 200px) and not currently loading
            if (!isLoading && !allLoaded && scrollTop + clientHeight > scrollHeight - 200) {
                loadMoreShowrooms();
            }
        });

        // Also listen for window scroll for cases where the container doesn't have its own scrollbar
        $(window).on('scroll', function() {
            if (!$('#pickup-div').is(':visible')) return;

            const rect = document.getElementById('showroom-list').getBoundingClientRect();
            const windowHeight = window.innerHeight;

            // If the bottom of the showroom list is visible and we're not loading
            if (!isLoading && !allLoaded && rect.bottom <= windowHeight + 200) {
                loadMoreShowrooms();
            }
        });
    }

    // Filter showrooms based on search term
    function filterShowrooms(searchTerm) {
        searchTerm = searchTerm.toLowerCase().trim();

        if (searchTerm === '') {
            // If no search term, use all showrooms
            filteredShowrooms = [...allShowrooms];
        } else {
            // Filter showrooms based on search term
            filteredShowrooms = allShowrooms.filter(showroom =>
                (showroom.name && showroom.name.toLowerCase().includes(searchTerm)) ||
                (showroom.address && showroom.address.toLowerCase().includes(searchTerm)) ||
                (showroom.city && showroom.city.city_name && showroom.city.city_name.toLowerCase().includes(searchTerm))
            );
        }

        // Reset display state
        displayedCount = 0;
        allLoaded = false;

        // Clear the current list
        $('#showroom-list').empty();

        // Load the first batch
        loadMoreShowrooms();
    }

    // Load more showrooms
    function loadMoreShowrooms() {
        if (isLoading || allLoaded) return;

        isLoading = true;

        // Show loading indicator at the bottom
        if ($('#load-more-indicator').length === 0) {
            $('#showroom-list').after('<div id="load-more-indicator" style="text-align:center; padding:15px;"><i class="fa fa-spinner fa-spin"></i> Loading more showrooms...</div>');
        } else {
            $('#load-more-indicator').show();
        }

        // Simulate network delay for smoother UX (remove in production if not needed)
        setTimeout(function() {
            const startIndex = displayedCount;
            const endIndex = Math.min(startIndex + itemsPerBatch, filteredShowrooms.length);
            const selectedShowroom = sessionStorage.getItem('showroom_id') || '';

            if (startIndex >= filteredShowrooms.length) {
                // All showrooms have been loaded
                allLoaded = true;
                $('#load-more-indicator').hide();
                isLoading = false;
                return;
            }

            const showroomsToShow = filteredShowrooms.slice(startIndex, endIndex);

            if (showroomsToShow.length === 0) {
                if (startIndex === 0) {
                    $('#showroom-list').html('<div style="padding:20px; width:100%; text-align:center; grid-column: 1 / -1;">No showrooms match your search.</div>');
                }
                allLoaded = true;
                $('#load-more-indicator').hide();
                isLoading = false;
                return;
            }

            // Build HTML for this batch
            let showroomHTML = '';

            // Create HTML for each showroom card
            showroomsToShow.forEach(showroom => {
                const isSelected = selectedShowroom == showroom.id ? 'selected' : '';
                showroomHTML += `
                    <div class="showroom-item ${isSelected}" data-showroom-city-id="${showroom.city_id}" data-showroom-id="${showroom.id}">
                        <div class="showroom-item-content">
                            <div class="showroom-item-header">
                                <div class="showroom-radio-container">
                                    <input type="radio" class="showroom-radio" name="showroom"
                                           id="showroom-${showroom.id}" value="${showroom.id}"
                                           ${isSelected ? 'checked' : ''}>
                                    <label class="pop-up-name" for="showroom-${showroom.id}">
                                        ${showroom.name || 'Unnamed Showroom'}
                                        <span class="pop-up-showroom-type">Showroom</span>
                                    </label>
                                </div>
                                <div class="showroom-actions">
                                    <a href="https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(showroom.address || '')}"
                                       target="_blank" class="view-map-btn" title="View on Map">
                                        <i class="fa fa-map"></i>
                                        <span class="action-text">Map</span>
                                    </a>
                                </div>
                            </div>
                            <div class="showroom-details">
                                <div class="showroom-line">
                                    <i class="fa fa-store showroom-icon"></i>
                                    ${showroom.address || 'Address not available'}
                                </div>
                                <div class="showroom-line">
                                    <i class="fa fa-city showroom-icon"></i>
                                    ${showroom.city ? showroom.city.city_name : 'N/A'}
                                </div>
                                <div class="showroom-phone">
                                    <i class="fa fa-phone showroom-icon"></i>
                                    <a href="tel:${showroom.contact_number || ''}" class="showroom-phone-link">
                                        <span>${showroom.contact_number || 'N/A'}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Append to the DOM (not replace)
            $('#showroom-list').append(showroomHTML);

            // Update displayed count
            displayedCount = endIndex;

            // Check if all items have been loaded
            if (displayedCount >= filteredShowrooms.length) {
                allLoaded = true;
            }

            // Hide loading indicator
            $('#load-more-indicator').hide();
            isLoading = false;
        }, 300); // Small delay for better UX
    }

    // Search input handler with debounce
    let searchTimeout;
    $('#showroom-search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            filterShowrooms($(this).val());
        }, 300); // 300ms debounce delay
    });

    // Make showroom item clickable and highlight selection
    $(document).on('click keypress', '.showroom-item', function (e) {
        if (e.type === 'click' || (e.type === 'keypress' && (e.which === 13 || e.which === 32))) {
            // Remove selection from all
            $('.showroom-item').removeClass('selected');
            // Add selection to clicked
            $(this).addClass('selected');
            // Cache the jQuery element for reuse
            const selectedElement = $(this);
            // Set radio checked
            $(this).find('.showroom-radio').prop('checked', true);
            // Use attr to reliably read the data attribute
           // alert('new ' + selectedElement.attr('data-showroom-city-id'));
            // Store in sessionStorage
            const showroomId = $(this).data('showroom-id');
            sessionStorage.setItem('showroom_id', showroomId);
            sessionStorage.setItem('customer_address_id', '');
            sessionStorage.setItem('city_id', '');
            sessionStorage.setItem('checkDeliveryTypeCheckedCheckbox', 'pickup');
            // sessionStorage.setItem('city_id', selectedElement.attr('data-showroom-city-id'));
          
            // Find the showroom name for better user feedback
            const showroomName = $(this).find('.pop-up-name').text().trim();

            // Show a confirmation toast
            if (typeof toastr !== 'undefined') {
                toastr.success('Showroom selected successfully', '', {
                    timeOut: 2000,
                    progressBar: true
                });
            }

            // Trigger change event for delivery logic if needed
            $(this).find('.showroom-radio').trigger('change');
        }
    });
</script>

<script>
    // checkout page start
    $(document).ready(function () {

        // Show the popup
        $('#ax-open-popup').on('click', function () {
            $('#ax-popup-overlay').fadeIn();
            $('#ax-popup').fadeIn();
        });

        // Close the popup
        $('#ax-close-popup, #ax-popup-overlay').on('click', function () {
            $('#ax-popup-overlay').fadeOut();
            $('.ax-popup').fadeOut();
        });

        let delivery_mode_type = sessionStorage.getItem('delivery_mode_type') || '';



        $('.delivery_mode_type').on('change', function () {
            sessionStorage.setItem('delivery_mode_type', $(this).val());
            
            // Trigger delivery charge recalculation with optimized pricing
            if (!window.initialPageLoad) {
                console.log('Delivery mode changed to:', $(this).val());
                debouncedDeliveryCalculation();
            }
        });

        if (delivery_mode_type === 'express') {
            // Set checked without triggering click event
            $('input[name="delivery_mode_type"][value="express"]').prop('checked', true);
            sessionStorage.setItem('delivery_mode_type', 'express');
        } else {
            // Set checked without triggering click event
            $('input[name="delivery_mode_type"][value="standard"]').prop('checked', true);
            sessionStorage.setItem('delivery_mode_type', 'standard');
        }

        let checkDeliveryTypeCheckedCheckbox = sessionStorage.getItem('checkDeliveryTypeCheckedCheckbox') || '';

        if (checkDeliveryTypeCheckedCheckbox === 'pickup') {
            // Set checked without triggering click event
            $('#pickup-from-showroom').prop('checked', true);
            // Show/hide appropriate divs manually
            $('#pickup-div').show();
            $('#delivery-div').hide();
            let showroom_id = sessionStorage.getItem('showroom_id') || '';
            if(showroom_id){
                setTimeout(function () {
                    $('#container').append('<input type="radio" name="showroom" value="' + showroom_id + '" id="showroom-"' + showroom_id + '"">');
                    $('input[name="showroom"][value="' + showroom_id + '"]').prop('checked', true);
                }, 1000); // Adjust this to match your load delay


            }
        } else if (checkDeliveryTypeCheckedCheckbox === 'delivered') {
            // Set checked without triggering click event
            $('#deliver-to-address').prop('checked', true);
            // Show/hide appropriate divs manually
            $('#delivery-div').show();
            $('#pickup-div').hide();
            let customer_address_id = sessionStorage.getItem('customer_address_id') || '';
            if(customer_address_id){
                // Set checked without triggering click event
                $('input[name="address"][value="' + customer_address_id + '"]').prop('checked', true);
            }
        }

    });
</script>
<script>
    $(document).on('click', '#payment-confirm', function () {
        // Select the checked radio button
        const checkedRadio = $('input[name="method"]:checked');

        if (!checkedRadio.length) { 
            toastr.warning('Please select a payment method', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }
        let delivery_option = $('input[name="delivery_option"]:checked').val();
        if(delivery_option == 'delivery'){
            if($('input[name="address"]:checked').length == 0){
                toastr.warning('Please select a delivery address', '', {
                timeOut: 3000,
                progressBar: true
            });
                return;
            }
        } else if(delivery_option == 'pickup'){
            if($('input[name="showroom"]:checked').length == 0){
                toastr.warning('<?= __('Please select a showroom to pickup from.') ?>', '', {
                timeOut: 3000,
                progressBar: true
            });
                return;
            }
        }
        // Check if mobile number is required and provided for MTN MoMo
        const paymentMethod = checkedRadio.val();
        let mobileNo = '';

        if (paymentMethod === 'MTN MoMo') {
            const countryCode = $('#ax-country-select').val().trim();
            const phoneNumber = $('#ax-mobile-input').val().trim();

            if (!phoneNumber) {
                toastr.warning('Please enter your mobile number for MTN MoMo payment', '', {
                    timeOut: 3000,
                    progressBar: true
                });
                $('#ax-mobile-input-error').text('Please enter your mobile number').show();
                return;
            }

            // Basic validation for mobile number format
            if (!/^\d{10,15}$/.test(phoneNumber)) {
                toastr.warning('Please enter a valid mobile number (10-15 digits)', '', {
                    timeOut: 3000,
                    progressBar: true
                });
                $('#ax-mobile-input-error').text('Please enter a valid mobile number (10-15 digits)').show();
                return;
            }

            // Format the mobile number with country code
            mobileNo =  phoneNumber;
        }

        // Get cart items from order summary section
        const cartItems = [];
        let calculatedTotal = 0;

        $('.my-order-item-details').each(function () {
            const itemContainer = $(this);
            const itemId = itemContainer.find('.updateCartItem').first().data('item-id');
            const itemName = itemContainer.find('.my-order-item-details-title').text().trim();
            const qty = parseInt(itemContainer.find('.cartItemQty').text().trim(), 10);
            const price = Math.max(0, parseFloat(itemContainer.find('.cart_items_list_price').val()));
            const category_name = itemContainer.find('.cart_items_list_category_name').val();
            const product_id = itemContainer.find('.cart_items_list_id').val();
            const brand_name = itemContainer.find('.cart_items_list_brand_name').val();

            if (itemId && qty && price) {
                const itemTotal = Math.max(0, qty * price); // Ensure item total is not negative
                cartItems.push({
                    cart_item_id: itemId,
                    product_id: product_id,
                    quantity: qty,
                    itemName: itemName,
                    price: price,
                    category_name: category_name,
                    brand_name: brand_name,
                    item_total: itemTotal
                });
                calculatedTotal += itemTotal;
            }
        });

        if (cartItems.length === 0) {
            toastr.warning('Your cart is empty', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }
        // Get coupon code if applied (from sessionStorage or input field)
        const couponCode = sessionStorage.getItem('appliedCoupon') || storage.get('appliedCoupon') || $('.couponName').val() || '';
        // Get redeem points if applied (from sessionStorage)
        const redeemPoints = parseFloat(sessionStorage.getItem('redeemPoint')) || 0;
        // Get the total price from PHP
        const serverTotal = Math.max(0, <?= $totalPrice ?>);
        // Verify if calculated total matches server total (both should be non-negative)
     
        if (Math.abs(calculatedTotal - serverTotal) > 0.01 || calculatedTotal < 0 || serverTotal < 0) {
            toastr.error('Price mismatch detected. Please refresh the page.', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }
        const createOrder = '<?= $this->Url->build(['controller' => 'Account', 'action' => 'createOrder']) ?>';
        const inputPointVal = parseFloat($(".radeem-input").val());
        const delivery_mode = $('input[name="delivery_option"]:checked').val();

        const orderInfo = {
            wallet: parseFloat(sessionStorage.getItem('walletAmount')) || 0,
            delivery_mode_type: sessionStorage.getItem('delivery_mode_type'),
            payment_method: paymentMethod,
            cart_items: cartItems,
            coupon_code: couponCode,
            coupon_amount: parseFloat(sessionStorage.getItem('coupon_amount')) || parseFloat(storage.get('coupon_amount')) || 0,
            redeem_points: redeemPoints,
            total_amount: serverTotal,
            sub_amount: serverTotal,
            mobile_no: mobileNo,
            final_amount: parseFloat(sessionStorage.getItem('final_amount')) || 0,
            delivery_charge: parseFloat(sessionStorage.getItem('delivery_charge')) || parseFloat(storage.get('delivery_charge')) || 0,
            delivery_mode,
            showroom_id: sessionStorage.getItem('showroom_id'),
            customer_address_id: sessionStorage.getItem('customer_address_id'),
            inputPointVal,
        };
        // Disable the button and show the loader
        const $button = $(this);
        $button.prop('disabled', true).addClass('loading');
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'begin_checkout',
            ecommerce: {
                items: cartItems.map(item => ({
                    item_id: item.product_id,
                    item_name: item.name,
                    price: Number(parseFloat(item.item_total).toFixed(2)),
                    item_category: item.category_name,
                    item_brand: item.brand_name,
                    quantity: item.quantity
                })),
                ...(typeof cartCoupon !== 'undefined' && cartCoupon && { coupon: cartCoupon }),
                ...(typeof cartTotal !== 'undefined' && cartTotal && {
                    value: Number(parseFloat(cartTotal).toFixed(2)),
                    currency: 'XOF'
                })
            }
        });
        ajaxCall('POST', createOrder, orderInfo)
            .then(response => {
                // Hide the loader
                $button.removeClass('loading');

                if (response.status === 'success') {
                    // Clear all discount-related sessionStorage on successful order
                    sessionStorage.removeItem('walletAmount');
                    sessionStorage.removeItem('coupon_amount');
                    sessionStorage.removeItem('appliedCoupon');
                    sessionStorage.removeItem('redeemPoint');
                    sessionStorage.removeItem('delivery_charge');
                    sessionStorage.removeItem('final_amount');
                    storage.remove('walletAmount');
                    storage.remove('coupon_amount');
                    storage.remove('appliedCoupon');
                    storage.remove('redeemPoint');
                    storage.remove('delivery_charge');

                    toastr.success(response.message, '', {
                        timeOut: 3000,
                        progressBar: true,
                        onHidden: function () {
                            if (response.data.payment_method === "Wave") {
                                window.location.href = response.wave_launch_url;
                            } else {
                                window.location.href = response.redirect_url.startsWith('http')
                                    ? response.redirect_url
                                    : searchAjaxUrl + response.redirect_url;
                            }
                        }
                    });
                } else {
                    // Re-enable the button on error
                    $button.prop('disabled', false);

                    toastr.error(response.message, '', {
                        timeOut: 3000,
                        progressBar: true
                    });
                }
            })
            .catch(error => {
                // Hide the loader and re-enable the button on error
                $button.removeClass('loading').prop('disabled', false);

                console.error('Order creation error:', error);
                toastr.error('An error occurred while processing your order', '', {
                    timeOut: 3000,
                    progressBar: true
                });
            });
    });

    // Handle Apply for Credit button click
  /*  $(document).on('click', '#apply-credit-button', function(e) {
        e.preventDefault();


        // Debug: Check current form state
        const deliveryMethod = $('input[name="delivery_option"]:checked').val();
        const selectedAddress = $('input[name="address"]:checked').val();
        const selectedShowroom = $('input[name="showroom"]:checked').val();

        console.log('Current form state:', {
            deliveryMethod: deliveryMethod,
            selectedAddress: selectedAddress,
            selectedShowroom: selectedShowroom
        });

        // Validate required fields before opening credit modal
        if (!validateCheckoutForm()) {
            console.log('Validation failed, not opening modal');
            return;
        }

        // Open the credit application modal
        $('#multistep-form-modal').addClass('show');
        $('body').addClass('loyalty-popup-open');
        console.log('Credit application modal opened');
    });
    */

    // Debug button handler
    $(document).on('click', '#debug-form-data', function(e) {
        e.preventDefault();
        console.log('=== FORM DEBUG INFO ===');

        // Check credit partner selection
        const creditPartnerInputs = $('input[name="credit_partner"]');
        const checkedCreditPartner = $('input[name="credit_partner"]:checked');

        console.log('Credit Partner Debug:', {
            total_inputs: creditPartnerInputs.length,
            checked_count: checkedCreditPartner.length,
            checked_value: checkedCreditPartner.val(),
            all_values: creditPartnerInputs.map(function() { return this.value; }).get()
        });

        // Check EMI plan selection
        const emiPlanInputs = $('input[name="emi_plan"]');
        const checkedEmiPlan = $('input[name="emi_plan"]:checked');

        console.log('EMI Plan Debug:', {
            total_inputs: emiPlanInputs.length,
            checked_count: checkedEmiPlan.length,
            checked_value: checkedEmiPlan.val()
        });

        // Check form fields
        console.log('Form Fields Debug:', {
            registration_id: $('#registration_id').val(),
            full_name: $('#full_name').val(),
            email: $('#email').val(),
            terms_checked: $('#terms_agreement').is(':checked')
        });

        console.log('=== END DEBUG INFO ===');
    });

    // Test credit partner selection button
    $(document).on('click', '#test-credit-partner', function(e) {
        e.preventDefault();
        console.log('=== TESTING CREDIT PARTNER SELECTION ===');

        // Find first credit partner and select it
        const firstPartner = $('input[name="credit_partner"]').first();
        if (firstPartner.length > 0) {
            console.log('Selecting first credit partner:', firstPartner.val());

            // Clear all selections first
            $('input[name="credit_partner"]').prop('checked', false);
            $('.credit-partner-card').removeClass('selected');

            // Select the first one
            firstPartner.prop('checked', true);
            firstPartner.closest('.credit-partner-card').addClass('selected');
            firstPartner.trigger('change');

            console.log('Credit partner selected. Checking status:', firstPartner.is(':checked'));

            // Also select first EMI plan if available
            const firstPlan = $('input[name="emi_plan"]').first();
            if (firstPlan.length > 0) {
                console.log('Selecting first EMI plan:', firstPlan.val());
                $('input[name="emi_plan"]').prop('checked', false);
                $('.emi-plan-card').removeClass('selected');
                firstPlan.prop('checked', true);
                firstPlan.closest('.emi-plan-card').addClass('selected');
                firstPlan.trigger('change');
            }

            toastr.success('Test selection completed!', '', { timeOut: 2000 });
        } else {
            console.error('No credit partner inputs found');
            toastr.error('No credit partners found!', '', { timeOut: 2000 });
        }

        console.log('=== END TEST ===');
    });

    // Show/hide mobile number input based on payment method
    $(document).on('change', 'input[name="method"]', function () {
        const selectedMethod = $(this).val();
        console.log('Payment method changed to:', selectedMethod);

        // Handle Credit payment method
        if (selectedMethod === 'Credit') {
            // Show Apply for Credit button and hide Pay button
            $('#payment-confirm').hide();
            $('#apply-credit-button').show();
            console.log('Credit payment method selected - showing credit application button');
        } else {
            // Show Pay button and hide Apply for Credit button
            $('#apply-credit-button').hide();
            $('#payment-confirm').show();
            console.log('Non-credit payment method selected - showing pay button');
        }

        // Force hide the mobile input with multiple approaches
        $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');

        if (selectedMethod === 'MTN MoMo') {
            // Force show for MTN MoMo
            $('#momo-mobile-input').show().css('display', 'flex').addClass('show-input').attr('style', 'display: flex !important');
            console.log('MTN MoMo selected, showing mobile input');
        } else {
            // Force hide for other methods
            $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');
            // Clear any error messages when hiding the input
            $('#ax-mobile-input-error').hide();
            console.log('Other method selected, hiding mobile input');
        }

        // Debug check
        setTimeout(function() {
            console.log('Mobile input display style:', $('#momo-mobile-input').css('display'));
            console.log('Mobile input has show-input class:', $('#momo-mobile-input').hasClass('show-input'));
            console.log('Mobile input style attribute:', $('#momo-mobile-input').attr('style'));
        }, 100);
    });

    // Add click handler for payment method labels to ensure the change event is triggered
    $(document).on('click', '.pay-with-card-i-l label', function() {
        const radioId = $(this).attr('for');
        $('#' + radioId).prop('checked', true).trigger('change');
    });

    // Handle input on mobile number field to clear error message
    $(document).on('input', '#ax-mobile-input', function() {
        $('#ax-mobile-input-error').hide();
    });

    // Handle country code change
    $(document).on('change', '#ax-country-select', function() {
        $('#ax-mobile-input-error').hide();
    });

    // Trigger change event on page load for default selected method
    $(document).ready(function () {
        // Initialize Select2 for country code dropdowns
        // try {
        //     // Initialize Select2 for MTN MoMo country code dropdown
        //     $('#ax-country-select').select2({
        //         minimumResultsForSearch: 0,
        //         allowClear: false,
        //         placeholder: 'Select'
        //     });

        //     // Set country code based on PHP variable or default to 237
        //     var countryCode = '<?= $mtn_country_code ?>' || '225';
        //     $('#ax-country-select').val(countryCode).trigger('change');
        //     console.log('Set country code to: ' + countryCode);

        //     // Initialize Select2 for address form country code dropdown
        //     $('#address-country-select').select2({
        //         minimumResultsForSearch: 0,
        //         allowClear: false,
        //         placeholder: 'Select',
        //         dropdownParent: $('body') // Attach to body to avoid positioning issues
        //     });

        //     // Only set default country code if no value is already selected
        //     // This preserves any existing country code from a saved address
        //     if (!$('#address-country-select').val() || $('#address-country-select').val() === '') {
        //         $('#address-country-select').val('237').trigger('change');
        //         console.log('Set default address country code to 237 (no previous value)');
        //     } else {
        //         // Make sure to trigger change event to properly update the Select2 display
        //         $('#address-country-select').trigger('change');
        //         console.log('Keeping existing address country code: ' + $('#address-country-select').val());
        //     }

        //     // Format country code options
        //     function formatCountryCode(state) {
        //         if (!state.id) return state.text;
        //         return $('<span style="font-weight: 500;">+' + state.id + '</span>');
        //     }

        //     console.log('Select2 initialized for country code dropdowns');
        // } catch (e) {
        //     console.error('Error initializing Select2:', e);
        // }

        $(".country-code-dropdown").select2({
            templateResult: formatState,
            templateSelection: formatState
        });
        function formatState (state) {
            if (!state.id) {
                return state.text;
            }
            var baseUrl = "/assets";
            var fileName = state.text
            .replace(/[()]/g, '')           // Remove parentheses
            .replace(/[0-9+]/g, "")
            .trim()
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/-+/g, "-");
            var $state = $(
                '<span><img src="' + baseUrl + '/' + fileName + '.png" class="img-flag" /> ' + state.text + '</span>'
            );
            // Use .text() instead of HTML string concatenation to avoid script injection issues
            $state.find("span").text(state.text);
            $state.find("img").attr("src", baseUrl + "/" + fileName + ".png");

            return $state;
        };

        // Check if MTN MoMo is selected and show the mobile input if it is
        const selectedMethod = $('input[name="method"]:checked').val();
        console.log('Initial payment method:', selectedMethod);

        // Force hide mobile input by default
        $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');

        if (selectedMethod === 'MTN MoMo') {
            // Force show for MTN MoMo
            $('#momo-mobile-input').show().css('display', 'flex').addClass('show-input').attr('style', 'display: flex !important');
            console.log('MTN MoMo selected initially, showing mobile input');
        } else {
            // Force hide for other methods
            $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');
            console.log('Other method selected initially, hiding mobile input');
        }

        // Debug check
        setTimeout(function() {
            console.log('Initial mobile input display style:', $('#momo-mobile-input').css('display'));
            console.log('Initial mobile input has show-input class:', $('#momo-mobile-input').hasClass('show-input'));
            console.log('Initial mobile input style attribute:', $('#momo-mobile-input').attr('style'));
        }, 100);

        // Manually trigger change event for the selected payment method
        $('input[name="method"]:checked').trigger('change');
    });

// =============================================================================
// OPTIMIZED DELIVERY CALCULATION SYSTEM
// =============================================================================

// =============================================================================
// OPTIMIZED DELIVERY CALCULATION SYSTEM
// =============================================================================
// 
// This system provides:
// 1. Centralized delivery charge calculation with proper debouncing
// 2. Integration with optimized price calculation system  
// 3. Prevention of duplicate calculations during delivery mode changes
// 4. Proper handling of pickup vs delivery modes
// 5. Real-time price updates when delivery mode changes
//
// Key Features:
// - debouncedDeliveryCalculation(): Prevents multiple rapid calls
// - calculateOptimizedDeliveryCharge(): Handles all delivery charge logic
// - Automatic integration with debouncedOptimizedCalculation()
// - Proper storage sync between sessionStorage and storage helper
// =============================================================================

// Debounced delivery calculation to prevent multiple rapid calls
let deliveryCalculationTimeout = null;
function debouncedDeliveryCalculation() {
    if (deliveryCalculationTimeout) {
        clearTimeout(deliveryCalculationTimeout);
    }
    deliveryCalculationTimeout = setTimeout(function() {
        calculateOptimizedDeliveryCharge();
        deliveryCalculationTimeout = null;
    }, 200); // 200ms debounce for delivery calculations
}

// Optimized delivery charge calculation
function calculateOptimizedDeliveryCharge() {
    // Skip during initial page load
    if (window.initialPageLoad) {
        console.log('Skipping delivery calculation during page load');
        return;
    }

    let delivery_mode = $('input[name="delivery_option"]:checked').val();
    
    // Handle pickup mode - no delivery charge
    if (delivery_mode === 'pickup') {
        console.log('Pickup mode - setting delivery charge to 0');
        storage.set('delivery_charge', 0);
        sessionStorage.setItem('delivery_charge', 0);
        $(".delivery-state").text('Free');
        
        // Use optimized calculation system
        debouncedOptimizedCalculation();
        return;
    }
    
    // Get customer address for delivery
    let customer_address_id = '';
    let selectedAddress = $('input[name="address"]:checked');
    customer_address_id = selectedAddress.data('city-id') || '';
    
    if (!customer_address_id) {
        console.log('No valid address selected for delivery charge calculation');
        return;
    }

    // Prepare delivery charge calculation data
    const deliveryCharges = '<?= $this->Url->build(["controller" => "Account", "action" => "getDeliveryChargeFromCityNew"]) ?>';
    const jsondata = {
        cityId: customer_address_id,
        delivery_mode: $(".delivery_mode_type:checked").val(),
        weightQuantityArray: <?php echo json_encode($weightQuantityArray); ?>,
        sizeQuantityArray: <?php echo json_encode($sizeQuantityArray); ?>,
    };

    console.log('Calculating optimized delivery charge with data:', jsondata);
    
    // Show loading state
    $(".delivery-state").html('<small>Calculating...</small>');

    // Make AJAX call for delivery charge
    $.ajax({
        headers: {'X-CSRF-Token': '<?= $this->request->getAttribute("csrfToken") ?>'},
        url: deliveryCharges,
        type: 'POST',
        data: jsondata,
        success: function(response) {
            if (response.status === 'success') {
                const prevCharge = parseFloat(storage.get('delivery_charge', 0)) || 0;
                const newCharge = parseFloat(response.total_delivery_charge) || 0;

                // Only update if charge actually changed
                if (prevCharge !== newCharge) {
                    storage.set('delivery_charge', newCharge);
                    sessionStorage.setItem('delivery_charge', newCharge);
                    $(".delivery-state").text(newCharge > 0 ? formatAmount(newCharge) : 'Free');
                    
                    // Use optimized calculation system
                    debouncedOptimizedCalculation();

                    // Adjust wallet for optimal discounts
                    if (typeof adjustWalletForOptimalDiscounts === 'function') {
                        setTimeout(function() {
                            adjustWalletForOptimalDiscounts();
                        }, 250);
                    }

                    // Show success notification
                    if (!window.initialPageLoad) {
                        toastr.success('Shipping charge updated', '', {
                            timeOut: 2000,
                            progressBar: true
                        });
                    }
                } else {
                    // Just update display without notification
                    $(".delivery-state").text(newCharge > 0 ? formatAmount(newCharge) : 'Free');
                }

                console.log('Delivery charge calculated:', newCharge);
            } else {
                storage.set('delivery_charge', 0);
                sessionStorage.setItem('delivery_charge', 0);
                $(".delivery-state").text('Free');
                
                // Use optimized calculation system
                debouncedOptimizedCalculation();

                console.log('Free shipping applied');
            }
        },
        error: function(error) {
            console.error('Delivery charge calculation error:', error);
            $(".delivery-state").text('Error');
            
            // Fallback to no charge
            storage.set('delivery_charge', 0);
            sessionStorage.setItem('delivery_charge', 0);
            debouncedOptimizedCalculation();
        }
    });
}

    // Single event handler for all delivery-related changes
    // Using a debounce variable to prevent multiple simultaneous calls
    let deliveryUpdateTimeout = null;

    // Wallet amount changes are handled in the popup functionality above


    $(document).on('change', 'input[name="delivery_mode_type"],input[name="delivery_option"], input[name="showroom"], input[name="address"]', function () {
        // Skip delivery charge calculation during initial page load
        if (window.initialPageLoad) {
            console.log('Skipping delivery charge calculation during initial page load');
            return;
        }

        // If we're already calculating, don't start another calculation
        if (deliveryUpdateTimeout) {
            clearTimeout(deliveryUpdateTimeout);
            console.log('Cleared pending delivery charge calculation');
        }

        // Set a short timeout to debounce multiple rapid changes
        deliveryUpdateTimeout = setTimeout(function() {
            // Clear the timeout reference
            deliveryUpdateTimeout = null;
            let delivery_mode = $('input[name="delivery_option"]:checked').val();
            const selectedElement = $(event.target);
            const selectedLabel = $('label[for="' + selectedElement.attr('id') + '"]').text();
        
            let triggeredName = selectedElement.attr('name');
            console.log('Delivery option changed:', triggeredName);
            
            // Skip delivery charge calculation if pickup is selected and delivery mode is being changed
            if (delivery_mode === 'pickup' && triggeredName === 'delivery_mode_type') {
                console.log('Skipping delivery charge calculation - pickup mode selected');
                return;
            }

            if (triggeredName === 'address') {
                sessionStorage.setItem('city_id', selectedElement.data('city-id'));
                sessionStorage.setItem('customer_address_id', $('input[name="address"]:checked').val());
                sessionStorage.setItem('showroom_id', '');
            } else if (triggeredName === 'showroom') {
                sessionStorage.setItem('showroom_id', $('input[name="showroom"]:checked').val());
                sessionStorage.setItem('customer_address_id', '');
                sessionStorage.setItem('city_id', '');
                sessionStorage.setItem('showroom-city-id', selectedElement.data('showroom-city-id'));
            }

            if (delivery_mode === 'pickup') {
                sessionStorage.setItem('checkDeliveryTypeCheckedCheckbox', 'pickup');
                // Set delivery charge to 0 for pickup and use optimized calculation
                storage.set('delivery_charge', 0);
                sessionStorage.setItem('delivery_charge', 0);
                $(".delivery-state").text('Free');
                debouncedOptimizedCalculation();
                return;
            } else if (delivery_mode === 'delivery') {
                sessionStorage.setItem('checkDeliveryTypeCheckedCheckbox', 'delivered');
            }

            // Use the new optimized delivery calculation system
            debouncedDeliveryCalculation();
        }, 300); // 300ms debounce delay
    });

    async function ajaxCall(method, url, data = null) {
        try {
            const options = {
                dataType: 'json',
                method: method.toUpperCase(),
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
            };

            if (data && method.toUpperCase() !== 'GET') {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(url, options);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json(); // Assuming the response is JSON
        } catch (error) {
            console.error('Error in AJAX call:', error.message);
            throw error;
        }
    }
</script>


<script>
function checkAndSelectRadio() {
    var cityId = sessionStorage.getItem('city_id');
    var customerAddressId = sessionStorage.getItem('customer_address_id');
    var deliveryCharge = sessionStorage.getItem('delivery_charge');

    if (cityId && customerAddressId) {
        // Find the radio button where value matches customerAddressId and data-city-id matches cityId
        var selectedRadio = $("input[name='address'][data-city-id='" + cityId + "'][value='" + customerAddressId + "']");

        if (selectedRadio.length > 0) {
            // Set checked without triggering change event
            selectedRadio.prop('checked', true);
        }
    }

    // Set delivery charge in .delivery-state
    if (deliveryCharge !== null) {
        $(".delivery-state").html(formatAmount(deliveryCharge));
    }
}


    // Removed duplicate document.ready function for delivery charge

</script>

<?php $this->end(); ?>
<script>
// Check if jQuery is loaded
function checkJquery() {
    if (window.jQuery) {
        // jQuery is loaded, initialize our code
        initializeCheckout();
    } else {
        // jQuery is not loaded yet, wait and try again
        setTimeout(checkJquery, 100);
    }
}

// Define storage object first to avoid reference errors
window.storage = {
    get(key, fallback = null) {
        let v = sessionStorage.getItem(key);
        if (v === null || v === undefined) return fallback;
        if (!isNaN(v) && v !== '') return parseFloat(v);
        return v;
    },
    set(key, val) {
        sessionStorage.setItem(key, val);
    },
    remove(key) {
        sessionStorage.removeItem(key);
    }
};

// =============================================================================
// OPTIMIZED PRICE CALCULATION SYSTEM
// =============================================================================

// Global calculation state to prevent duplicate applications
let calculationInProgress = false;
let lastCalculationValues = null;

// Centralized price calculation function
function calculateOptimizedTotal() {
    // Prevent duplicate calculations
    if (calculationInProgress) {
        console.log('Calculation already in progress, skipping...');
        return;
    }
    
    calculationInProgress = true;
    
    try {
        // First, sanitize any invalid stored values
        sanitizeStoredValues();
        
        // Get base values
        let subAmount = <?= $totalPrice ?>;
        let paybycredit = <?php echo $payByCreditActive; ?>;
        
        // Initialize discount values
        let couponAmount = 0;
        let redeemPoint = 0;
        let walletUsed = 0;
        let delivery_charge = 0;
        
        // If pay by credit is active, reset all discounts
        if (paybycredit) {
            storage.remove('coupon_amount');
            storage.remove('redeemPoint');
            storage.remove('delivery_charge');
            sessionStorage.removeItem('walletAmount');
            
            // Update UI to show no discounts applied
            resetDiscountUI();
            console.log('Pay by credit is active - all discounts reset');
        } else {
            // Get discount values from storage
            couponAmount = parseFloat(storage.get('coupon_amount', 0)) || 0;
            redeemPoint = parseFloat(storage.get('redeemPoint', 0)) || 0;
            walletUsed = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
            delivery_charge = parseFloat(storage.get('delivery_charge', 0)) || 0;
            
            // Handle non-numeric delivery charge
            if (isNaN(delivery_charge) || delivery_charge === 'Free') {
                delivery_charge = 0;
            }
            
            // Debug logging for troubleshooting
            console.log('Raw storage values:', {
                couponAmount_raw: storage.get('coupon_amount', 0),
                redeemPoint_raw: storage.get('redeemPoint', 0),
                walletAmount_raw: sessionStorage.getItem('walletAmount'),
                delivery_charge_raw: storage.get('delivery_charge', 0)
            });
            
            console.log('Parsed values:', {
                couponAmount, redeemPoint, walletUsed, delivery_charge, subAmount
            });
        }
        
        // Validate discount amounts don't exceed limits
        let maxDiscountAmount = subAmount + delivery_charge;
        let totalDiscounts = couponAmount + redeemPoint + walletUsed;
        
        if (totalDiscounts > maxDiscountAmount) {
            console.warn('Total discounts exceed maximum allowed:', {
                totalDiscounts,
                maxDiscountAmount,
                breakdown: { couponAmount, redeemPoint, walletUsed }
            });
            
            // Proportionally reduce discounts to fit within limits
            let reductionRatio = maxDiscountAmount / totalDiscounts;
            couponAmount = Math.floor(couponAmount * reductionRatio);
            redeemPoint = Math.floor(redeemPoint * reductionRatio);
            walletUsed = Math.floor(walletUsed * reductionRatio);
            
            // Update storage with corrected values
            storage.set('coupon_amount', couponAmount);
            storage.set('redeemPoint', redeemPoint);
            sessionStorage.setItem('walletAmount', walletUsed.toString());
        }
        
        // Calculate final total
        let totalAmount = subAmount - (couponAmount + redeemPoint + walletUsed) + delivery_charge;
        
        // Ensure total is never negative
        if (totalAmount < 0) {
            totalAmount = 0;
        }
        
        // Check if values actually changed to prevent unnecessary updates
        let currentValues = {
            subAmount,
            couponAmount,
            redeemPoint,
            walletUsed,
            delivery_charge,
            totalAmount
        };
        
        if (lastCalculationValues && JSON.stringify(currentValues) === JSON.stringify(lastCalculationValues)) {
            console.log('No changes detected, skipping UI update');
            calculationInProgress = false;
            return;
        }
        
        lastCalculationValues = currentValues;
        
        // Update storage with final amount
        storage.set('final_amount', totalAmount);
        
        // Update UI elements
        updatePriceDisplayUI(totalAmount, delivery_charge);
        
        // Log calculation details
        console.log('Optimized calculation complete:', currentValues);
        
    } catch (error) {
        console.error('Error in calculateOptimizedTotal:', error);
    } finally {
        calculationInProgress = false;
    }
}

// Update UI elements with calculated prices
function updatePriceDisplayUI(totalAmount, deliveryCharge) {
    $('.totalAmount').html(formatAmount(totalAmount));
    $('#payment-confirm').html("Pay " + formatAmount(totalAmount) + " FCFA");
    
    // Update Wave payment visibility when payment button is updated
    setTimeout(function() {
        updateWavePaymentVisibility();
    }, 50);
    
    // Update delivery state display
    if (deliveryCharge && deliveryCharge > 0) {
        $(".delivery-state").html(formatAmount(deliveryCharge));
    } else {
        $(".delivery-state").html('-');
    }
    
    // Update individual discount displays with actual applied amounts
    updateDiscountDisplays();
}

// Update individual discount amount displays
function updateDiscountDisplays() {
    // Update loyalty points display
    let redeemPoint = parseFloat(storage.get('redeemPoint', 0)) || 0;
    $(".lyt-reedem-point").html(redeemPoint);
    
    if (redeemPoint > 0) {
        $(".loyalty-applied").show();
        $(".no-loyalty").hide();
    } else {
        $(".loyalty-applied").hide();
        $(".no-loyalty").show();
    }
    
    // Update wallet display
    let walletAmount = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
    $('.wallet-used-amount').text(formatAmount(walletAmount));
    
    if (walletAmount > 0) {
        $('.wallet-applied').show();
        $('.no-wallet').hide();
    } else {
        $('.wallet-applied').hide();
        $('.no-wallet').show();
    }
    
    // Update coupon display if exists
    let couponAmount = parseFloat(storage.get('coupon_amount', 0)) || 0;
    if ($('.coupon-used-amount').length) {
        $('.coupon-used-amount').text(formatAmount(couponAmount));
    }
    
    console.log('Updated discount displays:', {
        loyalty: redeemPoint,
        wallet: walletAmount,
        coupon: couponAmount
    });
}

// Sanitize stored values to prevent display issues
function sanitizeStoredValues() {
    let subAmount = <?= $totalPrice ?>;
    let maxAllowedDiscount = subAmount * 2; // Allow up to 2x order amount as safety buffer
    
    // Check and fix loyalty points
    let redeemPoint = parseFloat(storage.get('redeemPoint', 0)) || 0;
    if (redeemPoint > maxAllowedDiscount) {
        console.warn('Invalid loyalty points detected:', redeemPoint, 'resetting to 0');
        storage.set('redeemPoint', 0);
        $(".lyt-reedem-point").html(0);
        $(".loyalty-applied").hide();
        $(".no-loyalty").show();
    }
    
    // Check and fix wallet amount
    let walletAmount = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
    if (walletAmount > maxAllowedDiscount) {
        console.warn('Invalid wallet amount detected:', walletAmount, 'resetting to 0');
        sessionStorage.removeItem('walletAmount');
        $('.wallet-used-amount').text('0');
        $('.wallet-applied').hide();
        $('.no-wallet').show();
    }
    
    // Check and fix coupon amount
    let couponAmount = parseFloat(storage.get('coupon_amount', 0)) || 0;
    if (couponAmount > maxAllowedDiscount) {
        console.warn('Invalid coupon amount detected:', couponAmount, 'resetting to 0');
        storage.set('coupon_amount', 0);
    }
    
    console.log('Value sanitization complete');
}

// Reset all discount UI elements
function resetDiscountUI() {
    // Reset loyalty UI
    $(".loyalty-applied").hide();
    $(".no-loyalty").show();
    $(".lyt-reedem-point").html('0');
    $(".lytPoints").val('0');
    
    // Reset wallet UI
    $('.wallet-applied').hide();
    $('.no-wallet').show();
    $('.wallet-used-amount').text('0');
    $('.walletAmount').val('0');
    
    // Reset coupon UI (if exists)
    $('.coupon-applied').hide();
    $('.no-coupon').show();
}

// Debounced wrapper for optimized calculation
let optimizedCalculationTimeout = null;
function debouncedOptimizedCalculation() {
    if (optimizedCalculationTimeout) {
        clearTimeout(optimizedCalculationTimeout);
    }
    optimizedCalculationTimeout = setTimeout(function() {
        calculateOptimizedTotal();
        optimizedCalculationTimeout = null;
    }, 150); // Slightly longer debounce for better performance
}

// =============================================================================
// LEGACY FUNCTION COMPATIBILITY
// =============================================================================

// Debounce mechanism for updateTotalAmount
let updateTotalTimeout = null;
function debouncedUpdateTotalAmount() {
    // Redirect to optimized calculation
    debouncedOptimizedCalculation();
}

// Legacy updateTotalAmount function - now uses optimized calculation
function updateTotalAmount() {
    calculateOptimizedTotal();
    updateWavePaymentVisibility(); // Add Wave payment visibility control
}

// Update Wave payment method visibility based on total amount
function updateWavePaymentVisibility() {
    try {
        let finalAmount = parseFloat(storage.get('final_amount', 0)) || 0;
        
        // Also check the pay button value as additional verification
        let payButtonAmount = getPayButtonAmount();
        
        // Use the more restrictive check - if either source shows 0, hide Wave
        let shouldHideWave = (finalAmount <= 0) || (payButtonAmount <= 0);
        
        console.log('Wave visibility check:', {
            finalAmount,
            payButtonAmount,
            shouldHideWave
        });
        
        // Try multiple selectors to find Wave payment method
        let waveMethod = document.querySelector('.payment-method-option[data-method="wave"]') ||
                        document.querySelector('.payment-method-option[data-method="Wave"]') ||
                        document.querySelector('#method_Wave')?.closest('.payment-method-option') ||
                        document.querySelector('input[value="Wave"]')?.closest('div');
        
        if (waveMethod) {
            if (shouldHideWave) {
                // Hide Wave payment method when amount is 0
                waveMethod.style.display = 'none';
                
                // If Wave was selected, select another payment method
                let waveRadio = waveMethod.querySelector('input[type="radio"]') ||
                               document.querySelector('input[value="Wave"]');
                if (waveRadio && waveRadio.checked) {
                    // Find the first visible payment method and select it
                    let firstVisibleMethod = document.querySelector('.payment-method-option:not([style*="display: none"]) input[type="radio"]') ||
                                           document.querySelector('input[name="method"]:not([value="Wave"])');
                    if (firstVisibleMethod) {
                        firstVisibleMethod.checked = true;
                        console.log('Auto-selected alternative payment method:', firstVisibleMethod.value);
                    }
                }
                console.log('Wave payment method hidden - pay amount is 0');
            } else {
                // Show Wave payment method when amount is greater than 0
                waveMethod.style.display = 'block';
                console.log('Wave payment method shown - pay amount is', finalAmount);
            }
        } else {
            console.log('Wave payment method not found in DOM');
        }
    } catch (error) {
        console.error('Error updating Wave payment visibility:', error);
    }
}

// Utility function to extract amount from pay button
function getPayButtonAmount() {
    try {
        let payButton = document.getElementById('payment-confirm');
        if (!payButton) return 0;
        
        let payButtonText = payButton.textContent || payButton.innerText || '';
        
        // Extract amount from pay button text (e.g., "Pay 0 FCFA" or "Pay 1,500 FCFA")
        let amountMatch = payButtonText.match(/Pay\s+([\d,.\s]+)\s*FCFA/i);
        if (amountMatch) {
            let amountStr = amountMatch[1].replace(/[,\s]/g, '');
            return parseFloat(amountStr) || 0;
        }
        
        return 0;
    } catch (error) {
        console.error('Error extracting pay button amount:', error);
        return 0;
    }
}

// =============================================================================
// OPTIMIZED VALIDATION AND SYNC FUNCTIONS
// =============================================================================

// Force update payment button with current totals
function forceUpdatePaymentButton() {
    let finalAmount = storage.get('final_amount', 0);
    $('#payment-confirm').html("Pay " + formatAmount(finalAmount) + " FCFA");
    console.log('Forced payment button update with amount:', finalAmount);

    // Update Wave payment visibility when button is updated
    setTimeout(function() {
        updateWavePaymentVisibility();
    }, 50);
}

// Validate checkout form before opening credit modal (relaxed validation for credit applications)
function validateCheckoutForm() {
    // For credit applications, we only require delivery method selection
    // Address/showroom can be selected later during order creation
    const deliveryMethod = $('input[name="delivery_option"]:checked').val();
    if (!deliveryMethod) {
        toastr.warning('<?= __("Please select a delivery method before applying for credit") ?>', '', {
            timeOut: 3000,
            progressBar: true
        });
        return false;
    }

    console.log('Credit application validation passed:', {
        deliveryMethod: deliveryMethod,
        note: 'Address/showroom selection will be required during order creation'
    });

    return true;
}

// Submit credit application and create order
function submitCreditApplicationAndCreateOrder() {
    console.log('=== CREDIT APPLICATION SUBMISSION STARTED ===');

    // STEP 1: Ensure credit partner is selected
    let creditPartnerValue = null;
    const allPartners = $('input[name="credit_partner"]');
    const checkedPartner = $('input[name="credit_partner"]:checked');

    console.log('=== CREDIT PARTNER DEBUGGING ===');
    console.log('Partners available:', allPartners.length);
    console.log('Partners checked:', checkedPartner.length);

    // Debug: Show all available partner values
    console.log('All partner values:', allPartners.map(function() {
        return {
            id: this.id,
            value: this.value,
            checked: this.checked
        };
    }).get());

    if (checkedPartner.length > 0) {
        creditPartnerValue = checkedPartner.val();
        console.log('Found selected partner ID:', creditPartnerValue);
        console.log('Selected partner element:', checkedPartner[0]);
    } else if (allPartners.length > 0) {
        // Auto-select first partner
        const firstPartner = allPartners.first();
        console.log('Auto-selecting first partner...');
        console.log('First partner element:', firstPartner[0]);
        console.log('First partner value before selection:', firstPartner.val());

        firstPartner.prop('checked', true);
        creditPartnerValue = firstPartner.val();

        console.log('First partner value after selection:', creditPartnerValue);
        console.log('Verification - is first partner now checked?', firstPartner.is(':checked'));
    } else {
        console.error('NO CREDIT PARTNERS FOUND AT ALL!');
    }

    // STEP 2: Ensure EMI plan is selected
    let emiPlanValue = null;
    const allPlans = $('input[name="emi_plan"]');
    const checkedPlan = $('input[name="emi_plan"]:checked');

    console.log('EMI plans available:', allPlans.length);
    console.log('EMI plans checked:', checkedPlan.length);

    if (checkedPlan.length > 0) {
        emiPlanValue = checkedPlan.val();
        console.log('Found selected plan:', emiPlanValue);
    } else if (allPlans.length > 0) {
        // Auto-select first plan
        const firstPlan = allPlans.first();
        firstPlan.prop('checked', true);
        emiPlanValue = firstPlan.val();
        console.log('Auto-selected first plan:', emiPlanValue);
    }

    // STEP 3: Final fallback for credit partner
    if (!creditPartnerValue || creditPartnerValue === '') {
        console.log('EMERGENCY: Credit partner value is still empty, using fallback');
        // Try to get any available partner ID
        if (allPartners.length > 0) {
            const fallbackValue = allPartners.first().attr('value') || allPartners.first().prop('value') || '1';
            console.log('Using fallback partner value:', fallbackValue);
            creditPartnerValue = fallbackValue;
        } else {
            console.log('Using hardcoded fallback value: 1');
            creditPartnerValue = '1'; // Hardcoded fallback
        }
    }

    // STEP 4: Final fallback for EMI plan
    if (!emiPlanValue || emiPlanValue === '') {
        console.log('EMERGENCY: EMI plan value is empty, using fallback');
        if (allPlans.length > 0) {
            const fallbackPlan = allPlans.first().attr('value') || allPlans.first().prop('value') || '1';
            console.log('Using fallback EMI plan value:', fallbackPlan);
            emiPlanValue = fallbackPlan;
        } else {
            emiPlanValue = '1'; // Hardcoded fallback
        }
    }

    // STEP 5: Collect all form data
    const formData = {
        registration_id: $('#registration_id').val() || '',
        name: $('#full_name').val() || '',
        email: $('#email').val() || '',
        country_code: $('#multistep-country-select').val() || '',
        phone_number: $('#multistep-phone-input').val() || '',
        credit_partner: creditPartnerValue || '1', // Ensure never empty
        emi_plan: emiPlanValue || '1', // Ensure never empty
        terms_agreement: $('#terms_agreement').is(':checked') || false
    };

    console.log('=== FINAL FORM DATA ===');
    console.log('Credit Partner Value:', formData.credit_partner);
    console.log('EMI Plan Value:', formData.emi_plan);
    console.log('Complete form data:', formData);

    // STEP 6: Validation (should pass now with fallbacks)
    const missingFields = [];
    if (!formData.registration_id.trim()) missingFields.push('Registration ID');
    if (!formData.name.trim()) missingFields.push('Full Name');
    if (!formData.email.trim()) missingFields.push('Email');
    if (!formData.country_code.trim()) missingFields.push('Country Code');
    if (!formData.phone_number.trim()) missingFields.push('Phone Number');
    if (!formData.credit_partner || formData.credit_partner.trim() === '') {
        console.error('CRITICAL: Credit partner is STILL empty after all fallbacks!');
        console.error('This should never happen. Credit partner value:', formData.credit_partner);
        missingFields.push('Credit Partner');
    }
    if (!formData.emi_plan || formData.emi_plan.trim() === '') {
        console.error('CRITICAL: EMI plan is STILL empty after all fallbacks!');
        missingFields.push('EMI Plan');
    }
    if (!formData.terms_agreement) missingFields.push('Terms Agreement');

    if (missingFields.length > 0) {
        console.error('Validation failed. Missing fields:', missingFields);
        console.error('Form data:', formData);

        toastr.error('<?= __("Missing required fields") ?>: ' + missingFields.join(', '), '', {
            timeOut: 5000,
            progressBar: true
        });
        $('.multistep-submit').prop('disabled', false).html('<i class="fa fa-paper-plane" style="margin-right: 8px;"></i>Submit Application');
        return;
    }

    // Submit credit application
    $.ajax({
        url: '<?= $this->Url->build(['controller' => 'Website', 'action' => 'submitCreditApplication']) ?>',
        type: 'POST',
        data: formData,
        dataType: 'json',
        headers: {
            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
        },
        success: function(response) {
            if (response.status === 'success') {
                // Close credit modal
                $('#multistep-form-modal').removeClass('show').fadeOut(300);
                $('body').removeClass('modal-open');

                // Remove the force styling and restore normal z-index
                $('#force-multistep-behind').remove();
                $('header, .header, navbar, .navbar, .navbar-bg, nav, .nav, .main-navbar, .sticky').css({
                    'z-index': '1000',
                    'position': 'relative'
                });

                // Reset the form
                resetMultiStepForm();

                // Show success message
                toastr.success('<?= __("Credit application submitted successfully! Creating your order...") ?>', '', {
                    timeOut: 3000,
                    progressBar: true
                });

                // Now create the order with credit payment method
                setTimeout(function() {
                    createOrderWithCreditPayment(response.credit_application_id);
                }, 1000);

            } else {
                toastr.error(response.message || '<?= __("Failed to submit credit application") ?>', '', {
                    timeOut: 3000,
                    progressBar: true
                });
                $('.multistep-submit').prop('disabled', false).html('<i class="fa fa-paper-plane" style="margin-right: 8px;"></i>Submit Application');
            }
        },
        error: function(xhr, status, error) {
            console.error('Credit application submission error:', error);
            toastr.error('<?= __("An error occurred while submitting your credit application") ?>', '', {
                timeOut: 3000,
                progressBar: true
            });
            $('.multistep-submit').prop('disabled', false).html('<i class="fa fa-paper-plane" style="margin-right: 8px;"></i>Submit Application');
        }
    });
}

// Create order with credit payment method
function createOrderWithCreditPayment(creditApplicationId) {
    // Get cart items from order summary section
    const cartItems = [];
    let calculatedTotal = 0;

    $('.my-order-item-details').each(function () {
        const itemContainer = $(this);
        const itemId = itemContainer.find('.updateCartItem').first().data('item-id');
        const itemName = itemContainer.find('.my-order-item-details-title').text().trim();
        const qty = parseInt(itemContainer.find('.cartItemQty').text().trim(), 10);
        const price = Math.max(0, parseFloat(itemContainer.find('.cart_items_list_price').val()));
        const category_name = itemContainer.find('.cart_items_list_category_name').val();
        const product_id = itemContainer.find('.cart_items_list_id').val();
        const brand_name = itemContainer.find('.cart_items_list_brand_name').val();

        if (itemId && qty && price) {
            const itemTotal = Math.max(0, qty * price);
            cartItems.push({
                cart_item_id: itemId,
                product_id: product_id,
                quantity: qty,
                itemName: itemName,
                price: price,
                category_name: category_name,
                brand_name: brand_name,
                total: itemTotal
            });
            calculatedTotal += itemTotal;
        }
    });

    // Prepare order data
    const delivery_mode = $('input[name="delivery_option"]:checked').val();
    const orderData = {
        payment_method: 'Credit',
        total_amount: calculatedTotal,
        final_amount: calculatedTotal,
        delivery_mode: delivery_mode,
        delivery_mode_type: delivery_mode,
        cart_items: cartItems,
        credit_application_id: creditApplicationId
    };

    // Add address or showroom data (validate that they are selected)
    if (delivery_mode === 'delivery') {
        const selectedAddress = $('input[name="address"]:checked').val();
        if (!selectedAddress) {
            toastr.error('<?= __("Please select a delivery address before creating the order") ?>', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }
        orderData.customer_address_id = selectedAddress;
    } else if (delivery_mode === 'pickup') {
        const selectedShowroom = $('input[name="showroom"]:checked').val();
        if (!selectedShowroom) {
            toastr.error('<?= __("Please select a pickup location before creating the order") ?>', '', {
                timeOut: 3000,
                progressBar: true
            });
            return;
        }
        orderData.showroom_id = selectedShowroom;
    }

    // Submit order
    $.ajax({
        url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'createOrder']) ?>',
        type: 'POST',
        data: orderData,
        dataType: 'json',
        headers: {
            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
        },
        success: function(response) {
            if (response.status === 'success') {
                toastr.success('<?= __("Order created successfully!") ?>', '', {
                    timeOut: 3000,
                    progressBar: true
                });

                // Redirect to success page or order confirmation
                if (response.redirect_url) {
                    window.location.href = response.redirect_url;
                } else {
                    // Fallback redirect
                    window.location.href = '/account/orders';
                }
            } else {
                toastr.error(response.message || '<?= __("Failed to create order") ?>', '', {
                    timeOut: 3000,
                    progressBar: true
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Order creation error:', error);
            toastr.error('<?= __("An error occurred while creating your order") ?>', '', {
                timeOut: 3000,
                progressBar: true
            });
        }
    });
}

// Validate and sync UI with stored values
function validateAndSyncDiscountUI() {
    // Validate loyalty points
    let redeemPoint = parseFloat(storage.get('redeemPoint', 0)) || 0;
    $(".lyt-reedem-point").html(redeemPoint);
    $(".lytPoints").val(redeemPoint);
    
    if (redeemPoint && redeemPoint > 0) {
        $(".loyalty-applied").show();
        $(".no-loyalty").hide();
    } else {
        $(".loyalty-applied").hide();
        $(".no-loyalty").show();
    }
    
    // Validate wallet
    let walletAmount = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
    $('.wallet-used-amount').text(formatAmount(walletAmount));
    $('.walletAmount').val(walletAmount);
    
    if (walletAmount > 0) {
        $('.wallet-applied').show();
        $('.no-wallet').hide();
    } else {
        $('.wallet-applied').hide();
        $('.no-wallet').show();
    }
    
    // Validate coupon
    let couponAmount = parseFloat(storage.get('coupon_amount', 0)) || 0;
    if ($('.coupon-used-amount').length) {
        $('.coupon-used-amount').text(formatAmount(couponAmount));
    }
    
    console.log('UI validation complete:', {
        redeemPoint,
        walletAmount,
        couponAmount
    });
}

// Complete recalculation and sync function - now uses optimized calculation
function forceRecalculateAndSync() {
    console.log('Force recalculation started - using optimized system');
    
    // Use the optimized calculation system
    calculateOptimizedTotal();
    
    // Sync discount UI
    setTimeout(function() {
        validateAndSyncDiscountUI();
    }, 50);
    
    console.log('Force recalculation complete');
}

// Global variables to control delivery charge calculations
window.initialPageLoad = true;
window.deliveryChargeCallCount = 0;
window.maxInitialCalls = 1; // Only allow one call during page load
window.deliveryChargeCalculated = false;

// Completely disable automatic delivery charge calculations on page load
// We'll manually trigger it once when needed
console.log('Page load started, delivery charge calculations disabled');

// Set a timeout to reset the flag after page load is complete
setTimeout(function() {
    window.initialPageLoad = false;
    console.log('Page load complete, delivery charge calculations will be enabled');

    // Only calculate if it hasn't been calculated yet
    if (!window.deliveryChargeCalculated) {
        console.log('Performing initial delivery charge calculation');
      //  calculateDeliveryChargeOnce();
    }
}, 1500); // Increased timeout to ensure page is fully loaded

// Function to calculate delivery charge only once during page load
function calculateDeliveryChargeOnce22() {
    // Legacy function - redirect to optimized system
    console.log('Legacy calculateDeliveryChargeOnce22 called - using optimized system');
    
    // If we've already calculated the delivery charge, don't do it again
    if (window.deliveryChargeCalculated) {
        console.log('Delivery charge already calculated, skipping');
        return;
    }

    // Mark as calculated to prevent further calls
    window.deliveryChargeCalculated = true;
    
    // Use optimized system for initial calculation
    setTimeout(function() {
        calculateOptimizedDeliveryCharge();
    }, 500);
}

// Legacy function - now redirects to optimized system
function calculateDeliveryCharge() {
    console.log('Legacy calculateDeliveryCharge called - redirecting to optimized system');
    debouncedDeliveryCalculation();
}

// Second updateTotalAmount function removed - using optimized calculation system above

// Start checking for jQuery when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Add a direct fix for the mobile number input and Select2 initialization
    setTimeout(function() {
        // Check if jQuery is loaded
        if (window.jQuery) {
            // Get the selected payment method
            var selectedMethod = jQuery('input[name="method"]:checked').val();
            console.log('DOMContentLoaded payment method:', selectedMethod);

            // Force hide the mobile input
            if (selectedMethod !== 'MTN MoMo') {
                jQuery('#momo-mobile-input').hide().css('display', 'none').attr('style', 'display: none !important');
                console.log('Forced hide mobile input on DOMContentLoaded');
            }

            // Add a global click handler for payment methods
            jQuery(document).on('click', 'input[name="method"]', function() {
                var method = jQuery(this).val();
                console.log('Payment method clicked:', method);

                if (method === 'MTN MoMo') {
                    jQuery('#momo-mobile-input').show().css('display', 'flex').attr('style', 'display: flex !important');
                } else {
                    jQuery('#momo-mobile-input').hide().css('display', 'none').attr('style', 'display: none !important');
                }
            });

            // Initialize Select2 for country code dropdowns if Select2 is available
            if (jQuery.fn.select2) {
                // Initialize Select2 for MTN MoMo country code dropdown
                jQuery('#ax-country-select').select2({
                    width: '100%',
                    dropdownAutoWidth: true,
                    minimumResultsForSearch: 0,
                    allowClear: false,
                    placeholder: 'Select',
                    dropdownCssClass: 'country-code-dropdown',
                    dropdownParent: jQuery('body'), // Attach to body to avoid positioning issues
                    templateResult: formatCountryCode,
                    templateSelection: formatCountryCode
                });

                // Set country code based on PHP variable or default to 237
                var countryCode = '<?= $mtn_country_code ?>' || '237';
                jQuery('#ax-country-select').val(countryCode).trigger('change');
                console.log('Set country code to: ' + countryCode + ' in DOMContentLoaded');

                // Initialize Select2 for address form country code dropdown
                jQuery('#address-country-select').select2({
                    width: '100%',
                    dropdownAutoWidth: true,
                    minimumResultsForSearch: 0,
                    allowClear: false,
                    placeholder: 'Select',
                    dropdownCssClass: 'country-code-dropdown',
                    dropdownParent: jQuery('body'), // Attach to body to avoid positioning issues
                    templateResult: formatCountryCode,
                    templateSelection: formatCountryCode
                });

                // Set default country code (237 for Cameroon)
                if (!jQuery('#address-country-select').val()) {
                    jQuery('#address-country-select').val('237').trigger('change');
                    console.log('Set default country code to 237 for address in DOMContentLoaded');
                }

                // Format country code options
                function formatCountryCode(state) {
                    if (!state.id) return state.text;
                    return jQuery('<span style="font-weight: 500;">+' + state.id + '</span>');
                }

                console.log('Select2 initialized on DOMContentLoaded');
            } else {
                console.warn('Select2 not available on DOMContentLoaded');
            }
        }
    }, 500);

    // Continue with normal initialization
    checkJquery();
});

// --- Utility Functions ---
function formatAmount(amount) {
    if (isNaN(amount)) return '0';
    return amount.toString().split('.')[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
}
// Enable/disable Cash on Delivery based on selected city (city_id === 2 allowed)
function updateCodAvailability() {
    try {
        var cityId = storage.get('city_id') || sessionStorage.getItem('city_id') || '';
        // normalize
        cityId = cityId === null ? '' : String(cityId);

        // Find COD radio by exact value 'Cash on Delivery'
        var $cod = $('input[name="method"][value="Cash on Delivery"]');
        if (!$cod.length) return; // nothing to do if COD not present
        var abidjanCityId = <?= $ABIDJAN_CITY_ID ?>;
        var codAllowed = (cityId === abidjanCityId || cityId === abidjanCityId.toString());

        if (codAllowed) {
            $cod.prop('disabled', false).closest('div').removeClass('payment-disabled');
            $cod.next('label').css('opacity', 1);
        } else {
            // disable COD in UI
            $cod.prop('disabled', true).closest('div').addClass('payment-disabled');
            $cod.next('label').css('opacity', 0.6);

            // If COD was selected, switch to first available enabled method
            if ($cod.is(':checked')) {
                var $first = $('input[name="method"]:enabled').first();
                if ($first.length) {
                    $first.prop('checked', true).trigger('change');
                    toastr.warning('Cash on Delivery is not available for the selected city. Please choose another payment method.');
                }
            }
        }
    } catch (e) {
        console.error('updateCodAvailability error', e);
    }
}
// Second updateTotalAmount function removed - using optimized calculation system above

// Main initialization function that will run when jQuery is loaded
function initializeCheckout() {
    // Initialize Select2 for country code dropdowns
    try {
        // Initialize Select2 for MTN MoMo country code dropdown
        if ($.fn.select2) {
            $('#ax-country-select').select2({
                minimumResultsForSearch: 0,
                allowClear: false,
                placeholder: 'Select'
            });

            // Set country code based on PHP variable or default to 237
            var countryCode = '<?= $mtn_country_code ?>' || '237';
            $('#ax-country-select').val(countryCode).trigger('change');
            console.log('Set country code to: ' + countryCode + ' in initializeCheckout');

            // Initialize Select2 for address form country code dropdown
            $('#address-country-select').select2({
                minimumResultsForSearch: 0,
                allowClear: false,
                placeholder: 'Select'
            });

            // Set default country code (237 for Cameroon)
            if (!$('#address-country-select').val()) {
                $('#address-country-select').val('237').trigger('change');
                console.log('Set default country code to 237 for address in initializeCheckout');
            }

            // Format country code options
            function formatCountryCode(state) {
                if (!state.id) return state.text;
                return $('<span style="font-weight: 500;">+' + state.id + '</span>');
            }

            console.log('Select2 initialized in initializeCheckout');
        } else {
            console.warn('Select2 not available in initializeCheckout');
        }
    } catch (e) {
        console.error('Error initializing Select2 in initializeCheckout:', e);
    }
    // --- Coupon UI ---
    let appliedCoupon = storage.get('appliedCoupon', '');
    let couponAmount = storage.get('coupon_amount', 0);

    // Update coupon display based on whether a coupon is applied
    if (appliedCoupon && appliedCoupon !== '') {
        $(".appliedCoupon").html(appliedCoupon);
        $(".couponName").val(appliedCoupon);
        $(".offerApplied").html(couponAmount || '0');
        $(".coupon-applied").show();
        $(".no-coupon").hide();
    } else {
        $(".appliedCoupon").html('');
        $(".couponName").val('');
        $(".offerApplied").html('0');
        $(".coupon-applied").hide();
        $(".no-coupon").show();
    }

    // --- Loyalty Points UI ---
    let redeemPoint = storage.get('redeemPoint', 0);
    $(".lyt-reedem-point").html(redeemPoint);
    $(".lytPoints").val(redeemPoint);
    $(".radeem-input").val(redeemPoint);

    // Update loyalty status display based on whether points are applied
    if (redeemPoint && redeemPoint > 0) {
        $(".loyalty-applied").show();
        $(".no-loyalty").hide();
    } else {
        $(".loyalty-applied").hide();
        $(".no-loyalty").show();
    }

    // --- Delivery Mode ---
    let delivery_mode_type = storage.get('delivery_mode_type', 'standard');
    $(`input[name="delivery_mode_type"][value="${delivery_mode_type}"]`).prop('checked', true);

    // --- Delivery Option ---
    let deliveryType = storage.get('checkDeliveryTypeCheckedCheckbox', 'pickup');
    if (deliveryType === 'pickup') {
        // Set checked without triggering change event
        $('#pickup-from-showroom').prop('checked', true);
        // Show/hide appropriate divs manually
        $('#pickup-div').show();
        $('#delivery-div').hide();
        
        // Hide shipping sections for pickup
        $('.shipping-section').addClass('hidden disabled');

        let showroom_id = storage.get('showroom_id', '');
        if (showroom_id) {
            $(`input[name="showroom"][value="${showroom_id}"]`).prop('checked', true);
        }
    } else {
        // Set checked without triggering change event
        $('#deliver-to-address').prop('checked', true);
        // Show/hide appropriate divs manually
        $('#delivery-div').show();
        $('#pickup-div').hide();
        
        // Show shipping sections for home delivery
        $('.shipping-section').removeClass('hidden disabled');

        let customer_address_id = storage.get('customer_address_id', '');
        if (customer_address_id) {
           $(`input[name="address"][value="${customer_address_id}"]`).prop('checked', true);
        }
    }

    // --- Delivery Charge --- (handled below)

    // --- Update total on load without recalculating delivery charge ---
    // Use the stored delivery charge value without making an API call
    let storedDeliveryCharge = storage.get('delivery_charge', 0);
    
    // For pickup mode, always set delivery charge to 0
    if (deliveryType === 'pickup') {
        storedDeliveryCharge = 0;
        storage.set('delivery_charge', 0);
        $(".delivery-state").text('Free');
    } else {
        if (storedDeliveryCharge) {
            $(".delivery-state").text(formatAmount(storedDeliveryCharge));
        } else {
            $(".delivery-state").text('-');
        }
    }

    // Update the total amount without triggering delivery charge calculation
    updateTotalAmount();

    // Call checkAndSelectRadio to set the correct radio buttons without triggering change events
    checkAndSelectRadio();

    // Initialize payment method display
    const selectedPaymentMethod = $('input[name="method"]:checked').val();
  //  console.log('Payment method initialized:', selectedPaymentMethod);

    // Handle Credit payment method on page load
    if (selectedPaymentMethod === 'Credit') {
        $('#payment-confirm').hide();
        $('#apply-credit-button').show();
        console.log('Credit payment method detected on page load');
    } else {
        $('#apply-credit-button').hide();
        $('#payment-confirm').show();
    }

    // Force hide mobile input by default
    $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');

    if (selectedPaymentMethod === 'MTN MoMo') {
        // Force show for MTN MoMo
        $('#momo-mobile-input').show().css('display', 'flex').addClass('show-input').attr('style', 'display: flex !important');
    //    console.log('MTN MoMo selected in initialization, showing mobile input');
    } else {
        // Force hide for other methods
        $('#momo-mobile-input').hide().css('display', 'none').removeClass('show-input').attr('style', 'display: none !important');
     //   console.log('Other method selected in initialization, hiding mobile input');
    }

    // Debug check
    setTimeout(function() {
        // console.log('Initialization mobile input display style:', $('#momo-mobile-input').css('display'));
        // console.log('Initialization mobile input has show-input class:', $('#momo-mobile-input').hasClass('show-input'));
        // console.log('Initialization mobile input style attribute:', $('#momo-mobile-input').attr('style'));
    }, 100);

    // Manually trigger change event for the selected payment method
    $('input[name="method"]:checked').trigger('change');

    // Update COD availability based on selected city
    try {
        if (typeof updateCodAvailability === 'function') {
            updateCodAvailability();
        }
    } catch (e) {
        console.error('updateCodAvailability not available yet', e);
    }

// --- Coupon Apply/Remove ---
$(document).on('click', '.checkCouponCode', async function () {
    let couponName = $(".couponName").val();
    if (!couponName || couponName.trim() === '') {
        toastr.warning('Please enter a coupon code');
        return;
    }

    try {
        let res = await applyOffers(couponName, <?= $totalPrice ?>);
        if (res.status == 'success' || res.status == 200) {
            // Save coupon data
            storage.set('appliedCoupon', couponName);
            storage.set('coupon_amount', res.data.coupon_amount);

            // Update UI
            $(".appliedCoupon").html(couponName);
            $(".offerApplied").html(res.data.coupon_amount);
            $(".coupon-applied").show();
            $(".no-coupon").hide();

            updateTotalAmount();

            // Immediately adjust wallet amount for optimal discount distribution
            adjustWalletForOptimalDiscounts();

            // Update loyalty max display if loyalty popup is open
            updateLoyaltyMaxDisplay();

            toastr.success(res.message);
        } else {
            // Clear coupon data
            storage.remove('coupon_amount');
            storage.remove('appliedCoupon');

            // Update UI
            $(".appliedCoupon").html('');
            $(".offerApplied").html('0');
            $(".coupon-applied").hide();
            $(".no-coupon").show();

            updateTotalAmount();
            
            // Update loyalty max display if loyalty popup is open
            updateLoyaltyMaxDisplay();
            
            toastr.warning(res.message);
        }
    } catch (e) {
        console.error('Error applying coupon:', e);
        toastr.error('Error applying coupon');
    }
});
$(document).on('click', '.removeCoupon', function () {
    // Clear coupon data
    storage.remove('coupon_amount');
    storage.remove('appliedCoupon');

    // Update UI
    $(".appliedCoupon").html('');
    $(".offerApplied").html('0');
    $(".couponName").val('');
    $(".coupon-applied").hide();
    $(".no-coupon").show();

    updateTotalAmount();
    // Update wallet popup if it's open
    updateWalletPopupIfOpen();
    
    // Update loyalty max display if loyalty popup is open
    updateLoyaltyMaxDisplay();
    
    // Validate and sync UI with actual stored values
    setTimeout(function() {
        validateAndSyncDiscountUI();
        forceUpdatePaymentButton();
    }, 250);
    
    toastr.success("Coupon removed successfully!", '', {
        timeOut: 3000,
        progressBar: true
    });
});

// --- Remove Loyalty Points ---
$(document).on('click', '.removeLoyalty', function () {
    // Clear loyalty data
    storage.set('redeemPoint', 0);

    // Update UI
    $(".lyt-reedem-point").html('0');
    $(".lytPoints").val('0');
    $(".loyalty-applied").hide();
    $(".no-loyalty").show();

    // Use optimized calculation system
    debouncedOptimizedCalculation();
    
    // Update loyalty max display if loyalty popup is open
    updateLoyaltyMaxDisplay();
    
    // Adjust wallet amount when loyalty points are removed
    if (typeof adjustWalletForOptimalDiscounts === 'function') {
        setTimeout(function() {
            adjustWalletForOptimalDiscounts();
        }, 200);
    }

    toastr.success("<?= __('Loyalty points removed successfully!') ?>", '', {
        timeOut: 3000,
        progressBar: true
    });
});

// --- Loyalty Points Redeem ---
$(".radeem-btn").on("click", async function () {
    let inputPoint = parseFloat($(".radeem-input").val());
    
    // Calculate the current total before applying loyalty points
    let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
    let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
    let walletUsed = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
    let subAmount = <?= $totalPrice ?>;
    
    // Calculate total amount available for loyalty redemption (total after other discounts but before loyalty)
    let availableAmount = subAmount - (couponAmount + walletUsed) + delivery_charge;
    
    try {
        let response = await $.ajax({
            headers: {'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'},
            url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'loyaltyPointVerify']) ?>",
            type: 'POST',
            data: {redeem_points: inputPoint, final_amount: availableAmount}
        });
        if (response.status == 'success') {
            // The inputPoint is the currency equivalent amount (1 point = 1 FCFA)
            let loyaltyDiscountAmount = parseFloat(inputPoint) || 0;
            
            $(".lyt-reedem-point").html(loyaltyDiscountAmount);
            $(".lytPoints").val(loyaltyDiscountAmount);
            storage.set('redeemPoint', loyaltyDiscountAmount);
            
            // Update loyalty status display
            $(".loyalty-applied").show();
            $(".no-loyalty").hide();
            
            closePopup();
            
            // Debug logging
            console.log('Loyalty points applied:', {
                inputPoint: inputPoint,
                loyaltyDiscountAmount: loyaltyDiscountAmount,
                storedValue: storage.get('redeemPoint')
            });
            
            // Use optimized calculation system
            debouncedOptimizedCalculation();

            // Adjust wallet amount when loyalty points are applied
            if (typeof adjustWalletForOptimalDiscounts === 'function') {
                setTimeout(function() {
                    adjustWalletForOptimalDiscounts();
                }, 200);
            }

            toastr.success(response.message);
        } else {
            storage.set('redeemPoint', 0);
            $(".lyt-reedem-point").html('0');
            $(".lytPoints").val('0');
            
            // Update loyalty status display
            $(".loyalty-applied").hide();
            $(".no-loyalty").show();
            
            // Debug logging
            console.log('Loyalty points reset to 0');
            console.log('Storage redeemPoint set to:', storage.get('redeemPoint'));
            
            // Use debounced update to handle multiple rapid changes
            debouncedUpdateTotalAmount();

            // Adjust wallet amount when loyalty points are removed
            if (typeof adjustWalletForOptimalDiscounts === 'function') {
                adjustWalletForOptimalDiscounts();
            }
            
            // Force final payment button update
            setTimeout(forceUpdatePaymentButton, 300);

            toastr.warning(response.message);
        }
    } catch (e) {
        toastr.error('Error redeeming points');
    }
});

// --- Helper Function to Update Loyalty Max Display ---
function updateLoyaltyMaxDisplay() {
    // Only update if loyalty popup is visible
    if ($('#popup').is(':visible')) {
        // Calculate maximum redeemable amount (based on order total only)
        let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
        let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
        let walletUsed = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
        let subAmount = <?= $totalPrice ?>;
        
        // Calculate available amount for loyalty redemption
        let availableAmount = subAmount - (couponAmount + walletUsed) + delivery_charge;
        
        // Update display (based on order total only)
        $(".max-redeemable-points").text(Math.floor(availableAmount));
        $(".radeem-input").attr('placeholder', `Enter points (max: ${Math.floor(availableAmount)})`);
    }
}

// --- Use Maximum Loyalty Points Button ---
$(document).on('click', '.use-max-loyalty-btn', function() {
    // Calculate maximum redeemable amount (based on order total only)
    let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
    let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
    let walletUsed = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
    let subAmount = <?= $totalPrice ?>;
    
    // Calculate available amount for loyalty redemption
    let availableAmount = subAmount - (couponAmount + walletUsed) + delivery_charge;
    
    if (availableAmount > 0) {
        $(".radeem-input").val(Math.floor(availableAmount));
        toastr.info(`Set to maximum order total: ${Math.floor(availableAmount)} points`, '', {
            timeOut: 2000,
            progressBar: true
        });
    } else {
        toastr.warning('No points can be redeemed with current order total', '', {
            timeOut: 3000,
            progressBar: true
        });
    }
});

// --- Loyalty Points Input Validation ---
$(document).on('input', '.radeem-input', function() {
    let inputValue = parseFloat($(this).val()) || 0;
    
    // Calculate maximum redeemable amount (based on order total only)
    let couponAmount = parseFloat(sessionStorage.getItem('coupon_amount')) || 0;
    let delivery_charge = parseFloat(sessionStorage.getItem('delivery_charge')) || 0;
    let walletUsed = parseFloat(sessionStorage.getItem('walletAmount')) || 0;
    let subAmount = <?= $totalPrice ?>;
    
    // Calculate available amount for loyalty redemption
    let availableAmount = subAmount - (couponAmount + walletUsed) + delivery_charge;
    
    // Validate and limit input only by order total
    if (inputValue > availableAmount) {
        $(this).val(availableAmount);
        if (availableAmount > 0) {
            toastr.warning(`Maximum redeemable points: ${availableAmount}`, '', {
                timeOut: 3000,
                progressBar: true
            });
        } else {
            toastr.warning('No points can be redeemed with current order total', '', {
                timeOut: 3000,
                progressBar: true
            });
        }
    }
});


// --- Delivery Mode/Option/Address/Showroom Change ---
$(document).on('change', 'input[name="delivery_mode_type"],input[name="delivery_option"], input[name="showroom"], input[name="address"]', async function (event) {
    let delivery_mode = $('input[name="delivery_option"]:checked').val();
    let triggeredName = $(event.target).attr('name');
    
    console.log('Delivery change triggered:', triggeredName, 'Mode:', delivery_mode);
    
    // Skip delivery charge calculation if pickup is selected and delivery mode is being changed
    if (delivery_mode === 'pickup' && triggeredName === 'delivery_mode_type') {
        console.log('Skipping delivery charge calculation - pickup mode selected (legacy handler)');
        return;
    }
    
    // When switching delivery options, uncheck delivery mode types until proper selection is made
    if (triggeredName === 'delivery_option') {
        console.log('Delivery option changed - unchecking delivery mode types');
        $('input[name="delivery_mode_type"]').prop('checked', false);
        
        // If switching to delivery, ensure no showroom is selected
        if (delivery_mode === 'delivery') {
            $('input[name="showroom"]').prop('checked', false);
            storage.remove('showroom_id');
        }
        // If switching to pickup, ensure no address is selected
        else if (delivery_mode === 'pickup') {
            $('input[name="address"]').prop('checked', false);
            storage.remove('customer_address_id');
            storage.remove('city_id');
        }
    }
    
    // When changing showroom, uncheck delivery mode types until showroom is confirmed
    if (triggeredName === 'showroom') {
        console.log('Showroom changed - unchecking delivery mode types');
        $('input[name="delivery_mode_type"]').prop('checked', false);
    }
    
    // When changing address, uncheck delivery mode types until address is confirmed
    if (triggeredName === 'address') {
        console.log('Address changed - unchecking delivery mode types');
        $('input[name="delivery_mode_type"]').prop('checked', false);
    }
    
    if (triggeredName === 'address') {
        storage.set('city_id', $(this).data('city-id'));
        storage.set('customer_address_id', $('input[name="address"]:checked').val());
        storage.set('showroom_id', '');
        // update COD availability when address (city) changes
        if (typeof updateCodAvailability === 'function') updateCodAvailability();
        
        // Auto-select standard delivery mode when address is selected
        setTimeout(function() {
            // Only auto-select if no delivery mode is selected and we have a valid city ID
            if (!$('input[name="delivery_mode_type"]:checked').length && storage.get('city_id')) {
                console.log('Auto-selecting standard delivery mode for address');
                $('input[name="delivery_mode_type"][value="standard"]').prop('checked', true);
                // Trigger change event with a slight delay to ensure proper processing
                setTimeout(function() {
                    $('input[name="delivery_mode_type"][value="standard"]').trigger('change');
                }, 50);
            }
        }, 150);
        
    } else if (triggeredName === 'showroom') {
        storage.set('showroom_id', $('input[name="showroom"]:checked').val());
        storage.set('customer_address_id', '');
        storage.set('city_id', '');
        // showroom selected -> clear city for delivery, update COD availability
        if (typeof updateCodAvailability === 'function') updateCodAvailability();
        
        // Auto-select standard delivery mode when showroom is selected
        setTimeout(function() {
            // Only auto-select if no delivery mode is selected and we have a valid showroom ID
            if (!$('input[name="delivery_mode_type"]:checked').length && storage.get('showroom_id')) {
                console.log('Auto-selecting standard delivery mode for showroom pickup');
                $('input[name="delivery_mode_type"][value="standard"]').prop('checked', true);
                // Trigger change event with a slight delay to ensure proper processing
                setTimeout(function() {
                    $('input[name="delivery_mode_type"][value="standard"]').trigger('change');
                }, 50);
            }
        }, 150);
    }
    
    if (delivery_mode === 'pickup') {
        storage.set('checkDeliveryTypeCheckedCheckbox', 'pickup');
        // Set delivery charge to 0 for pickup and skip AJAX call
        storage.set('delivery_charge', 0);
        $(".delivery-state").text('Free');
        updateTotalAmount();
        return;
    } else if (delivery_mode === 'delivery') {
        storage.set('checkDeliveryTypeCheckedCheckbox', 'delivered');
    }
    
    // After processing delivery/showroom/address changes ensure COD availability is refreshed
    if (typeof updateCodAvailability === 'function') updateCodAvailability();
    let customer_address_id = '';
    if (delivery_mode === 'pickup') {
        $('input[name="address"]').prop('checked', false);
        customer_address_id = 0;
    } else {
        $('input[name="showroom"]').prop('checked', false);
        
        // Get the city ID from the selected address or use the stored city_id
        if (triggeredName === 'address') {
            // When address is being selected, get city from the event target
            customer_address_id = $(this).data('city-id');
        } else {
            // For other triggers (delivery_mode_type, etc), get from selected address or storage
            let selectedAddress = $('input[name="address"]:checked');
            if (selectedAddress.length) {
                customer_address_id = selectedAddress.data('city-id');
            } else {
                customer_address_id = storage.get('city_id');
            }
        }
    }
    
    console.log('Delivery charge calculation - Mode:', delivery_mode, 'City ID:', customer_address_id, 'Triggered by:', triggeredName);
    
    // Validate that we have a proper city ID for delivery mode
    if (delivery_mode === 'delivery' && (!customer_address_id || customer_address_id === '' || customer_address_id === '0')) {
        console.log('No valid city ID found for delivery mode - skipping delivery charge calculation');
        toastr.warning('Please select a delivery address');
        return;
    }
    
    // Validate that delivery mode type is selected
    let selectedDeliveryModeType = $(".delivery_mode_type:checked").val();
    if (!selectedDeliveryModeType) {
        console.log('No delivery mode type selected - skipping delivery charge calculation');
        // Don't show error here as auto-selection might still be in progress
        return;
    }
    
    // Get delivery charge via AJAX
    const deliveryCharges = '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getDeliveryChargeFromCityNew']) ?>';
    const jsondata = {
        cityId: customer_address_id,
        delivery_mode: selectedDeliveryModeType,
        weightQuantityArray: <?php echo json_encode($weightQuantityArray); ?>,
        sizeQuantityArray: <?php echo json_encode($sizeQuantityArray); ?>,
    };
            // if(delivery_mode === 'delivery'){
            // sessionStorage.removeItem('showroom_id');
            // }else{
            // sessionStorage.removeItem('customer_address_id');
            // sessionStorage.removeItem('city_id');
            // }
    try {
        console.log('Sending delivery charge request:', jsondata);
        let response = await $.ajax({
            headers: {'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'},
            url: deliveryCharges,
            type: 'POST',
            data: jsondata
        });
        
        console.log('Delivery charge response:', response);
        
        if (response.status === 'success') {

            storage.set('delivery_charge', response.total_delivery_charge);
            $(".delivery-state").text(formatAmount(response.total_delivery_charge));
            updateTotalAmount();

            // Adjust wallet amount when delivery charge changes
            if (typeof adjustWalletForOptimalDiscounts === 'function') {
                adjustWalletForOptimalDiscounts();
            }

            // Update loyalty max display if loyalty popup is open
            updateLoyaltyMaxDisplay();

            if(response.showroom==false){
                toastr.success(response.message);
            }
        } else {
            console.log('Delivery charge failed:', response.message);
            storage.set('delivery_charge', 0);
            $(".delivery-state").text('Free');
            updateTotalAmount();

            // Adjust wallet amount when delivery becomes free
            if (typeof adjustWalletForOptimalDiscounts === 'function') {
                adjustWalletForOptimalDiscounts();
            }

            // Update loyalty max display if loyalty popup is open
            updateLoyaltyMaxDisplay();

            toastr.warning(response.message);
        }
    } catch (e) {
        console.error('Delivery charge AJAX error:', e);
        console.log('Failed request data:', jsondata);
        toastr.warning('Error calculating delivery charge. Please try selecting the address again.');
        // toastr.error('Error fetching delivery charge');
    }
});

// --- Payment Confirm ---
$(document).on('click', '#payment-confirm', async function () {
    // read delivery_option
//    alert('Please select a delivery address');

});

// --- Helper AJAX Functions ---

function deleteToCart(cart_item_id) {
    return $.ajax({
        headers: {'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'},
        url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'deleteCartItem']) ?>",
        type: 'POST',
        data: {cart_item_id}
    });
}
function applyOffers(couponName, amt) {
    return $.ajax({
        headers: {'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'},
        url: "<?= $this->Url->build(['controller' => 'Account', 'action' => 'applyoffer']) ?>",
        type: 'POST',
        data: {'coupon_code': couponName, 'subTotal': amt}
    });
}
function closePopup() {
    hideLoyaltyPopup();
}

// Close the initializeCheckout function
}

// Enhanced Select2 initialization for searchable-select
$(document).ready(function() {
    // Fix any issues with the dropdown after it's opened
    $(document).on('mouseenter', '.select2-results__option', function() {
        // Ensure country names are visible on hover
        $(this).find('.select2-country-name').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
        });
    });
    
});
</script>
