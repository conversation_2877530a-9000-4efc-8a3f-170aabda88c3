<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;
use Cake\I18n\FrozenDate;
use Cake\Log\Log;


/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class WarehouseStockRequestsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Suppliers;
    protected $Warehouses;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $SupplierProducts;
    protected $Roles;
    protected $Users;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Suppliers = $this->fetchTable('Suppliers');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockRequestItems = $this->fetchTable('StockRequestItems');
        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->Roles = $this->fetchTable('Roles');
        $this->Users = $this->fetchTable('Users');
    }
    
    public function index()
    {
        // Get the currently authenticated user
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            // Check if the user's role is "Warehouse Manager"
            if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'warehouse assistant') {
                // Get the warehouse_id for the manager
                if(strtolower($role->name) === 'warehouse manager')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['manager_id' => $requested_user->id])
                        ->first();
                }
                else if(strtolower($role->name) === 'warehouse assistant')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['assistant_id' => $requested_user->id])
                        ->first();
                }

                if (!empty($warehouse)) {
                    $warehouseId = $warehouse->id;

                    // Update the StockRequests query to filter by warehouse_id
                    $stock_requests = $this->StockRequests->find()
                        ->select([
                            'StockRequests.id',
                            'StockRequests.request_date',
                            'StockRequests.requestor_type',
                            'StockRequests.request_status',
                            'StockRequests.manager_review_status',
                            'StockRequests.supervisor_verify_status',
                            'StockRequests.status',
                            'StockRequests.created',
                            'Suppliers.name', // Supplier name
                            'Warehouses.name',
                            'SupplierPurchaseOrders.id',
                            'SupplierPurchaseOrders.order_date',
                            'SupplierPurchaseOrders.bill_no',
                            'SupplierPurchaseOrders.payment_status',
                            'SupplierPurchaseOrders.delivery_status'
                        ])
                        ->join([
                            'table' => 'supplier_purchase_orders',
                            'alias' => 'SupplierPurchaseOrders',
                            'type' => 'INNER',
                            'conditions' => 'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
                        ])
                        ->contain(['Suppliers', 'Warehouses'])
                        ->where([
                            'StockRequests.requestor_type' => 'Warehouse',
                            'StockRequests.status IN' => ['A', 'I'],
                            'StockRequests.warehouse_id' => $warehouseId // Filter by warehouse_id
                        ])
                        ->order(['StockRequests.id' => 'DESC'])
                        ->toArray();
                } else {
                    $stock_requests = []; // No warehouse assigned to this manager
                }
            } else {
                // Handle cases for non-Warehouse Manager roles
                $stock_requests = $this->StockRequests->find()
                    ->select([
                        'StockRequests.id',
                        'StockRequests.request_date',
                        'StockRequests.requestor_type',
                        'StockRequests.request_status',
                        'StockRequests.manager_review_status',
                        'StockRequests.supervisor_verify_status',
                        'StockRequests.status',
                        'StockRequests.created',
                        'Suppliers.name', // Supplier name
                        'Warehouses.name',
                        'SupplierPurchaseOrders.id',
                        'SupplierPurchaseOrders.order_date',
                        'SupplierPurchaseOrders.bill_no',
                        'SupplierPurchaseOrders.payment_status',
                        'SupplierPurchaseOrders.delivery_status'
                    ])
                    ->join([
                        'table' => 'supplier_purchase_orders',
                        'alias' => 'SupplierPurchaseOrders',
                        'type' => 'INNER',
                        'conditions' => 'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
                    ])
                    ->contain(['Suppliers', 'Warehouses'])
                    ->where([
                        'StockRequests.requestor_type' => 'Warehouse',
                        'StockRequests.status IN' => ['A', 'I']
                    ])
                    ->order(['StockRequests.id' => 'DESC'])
                    ->toArray();
            }
        } else {
            $stock_requests = []; // Handle unauthenticated users
        }

        $this->set(compact('stock_requests'));
    }

    public function add()
    {

        $stock_request = $this->StockRequests->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $data['requestor_type'] = 'Warehouse';
            $data['manager_review_status'] = 'Pending';
            $data['supervisor_verify_status'] = 'Pending';
            $data['request_status'] = 'Pending';

            $stock_request = $this->StockRequests->patchEntity($stock_request, $data);


            if ($this->StockRequests->save($stock_request)) {

                $this->saveStockRequestItems($stock_request, $data);

                // Create a new supplier purchase order
                $purchaseOrder = $this->createSupplierPurchaseOrder($stock_request, $data);

                if ($purchaseOrder) {
                    // Save related purchase order items
                    $this->saveSupplierPurchaseOrderItems($purchaseOrder, $stock_request);
                    
                    $this->sendStockRequestToSupplierEmail($stock_request);

                    $this->Flash->success(__('The stock request have been saved.'));

                    return $this->redirect(['action' => 'index']);

                } else {
                    $this->Flash->error(__('The stock request could not be saved. Please, try again.'));
                }

            }
            else
            {
                $this->Flash->error(__('The stock request could not be saved. Please, try again.'));
            }
        }

            $suppliers = $this->Suppliers->find()
                ->where(['Suppliers.status' => 'A'])
                ->order(['Suppliers.name' => 'ASC'])
                ->toArray();
            
        $products = $this->Products->find()
            ->where(['Products.status' => 'A'])
            ->order(['Products.name' => 'ASC'])
            ->toArray();   

        // Get the logged-in user
        $requested_user = $this->Authentication->getIdentity();

        // Fetch warehouses based on user role
        $userRole = null;
        $warehouses = [];
        $warehouse = null;

        if (!empty($requested_user)) {
            
            $user = $this->Users->get($requested_user->id, [
                'contain' => ['Roles'], // Assuming the Users table is associated with Roles
            ]);

            $userRole = strtolower($user->role->name);

            if ($userRole === 'warehouse manager') {
                // Fetch only the warehouse managed by this user
                $warehouse = $this->Warehouses->find()
                    ->where(['manager_id' => $requested_user->id, 'status' => 'A'])
                    ->first();
            } else {
                // Fetch all active warehouses for other roles
                $warehouses = $this->Warehouses->find()
                    ->where(['status' => 'A'])
                    ->order(['name' => 'ASC'])
                    ->toArray();
            }
        }

        // Set variables for the view
        $this->set(compact('suppliers', 'warehouses', 'products', 'requested_user', 'warehouse', 'userRole'));

    }

    private function sendStockRequestToSupplierEmail($stock_request)
    {   

        $toEmails = [];

        // Fetch Supplier details
        $supplier = $this->Suppliers->get($stock_request->supplier_id);

        $supplierEmail = $supplier->contact_email; // Fetch email from supplier.contact_email

        // Fetch Warehouse details
        $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
            'contain' => ['Managers'] // Assuming Warehouses have a manager/user
        ]);

        $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
        $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

        if (!empty($supplierEmail)) {
            $toEmails[] = $supplierEmail; // Send to supplier
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for stock request ID: " . $stock_request->id);
            return;
        }

        $toEmails[] = '<EMAIL>';

        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->select([
                'StockRequestItems.id',
                'StockRequestItems.requested_quantity',
                'StockRequestItems.product_id',
                'StockRequestItems.product_variant_id',
                'StockRequestItems.product_attribute_id',
                'StockRequestItems.stock_request_id',
                'StockRequestItems.supervisor_approved_quantity',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockRequestItems.stock_request_id' => $stock_request->id])
            ->toArray();

        // Add supplier price and attributes as in your code
        foreach ($stockRequestItems as &$item) {
            // Get supplier price
            $supplierPriceQuery = $this->SupplierProducts->find()
                ->select(['supplier_price'])
                ->where([
                    'SupplierProducts.supplier_id' => $stock_request->supplier_id,
                    'SupplierProducts.product_id' => $item->product_id,
                ]);

            if ($item->product_variant_id) {
                $supplierPriceQuery->where(['SupplierProducts.product_variant_id' => $item->product_variant_id]);
            }

            $supplierProduct = $supplierPriceQuery->first();
            $item->supplier_price = $supplierProduct->supplier_price ?? null;

            // Get attribute
            $item->attributes = [];
            if ($item->product_attribute_id) {
                $attribute = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain(['Attributes', 'AttributeValues'])
                    ->first();

                if ($attribute) {
                    $item->attributes = [
                        'attribute_name' => $attribute->attribute->name ?? '',
                        'attribute_value' => $attribute->attribute_value->value ?? ''
                    ];
                }
            }
        }

        $items = [];

        foreach ($stockRequestItems as $item_data) {
            $productName = $item_data->_matchingData['Products']->name ?? 'N/A';

            $variant = $item_data->_matchingData['ProductVariants'] ?? null;
            $variantName = (!empty($variant) && !empty($variant->id)) ? ($variant->variant_name ?? 'N/A') : 'N/A';

            $attribute = 'N/A';
            if (!empty($item_data->attributes) && is_array($item_data->attributes)) {
                $attrName = $item_data->attributes['attribute_name'] ?? null;
                $attrValue = $item_data->attributes['attribute_value'] ?? null;
                if ($attrName && $attrValue) {
                    $attribute = $attrName . ':' . $attrValue;
                }
            }

            $items[] = [
                'product_name'  => $productName,
                'variant_name'  => $variantName,
                'attribute'     => $attribute,
                'quantity'      => $item_data->requested_quantity ?? 0,
            ];
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Pending',
            'warehouse_name' => $warehouse->name,
            'supplier_name' => $supplier->name,
            'request_date' => $stock_request->created ? $stock_request->created->format('d-m-Y') : 'N/A',
            'bill_no' => $stock_request->bill_no,
            'required_delivery_date' => $stock_request->required_delivery_date,
            'items' => $items
        ];

        $subject = "Stock Request #{$stock_request->id}";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_request_supplier',
            $emailData
        );
    }

    public function getProductsBySupplier($supplierId = null)
    {

        // Fetch products related to the selected supplier
        $products = $this->Products->find()
            ->distinct(['Products.id'])
            ->innerJoinWith('SupplierProducts') // Association name
            ->where([
                'SupplierProducts.status' => 'A',
                'SupplierProducts.supplier_id' => $supplierId
            ])
            ->order(['Products.name' => 'ASC'])
            ->toArray();

        $this->set([
                    'products' => $products,
                    '_serialize' => ['variants'],
                ]);

        return $this->response->withType('application/json')
                ->withStringBody(json_encode(['products' => $products]));

    }


    protected function createSupplierPurchaseOrder($stockRequest, $data)
    {
        $paymentDueDate = FrozenDate::today()->format('Y-m-d');

        $purchaseOrderData = [
            'stock_request_id' => $stockRequest->id,
            'supplier_id' => $data['supplier_id'],
            'order_date' => $data['order_date'], // Assuming today's date
            'bill_no' => $data['bill_no'],
            'supplier_bill_no' => $data['supplier_bill_no'],
            'status' => 'A',
            // 'payment_status' => $data['payment_status'],
            'delivery_status' => $data['delivery_status'],
            'deliver_to' => 'Warehouse',
            'id_deliver_to' => $data['warehouse_id'],
            'required_delivery_date' => $data['required_delivery_date'],
            'payment_due_date' => $paymentDueDate,
            // Add other fields as needed
        ];

        $purchaseOrder = $this->SupplierPurchaseOrders->newEmptyEntity();
        $purchaseOrder = $this->SupplierPurchaseOrders->patchEntity($purchaseOrder, $purchaseOrderData);

        if ($this->SupplierPurchaseOrders->save($purchaseOrder)) {
            return $purchaseOrder;
        }
        return false;
    }

    protected function saveSupplierPurchaseOrderItems($purchaseOrder, $stockRequest)
    {
        // Ensure purchase order and stock request exist
        if (!$purchaseOrder || !$stockRequest) {
            return false;
        }

        // Delete all existing items related to this purchase order
        $this->SupplierPurchaseOrdersItems->deleteAll(['supplier_purchase_order_id' => $purchaseOrder->id]);

        // Fetch StockRequestItems related to this stock request
        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->contain(['ProductVariants', 'Products']) // Include related data from ProductVariants and Products
            ->where(['stock_request_id' => $stockRequest->id])
            ->toArray();

        if (empty($stockRequestItems)) {
            return false; // No items found, no need to proceed
        }

        $purchaseOrderItems = []; // Array to hold all items for bulk insert

        foreach ($stockRequestItems as $stockRequestItem) {
            // Determine the SKU
            $sku = !empty($stockRequestItem->product_variant_id) && !empty($stockRequestItem->product_variant) 
                ? $stockRequestItem->product_variant->sku 
                : ($stockRequestItem->product->sku ?? null);

            if (!$sku) {
                continue; // Skip item if SKU is missing
            }

            $purchaseOrderItems[] = [
                'supplier_purchase_order_id' => $purchaseOrder->id,
                'product_id' => $stockRequestItem->product_id,
                'product_variant_id' => $stockRequestItem->product_variant_id ?? null,
                'product_attribute_id' => $stockRequestItem->product_attribute_id ?? null,
                'sku' => $sku,
                'quantity' => $stockRequestItem->requested_quantity,
                'approved_quantity' => $stockRequestItem->requested_quantity
            ];
        }

        // Bulk save for better performance
        $entities = $this->SupplierPurchaseOrdersItems->newEntities($purchaseOrderItems);
        if (!$this->SupplierPurchaseOrdersItems->saveMany($entities)) {
            Log::write('error', 'Failed to save purchase order items: ' . json_encode($purchaseOrderItems));
            return false;
        }

        return true;
    }

    protected function saveStockRequestItems($stock_request, $data)
    {
        if (!empty($data['product_id']) && is_array($data['product_id'])) {

            $stock_request_id = $stock_request->id;
            $product_ids = $data['product_id'];
            $variant_ids = $data['product_variant_id'];
            $attribute_ids = $data['product_attribute_id'];
            $quantities = $data['quantity'];

            // Delete existing records for this stock request
            $this->StockRequestItems->deleteAll(['stock_request_id' => $stock_request_id]);

            for ($i = 0; $i < count($product_ids); $i++) {
                $stockRequestItem = $this->StockRequestItems->newEntity([
                    'stock_request_id' => $stock_request_id,
                    'product_id' => $product_ids[$i],
                    'product_variant_id' => $variant_ids[$i] ?? null,
                    'product_attribute_id' => $attribute_ids[$i] ?? null,
                    'requested_quantity' => $quantities[$i] ?? 0
                ]);

                if (!$this->StockRequestItems->save($stockRequestItem)) {
                    return false;
                }
            }
        }

        return true;
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The stock request could not be deleted. Please, try again.'];

        try {
            $record = $this->StockRequests->get($id);
            $record->status = 'D';

            if ($this->StockRequests->save($record)) {

                $supplierPurchaseOrder = $this->SupplierPurchaseOrders->find()
                    ->where(['SupplierPurchaseOrders.stock_request_id' => $id])
                    ->first();

                if ($supplierPurchaseOrder) {
                   
                    $supplierPurchaseOrder->status = 'D';

                    $this->SupplierPurchaseOrders->save($supplierPurchaseOrder);

                    $response = ['success' => true, 'message' => 'The stock request has been marked as deleted.'];
                }
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function edit($id = null)
    {

        $stock_request = $this->StockRequests->get($id, contain: []);

        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();
            
            $stock_request = $this->StockRequests->patchEntity($stock_request, $this->request->getData());

            if ($this->StockRequests->save($stock_request)) {

                $this->saveStockRequestItems($stock_request, $data);

                $purchaseOrder = $this->editSupplierPurchaseOrder($stock_request, $data);

                if ($purchaseOrder) {
                    // Save related purchase order items
                    $this->saveSupplierPurchaseOrderItems($purchaseOrder, $stock_request);
                    
                    $this->Flash->success(__('The stock request have been saved.'));

                    return $this->redirect(['action' => 'index']);

                } else {
                    $this->Flash->error(__('The stock request could not be saved. Please, try again.'));
                }

            }
            else
            {
                $this->Flash->error(__('The stock request could not be saved. Please, try again.'));
            }
        }

        $stockRequest = $this->StockRequests->find()
            ->select([
                'StockRequests.id',
                'StockRequests.request_date',
                'StockRequests.request_status',
                'StockRequests.supervisor_verified_time',
                'StockRequests.requested_by',
                'StockRequests.verified_by',
                'StockRequests.status',
                'Warehouses.name',
                'Suppliers.name',
                'Suppliers.credit_period',
                'Users.first_name',
                'Users.last_name',
                'SupplierPurchaseOrders.id',
                'SupplierPurchaseOrders.order_date',
                'SupplierPurchaseOrders.bill_no',
                'SupplierPurchaseOrders.supplier_bill_no',
                'SupplierPurchaseOrders.payment_status',
                'SupplierPurchaseOrders.delivery_status',
                'SupplierPurchaseOrders.required_delivery_date',
                'SupplierPurchaseOrders.payment_due_date'
            ])
            ->join([
                'table' => 'supplier_purchase_orders',
                'alias' => 'SupplierPurchaseOrders',
                'type' => 'INNER',
                'conditions' => 'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
            ])
            ->contain([
                'Warehouses',
                'Suppliers',
                'Users'
            ])
            ->where(['StockRequests.id' => $id])
            ->first();

        // Fetch StockRequestItems for the StockRequest with id = 7
        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->select([
                'StockRequestItems.id',
                'StockRequestItems.requested_quantity',
                'StockRequestItems.product_id',
                'StockRequestItems.product_variant_id',
                'StockRequestItems.product_attribute_id',
                'StockRequestItems.stock_request_id',
                'StockRequestItems.supervisor_approved_quantity',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockRequestItems.stock_request_id' => $id])
            ->toArray();
                
        foreach ($stockRequestItems as &$item) {
            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        $products = $this->Products->find()
            ->where(['Products.status' => 'A'])
            ->order(['Products.name' => 'ASC'])
            ->toArray();
        
        $stock_request = $this->StockRequests->get($id, [
            'contain' => ['Suppliers']
        ]);

        $supplierId = $stock_request->supplier_id;

        $products = $this->Suppliers->Products->find()
            ->distinct(['Products.id'])
            ->matching('SupplierProducts', function ($q) use ($supplierId) {
                return $q->where(
                    [
                        'SupplierProducts.status' => 'A',
                        'SupplierProducts.supplier_id' => $supplierId
                    ]
                );
            })
            ->all();

        $this->set(compact('stockRequest', 'stockRequestItems', 'products'));
    }

    protected function editSupplierPurchaseOrder($stockRequest, $data)
    {
        // Check if a Supplier Purchase Order exists for the given Stock Request ID
        $purchaseOrder = $this->SupplierPurchaseOrders
            ->find()
            ->where(['stock_request_id' => $stockRequest->id])
            ->first();

        if (!$purchaseOrder) {
            // If no existing purchase order, create a new one
            $purchaseOrder = $this->SupplierPurchaseOrders->newEmptyEntity();
        }

        // Prepare the data to be patched into the purchase order
        $purchaseOrderData = [
            'stock_request_id' => $stockRequest->id,
            'order_date' => $data['order_date'],
            'bill_no' => $data['bill_no'],
            'supplier_bill_no' => $data['supplier_bill_no'],
            'status' => $data['status'],
            // 'payment_status' => $data['payment_status'],
            'delivery_status' => $data['delivery_status'],
            'required_delivery_date' => $data['required_delivery_date']
            // 'payment_due_date' => $data['payment_due_date'],
            // Add other fields as needed
        ];

        // Patch the existing or new purchase order with the data
        $purchaseOrder = $this->SupplierPurchaseOrders->patchEntity($purchaseOrder, $purchaseOrderData);

        // Save the updated purchase order
        if ($this->SupplierPurchaseOrders->save($purchaseOrder)) {
            return $purchaseOrder;
        }
        
        // If save fails, return false
        return false;
    }

    public function view($id = null)
    {
        $stockRequest = $this->StockRequests->find()
            ->select([
                'StockRequests.id',
                'StockRequests.request_date',
                'StockRequests.request_status',
                'StockRequests.supervisor_verified_time',
                'StockRequests.requested_by',
                'StockRequests.verified_by',
                'StockRequests.status',
                'StockRequests.supplier_id',
                'Warehouses.name',
                'Suppliers.name',
                'Users.first_name',
                'Users.last_name',
                'SupplierPurchaseOrders.id',
                'SupplierPurchaseOrders.order_date',
                'SupplierPurchaseOrders.bill_no',
                'SupplierPurchaseOrders.supplier_bill_no',
                'SupplierPurchaseOrders.payment_status',
                'SupplierPurchaseOrders.delivery_status',
                'SupplierPurchaseOrders.required_delivery_date',
                'SupplierPurchaseOrders.payment_due_date',
                'StockMovements.movement_date'
            ])
            ->join([
                [
                    'table' => 'supplier_purchase_orders',
                    'alias' => 'SupplierPurchaseOrders',
                    'type' => 'INNER',
                    'conditions' => 'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
                ],
                [
                    'table' => 'stock_movements',
                    'alias' => 'StockMovements',
                    'type' => 'LEFT',
                    'conditions' => 'StockMovements.referenceID = StockRequests.id'
                ]
            ])
            ->contain([
                'Warehouses',
                'Suppliers',
                'Users'
            ])
            ->where(['StockRequests.id' => $id])
            ->first();

        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->select([
                'StockRequestItems.id',
                'StockRequestItems.requested_quantity',
                'StockRequestItems.product_id',
                'StockRequestItems.product_variant_id',
                'StockRequestItems.product_attribute_id',
                'StockRequestItems.stock_request_id',
                'StockRequestItems.supervisor_approved_quantity',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku', 
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where(['StockRequestItems.stock_request_id' => $id])
            ->toArray();


        foreach ($stockRequestItems as &$item) {

            // If product_variant_id is available, get the supplier_price from SupplierProducts
            if ($item->product_variant_id) {
                $supplierPriceQuery = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where([
                        'SupplierProducts.supplier_id' => $stockRequest->supplier_id,
                        'SupplierProducts.product_id' => $item->product_id,
                        'SupplierProducts.product_variant_id' => $item->product_variant_id,
                    ])
                    ->first();

                // Set the supplier price if found
                if ($supplierPriceQuery) {
                    $item->supplier_price = $supplierPriceQuery->supplier_price;
                }
            } else {
                // If no product_variant_id, get supplier price from SupplierProducts by product_id
                $supplierPriceQuery = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where([
                        'SupplierProducts.supplier_id' => $stockRequest->supplier_id,
                        'SupplierProducts.product_id' => $item->product_id,
                    ])
                    ->first();

                // Set the supplier price if found
                if ($supplierPriceQuery) {
                    $item->supplier_price = $supplierPriceQuery->supplier_price;
                }
            }

            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        $requested_user = $this->Authentication->getIdentity();
        
        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $this->set(compact('stockRequest', 'stockRequestItems', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'role')); 
    }

    public function getVariants($productId)
    {
        $this->request->allowMethod(['get']);

        // Fetch variants for the selected product
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku'])
            ->where(['ProductVariants.product_id' => $productId])
            ->andWhere(['ProductVariants.status' => 'A'])
            ->toArray();

        // Prepare the response
        $response = [
            'variants' => [],
            'attributes' => []
        ];

        $validVariants = [];

        foreach ($variants as $variant) {
            // Check if the variant exists in the supplier_products table with status 'A'
            $existsInSupplierProducts = $this->SupplierProducts->exists([
                'product_variant_id' => $variant->id,
                'status' => 'A'
            ]);

            if ($existsInSupplierProducts) {
                $validVariants[] = $variant->id;
                $response['variants'][$variant->id] = [
                    'name' => $variant->variant_name,
                    'sku' => $variant->sku,
                ];
            }
        }

        // Only fetch attributes if valid variants exist
        if (!empty($validVariants)) {
            $productAttributes = $this->ProductAttributes->find()
                ->contain(['Attributes', 'AttributeValues'])
                ->where(['ProductAttributes.product_id' => $productId])
                ->andWhere(['ProductAttributes.status' => 'A'])
                ->toArray();

            foreach ($productAttributes as $productAttribute) {
                $response['attributes'][] = [
                    'attribute_id' => $productAttribute->id,
                    'attribute_name' => $productAttribute->attribute->name,
                    'attribute_value' => $productAttribute->attribute_value->value,
                ];
            }
        }

        $this->set([
            'response' => $response,
            '_serialize' => ['response'],
        ]);

        // Return JSON response
        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    
    //Get no stock request
     public function getNoStockRequests()
    {
        Log::debug('getNoStockRequests called');
        $this->request->allowMethod(['ajax']); // Allow only AJAX

        $data = $this->StockRequests->getNoStockRequest();
Log::debug('No Stock Requests: ' . json_encode($data));
        return $this->response
                    ->withType('application/json')
                    ->withStringBody(json_encode($data));
        // $this->set([
        //     'success' => true,
        //     'data' => $data,
        //     '_serialize' => ['success', 'data']
        // ]);
    }
}
