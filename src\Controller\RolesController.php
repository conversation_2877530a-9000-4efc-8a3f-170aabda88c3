<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Routing\Router;
use App\Model\Table\ModulesTable;
use App\Model\Table\PermissionsTable;
use Cake\View\JsonView;
use Cake\Core\Configure;

/**
 * Roles Controller
 *
 * @property \App\Model\Table\RolesTable $Roles
 */
class RolesController extends AppController
{
    protected $Modules;
    protected $Permissions;

    public function initialize(): void
    {
        parent::initialize();
        $this->Modules = $this->fetchTable('Modules');
        $this->Permissions = $this->fetchTable('Permissions');
        $this->viewBuilder()->setLayout('admin');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'login', 'logout']);
    }

    public function index()
    {

        $query = $this->Roles->find();

        $query->select([
                'Roles.id',
                'Roles.name',
                'Roles.status',
                'user_count' => $query->func()->count('Users.id')
            ])
            ->leftJoinWith('Users', function ($q) {
                return $q->where(['Users.status' => 'A']);
             })
            ->group(['Roles.id'])
            ->order(['Roles.name' => 'ASC']);

        $query->where(['Roles.status !=' => 'D']);
        $roles = $query->toArray();

        $title = 'Manage Roles';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $this->set(compact('roles', 'title', 'status', 'statusMap'));
    }

    public function view($id = null)
    {

        $role = $this->Roles->get($id, [
            'contain' => ['Permissions']
        ]);

        $modules = $this->Modules->find('all')->toArray();
        $moduleHierarchy = $this->buildHierarchy($modules);
        // echo "<pre>"; print_r($moduleHierarchy); die;

        $permissionsData = $this->Permissions->find('all', [
            'conditions' => ['Permissions.role_id' => $id]
        ])->toArray();

        $permissions = [];
        foreach ($permissionsData as $permission) {
            $permissions[$permission->module_id] = [
                'can_view' => !empty($permission->can_view) ? $permission->can_view : 0,
                'can_create' => !empty($permission->can_create) ? $permission->can_create : 0,
                'can_edit' => !empty($permission->can_edit) ? $permission->can_edit : 0,
                'can_approve' => !empty($permission->can_approve) ? $permission->can_approve : 0,
                'store_based' => !empty($permission->store_based) ? $permission->store_based : 0,
                'can_delete' => !empty($permission->can_delete) ? $permission->can_delete : 0,
            ];
        }

        $title = 'Role | View';
        $this->set(compact('role', 'modules', 'permissions', 'title'));
    }

    public function add()
    {
        $role = $this->Roles->newEmptyEntity();
        if ($this->request->is('post')) {
            $inputData = $this->request->getData();
            $inputData['slug'] = $this->stringToSlug(trim($inputData['name']));
            $role = $this->Roles->patchEntity($role, $inputData);
            $roleId = $this->Roles->save($role);
            if ($roleId) {
                $id = $roleId->id;
                $permissionsData = $this->request->getData('Permission');
                
                try {
                    // Get all modules to build hierarchy
                    $modules = $this->Modules->find('all')->toArray();
                    
                    // Process permissions and ensure parent permissions are created
                    $processedPermissions = $this->processPermissionsWithHierarchy($permissionsData, $modules);
                    
                    foreach ($processedPermissions as $moduleId => $permissions) {
                        $permission = $this->Permissions->newEntity([
                            'role_id' => $id,
                            'module_id' => $moduleId,
                            'can_view' => !empty($permissions['_read']) ? 1 : 0,
                            'can_create' => !empty($permissions['_create']) ? 1 : 0,
                            'can_edit' => !empty($permissions['_update']) ? 1 : 0,
                            'can_approve' => !empty($permissions['_approve']) ? 1 : 0,
                            'store_based' => !empty($permissions['_store']) ? 1 : 0,
                            'can_delete' => !empty($permissions['_delete']) ? 1 : 0
                        ]);
                        $this->Permissions->save($permission);
                    }
                } catch (\Exception $e) {
                    // Log the error for debugging
                    \Cake\Log\Log::error('Error processing permissions for new role ' . $id . ': ' . $e->getMessage());
                    $this->Flash->error(__('There was an error processing permissions. Please try again.'));
                    return $this->redirect(['action' => 'add']);
                }

                $this->Flash->success(__('The role has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The role could not be saved. Please, try again.'));
        }

        $modules = $this->Modules->find('all')->toArray();
        $moduleHierarchy = $this->buildHierarchy($modules);

        $title = 'Role | Add';
        $this->set(compact('role', 'title', 'modules'));
    }

    private function buildHierarchy($modules, $parentId = null)
    {
        $hierarchy = [];
        foreach ($modules as $module) {
            if ($module->parent_id === $parentId) {
                $children = $this->buildHierarchy($modules, $module->id);
                if ($children) {
                    $module->children = $children;
                }
                $hierarchy[] = $module;
            }
        }
        return $hierarchy;
    }

    public function edit($id = null)
    {
        $role = $this->Roles->get($id, [
            'contain' => ['Permissions']
        ]);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $permissionsData = $this->request->getData('Permission');

            $role = $this->Roles->patchEntity($role, $this->request->getData());

            if ($this->Roles->save($role)) {
                $this->Permissions->deleteAll(['role_id' => $id]);
                
                try {
                    // Get all modules to build hierarchy
                    $modules = $this->Modules->find('all')->toArray();
                    $moduleHierarchy = $this->buildHierarchy($modules);
                    
                    // Process permissions and ensure parent permissions are created
                    $processedPermissions = $this->processPermissionsWithHierarchy($permissionsData, $modules);
                    
                    foreach ($processedPermissions as $moduleId => $permissions) {
                        $permission = $this->Permissions->newEntity([
                            'role_id' => $id,
                            'module_id' => $moduleId,
                            'can_view' => !empty($permissions['_read'] ?? '0') ? 1 : 0,
                            'can_create' => !empty($permissions['_create'] ?? '0') ? 1 : 0,
                            'can_edit' => !empty($permissions['_update'] ?? '0') ? 1 : 0,
                            'can_approve' => !empty($permissions['_approve'] ?? '0') ? 1 : 0,
                            'store_based' => !empty($permissions['_store'] ?? '0') ? 1 : 0,
                            'can_delete' => !empty($permissions['_delete'] ?? '0') ? 1 : 0
                        ]);
                        $this->Permissions->save($permission);
                    }
                } catch (\Exception $e) {
                    // Log the error for debugging
                    \Cake\Log\Log::error('Error processing permissions for role ' . $id . ': ' . $e->getMessage());
                    $this->Flash->error(__('There was an error processing permissions. Please try again.'));
                    return $this->redirect(['action' => 'edit', $id]);
                }
            }

            $this->Flash->success(__('The permissions have been saved.'));
            return $this->redirect(['action' => 'index']);
        }

        $modules = $this->Modules->find('all')->toArray();
        $moduleHierarchy = $this->buildHierarchy($modules);
        // echo "<pre>"; print_r($moduleHierarchy); die;

        $permissionsData = $this->Permissions->find('all', [
            'conditions' => ['Permissions.role_id' => $id]
        ])->toArray();

        $permissions = [];
        foreach ($permissionsData as $permission) {
            $permissions[$permission->module_id] = [
                'can_view' => !empty($permission->can_view) ? $permission->can_view : 0,
                'can_create' => !empty($permission->can_create) ? $permission->can_create : 0,
                'can_edit' => !empty($permission->can_edit) ? $permission->can_edit : 0,
                'can_approve' => !empty($permission->can_approve) ? $permission->can_approve : 0,
                'store_based' => !empty($permission->store_based) ? $permission->store_based : 0,
                'can_delete' => !empty($permission->can_delete) ? $permission->can_delete : 0,
            ];
        }

        $title = 'Role | Edit';
        $this->set(compact('role', 'modules', 'permissions', 'title'));
    }

    /**
     * Process permissions ensuring parent permissions are created when child permissions exist
     *
     * @param array $permissionsData The permissions data from the form
     * @param array $modules All modules array
     * @return array Processed permissions with parent permissions included
     */
    private function processPermissionsWithHierarchy($permissionsData, $modules)
    {
        // First validate and sanitize the input data
        $validatedData = $this->validatePermissionData($permissionsData);
        $processedPermissions = $validatedData;
        
        // Create a map of module ID to module data for quick lookup
        $moduleMap = [];
        foreach ($modules as $module) {
            $moduleMap[$module->id] = $module;
        }
        
        // For each permission granted, ensure all parent modules also have permissions
        foreach ($validatedData as $moduleId => $permissions) {
            $currentModule = $moduleMap[$moduleId] ?? null;
            if ($currentModule && $currentModule->parent_id !== null) {
                $this->ensureParentPermissions($currentModule, $permissions, $processedPermissions, $moduleMap);
            }
        }
        
        return $processedPermissions;
    }
    
    /**
     * Normalize a permission array to ensure it has all required keys
     *
     * @param array $permissions The permissions array to normalize
     * @return array Normalized permissions array
     */
    private function normalizePermissionArray($permissions)
    {
        $defaultPermissions = [
            '_read' => '0',
            '_create' => '0',
            '_update' => '0',
            '_approve' => '0',
            '_store' => '0',
            '_delete' => '0'
        ];
        
        // If permissions is not an array, return defaults
        if (!is_array($permissions)) {
            return $defaultPermissions;
        }
        
        // Merge with defaults to ensure all keys exist
        return array_merge($defaultPermissions, $permissions);
    }
    
    /**
     * Validate and sanitize permission data from the form
     *
     * @param array $permissionsData The raw permissions data from the form
     * @return array Validated and sanitized permissions data
     */
    private function validatePermissionData($permissionsData)
    {
        if (!is_array($permissionsData)) {
            return [];
        }
        
        $validatedData = [];
        foreach ($permissionsData as $moduleId => $permissions) {
            if (!is_numeric($moduleId) || !is_array($permissions)) {
                continue; // Skip invalid entries
            }
            
            $validatedData[$moduleId] = $this->normalizePermissionArray($permissions);
        }
        
        return $validatedData;
    }
    
    /**
     * Recursively ensure parent modules have permissions when child has permissions
     *
     * @param object $module The current module
     * @param array $childPermissions The permissions of the child module
     * @param array &$processedPermissions Reference to the permissions array to modify
     * @param array $moduleMap Map of module ID to module data
     */
    private function ensureParentPermissions($module, $childPermissions, &$processedPermissions, $moduleMap)
    {
        if ($module->parent_id === null) {
            return; // Reached top level
        }
        
        $parentModule = $moduleMap[$module->parent_id] ?? null;
        if (!$parentModule) {
            return; // Parent module not found
        }
        
        // Initialize parent permissions if they don't exist
        if (!isset($processedPermissions[$parentModule->id])) {
            $processedPermissions[$parentModule->id] = [
                '_read' => '0',
                '_create' => '0',
                '_update' => '0',
                '_approve' => '0',
                '_store' => '0',
                '_delete' => '0'
            ];
        }
        
        // Grant parent permissions based on child permissions
        // If child has any permission, parent should have at least view permission
        if ($childPermissions['_read'] == '1' || 
            $childPermissions['_create'] == '1' || 
            $childPermissions['_update'] == '1' || 
            $childPermissions['_approve'] == '1' || 
            $childPermissions['_store'] == '1' || 
            $childPermissions['_delete'] == '1') {
            
            $processedPermissions[$parentModule->id]['_read'] = '1';
        }
        
        // If child has create permission, parent should have create permission
        if ($childPermissions['_create'] == '1') {
            $processedPermissions[$parentModule->id]['_create'] = '1';
        }
        
        // If child has update permission, parent should have update permission
        if ($childPermissions['_update'] == '1') {
            $processedPermissions[$parentModule->id]['_update'] = '1';
        }
        
        // If child has approve permission, parent should have approve permission
        if ($childPermissions['_approve'] == '1') {
            $processedPermissions[$parentModule->id]['_approve'] = '1';
        }
        
        // If child has store permission, parent should have store permission
        if ($childPermissions['_store'] == '1') {
            $processedPermissions[$parentModule->id]['_store'] = '1';
        }
        
        // If child has delete permission, parent should have delete permission
        if ($childPermissions['_delete'] == '1') {
            $processedPermissions[$parentModule->id]['_delete'] = '1';
        }
        
        // Recursively ensure grandparent permissions
        $this->ensureParentPermissions($parentModule, $processedPermissions[$parentModule->id], $processedPermissions, $moduleMap);
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        try {
            $record = $this->Roles->get($id);
            $record->status = 'D';
            if ($this->Roles->save($record)) {
                $response = ['success' => true, 'message' => 'The role has been marked as deleted.'];
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function filterSearch()
    {
        $status = $this->request->getQuery('filterStatus');

        if ($this->request->is('ajax')) {

            $query = $this->Roles->find();

            if ($status) {
                $query->where(['Roles.status' => $status]);
            } else {
                $query->where(['Roles.status' => 'A']);
            }
            $roles = [];
            $i = 1;
            foreach ($query as $role) {

                $statusMap = [
                    'A' => ['label' => 'Active', 'class' => 'col-green'],
                    'I' => ['label' => 'Inactive', 'class' => 'col-blue'],
                    'D' => ['label' => 'Deleted', 'class' => 'col-red']
                ];

                $status = $statusMap[$role->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                $roles[] = [
                    'id' => $i,
                    'name' => $role->name,
                    'description' => $role->description,
                    'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                    'actions' => '<a href="' . Router::url(['controller' => 'Brands', 'action' => 'view', $role->id], true) . '" class="btn-view" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a> ' .
                        '<a href="' . Router::url(['controller' => 'Roles', 'action' => 'edit', $role->id], true) . '" class="btn-edit" data-toggle="tooltip" title="Edit"><i class="far fa-edit m-r-10"></i></a>' .
                        '<a href="' . Router::url(['controller' => 'Roles', 'action' => 'delete', $role->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'
                ];
                $i++;
            }

            $this->set([
                'brands' => $roles,
                '_serialize' => ['roles'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $roles]));
        }

        return null;
    }
}