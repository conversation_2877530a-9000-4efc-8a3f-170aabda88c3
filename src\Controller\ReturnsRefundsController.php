<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ReturnsRefundsController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $OrderReturns;
    protected $Roles;
    protected $Users;
    protected $Showrooms;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->Roles = $this->fetchTable('Roles');
        $this->Users = $this->fetchTable('Users');
        $this->Showrooms = $this->fetchTable('Showrooms');
    }

    public function index()
    {

        // $orderRefunds = $this->OrderReturns->find()
        //     ->contain([
        //         'OrderItems' => ['fields' => ['id', 'status']],
        //         'Orders' => [
        //             'Customers' => [
        //                 'Users'
        //             ]
        //         ]
        //     ])
        //     ->where(['OrderReturns.status IN' => ['Refund Pending', 'Refunded']])
        //     ->order(['OrderReturns.id' => 'DESC'])
        //     ->enableHydration(false)
        //     ->all();

        // $orderReturns = $this->OrderReturns->find()
        //     ->contain([
        //         'OrderItems' => ['fields' => ['id', 'status']],
        //         'Orders' => [
        //             'fields' => ['id', 'customer_id', 'order_date', 'status'],
        //             'Customers' => [
        //                 'Users' => [
        //                     'fields' => ['id', 'first_name', 'last_name', 'email']
        //                 ]
        //             ],
        //             'Transactions' => [
        //                 'fields' => ['id', 'order_id', 'payment_method', 'payment_status']
        //             ]
        //         ]
        //     ])
        //     ->order(['OrderReturns.id' => 'DESC']) // sort by latest request_id
        //     ->enableHydration(false) // optional: returns raw arrays
        //     ->all();

        $requested_user = $this->Authentication->getIdentity();
        $conditions = [];

        if (!empty($requested_user)) {
            $role = $this->Roles->get($requested_user->role_id);
            $roleName = strtolower($role->name);

            if ($roleName === 'showroom manager') {
                // Show only their own
                $conditions['OrderReturns.requested_by'] = $requested_user->id;

            } elseif ($roleName === 'showroom supervisor') {
                // Get showroom managers under this supervisor
                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id', 'showroom_manager'])
                    ->where(['showroom_supervisor' => $requested_user->id])
                    ->all();

                $managerIds = $supervisorShowrooms
                    ->extract('showroom_manager')
                    ->filter()
                    ->toList();

                // Include supervisor's own records too
                $userIds = array_filter(array_merge($managerIds, [$requested_user->id]));

                $conditions['OrderReturns.requested_by IN'] = !empty($userIds) ? $userIds : [-1];

            } elseif ($roleName === 'call center agent') {
                // Own records only
                $conditions['OrderReturns.requested_by'] = $requested_user->id;

            } elseif ($roleName === 'call center supervisor') {
                // Get role ID for call center agent
                $agentRole = $this->Roles->find()
                    ->select(['id'])
                    ->where(['name' => 'Call Center Agent'])
                    ->first();

                if ($agentRole) {
                    $agentIds = $this->Users->find()
                        ->select(['id'])
                        ->where(['role_id' => $agentRole->id])
                        ->all()
                        ->extract('id')
                        ->toList();

                    $userIds = array_filter(array_merge($agentIds, [$requested_user->id]));

                    $conditions['OrderReturns.requested_by IN'] = !empty($userIds) ? $userIds : [-1];
                } else {
                    $conditions['OrderReturns.requested_by'] = $requested_user->id;
                }

            } elseif ($roleName !== 'admin') {
                // All other roles get nothing
                $conditions['OrderReturns.requested_by'] = -1;
            }
        }


        $orderRefunds = $this->OrderReturns->find()
            ->contain([
                'OrderItems' => ['fields' => ['id', 'status']],
                'Orders' => [
                    'Customers' => [
                        'Users'
                    ]
                ]
            ])
            ->where([
                'OrderReturns.status IN' => ['Refund Pending', 'Refunded'],
                ...$conditions // dynamic condition
            ])
            ->order(['OrderReturns.id' => 'DESC'])
            ->enableHydration(false)
            ->all();


        $orderReturns = $this->OrderReturns->find()
            ->contain([
                'OrderItems' => ['fields' => ['id', 'status']],
                'Orders' => [
                    'fields' => ['id', 'customer_id', 'order_date', 'order_number', 'status'],
                    'Customers' => [
                        'Users' => [
                            'fields' => ['id', 'first_name', 'last_name', 'email' , 'mobile_no']
                        ]
                    ],
                    'Transactions' => [
                        'fields' => ['id', 'order_id', 'payment_method', 'payment_status']
                    ]
                ]
            ])
            ->where($conditions)
            ->order(['OrderReturns.id' => 'DESC'])
            ->enableHydration(false)
            ->all();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');

        $this->set(compact('orderReturns', 'orderRefunds', 'orderstatuses', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'roleName'));


    }

}
