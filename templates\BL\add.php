<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Zone> $zones
 */
?>

<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>

    .select2-container--default .select2-selection--single.is-invalid {
        border-color: #dc3545 !important;
        border-width: 1px;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" fill="%23dc3545" viewBox="0 0 16 16"%3E%3Cpath d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zm0 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm.93-4.588-.857.857a.5.5 0 0 1-.708-.707l.857-.857V5.5a.5.5 0 0 1 1 0v2.912zM8 10.5a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5z"/%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: 1em 1em;
        padding-right: 2.25rem;
    }

    .error-msg {
        color: #dc3545;
        font-size: 0.875em;
        margin-top: 4px;
        display: block;
    }

    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
        margin-bottom: 10px;
    }

    #toRemovePadding {
        margin-bottom: 0px !important;
        padding-bottom: 0px !important;
    }

    #remove-border {
        border: none;
        background-color: transparent;
    }

    #thead {
        background-color: #0d839b !important;
        color: white;
    }

</style>
<?php $this->end();?>

<section class="section">
    <div
        class="section-header d-flex justify-content-between align-items-center mb-3"
    >
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item"><a
                    href="<?= $this->Url->build(['controller' => 'BL', 'action' => 'index']) ?>"><?= __('BL') ?></a>
            </li>
            <li class="breadcrumb-item active"><?= __('Add') ?></li>
        </ul>
        <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
            <small class="p-10 fw-bold"><?= __('BACK') ?></small>
            <span class="rotate me-2">⤣</span>
        </button>
    </div>
</section>    

<div class="section-body">
    <div class="container-fluid">
        <div class="card" id="toRemovePadding">
            <section id="view-product-product-details">
                <form id="add_stock_incoming_form" action="<?= $this->Url->build([
                            'controller' => 'BL',
                            'action' => 'add'
                        ]); ?>" method="post" enctype="multipart/form-data">
                <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                <div class="row align-items-center mt-4">
                    <h3 class="font-22 mb-4">Add New BL</h3>
                    <div class="col-sm-6">
                        <!-- Supplier Dropdown -->
                           <label for="supplierSelect" class="fw-bold"><?= __('Select Supplier:') ?></label>
                        <select name="supplier_id" id="supplierSelect" class="form-control form-select select2">
                            <option value=""><?= __('-- Select a Supplier --') ?></option>
                            
                            <?php foreach ($suppliers as $supplier): ?>
                                <option value="<?= h($supplier->id) ?>"><?= h($supplier->name) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <span id="supplier_error" style="color: #dc3545;display: none;"><?= __('Please Select Supplier') ?></span></div>
                        <div

                        <!-- BL Number Dropdown -->
                        <label for="billNoSelect" class="mt-2 fw-bold"><?= __('Select Bill Number:') ?></label>
                        <select name="stock_request_id" id="billNoSelect" class="form-control form-select select2" >
                            <option value="<?php echo $norequest[0]->id; ?>"><?= __('-- No bill numbers selected --') ?></option>
                        </select>
                        <span id="bill_no_error" style="color: #dc3545;display: none;"><?= __('Please Select Bill Number') ?></span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="table-responsive" id="table_report" tabindex="1">
                            <!-- <table class="table dataTable table-hover table-xl mb-0 mt-4" id="stockTable" style="display:none;">
                                <thead>
                                    <tr>
                                        <th>< ?= __('Id') ?></th>
                                        <th>< ?= __('Product Name') ?></th>
                                        <th>< ?= __('Product Variant') ?></th>
                                        <th>< ?= __('Product Attribute') ?>e</th>
                                        <th>< ?= __('SKU') ?></th>
                                        <th>< ?= __('Quantity') ?></th>
                                    </tr>
                                </thead>
                                <tbody id="table_datalist">
                                </tbody>
                            </table> -->

                            <table id="add_product_table" class="add_product_table table-responsive table table-striped mb-0 mt-4" style="display:none;">
                                <thead>
                                  <tr class="back-clr" id="thead">
                                    <td class="fw-bold"><?= __('Product') ?></td>
                                    <td class="fw-bold"><?= __('Product Variant') ?></td>
                                    <td class="fw-bold"><?= __('Product Attribute') ?></td>
                                    <td class="fw-bold"><?= __('SKU') ?></td>
                                    <td class="fw-bold"><?= __('Quantity') ?></td>
                                    <td class="fw-bold"><?= __('Remaining Quantity') ?></td>
                                    <td class="fw-bold"><?= __('Action') ?></td>
                                  </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <!-- Product Dropdown -->
                                        <td class="col-sm-3">
                                        <select id="product_option" class="form-control form-select product_option select2">
                                            <option value="0"><?= __('Select Product') ?></option>
                                        </select>
                                        <span id="product_option_error" style="color: #dc3545;"></span>
                                        </td>
                                
                                        <td>
                                        <?php 
                                                echo $this->Form->control('variant_id', [
                                                'id' => 'po_variant_id',
                                                'type' => 'select',
                                                'label' => false,
                                                'empty' => __('Select Variant'),
                                                'options' => [],
                                                'class' => 'form-control select2'
                                            ]);
                                        ?>

                                    </td>

                                    <td>
                                        <?php 
                                                echo $this->Form->control('attribute_id', [
                                                'id' => 'po-attribute-dropdown',
                                                'type' => 'select',
                                                'label' => false,
                                                'empty' => __('Select Attribute'),
                                                'options' => [],
                                                'class' => 'form-control select2'
                                            ]);
                                        ?>

                                    </td>

                                    <td>
                                            <span id="product_sku"></span>
                                    </td>
                                
                                        <!-- Quantity Input -->
                                        <td>
                <input type="number" min="1" 
                       class="form-control product_quantity_new" 
                       id="product_quantity[]" 
                       name="product_quantity[]" >
                
            </td>
                                        <td>
                                        <input type="number" min="1" class="form-control remaining_quantity" id="remaining_quantity" readonly >
                                        <span id="remaining_quantity_error" style="color: #dc3545;"></span>
                                        </td>
                                        <!-- Action Button -->
                                        <td>
                                        <button type="button" id="updateQuest" class="btn btn-md updateQuest"><?= __('Add') ?></button>
                                        </td>
                                    </tr>
                                </tbody>
                              </table>

                        </div>
                    </div>
                </div>
                <div class="row pt-4">
                    <div class="col-12">
                            <div class="form-group row">
                                <label for="supplier-bill-no" class="col-sm-2 col-form-label fw-bold" style="width: 180px !important;"><?= __('BL Number') ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5">
                                    <input type="text" name="bl_no" id="supplier-bill-no" class="form-control" style="width: 250px !important;">
                                    <span id="supplier_bill_no_error" style="color: #dc3545;display: none;"><?= __('Please enter supplier bill no.') ?></span>
                                </div>
                                <?= $this->Form->error('bl_no'); ?>
                            </div>
                            <div class="form-group row">
                                <label for="status" class="col-sm-2 col-form-label fw-bold" style="width: 180px !important;"><?= __('PO Number') ?></label>
                                <div class="col-sm-5">
                                    <input type="text" id="pon" style="width: 250px !important;" readonly>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="status" class="col-sm-2 col-form-label fw-bold" style="width: 180px !important;"><?= __('Upload Documents') ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-5">
                                    <?php echo $this->Form->control('document', [
                                        'type' => 'file',
                                        'class' => 'form-control',
                                        'id' => 'imageInput',
                                        'placeholder' => __('Web Image'),
                                        'accept' => implode(',', $webImageType),
                                        'data-max-size' => '10MB',
                                        'label' => false,
                                        'style' => "width: 250px !important;"
                                    ]); ?>
                                    <span><?= $file_acceptance_msg ?></span>
                                    <span id="document_error" style="color: #dc3545;display: none;"><?= __('Please upload a document before saving.') ?></span>
                                    <div id="previeContainer">
                                        <ul id="imagePreviewContainer">

                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="deliveryDate" class="col-sm-2 col-form-label fw-bold" style="width: 180px !important;"><?= __('BL Date') ?> <sup class="text-danger font-11">*</sup></label>
                                <div class="col-sm-4 d-flex">
                                    <input type="date" name="bl_date" id="bl_date" style="width: 250px !important;" readonly value="<?= date('Y-m-d') ?>">
                                    <span id="movement_date_error" style="color: #dc3545;display: none;"><?= __('Please choose delivery date') ?></span>
                                </div>
                            </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 my-4">
                        <div class="text-left">
                            <button class="btn" id="save" style="border-radius: 5px;padding: 5px 25px;text-transform: uppercase;">
                                <?= __('Save') ?>
                            </button>
                        </div>
                    </div>
                </div>
                </form>
            </section>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    let allFiles = [];
    let error = 0;

    document.getElementById('imageInput').addEventListener('change', function (event) {
        let files = Array.from(event.target.files);

        if (files.length === 0) {
            allFiles = [];
            renderPreviews();
            return false;
        }

        let validFiles = [];
        let invalidFiles = [];

        let file = files[0]; // Get only the first file for single image
        let fileExtension = file.name.split('.').pop().toLowerCase();
        let allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'];

        // Check if the file has a valid extension
        if (allowedExtensions.includes(fileExtension)) {

            if (['jpg', 'jpeg', 'png'].includes(fileExtension)) {
                let img = new Image();
                img.src = URL.createObjectURL(file);

                img.onload = function () {
                    let fileSize = file.size / 1024 / 1024;  // Convert file size to MB

                    // Validate file size
                    if (fileSize <= <?= $webImageSize ?>) {
                        validFiles.push(file);  // Add to valid files array
                    } else {
                        invalidFiles.push({ file: file.name, reason: '<?= __('Document dimensions should be between '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight) ?>' });
                    }

                    $('#document_error').hide();
                    finalizeFileProcessing(validFiles, invalidFiles);
                };

                img.onerror = function () {
                    invalidFiles.push({ file: file.name, reason: '<?= __('Unable to load document') ?>' });
                    finalizeFileProcessing(validFiles, invalidFiles);
                };
            } else {

                $('#document_error').hide();

                // Handle PDF, DOC, DOCX
                validFiles.push(file);  // Add non-image files to valid files array (PDF, DOC, DOCX)
                finalizeFileProcessing(validFiles, invalidFiles);
            }

        } else {
            invalidFiles.push({ file: file.name, reason: '<?= __('Invalid document type. Only jpg, jpeg, png, pdf, doc, and docx are allowed.') ?>' });
            finalizeFileProcessing(validFiles, invalidFiles);
        }
    });

    // Finalize file processing
    function finalizeFileProcessing(validFiles, invalidFiles) {
        let html = "<ul>";
        invalidFiles.forEach((file) => {
            html += `<li>${file.file} - ${file.reason}</li>`; 
        });
        html += '</ul>';

        const wrapper = document.createElement('div');
        wrapper.innerHTML = html;

        if (invalidFiles.length > 0) {
            swal({
                title: "<?= __("Error") ?>", 
                content: wrapper,
                icon: "error",
                confirmButtonText: "<?= __("OK") ?>", 
                allowOutsideClick: true 
            });
        }

        if (validFiles.length > 0) {
            allFiles = validFiles.slice(0, 1);  // Keep only one valid file
            updateFileInput();
            renderPreviews();
        }
    }

    // Function to update the file input with only the single valid file
    function updateFileInput() {
        let dataTransfer = new DataTransfer();
        if (allFiles.length > 0) {
            dataTransfer.items.add(allFiles[0]);  // Add only the single valid file
        }
        document.getElementById('imageInput').files = dataTransfer.files;
    }

    // Render preview for the single image or document
    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';

        if (allFiles.length > 0) {
            let file = allFiles[0];
            let li = document.createElement('li');
            li.classList.add('image-thumbnail');

            let fileName = file.name;
            let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
            let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);
            let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
            shortName += '.' + extension;

            if (['pdf', 'doc', 'docx'].includes(extension)) {
                // For PDF, DOC, DOCX: Display an icon or filename
                li.innerHTML = `
                    <i class="fas fa-file-${extension === 'pdf' ? 'pdf' : 'word'}"></i>
                    <span class="file-name" title="${fileName}">${shortName}</span>
                    <button type="button" class="delete-img-btn">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            } else {
                // For Images: Display image preview
                let reader = new FileReader();
                reader.onload = function (e) {
                    li.innerHTML = `
                        <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
                        <span class="image-name" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                };
                reader.readAsDataURL(file);
            }

            previewContainer.appendChild(li);
        }
    }

    // Delete the single file preview
    document.getElementById('imagePreviewContainer').addEventListener('click', function (e) {
        if (e.target.closest('.delete-img-btn')) {
            allFiles = [];

            // Clear file input
            updateFileInput();
            renderPreviews();
        }
    });


    // Initialize Select2 for the dropdowns
    $(document).ready(function() {

        const supplierSelect = $('#supplierSelect');
        const billNoSelect = $('#billNoSelect');

        // Data from PHP passed to JavaScript
        const supplierData = <?= json_encode($suppliers) ?>;

        // Event listener for supplier selection change
        supplierSelect.on('change', function() {
            const selectedSupplierId = $(this).val();
            
            // Clear previous options in billNoSelect
           
            // Check if a supplier is selected
            if (selectedSupplierId) {
                //For no

//End for no stock request
                // If no supplier selected, clear products
                if (selectedSupplierId === '<?= __("Select Supplier") ?>') {
                    $('#product_option').html('<option value="0"><?= __('Select Product') ?></option>');
                    return;
                }
                // Make AJAX call to fetch products for the selected supplier
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'WarehouseStockIncoming', 'action' => 'getProductsBySupplier']) ?>/' + selectedSupplierId,
                    method: 'GET',
                    headers: {
                            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                        },
                    success: function (response) {
                        // Clear the current options in the product dropdown
                        $('#product_option').empty();
                        // Add a default "Select Product" option
                        $('#product_option').append('<option value="0">Select Product</option>');

                        // Loop through the returned products and populate the dropdown
                        response.products.forEach(function(product) {
                            $('#product_option').append(
                                '<option value="' + product.id + '" data-product-sku="' + product.sku + '">' +
                                    product.name +
                                '</option>'
                            );
                        });

                        // Reinitialize the select2 dropdown
                        $('#product_option').select2();
                        $('#add_product_table').show();

                    },
                    error: function () {
                        
                        swal('<?= __('Failed') ?>', '<?= __('Error fetching products!') ?>', 'error');
                    }
                });

                // $('#stockTable tbody').empty();
                $('#add_product_table tbody tr:not(:first-child)').remove();

                // Find the selected supplier data
                const selectedSupplier = supplierData.find(supplier => supplier.id === parseInt(selectedSupplierId));
                // Check if the selected supplier has purchase orders
                billNoSelect.find('option:not(:first)').remove();
                if (selectedSupplier && selectedSupplier.supplier_purchase_orders.length > 0) {
                    

                    // Remove the default option and add new ones
                    selectedSupplier.supplier_purchase_orders.forEach(order => {
                        billNoSelect.append(new Option(order.bill_no, order.stock_request_id));
                    });

                    // selectedSupplier.supplier_purchase_orders.forEach(order => {
                    //     const option = new Option(order.bill_no, order.stock_request_id);
                    //     $(option).attr('data-supplier-bill-no', order.supplier_bill_no); // Add data attribute
                    //     billNoSelect.append(option);
                    // });
                }
            }

            // Refresh the Select2 instance for the billNoSelect dropdown
            billNoSelect.trigger('change.select2');
        });




        
    });

//Check unique bl number in BL
        $('#supplier-bill-no').on('blur', function() {
            
        });

    $(document).on("change", ".product_option", function (event) {

        if($('option:selected', this).val() !== 0)
        {
            var productId = $('option:selected', this).val();

            if(productId == 0)
            {
                $(this).closest("tr").find('#product_sku').text('');
                return false;
            }

            $('#po-attribute-dropdown').empty();
            $('#po-attribute-dropdown').append('<option value="">' + 'Select Attribute' + '</option>');
            $('#po_variant_id').empty();
            $('#po_variant_id').append('<option value="">' + 'Select Variant' + '</option>');
            
            $(this).closest("tr").find('#product_sku').text('');
            var product_sku = $('option:selected', this).attr('data-product-sku');

            if (productId !== 0) {
                 
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'WarehouseStockRequests', 'action' => 'getVariants']) ?>/' + productId,
                    type: 'GET',
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.variants && Object.keys(response.variants).length > 0)
                        {
                            var variantDropdown = $('#po_variant_id');

                            // Clear the existing options in the variant dropdown
                            variantDropdown.empty();

                            const firstVariantKey = Object.keys(response.variants)[0];
                            const firstSKU = response.variants[firstVariantKey].sku;

                            $('#po_variant_id').closest("tr").find('#product_sku').text(firstSKU);

                            $.each(response.variants, function(id, variant) {
                                variantDropdown.append(
                                    $('<option>', {
                                        value: id,
                                        text: variant.name,
                                        'data-sku': variant.sku // Store the SKU in a data attribute
                                    })
                                );
                            });

                        }
                        else
                        {
                            $('#po_variant_id').closest("tr").find('#product_sku').text(product_sku);
                            $('#po_variant_id').empty();
                            $('#po_variant_id').append('<option value="">No variants available</option>');
                        }

                        if (response.attributes.length > 0) {

                            $('#po-attribute-dropdown').empty(); // Clear existing options

                            response.attributes.forEach(function(attribute) {
                                $('#po-attribute-dropdown').append(
                                    $('<option>', {
                                        value: attribute.attribute_id,
                                        text: attribute.attribute_name + ': ' + attribute.attribute_value
                                    })
                                );
                            });

                        } else {
                            $('#po-attribute-dropdown').empty();
                            $('#po-attribute-dropdown').append('<option value="">No attributes available</option>');
                        }
                    },
                    error: function() {

                        swal('<?= __('Failed') ?>', '<?= __('Unable to load variants. Please try again.') ?>', 'error');
                    }
                });
            }
        }
        else
        {
            $(this).closest("tr").find('#product_sku').text('');
            $(this).closest("tr").find('#product_supplier').text('');
            $(this).closest("tr").find('#product_image').attr('src','');
        }

    });

    $("table.add_product_table").on("click", ".updateQuest", function (event) {
        var error = 0;
        let supplier_id = $('#supplierSelect').val();
        if(supplier_id == 0){
            error = 1;
          $(this).find('#supplier_error').html('Please Select Supplier');
        }
        
        if($(this).closest("tr").find('#product_option').val() == 0)
        {  
          error = 1;
          $(this).closest("tr").find('#product_option_error').html('Select product');
        }
        if($(this).closest("tr").find('#product_quantity').val() == "")
        {
          error = 1;
          $(this).closest("tr").find('#product_quantity_error').html('Enter quantity');
        }

        if(error == 0)``
        {

            $('#product_option_error').html('');
            $('#product_quantity_error').html('');

            var newRow = $("<tr>");
            var cols = "";
            var pc_options = $(".product_option > option").clone();  


            cols += '<td><input type="hidden" class="form-control" name="product_id[]" value="'+$(this).closest("tr").find('.product_option').find(':selected').val()+'" />';
            cols += '<select class="form-control product_option ctemp1"/></select></td>';

            cols += '<td><input type="hidden" class="form-control" name="product_variant_id[]" value="'+$(this).closest("tr").find('#po_variant_id').find(':selected').val()+'" />';
            cols += '<input type="text" class="form-control ctemp2" value="'+$(this).closest("tr").find('#po_variant_id').find(':selected').text()+'"/></td>';

            cols += '<td><input type="hidden" class="form-control" name="product_attribute_id[]" value="'+$(this).closest("tr").find('#po-attribute-dropdown').find(':selected').val()+'" />';
            cols += '<input type="text" class="form-control ctemp3" value="'+$(this).closest("tr").find('#po-attribute-dropdown').find(':selected').text()+'"/></td>';

            cols += '<td><input type="hidden" class="form-control" name="sku[]" value="'+$(this).closest("tr").find("#product_sku").text()+'"/>';
            cols += '<input type="text" class="form-control ttemp1" value="'+$(this).closest("tr").find("#product_sku").text()+'"/></td>';

            // cols += '<td><input type="text" class="form-control ttemp2" value="'+$(this).closest("tr").find('.product_option').find(':selected').attr('data-product-supplier')+'"/></td>';

            cols += '<td><input type="hidden" class="form-control" name="quantity[]" value="'+$(this).closest("tr").find(".product_quantity_new").val()+'"/>';
            cols += '<input type="text" class="form-control ttemp4" value="'+$(this).closest("tr").find(".product_quantity_new").val()+'"/></td>';

            // cols += '<td><input type="hidden" class="form-control" name="defective_quantity[]" value="'+$(this).closest("tr").find(".defective_quantity").val()+'"/>';
            // cols += '<input type="text" class="form-control ttemp4" value="'+$(this).closest("tr").find(".defective_quantity").val()+'"/></td>';

            // cols += '<td><input type="hidden" class="form-control" name="damaged_quantity[]" value="'+$(this).closest("tr").find(".damaged_quantity").val()+'"/>';
            // cols += '<input type="text" class="form-control ttemp4" value="'+$(this).closest("tr").find(".damaged_quantity").val()+'"/></td>';
           
            // cols += '<td><img style="height: 40px;width: auto;" src="'+$(this).closest("tr").find('.product_option').find(':selected').attr('data-product-image')+'"></img></td>';


            cols += '<td><button type="button" class="ibtnDel btn admin_btn"><?= __("Delete") ?></button></td></tr>';
            newRow.append(cols);
            $("table.add_product_table").append(newRow);
            $(".ctemp1").append(pc_options); 
            $(".ctemp1").prop('disabled', 'true');

            $(".ctemp2").prop('disabled', 'true'); 
            $(".ctemp3").prop('disabled', 'true');

            $(".ttemp1").prop('disabled', 'true');
            $(".ttemp2").prop('disabled', 'true');
            $(".ttemp4").prop('disabled', 'true');

            $(".ctemp1 option[value='"+$(this).closest("tr").find(".product_option").find(':selected').val()+"']").attr('selected','selected');
            $(".product_option").removeClass('ctemp1');

            $(".ctemp2 option[value='"+$(this).closest("tr").find("#po_variant_id").find(':selected').val()+"']").attr('selected','selected');
            $("#po_variant_id").removeClass('ctemp2');

            $(".ctemp3 option[value='"+$(this).closest("tr").find("#po-attribute-dropdown").find(':selected').val()+"']").attr('selected','selected');
            $("#po-attribute-dropdown").removeClass('ctemp3');

            $('#po-attribute-dropdown').empty();
            $('#po-attribute-dropdown').append('<option value="">' + 'Select Attribute' + '</option>');
            $('#po_variant_id').empty();
            $('#po_variant_id').append('<option value="">' + 'Select Variant' + '</option>');

            $(this).closest("tr").find(".product_option").val(0).trigger('change');
            $(this).closest("tr").find(".product_quantity_new").val("");
            $(this).closest("tr").find(".damaged_quantity").val("");
            $(this).closest("tr").find(".defective_quantity").val("");
            $(this).closest("tr").find('#product_sku').text('');
            $(this).closest("tr").find('#product_image').attr('src','');
        }

    });
    $("table.add_product_table").on("click", ".ibtnDel", function (event) {    
         $(this).closest("tr").remove(); 
    });

    $('#po_variant_id').change(function() {
        var selectedSku = $(this).find('option:selected').data('sku');
        
        $(this).closest("tr").find('#product_sku').text(selectedSku);
    });

    $('#billNoSelect').on('change', function () {

        var selectedBillId = $(this).val();
        var selectedBillNo = $(this).find('option:selected').text();

        var selectedSupplier = $('#supplierSelect').val();

        var selectedSupplierBillNo = $(this).find('option:selected').attr('data-supplier-bill-no');

        $('#pon').val(selectedBillNo);
        // $('#supplier-bill-no').val(selectedSupplierBillNo);

        // Check if a Bill is selected
        if (selectedBillId) {

            // $('#stockTable tbody').empty();
            $('#add_product_table tbody tr:not(:first-child)').remove();

            // Make an AJAX call to fetch StockRequestItems based on selected Bill ID
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'WarehouseStockIncoming', 'action' => 'getStockRequestItems']) ?>",
                method: 'GET',
                data: { stock_request_id: selectedBillId },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function (response) {
// If data is found, show the table and populate it
                    if (response.success && response.data.length) {
                        $('#stockTable').show();
                        $('#add_product_table').show();
                        var tableData = '';

                        // Loop through the response data and generate table rows
                        $.each(response.data, function (index, item) {
                        if( item.requested_quantity > 0) {
                        tableData += `<tr>

                                <td><input type="hidden" class="form-control" name="product_id[]" value="${item.product_id}">${item.product_name}</td>

                                <td><input type="hidden" class="form-control" name="product_variant_id[]" value="${item.product_variant_id}">${item.product_variant}</td>

                                <td><input type="hidden" class="form-control" name="product_attribute_id[]" value="${item.product_attribute_id}">${item.product_attribute}</td>

                                <td><input type="hidden" class="form-control" name="sku[]" value="${item.sku}">${item.sku}</td>


                                <td>
                                    <input type="number" min="1" class="form-control product_quantity" name="quantity[]" value="" id="quantity" >
                                    <span id="product_quantity_error" class="product_quantity_error" style="color: #dc3545;"></span>

                                </td>
                                <td>
                                    <input type="number" min="1" class="form-control" name="remaining_quantity[]" value="${item.requested_quantity}" id="remaining_quantity" readonly >
                                </td>
                                <td><button type="button" class="ibtnDel btn admin_btn"><?= __('Delete') ?></button></td>
                                </tr>
                            `;
                        }
                        });
                        
                        // Append rows to table
                        // $('#table_datalist').html(tableData);
                        $('#add_product_table tr:last').after(tableData);
                    } else {
                        // Handle if no data is found
                        swal('<?= __('Failed') ?>', '<?= __('No items found for the selected Bill.') ?>', 'error');
                    }


                    //  $('#add_product_table tbody').append(tableData);
                    // Add validation for quantity vs remaining_quantity
                    $('#add_product_table').on('input', '#quantity', function () {
                        let qty = parseInt($(this).val()) || 0;
                        let remaining = parseInt($(this).closest('tr').find('#remaining_quantity').val()) || 0;

                        if (qty > remaining) {
                            swal('<?= __('Invalid Quantity') ?>', '<?=  __('Quantity must be less than remaining quantity.') ?>', 'error');
                            $(this).val(remaining); // reset to valid value
                        }
                    });
                },
                error: function () {
                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch products. Please try again.') ?>', 'error');
                }
            });
        } else {
            // If no bill is selected, hide the table
            // $('#stockTable').hide();
            $('#add_product_table').hide();
        }
    });


    $('#save').on('click', function(event) {
const form = document.getElementById('add_stock_incoming_form');
    // if (!form.checkValidity()) {
    //     event.preventDefault(); // stop submit
    //     form.reportValidity();  // show built-in error popup
    //     return;
    // }
        event.preventDefault(); // Prevent the default form submission
        let error = 0;
        if($("#supplierSelect option:selected").val() == '' || $("#supplierSelect option:selected").val() == null || $("#supplierSelect option:selected").val() == '<?= __('Select Supplier') ?>')
        {
            error = 1;
            $('#supplier_error').show();
            $('#supplierSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');

        }

        // if($("#billNoSelect option:selected").val() == '' || $("#billNoSelect option:selected").val() == null || $("#billNoSelect option:selected").val() == '<?= __('Select Bill No') ?>')
        // {
        //     error = 1;
        //     $('#bill_no_error').show();
        //     $('#billNoSelect').next('.select2-container').find('.select2-selection').addClass('is-invalid');

        // }
$('.product_quantity').each(function () {
    if ($(this).val() === "") {
        error = 1;
        $(this).closest("tr").find('.product_quantity_error')
             .html('Enter quantity');
    } else if ($(this).val() === "0") {
        error = 1;
        $(this).closest("tr").find('.product_quantity_error')
             .html('Enter a quantity greater than 0 or Delete this product');
     }
     else{
        $(this).closest("tr").find('.product_quantity_error')
             .html('');
    }

    
    

});
        if($("#bl_date").val() == '' || $("#bl_date").val() == null)
        {

            error = 1;
            $('#movement_date_error').show();
            $('#bl_date').addClass('is-invalid');
        }
        if($("#supplier-bill-no").val() == '' || $("#supplier-bill-no").val() == null)
        {
            error = 1;
            $('#supplier_bill_no_error').show();
            $('#supplier-bill-no').addClass('is-invalid');
        }
        
        // Check if a document is uploaded
        let fileInput = $("#imageInput")[0].files;
        if (fileInput.length === 0) {
            error = 1;

            $('#document_error').show();
            $('#imageInput').addClass('is-invalid');
        }
        
        //Check uniqe bl number in for supplier
        
        let supplierId = $('#supplierSelect').val(); // make sure you have supplier_id field
            let blNo = $('#supplier-bill-no').val();          
            // if (blNo.trim() === '') return;
            
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'BL', 'action' => 'checkUniqueBl']) ?>',
                    method: 'POST',
                    data: {
                        supplier_id: supplierId,
                        bl_no: blNo
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute("csrfToken") ?>'
                    },
                    success: function(response) {
                        console.log(response);
                        if (response.exists) {
                            error = 1;
                            
                            $('#supplier_bill_no_error')
                                .text('This BL No already exists for this supplier.')
                                .show();
                        } else {
                            $('#supplier_bill_no_error').hide();
                        }
                        if(error == 0)
                        {
                            $('#supplier_error').hide();
                            $('#bill_no_error').hide();
                            $('#movement_date_error').hide();
                            $('#supplier_bill_no_error').hide();
                            $('#document_error').hide();
                            $('#save').attr('disabled','disabled');
                            $('#add_stock_incoming_form').submit();
                        }
                    }
                });
            
    });

</script>

<?php $this->end(); ?>