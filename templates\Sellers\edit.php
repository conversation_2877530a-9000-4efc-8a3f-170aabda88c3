<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Merchant $merchant
 * @var \Cake\Collection\CollectionInterface|string[] $categories
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/intlTelInput.css'); ?>" />
<style>
input.parsley-success,
select.parsley-success,
textarea.parsley-success {
  color: #468847;
  background-color: #DFF0D8;
  border: 1px solid #D6E9C6;
}

input.parsley-error,
select.parsley-error,
textarea.parsley-error {
  color: #B94A48;
  background-color: #F2DEDE;
  border: 1px solid #EED3D7;
}

.parsley-errors-list {
  margin: 2px 0 3px;
  padding: 0;
  list-style-type: none;
  font-size: 0.9em;
  line-height: 0.9em;
  opacity: 0;
  color: #B94A48;

  transition: all .3s ease-in;
  -o-transition: all .3s ease-in;
  -moz-transition: all .3s ease-in;
  -webkit-transition: all .3s ease-in;
}

.parsley-errors-list.filled {
  opacity: 1;
}

    .invalid-feedback.parsley-required{
        display: block !important;
    }

/* .disabled-section {
    opacity: 0.6;
    pointer-events: none;
} */

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.document-preview .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.document-preview .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.document-preview small {
    font-size: 0.75rem;
}
.iti__selected-country{
    height: 40px !important;
}
#business-type + .invalid-feedback.filled,  #city-id + .invalid-feedback.filled {
    position: relative !important;
    top: 60px !important;
}
#category-ids + .invalid-feedback.filled {
    position: relative !important;
    top: 70px !important;
}

/* Image Preview Box Styles */
.image-preview-box {
    display: inline-block;
    position: relative;
}

.image-preview-box img {
    border-radius: 4px;
    transition: all 0.3s ease;
}

.image-preview-box img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.no-image-placeholder {
    border-radius: 4px;
    font-weight: 500;
}
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Merchants") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Sellers', 'action' => 'index']) ?>"><?= __("Manage Merchants") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Edit") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card mb-0 pb-0">
            <h6 class="m-b-20" style="color: #004958">Edit Merchant</h6>
            
            <?= $this->Form->create($merchant, [
                'id' => 'edit-merchant', 
                'novalidate' => true, 
                'type' => 'file',
                'data-parsley-validate' => 'true'
            ]); ?>

            <!-- Basic Information -->
            <div class="card mb-0 pb-0 <?= $isApprovedAndActive ? 'disabled-section' : '' ?>">
            
            <?php if ($isApprovedAndActive): ?>
                <div class="alert alert-warning" style="width: 56%;margin-left: 20px;">
                    <strong>Note:</strong> This merchant is approved and active. Editing is details may create data inconsistencies.
                </div>
            <?php endif; ?>
                <div class="card-header fw-bold">Basic Information</div>
                <div class="card-body">
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Company Name <sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('company_name', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter company name',
                                'data-parsley-required' => 'true',
                                'data-parsley-minlength' => '3',
                                'data-parsley-maxlength' => '255'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Email <sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('email', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter email',
                                'value' => $merchant->user->email ?? '',
                                'disabled' => true,
                                'readonly' => true
                            ]) ?>
                            <small class="form-text text-muted">Email cannot be changed for security reasons</small>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Phone Number <sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5 main-field">
                            <?php 
                            // Prepare the full phone number for display
                            $fullPhoneNumber = '';
                            if (!empty($merchant->country_code) && !empty($merchant->phone_number)) {
                                $fullPhoneNumber = $merchant->country_code . $merchant->phone_number;
                            } elseif (!empty($merchant->phone_number)) {
                                $fullPhoneNumber = $merchant->phone_number;
                            }
                            ?>
                            <?= $this->Form->control('phone_number', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter phone number',
                                'value' => $fullPhoneNumber,
                                'data-parsley-required' => 'true',
                                'data-parsley-internationalphone' => 'true'
                            ]) ?>
                            <input type="hidden" id="mobile_full" name="mobile_full" value="<?= $fullPhoneNumber ?>">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Business Type <sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('business_type', [
                                'options' => ['' => 'Select Business Type'] + $categories, 
                                'class' => 'form-control form-select select2', 
                                'label' => false, 
                                'placeholder' => 'Select business type',
                                'data-parsley-required' => 'true',
                                'empty' => 'Select Business Type'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Category <sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('category_ids', [
                                'options' => $businessCategories, 
                                'class' => 'form-control form-select select2', 
                                'multiple' => true,
                                'label' => false, 
                                'placeholder' => 'Select category',
                                'value' => $merchant->category_ids
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Description</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('description', [
                                'type' => 'textarea',
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter business description',
                                'rows' => 3
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Merchant Logo</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('merchant_logo', [
                                'type' => 'file', 
                                'class' => 'form-control', 
                                'label' => false,
                                'accept' => 'image/*',
                                'data-parsley-fileextension' => 'jpg,jpeg,png,gif',
                                'data-parsley-filesize' => '5'
                            ]) ?>
                            <small class="form-text text-muted">Only: JPG, JPEG, PNG, GIF Allowed. Max size: 5MB</small>
                            <small class="form-text text-muted">Dimensions: 240x180 and 320x250</small>
                            <?php if (!empty($merchant->merchant_logo)): ?>
                                <div class="mt-2">
                                    <div class="image-preview-box">
                                        <img src="<?= $this->Url->build('/', []) . $merchant->merchant_logo ?>" 
                                             alt="Current Logo"
                                             class="img-thumbnail"
                                             style="width: 100px; height: 100px; object-fit: cover; border: 2px solid #dee2e6;"/>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Banner</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('banner', [
                                'type' => 'file', 
                                'class' => 'form-control', 
                                'label' => false,
                                'accept' => 'image/*',
                                'data-parsley-fileextension' => 'jpg,jpeg,png,gif',
                                'data-parsley-filesize' => '5'
                            ]) ?>
                            <small class="form-text text-muted">Only: JPG, JPEG, PNG, GIF Allowed. Max size: 5MB</small>
                            <small class="form-text text-muted">Dimensions: 240x180 and 320x250</small>
                            <?php if (!empty($merchant->banner)): ?>
                                <div class="mt-2">
                                    <div class="image-preview-box">
                                        <img src="<?= $this->Url->build('/', []) . $merchant->banner ?>" 
                                             alt="Current Banner"
                                             class="img-thumbnail"
                                             style="width: 100px; height: 100px; object-fit: cover; border: 2px solid #dee2e6;"
                                             onerror="this.style.display='none';" />
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="card mb-0 pb-0 <?= $isApprovedAndActive ? 'disabled-section' : '' ?>">
                <div class="card-header fw-bold">Address Information</div>
                <div class="card-body">
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Address Line 1 <sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('address_line1', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter address line 1',
                                'data-parsley-required' => 'true'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Address Line 2</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('address_line2', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter address line 2'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Country</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('country', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter country'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">State</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('state_id', [
                                'options' => [null => 'Select State'] + $states, 
                                'class' => 'form-control form-select select2', 
                                'label' => false, 
                                'placeholder' => 'Select state',
                                'empty' => 'Select State'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">City <sup class="text-danger font-11">*</sup></label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('city_id', [
                                'options' => [null => 'Select City'] + $cities, 
                                'class' => 'form-control form-select select2', 
                                'label' => false, 
                                'placeholder' => 'Select city',
                                'data-parsley-required' => 'true',
                                'empty' => 'Select City'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Zipcode</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('zipcode', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter zipcode'
                            ]) ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Details -->
            <div class="card mb-0 pb-0 <?= $isApprovedAndActive ? 'disabled-section' : '' ?>">
                <div class="card-header fw-bold">Business Details</div>
                <div class="card-body">
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Tax Identification No</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('tax_identification_no', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter tax identification number'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">PAN Number</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('pan_no', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter PAN number'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Commission Rate (%)</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('commission_rate', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter commission rate',
                                'type' => 'number',
                                'step' => '0.01',
                                'min' => '0',
                                'max' => '100'
                            ]) ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bank Details -->
            <div class="card mb-0 pb-0 <?= $isApprovedAndActive ? 'disabled-section' : '' ?>">
                <div class="card-header fw-bold">Bank Details</div>
                <div class="card-body">
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Bank Account Number</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('bank_acc_no', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter bank account number',
                                'data-parsley-pattern' => '/^[0-9]*$/',
                                'data-parsley-pattern-message' => 'Account number must contain only numeric characters'
                            ]) ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">IFSC Code</label>
                        <div class="col-sm-5 main-field">
                            <?= $this->Form->control('ifsc_code', [
                                'class' => 'form-control', 
                                'label' => false, 
                                'placeholder' => 'Enter IFSC code',
                                'data-parsley-pattern' => '/^[A-Z0-9]*$/',
                                'data-parsley-pattern-message' => 'IFSC code must contain only uppercase letters and numbers'
                            ]) ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- KYC / Document Upload -->
            <div class="card mb-0 pb-0">
                <div class="card-header fw-bold">KYC / Document Upload (Optional)<small class="text-danger ml-46">&nbsp; *Each document should be less than 10MB</small></div>
                <div class="card-body">
                    <?php 
                    // Define document types for display
                    $documentTypes = [
                        'cerificate' => 'Business Certificate',
                        'licence' => 'Business License',
                        'others' => 'Other Documents'
                    ];
                    
                    // Group existing documents by type
                    $existingDocuments = [];
                    if (!empty($merchant->merchant_documents)) {
                        foreach ($merchant->merchant_documents as $document) {
                            $existingDocuments[$document->document_type] = $document;
                        }
                    }
                    ?>
                    
                    <?php if (!empty($merchant->merchant_documents)): ?>
                        <div class="form-group row">
                            <div class="col-sm-7">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> Uploading a new file will replace the existing document. Only upload if you need to update the current document.
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Business Certificate</label>
                        <div class="col-sm-5 main-field">
                            <input type="file" name="seller_document[cerificate]" class="form-control" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx" />
                            <small class="form-text text-muted">
                                <?= isset($existingDocuments['cerificate']) ? 'Upload a new file to replace the current certificate' : 'Upload business certificate document' ?>
                            </small>
                        </div>
                        <div class="col-sm-5 main-field">
                            <?php if (isset($existingDocuments['cerificate'])): ?>
                                <div class="document-preview">
                                    <div class="d-flex align-items-center">
                                        <?= $this->Html->link(
                                            ' View', 
                                            $this->Url->webroot($existingDocuments['cerificate']->uploaded_file), 
                                            ['target' => '_blank', 'class' => 'fas fa-file-alt text-white me-2 btn btn-sm btn-primary rounded-pill']
                                        ) ?>
                                        <span class="badge <?= $existingDocuments['cerificate']->status === 'A' ? 'bg-success' : ($existingDocuments['cerificate']->status === 'D' ? 'bg-danger' : 'bg-warning') ?>">
                                            <?= $existingDocuments['cerificate']->status === 'A' ? 'Verified' : ($existingDocuments['cerificate']->status === 'D' ? 'Rejected' : 'Pending') ?>
                                        </span>
                                    </div>
                                    <small class="text-muted">&nbsp; Uploaded: <?= $existingDocuments['cerificate']->created->format('d M Y H:i') ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Business License</label>
                        <div class="col-sm-5 main-field">
                            <input type="file" name="seller_document[licence]" class="form-control" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx" />
                            <small class="form-text text-muted">
                                <?= isset($existingDocuments['licence']) ? 'Upload a new file to replace the current license' : 'Upload business license document' ?>
                            </small>
                        </div>
                        <div class="col-sm-5 main-field">
                            <?php if (isset($existingDocuments['licence'])): ?>
                                <div class="document-preview">
                                    <div class="d-flex align-items-center">
                                        <?= $this->Html->link(
                                            ' View', 
                                            $this->Url->webroot($existingDocuments['licence']->uploaded_file), 
                                            ['target' => '_blank', 'class' => 'fas fa-file-alt text-white me-2 btn btn-sm btn-primary rounded-pill']
                                        ) ?>
                                        <span class="badge <?= $existingDocuments['licence']->status === 'A' ? 'bg-success' : ($existingDocuments['licence']->status === 'D' ? 'bg-danger' : 'bg-warning') ?>">
                                            <?= $existingDocuments['licence']->status === 'A' ? 'Verified' : ($existingDocuments['licence']->status === 'D' ? 'Rejected' : 'Pending') ?>
                                        </span>
                                    </div>
                                    <small class="text-muted">Uploaded: <?= $existingDocuments['licence']->created->format('d M Y H:i') ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label fw-bold">Other Documents</label>
                        <div class="col-sm-5 main-field">
                            <input type="file" name="seller_document[others]" class="form-control" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx" />
                            <small class="form-text text-muted">
                                <?= isset($existingDocuments['others']) ? 'Upload a new file to replace the current document' : 'Upload additional supporting documents' ?>
                            </small>
                        </div>
                        <div class="col-sm-5 main-field">
                            <?php if (isset($existingDocuments['others'])): ?>
                                <div class="document-preview">
                                    <div class="d-flex align-items-center">
                                        <?= $this->Html->link(
                                            ' View', 
                                            $this->Url->webroot($existingDocuments['others']->uploaded_file), 
                                            ['target' => '_blank', 'class' => 'fas fa-file-alt text-white me-2 btn btn-sm btn-primary rounded-pill']
                                        ) ?>
                                        <span class="badge <?= $existingDocuments['others']->status === 'A' ? 'bg-success' : ($existingDocuments['others']->status === 'D' ? 'bg-danger' : 'bg-warning') ?>">
                                            <?= $existingDocuments['others']->status === 'A' ? 'Verified' : ($existingDocuments['others']->status === 'D' ? 'Rejected' : 'Pending') ?>
                                        </span>
                                    </div>
                                    <small class="text-muted">&nbsp; Uploaded: <?= $existingDocuments['others']->created->format('d M Y H:i') ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-10 offset-sm-2">
                            <?php //if (!$isApprovedAndActive): ?>
                                <button type="submit" class="btn btn-primary">Save</button>
                                <!-- <button type="reset" class="btn btn-secondary">Reset</button> -->
                            <?php //endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <?= $this->Form->end() ?>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/parsley.js/2.9.2/parsley.min.js"></script>
<script src="<?= $this->Url->webroot('js/intlTelInputWithUtils.js'); ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script>
    const input = document.querySelector("#phone-number");
    const iti = window.intlTelInput(input, {
        initialCountry: "CI",
        showFlags: true,
        separateDialCode: true,
        preferredCountries: ["CI", "IN", "US", "GB"],
        utilsScript: "<?= $this->Url->webroot('js/intlTelInputUtils.js') ?>"
    });
    window.iti = iti;

    // Set the initial value if it exists
    const initialValue = input.value;
    if (initialValue) {
        iti.setNumber(initialValue);
    }

    input.addEventListener('input', function () {
        const selectedData = iti.getSelectedCountryData();
        document.querySelector("#mobile_full").value = iti.getNumber();
    });

    $(document).ready(function () {
        $('.select2').select2();
        
        // Initialize Parsley validation
        $('#edit-merchant').parsley({
            trigger: 'change',
            errorClass: 'is-invalid',
            successClass: 'is-valid',
            errorsWrapper: '<div class="invalid-feedback"></div>',
            errorTemplate: '<span class="invalid-feedback"></span>'
        });
        
        // Custom validation for international phone
        window.Parsley.addValidator('internationalPhone', {
            validateString: function(value) {
                if (window.iti && window.iti.isValidNumber()) {
                    return true;
                }
                return false;
            },
            messages: {
                en: 'Please enter a valid international phone number'
            }
        });
        
        // Custom validation for file extensions
        window.Parsley.addValidator('fileextension', {
            validateString: function(value, requirement) {
                if (!value) return true; // Let required validator handle empty files
                const file = document.querySelector('input[type="file"]');
                if (!file || !file.files[0]) return true;
                
                const fileName = file.files[0].name;
                const allowedExtensions = requirement.split(',');
                const fileExtension = fileName.split('.').pop().toLowerCase();
                
                return allowedExtensions.includes(fileExtension);
            },
            messages: {
                en: 'Please select a valid file format'
            }
        });
        
        // Custom validation for document files
        window.Parsley.addValidator('documentfile', {
            validateString: function(value) {
                if (!value) return true; // Let required validator handle empty files
                const file = document.querySelector('input[type="file"]');
                if (!file || !file.files[0]) return true;
                
                const fileName = file.files[0].name;
                const fileSize = file.files[0].size;
                const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];
                const maxSize = 10 * 1024 * 1024; // 10MB
                
                const fileExtension = fileName.split('.').pop().toLowerCase();
                
                if (!allowedExtensions.includes(fileExtension)) {
                    return false;
                }
                
                if (fileSize > maxSize) {
                    return false;
                }
                
                return true;
            },
            messages: {
                en: 'Please select a valid document file (JPG, JPEG, PNG, GIF, PDF, DOC, DOCX) under 10MB'
            }
        });
        
    $('#category-ids').select2({
        placeholder: 'Select category',
        allowClear: true
    });
        // Custom validation for file size
        window.Parsley.addValidator('filesize', {
            validateString: function(value, requirement) {
                if (!value) return true; // Let required validator handle empty files
                const file = document.querySelector('input[type="file"]');
                if (!file || !file.files[0]) return true;
                
                const fileSize = file.files[0].size;
                const maxSize = requirement * 1024 * 1024; // Convert MB to bytes
                
                return fileSize <= maxSize;
            },
            messages: {
                en: 'File size must be less than %s MB'
            }
        });
        
        // Form submission handler
        $('#edit-merchant').on('submit', function(e) {
            // Update phone number before validation
            const phoneNumber = iti.getNumber();
            if (phoneNumber) {
                $('#mobile_full').val(phoneNumber);
            }
            
            // Validate form with Parsley
            if (!$(this).parsley().validate()) {
                e.preventDefault();
                return false;
            }
                        
            // Disable submit button
            $('button[type="submit"]').attr('disabled', 'disabled');
        });
        
        // Auto-format bank fields
        $('#ifsc-code').on('input', function() {
            $(this).val($(this).val().toUpperCase());
        });
        
        $('#bank-acc-no').on('input', function() {
            $(this).val($(this).val().replace(/[^0-9]/g, ''));
        });
      
        // Phone number validation for international format
        $('#phone-number').on('input', function() {
            const phoneNumber = iti.getNumber();
            if (phoneNumber) {
                $('#mobile_full').val(phoneNumber);
            }
        });
    });
</script>
<?php $this->end(); ?> 