<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;

class ShowroomCashRefundsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('showroom_cash_refunds');
        $this->setPrimaryKey('id');
        $this->setDisplayField('id');

        $this->belongsTo('Showrooms');
        $this->belongsTo('OrderReturns');
        $this->belongsTo('Users', [
            'foreignKey' => 'created_by'
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('showroom_id')
            ->notEmptyString('showroom_id');

        $validator
            ->integer('order_return_id')
            ->notEmptyString('order_return_id');

        $validator
            ->decimal('amount')
            ->greaterThan('amount', 0, 'Refund amount must be greater than zero');

        $validator
            ->scalar('status')
            ->inList('status', ['Open', 'Closed']);

        return $validator;
    }
}
