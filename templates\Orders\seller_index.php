
<?php
/**
 * Seller Orders List (UI matches Orders/index.php)
 * @var \App\View\AppView $this
 * @var array $orders
 * @var array $allowedStatuses
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">

<style>
    #orderStatus,
    #paymentStatus {
        width: 200px;
    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style mb-0">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item active">
            <?= __('Manage Orders') ?>
        </li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4><?= __('Manage Orders') ?></h4>
                <div class="card-header-form d-flex align-items-center">
                    <form class="d-flex align-items-center m-l-10">
                        <div class="input-group me-2">
                            <input type="text" class="form-control search-control" placeholder="<?= __('Search') ?>"
                                id="customSearchBox" />
                            <div class="input-group-btn">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <button class="btn menu-toggle fw-bold" type="submit" style="
                                                        position: relative;
                                                        left: 10px;
                                                    ">
                                <i class="fas fa-filter"></i>
                                <?= __('Filter') ?>
                            </button>
                            <?php if (isset($userType) && $userType === 'Merchant'): ?>
                                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    Export
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'exportToCsv']) ?>">CSV</a></li>
                                    <li><a class="dropdown-item" href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'exportToExcel']) ?>">Excel</a></li>
                                </ul>
                            <?php endif; ?>

                        </div>
                    </form>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="input-group m-l-25">
                    <form accept-charset="utf-8" class="form-inline filter-rating attribute" id="filter-search">
                        <div class="d-flex">
                            <div class="form-group d-flex align-items-center">
                                <?php echo $this->Form->control('orderStatus', [
                                    'type' => 'select',
                                    'options' => $orderstatuses,
                                    'id' => 'orderStatus',
                                    'class' => 'form-control form-select p-10',
                                    'label' => false,
                                    'empty' => __('Order Status'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                                ]) ?>

                            </div>

                            <div class="form-group d-flex align-items-center m-l-20">
                                <?php echo $this->Form->control('paymentStatus', [
                                    'type' => 'select',
                                    'options' => $paymentstatuses,
                                    'id' => 'paymentStatus',
                                    'class' => 'form-control form-select p-10',
                                    'label' => false,
                                    'empty' => __('Payment Status'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                                ]) ?>
                            </div>

                            <?php
                            $paymentMethodOptions = [];
                            if (!empty($orders)) {
                                foreach ($orders as $o) {
                                    if (!empty($o->payment_method)) {
                                        $paymentMethodOptions[$o->payment_method] = $o->payment_method;
                                    }
                                }
                            }
                            ?>
                            <div class="form-group d-flex align-items-center m-l-20">
                                <?php echo $this->Form->control('paymentMethod', [
                                    'type' => 'select',
                                    'options' => $paymentMethodOptions,
                                    'id' => 'paymentMethod',
                                    'class' => 'form-control form-select p-10',
                                    'label' => false,
                                    'empty' => __('Payment Method'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                                ]) ?>
                            </div>

                            <div class="form-group d-flex align-items-center m-l-20">
                                <?php echo $this->Form->control('start_date', [
                                    'type' => 'date',
                                    'class' => 'form-control datepicker',
                                    'id' => 'start-date',
                                    'placeholder' => __('Select Start Date'),
                                    'label' => false
                                ]); ?>
                            </div>

                            <div class="form-group d-flex align-items-center m-l-20">
                                <?php echo $this->Form->control('end_date', [
                                    'type' => 'date',
                                    'class' => 'form-control datepicker',
                                    'id' => 'end-date',
                                    'placeholder' => __('Select End Date'),
                                    'label' => false
                                ]); ?>
                            </div>

                            <div class="form-group ms-4">
                                <button type="submit" id="btnFilter" class="btn btn-primary">
                                    <i class="fa fa-filter" aria-hidden="true"></i>
                                </button>
                                <button type="reset" id="btnReset" class="btn btn-primary">
                                    <i class="fas fa-redo-alt"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                    <hr />
                </div>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <div id="table-2_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer ">
                        <table class="table table-striped dataTable no-footer display nowrap table-hover border"
                            id="tblOrders" role="grid" aria-describedby="table-2_info">
                            <thead>
                                <tr role="row">
                                    <th style="width: 50px;"><?= __('Order ID') ?></th>
                                    <th style="width: 100px;"><?= __('Order Number') ?></th>
                                    <th style="width: 100px;max-width:100px"><?= __('Customer Name') ?></th>
                                    <th style="width: 100px;"><?= __('Total Amount') ?></th>
                                    <th style="width: 100px;"><?= __('Quantity') ?></th>
                                    <th style="width: 100px;"><?= __('Order Status') ?></th>
                                    <th style="width: 100px;"><?= __('Payment Method') ?></th>
                                    <th style="width: 100px;"><?= __('Payment Status') ?></th>
                                    <th style="width: 200px;"><?= __('Date & Time') ?></th>
                                    <th style="width: 50px;"><?= __('Actions') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                foreach ($orders as $list): ?>
                                    <tr>
                                        <td><?= h($list->id ?? '') ?></td>
                                        <td><?= h($list->order_number ?? '') ?></td>
                                        <td style="word-wrap: break-word; white-space: normal;"><?= h($list->full_name ? $list->full_name . ' Customer Mo. No. ' . $list->phone_number : '') ?></td>
                                        <td> <?= isset($list->total_amount)
                                                ? h(number_format(round($list->total_amount), 0, '', $thousandSeparator)) . ' ' . h($currencySymbol)
                                                : '-'; ?></td>
                                        <td><?= h($list->quantity ?? '') ?></td>
                                        <td><?= h(isset($orderstatuses[$list->status]) ? $orderstatuses[$list->status] : '') ?>
                                        </td>
                                        <td><?= h($list->payment_method ?? '') ?></td>
                                        <td><?= h(isset($paymentstatuses[$list->transaction_status]) ? $paymentstatuses[$list->transaction_status] : '') ?>
                                        </td>
                                        <td><?= h($list->order_date ? $list->order_date->format($dateFormat . ' ' . $timeFormat) : '') ?>
                                        </td>

                                        <td class="actions">
                                            <?php if ($canView): ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'view', $list->id]) ?>"
                                                    class="" data-bs-toggle="tooltip" title="<?= __('View') ?>"><i
                                                        class="far fa-eye"></i></a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>">
</script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script>
    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#tblOrders").DataTable({
        columnDefs: [{
                orderable: false,
                targets: [-1]
            }, // Make the last column non-sortable
        ],
        order: [],
        dom: "rtip", // Remove the default search box
        pageLength: paginationCount,
        language: {
            infoFiltered: ""
        }
    });

    $("#customSearchBox").on("keyup", function() {
        table.search(this.value).draw();
    });

    document.addEventListener("DOMContentLoaded", function() {
        const filterButton = document.querySelector(".btn.menu-toggle");
        const filterBodyContainer = document.getElementById(
            "filter-body-container"
        );

        filterButton.addEventListener("click", function(event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (filterBodyContainer.classList.contains("showing")) {
                // If currently showing, trigger hiding animation
                filterBodyContainer.classList.remove("showing");
                filterBodyContainer.classList.add("hiding");
                filterBodyContainer.addEventListener(
                    "animationend",
                    function() {
                        filterBodyContainer.classList.remove("hiding");
                        filterBodyContainer.style.display =
                            "none"; // Ensure it's hidden after animation
                    }, {
                        once: true
                    }
                );
            } else {
                // If currently hidden, trigger showing animation
                filterBodyContainer.style.display = "block"; // Ensure it's visible before animation
                filterBodyContainer.classList.add("showing");
            }
        });
    });



    $(document).ready(function() {
        $("#btnReset").on("click", function() {

            $("#orderStatus").val('');
            $("#paymentStatus").val('');
            $("#paymentMethod").val('');
            $("#start-date").val('');
            $("#end-date").val('');

            table.column(4).search(''); // order status
            table.column(6).search(''); // payment status
            table.column(5).search(''); // payment method
            table.search("");
            table.draw();
        });

        $("#btnFilter").on("click", function() {
            event.preventDefault();
            var orderStatus = $("#orderStatus").val();
            var paymentStatus = $("#paymentStatus").val();
            var paymentMethod = $("#paymentMethod").val();

            var orderStatusMap = <?= json_encode($orderstatuses) ?>;
            var paymentStatusMap = <?= json_encode($paymentstatuses) ?>;

            var orderStatusVal = orderStatusMap[orderStatus] !== undefined ? orderStatusMap[orderStatus] :
                '';
            var paymentStatusVal = paymentStatusMap[paymentStatus] !== undefined ? paymentStatusMap[
                paymentStatus] : '';

            table.column(4).search(orderStatusVal); // order status column
            table.column(6).search(paymentStatusVal); // payment status column
            table.column(5).search(paymentMethod || ''); // payment method column
            table.draw();
        });

        $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
            var start_date = $("#start-date").val();
            var end_date = $("#end-date").val();

            var minDate = start_date ? new Date(start_date) : null;
            var maxDate = end_date ? new Date(end_date) : null;

            var colDate = new Date(data[7].split(' ')[0]);

            if (
                (minDate === null && maxDate === null) ||
                (minDate === null && colDate <= maxDate) ||
                (maxDate === null && colDate >= minDate) ||
                (colDate >= minDate && colDate <= maxDate)
            ) {
                return true;
            }
            return false;
        });

    });
</script>
<?php $this->end(); ?>
