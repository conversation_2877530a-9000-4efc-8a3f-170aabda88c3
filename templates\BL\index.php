<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\User> $users
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item active"><?= __("BL") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("BL List ") ?></h4>
                <div class="card-header-form">
                    <div class="input-group">
                        <input type="text" class="form-control search-control" placeholder="<?= __("Search") ?>"
                            id="customSearchBox" />
                        <div class="input-group-btn">
                            <button class="btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <?php if ($canAdd): ?>
                        <a href="<?= $this->Url->build(['controller' => 'BL', 'action' => 'add']) ?>"
                            class="btn m-r-15">
                            <i class="fas fa-plus"></i>     
                            <?= __("Add New BL") ?>
                        </a>
                        <?php endif; ?>
                        <button class="btn menu-toggle" type="submit">
                            <i class="fas fa-filter"></i>
                            <?= __("Filter") ?>
                        </button>
                    </div>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="input-group m-l-25">
                    <div class="d-flex">
                        <!-- <div class="form-group d-flex align-items-center m-l-20">
                        <?php echo $this->Form->control('status', [
                                    'type' => 'select',
                                    'options' => $status,
                                    'id' => 'filterStatus',
                                    'class' => 'form-control form-select',
                                    'label' => false,
                                    'empty' => __('Filter By Status'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                            ]) ?>
                        </div> -->
                        <div class="form-group ms-4">
                            <button class="btn btn-primary" id="filter">
                                <i class="fa fa-filter" aria-hidden="true"></i>
                            </button>
                            <button type="reset" class="btn btn-primary" onclick="resetFilters()"><i
                                    class="fas fa-redo-alt"></i></button>
                        </div>
                    </div>
                    <hr />
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped"
                        id="table-1">
                        <thead>
                            <tr>
                                <th class="text-center">
                                    Id
                                </th>
                                <th>BL Number</th>
                                <th>Supplier</th>
                                <th>BL Date</th>
                                <th>PO Number</th>
                                <th>Action</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1;
                            foreach ($all_bls as $bl):
                                $supplier = $this->Supplier->getSupplierDetails($bl->supplier_id);
                            ?>
                                <tr>
                                    <td>
                                        <?= $i ?>
                                    </td>
                                    <td><?= h($bl->bl_no) ?></td>
                                    <td class="align-middle"><?= h($supplier->name) ?></td>
                                    <td><?= h($bl->bl_date) ?></td>
                                    <td><?= h($bl->stock_request_id) ?></td>
                                    <!-- <td>
                                        <?php
                                        $statusval = $statusMap[$list->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <span class="badge-outline <?= $statusval['class'] ?>">
                                            <?= h($statusval['label']) ?>
                                        </span>
                                    </td> -->
                                    <td>
                                        <?php if ($canView): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'BL', 'action' => 'view', $bl->id]) ?>"
                                            class="" data-toggle="tooltip" title="View"><i
                                                class="far fa-eye m-r-10"></i></a>
                                        <?php endif; ?>
                                        <!-- <?php if ($canEdit): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'edit', $list->id]) ?>"
                                            class="" data-toggle="tooltip" title="Edit"><i
                                                class="far fa-edit m-r-10"></i></a>
                                         <?php endif; ?> -->
                                        
                                    </td>
                                    <td>
                                    <button class="btn" id="save" type="button"
                                        onclick="window.location.href='<?= $this->Url->build(['controller' => 'WarehouseStockIncoming', 'action' => 'addIncomingStock', $bl->id]) ?>'"
                                        style="border-radius: 5px; padding: 5px 25px; text-transform: uppercase;">
                                        <?= __('Add Incoming Stock') ?>
                                    </button>
                                    </td>
                                </tr>
                                <?php $i++; endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script
    src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script>
    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#table-1").DataTable({
        columnDefs: [
            { orderable: false, targets: -1 } // Make the last column non-sortable
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        drawCallback: function() {
            var api = this.api();
            api.column(0, {search: 'applied', order: 'applied'}).nodes().each(function(cell, i) {
                cell.innerHTML = i + 1;
            });
        }
    });

    // table.column(5).search('Active', true, false, false).draw();

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });

    function resetFilters() {
        $('#customSearchBox').val('');
        $('#filterStatus').val('');
        table.search('').columns().search('').draw();
        table.column(5).search('Active', true, false, false).draw();
        table.draw();
    }

    $('#filter').on('click', function (event) {
        event.preventDefault();
        var status = $("#filterStatus").val();
        var statusVal = statusMap[status] !== undefined ? statusMap[status] : '';
        
        if (statusVal == '') {
            table.column(5).search('Active', true, false, false).draw();
        } else {
            table.column(5).search(statusVal, true, false, false).draw();
        }
        table.draw();
    });

</script>
<?php $this->end(); ?>