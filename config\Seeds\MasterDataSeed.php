<?php
declare(strict_types=1);

use Migrations\AbstractSeed;

class MasterDataSeed extends AbstractSeed
{
    public function run(): void
    {
        /*cities*/
        $this->execute("INSERT INTO `cities` (`id`, `state_id`, `city_name`) VALUES
        (1, NULL, '<PERSON><PERSON><PERSON><PERSON>'),
        (2, NULL, 'Abidjan'),
        (3, NULL, 'A<PERSON><PERSON><PERSON>'),
        (4, <PERSON>ULL, '<PERSON><PERSON><PERSON><PERSON>'),
        (5, NULL, 'Agboville'),
        (6, NULL, 'Agnibilékrou'),
        (7, NULL, '<PERSON><PERSON>'),
        (8, NULL, '<PERSON><PERSON>'),
        (9, NULL, '<PERSON><PERSON><PERSON><PERSON>'),
        (10, NULL, '<PERSON><PERSON><PERSON><PERSON>'),
        (11, NULL, '<PERSON><PERSON><PERSON><PERSON>'),
        (12, NULL, '<PERSON><PERSON><PERSON>'),
        (13, NULL, '<PERSON>oa'),
        (14, NULL, '<PERSON><PERSON><PERSON><PERSON>'),
        (15, NULL, '<PERSON><PERSON><PERSON><PERSON>'),
        (16, <PERSON>ULL, '<PERSON><PERSON>'),
        (17, <PERSON>ULL, '<PERSON><PERSON><PERSON>'),
        (18, <PERSON>ULL, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'),
        (19, <PERSON>ULL, '<PERSON><PERSON><PERSON>'),
        (20, NULL, '<PERSON><PERSON><PERSON><PERSON>'),
        (21, <PERSON><PERSON><PERSON>, '<PERSON>-<PERSON>am'),
        (22, NULL, '<PERSON>-Lahou'),
        (23, NULL, '<PERSON><PERSON>'),
        (24, NULL, 'Kor<PERSON>o'),
        (25, NULL, 'Man'),
        (26, NULL, 'M<PERSON>agui'),
        (27, NULL, '<PERSON>dienn<PERSON>'),
        (28, NU<PERSON>, '<PERSON>um<PERSON>'),
        (29, N<PERSON>LL, '<PERSON>-<PERSON><PERSON>dro'),
        (30, NULL, 'Séguéla'),
        (31, NULL, 'Sinfra'),
        (32, NULL, 'Soubré'),
        (33, NULL, 'Tabou'),
        (34, NULL, 'Yamoussoukro'),
        (35, NULL, 'Zuénoula');");

        /*contact_query_types*/
        $this->execute("INSERT INTO `contact_query_types` (`id`, `name`, `created`, `updated`) VALUES
        (1, 'Item damaged or defective', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (2, 'Wrong item delivered', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (3, 'Changed my mind', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (4, 'Delivery delay', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (5, 'Others', '2024-10-19 09:30:06', '2024-10-19 09:30:06');");

        /*delivery_partners*/
        $this->execute("INSERT INTO `delivery_partners` (`id`, `partner_name`, `contact_no`, `contact_email`, `address`, `city_id`, `status`, `created`, `modified`) VALUES
        (1, 'partner 1', '1111111111', '<EMAIL>', 'address 1', 1, 'A', '2025-01-09 10:20:24', '2025-01-09 10:20:24'),
        (2, 'partner 2', '1222222222', '<EMAIL>', 'address 2', 28, 'A', '2025-01-09 10:20:24', '2025-01-09 10:20:54'),
        (3, 'Partner 3', '8897112233', '<EMAIL>', 'Test a ', 1, 'A', '2025-04-16 06:20:19', '2025-04-16 06:20:19');");

        /*delivery_charges*/
        $this->execute("INSERT INTO `delivery_charges` (`id`, `delivery_partner`, `city_id`, `delivery_mode`, `type`, `product_size`, `weight`, `charge`, `status`, `created`, `modified`) VALUES
        (1, NULL, 2, 'showroom', 'Weight', NULL, '3', 100.00, 'A', '2024-10-22 15:54:23', '2024-10-22 15:54:23'),
        (2, NULL, 2, 'standard', 'Size', 'Large', NULL, 30.00, 'A', '2025-03-26 18:57:34', '2025-03-26 18:57:34'),
        (3, NULL, 2, 'express', 'Size', 'Very Large', NULL, 100.00, 'A', '2025-03-26 18:57:58', '2025-03-26 18:57:58'),
        (4, NULL, 2, 'express', 'Size', 'Large', NULL, 50.00, 'A', '2025-03-26 18:57:58', '2025-03-26 18:57:58'),
        (5, NULL, 2, 'standard', 'Size', 'Very Large', NULL, 50.00, 'A', '2025-03-26 18:57:34', '2025-03-26 18:57:34'),
        (6, NULL, 2, 'express', 'Size', 'Medium', NULL, 10.00, 'A', '2025-03-26 18:57:58', '2025-03-26 18:57:58'),
        (7, NULL, 2, 'standard', 'Size', 'Medium', NULL, 5.00, 'A', '2025-03-26 18:57:58', '2025-03-26 18:57:58'),
        (8, NULL, 1, 'standard', 'Weight', NULL, NULL, 10.00, NULL, '2025-04-15 13:57:41', '2025-04-15 13:57:41'),
        (9, 1, 1, 'standard', 'Weight', NULL, '1', 15.00, 'A', '2025-04-15 19:07:59', '2025-04-15 19:07:59'),
        (10, 2, 3, 'express', 'Weight', NULL, '1', 55.00, 'A', '2025-04-15 19:08:47', '2025-04-15 19:08:47'),
        (11, 2, 1, 'standard', 'Weight', NULL, '1', 30.00, 'A', '2025-04-15 19:09:33', '2025-04-15 19:09:33'),
        (12, 3, 1, 'standard', 'Weight', NULL, '1', 20.00, 'A', '2025-04-15 19:09:33', '2025-04-15 19:09:33'),
        (13, 1, 1, 'express', 'Weight', NULL, '1', 35.00, 'A', '2025-04-15 19:07:59', '2025-04-15 19:07:59'),
        (14, 2, 1, 'express', 'Weight', NULL, '1', 50.00, 'A', '2025-04-15 19:09:33', '2025-04-15 19:09:33'),
        (15, 3, 1, 'express', 'Weight', NULL, '1', 40.00, 'A', '2025-04-15 19:09:33', '2025-04-15 19:09:33'),
        (16, 2, 3, 'standard', 'Weight', NULL, '1', 42.00, 'A', '2025-04-15 19:08:47', '2025-04-15 19:08:47'),
        (17, 1, 3, 'express', 'Weight', NULL, '1', 52.00, 'A', '2025-04-15 19:08:47', '2025-04-15 19:08:47'),
        (18, 1, 3, 'standard', 'Weight', NULL, '1', 38.00, 'A', '2025-04-15 19:08:47', '2025-04-15 19:08:47');");

        /*faq_categories*/
        $this->execute("INSERT INTO `faq_categories` (`id`, `name`, `status`, `created`, `modified`) VALUES
        (1, 'Delivery Issues', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (2, 'Product Quality', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (3, 'Return and Exchanges', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (4, 'Payment Issues', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (5, 'Account Management', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (6, 'Order Tracking', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (7, 'Discounts and Offers', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (8, 'Technical Issues', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (9, 'Cancellation Policy', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00'),
        (10, 'Privacy and Security', 'A', '2024-10-21 04:30:00', '2024-10-21 04:30:00');");

        /*global_search*/
        $this->execute("INSERT INTO `global_search` (`id`, `module_name`, `display_category`, `search_fields`, `display_field`, `status`, `created`, `modified`) VALUES
        (1, 'Orders', 'Orders', 'order_number', 'order_number', 'A', '2024-09-26 16:44:51', '2024-09-26 16:44:51'),
        (2, 'Customers', 'Customers', 'phone_number,', 'phone_number', 'A', '2024-09-26 16:45:43', '2024-09-26 16:45:43'),
        (3, 'Categories', 'Categories', 'name,meta_keyword', 'name', 'A', '2024-09-26 16:46:17', '2024-09-26 16:46:17'),
        (4, 'Products', 'Products', 'name,sku', 'name', 'A', '2024-09-26 16:46:52', '2024-09-26 16:46:52'),
        (5, 'Suppliers', 'Suppliers', 'name', 'name', 'A', '2024-09-26 16:47:53', '2024-09-26 16:47:53'),
        (6, 'Brands', 'Brands', 'name,meta_title,meta_keyword', 'name', 'A', '2024-10-08 12:27:01', '2024-10-08 12:27:41'),
        (7, 'Users', 'Users', 'first_name,last_name,email', 'first_name', 'A', '2024-10-08 12:30:10', '2024-10-08 12:30:10'),
        (8, 'Showrooms', 'Showrooms', 'name,email', 'name', 'A', '2024-10-08 12:34:50', '2024-10-08 12:34:50'),
        (9, 'Roles', 'Roles', 'name', 'name', 'A', '2024-10-08 12:35:44', '2024-10-08 12:35:44');");

        /*modules*/
        $this->execute("INSERT INTO `modules` (`id`, `parent_id`, `display_name`, `name`, `display_order`, `created`, `updated`) VALUES
        (1, NULL, 'Users', '', 11, '2024-07-11 13:12:17', '2024-09-20 08:12:51'),
        (2, NULL, 'Dashboards', 'Dashboards', 1, '2024-07-11 13:12:35', '2024-09-20 08:25:26'),
        (3, NULL, 'Manage Showrooms', 'Showrooms', 14, '2024-07-11 13:12:43', '2024-09-20 08:24:16'),
        (4, NULL, 'Catalog', '', 2, '2024-07-11 13:12:54', '2024-09-20 08:11:07'),
        (5, 4, 'Manage Brands', 'Brands', 3, '2024-07-11 13:13:02', '2024-09-20 08:25:20'),
        (6, 4, 'Manage Categories', 'Categories', 4, '2024-07-11 13:13:10', '2024-09-20 08:25:16'),
        (7, NULL, 'Design', '', 6, '2024-07-11 13:13:44', '2024-09-20 08:11:42'),
        (8, 7, 'Manage Banners', 'Banners', 7, '2024-07-11 13:13:53', '2024-09-20 08:25:05'),
        (9, 4, 'Manage Products', 'Products', 5, '2024-07-11 13:14:02', '2024-09-20 08:25:12'),
        (10, 7, 'Manage Ad Blocks', 'BannerAds', 8, '2024-07-11 13:14:11', '2024-09-20 08:25:03'),
        (11, 7, 'Manage CMS Pages', 'ContentPages', 9, '2024-07-11 13:14:19', '2024-09-20 08:24:44'),
        (12, 1, 'Manage Users', 'Users', 12, '2024-07-12 04:55:51', '2024-09-20 08:24:25'),
        (13, 1, 'Manage Roles', 'Roles', 13, '2024-07-12 04:58:13', '2024-09-20 08:24:20'),
        (14, 7, 'Manage Widgets', 'Widgets', 10, '2024-07-12 04:58:22', '2024-09-20 08:24:33'),
        (15, NULL, 'Manage Suppliers', 'Suppliers', 15, '2024-09-20 08:00:16', '2024-09-20 08:24:12'),
        (16, NULL, 'Manage Zones', 'Zones', 16, '2024-09-20 08:00:59', '2024-09-20 08:23:57'),
        (17, NULL, 'Orders', '', 0, '2024-09-20 08:01:56', '2024-09-20 08:01:56'),
        (18, 17, 'Manage Orders', 'Orders', 0, '2024-09-20 08:02:21', '2024-09-20 08:25:31'),
        (19, 17, 'Assign Orders', 'AssignOrders', 0, '2024-09-20 08:02:47', '2024-09-20 08:25:58'),
        (20, NULL, 'Manage Coupons', 'Offers', 0, '2024-09-20 08:03:19', '2024-09-25 09:02:34'),
        (21, NULL, 'Site Settings', '', 0, '2024-09-20 08:03:44', '2024-09-20 08:05:29'),
        (22, 21, 'Global Settings', 'SiteSettings', 0, '2024-09-20 08:04:08', '2024-09-20 08:26:39'),
        (23, 21, 'Theme Settings', 'SiteThemes', 0, '2024-09-20 08:06:01', '2024-09-20 08:26:49'),
        (24, NULL, 'Reports', '', 0, '2024-09-20 08:14:57', '2024-09-20 08:27:02'),
        (25, 24, 'Order Report', 'Reports', 0, '2024-09-20 08:15:16', '2024-11-12 08:37:48'),
        (26, 24, 'Store Performance Report', 'Reports', 0, '2024-09-20 08:15:35', '2024-11-12 08:42:17'),
        (27, 24, 'Sales Trends Report', 'Reports', 0, '2024-09-20 08:15:50', '2024-11-12 08:42:23'),
        (28, NULL, 'Drivers', 'Drivers', 0, '2024-09-20 08:16:44', '2024-09-20 08:27:19'),
        (29, NULL, 'Reviews', 'Reviews', 0, '2024-09-20 08:17:06', '2024-09-20 08:27:24'),
        (30, NULL, 'Manage Partners', 'Partners', 0, '2024-10-22 04:27:05', '2024-10-22 04:27:05'),
        (31, 7, 'Manage FAQs', 'Faqs', 0, '2024-10-23 11:51:15', '2024-10-23 11:58:32'),
        (32, NULL, 'Manage Deliveries', 'ManageDeliveries', 0, '2024-11-12 13:13:02', '2024-11-13 06:38:40'),
        (33, 24, 'Sales Person Performance Report', 'Reports', 0, '2024-09-20 08:15:50', '2024-11-12 08:42:23'),
        (34, NULL, 'Stock Management', '', 0, '2024-11-12 08:14:57', '2024-11-12 08:27:02'),
        (35, 34, 'Product Stock List', 'Stocks', 0, '2024-11-12 08:14:57', '2024-11-12 08:27:02'),
        (37, 1, 'Manage Modules', 'Modules', 0, '2024-11-13 06:24:26', '2024-11-13 06:24:26'),
        (39, 34, 'Stock Request From Showroom To Warehouse', 'ShowroomStockRequests', 0, '2024-11-12 08:14:57', '2024-11-15 12:21:33'),
        (41, 34, 'Stock Request From Warehouse To Supplier', 'WarehouseStockRequests', 0, '2024-11-12 08:14:57', '2024-11-15 12:21:33'),
        (42, 34, 'Incoming Stock to Warehouse', 'WarehouseStockIncoming', 0, '2024-11-22 08:14:57', '2024-11-22 12:21:33'),
        (43, 34, 'Incoming Stock to Showroom', 'ShowroomStockIncoming', 0, '2024-11-28 08:14:57', '2024-11-28 12:21:33'),
        (44, NULL, 'Manage Expenses', 'Expenses', 18, '2024-11-28 10:54:59', '2024-11-28 10:55:01'),
        (45, 15, 'Supplier Products', 'SupplierProducts', 0, '2024-12-02 05:56:32', '2024-12-02 05:56:32'),
        (46, 15, 'Supplier Purchase Orders', 'SupplierPurchaseOrders', 0, '2024-12-02 05:56:32', '2024-12-02 05:56:32'),
        (47, 15, 'Supplier Return Orders', 'SupplierReturnOrders', 0, '2024-12-02 05:56:32', '2024-12-02 05:56:32'),
        (48, 15, 'Supplier Payments', 'SupplierPayment', 0, '2024-12-02 05:56:32', '2024-12-02 05:56:32'),
        (49, 34, 'Outgoing stock from Warehouse', 'WarehouseStockOutgoing', 0, '2024-12-04 08:14:57', '2024-12-06 04:41:34'),
        (50, 34, 'Outgoing stock from Showroom', 'ShowroomStockOutgoing', 0, '2024-12-06 08:14:57', '2024-12-06 04:41:34'),
        (51, NULL, 'Manage Warehouses', 'Warehouses', 0, '2024-12-10 06:55:23', '2024-12-10 06:55:23'),
        (52, NULL, 'Manage Customers', 'Customers', 0, '2024-12-12 04:32:23', '2024-12-12 04:32:23'),
        (53, NULL, 'Manage CustomerGroups', 'CustomerGroups', 0, '2024-12-13 05:08:41', '2024-12-13 05:08:41'),
        (54, NULL, 'Manage Support Categories', 'SupportCategories', 0, '2024-12-17 14:15:53', '2024-12-17 14:15:53'),
        (55, NULL, 'Manage Support Requests', 'SupportTickets', 0, '2024-12-24 10:18:57', '2024-12-24 10:18:57'),
        (56, NULL, 'Shipment', 'Shipment', 0, '2025-01-03 10:18:57', '2025-01-03 10:18:57'),
        (57, NULL, 'ShipmentsAssignments', 'ShipmentsAssignments', 0, '2025-01-08 10:18:57', '2025-01-08 10:18:57'),
        (58, NULL, 'Coming Soon', 'ComingSoon', 0, '2025-01-10 04:56:35', '2025-01-10 04:56:35'),
        (59, NULL, 'Deals of the Day', 'ProductDeals', 0, '2025-01-10 04:56:58', '2025-01-10 04:56:58'),
        (61, NULL, 'Loyalty Settings', 'LoyaltySettings', 0, '2025-04-15 10:01:14', '2025-04-15 10:04:58'),
        (62, 34, 'Return Stock from Warehouse', 'WarehouseStockReturn', 0, '2024-12-06 08:14:57', '2024-12-06 04:41:34'),
        (63, 34, 'Return Stock from Showroom', 'ShowroomStockReturn', 0, '2024-12-06 08:14:57', '2024-12-06 04:41:34'),
        (64, 17, 'Returnss/Cancellations and Refunds', 'ReturnsRefunds', 0, '2025-05-16 08:02:21', '2025-07-31 14:15:41'),
        (65, 64, 'Returns/Cancellations Request', 'ReturnsCancellation', 0, '2025-05-23 08:02:21', '2025-07-31 14:15:33'),
        (66, 64, 'Refunds', 'Refunds', 0, '2025-05-23 08:02:21', '2025-05-23 08:02:21'),
        (67, NULL, 'Cash Desk', 'CashDesk', 0, '2025-05-30 08:02:21', '2025-05-30 08:02:21'),
        (68, NULL, 'Driver Return Orders', 'DriverReturnOrders', 0, '2025-06-15 08:02:21', '2025-06-15 08:02:21'),
        (69, NULL, 'BL', 'BL', 0, '2025-06-27 09:05:17', '2025-06-27 09:05:17'),
        (70, NULL, 'Sellers', 'Sellers', 0, '2025-07-30 10:59:30', '2025-07-30 10:59:30'),
        (71, 4, 'Product Drafts', 'ProductDrafts', 5, '2025-08-13 09:43:25', '2025-08-13 09:43:25');");

        /*municipalities*/
        $this->execute("INSERT INTO `municipalities` (`id`, `name`, `status`, `created`, `modified`) VALUES
        (1, 'Abobo', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (2, 'Adjamé', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (3, 'Anyama', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (4, 'Attécoubé', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (5, 'Bingerville', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (6, 'Cocody', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (7, 'Koumassi', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (8, 'Marcory', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (9, 'Plateau', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (10, 'Port bouët', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (11, 'Treichville', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (12, 'Songon', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12'),
        (13, 'Yopougon', 'A', '2024-11-15 11:18:12', '2024-11-15 11:18:12');");

        /*order_cancellation_categories*/
        $this->execute("INSERT INTO `order_cancellation_categories` (`id`, `name`, `created`, `updated`) VALUES
        (1, 'Item damaged or defective', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (2, 'Changed my mind', '2024-10-19 09:30:06', '2025-06-26 05:48:43'),
        (3, 'Delivery delay', '2024-10-19 09:30:06', '2025-06-26 05:49:15'),
        (4, 'Others', '2024-10-19 09:30:06', '2025-06-26 05:49:22');");

        /*order_return_categories*/
        $this->execute("INSERT INTO `order_return_categories` (`id`, `name`, `created`, `updated`) VALUES
        (1, 'Item damaged or defective', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (2, 'Wrong item delivered', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (3, 'Changed my mind', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (4, 'Delivery delay', '2024-10-19 09:30:06', '2024-10-19 09:30:06'),
        (5, 'Others', '2024-10-19 09:30:06', '2024-10-19 09:30:06');");

        /*payment_methods*/
        $this->execute("INSERT INTO `payment_methods` (`id`, `name`, `description`, `status`, `created`, `modified`) VALUES        
        (1, 'MTN MoMo', 'Pay with MoMo', 'A', '2024-10-17 08:16:28', '2024-11-21 10:28:35'),
        (2, 'Wave', 'Pay with Wave', 'A', '2024-10-17 08:16:28', '2024-11-21 10:30:18'),
        (3, 'Cash on Delivery', 'Pay cash upon delivery', 'A', '2024-10-17 08:16:28', '2024-10-17 08:16:28'),        
        (4, 'Credit', 'Credit/EMI', 'A', '2025-01-02 16:13:56', '2025-01-02 16:13:56'),
        (5, 'Wallet', 'Pay by Wallet', 'A', '2025-01-02 16:13:56', '2025-09-01 05:56:40'),
        (6, 'Pay by Cash', 'Pay by Cash', 'A', '2025-01-02 16:13:56', '2025-09-01 05:55:56');");

        /*payment_method_settings*/
        $this->execute("INSERT INTO `payment_method_settings` (`id`, `payment_method_id`, `attribute`, `value`) VALUES
        (1, 1, 'client_id', 'd351f391-aeb1-457f-a8b7-8da8d078d58f'),
        (2, 1, 'client_secret', 'd2556cde6fec481cbbf434c05a3e2ece'),
        (3, 1, 'subscription_key', 'c14fe6ef215d479ba6c12e84b6184723'),
        (4, 2, 'api_key', 'wave_ci_prod_O1D4cRUPKxFEJXC3iijIrmIDEZzBNgZ13g7lO8gNHY48Qgb097ncShsNe676uJ6LwWZTZVL6_VYqtWc9anS1MVUG2_sBqdXO6A'),
        (5, 2, 'wave_webhook_secret', 'wave_ci_WHS_qd5333q9xmbp1g1q583g6dpzg61qf0vmtx8m9kjbt9d99ghx6h70');");

         /*roles*/
        $this->execute("INSERT INTO `roles` (`id`, `name`, `slug`, `description`, `status`, `created`, `modified`) VALUES
        (1, 'Admin', 'admin', 'Admin role', 'A', '2024-07-11 01:39:46', '2025-07-30 07:04:12'),     
        (2, 'Driver', 'driver', 'Delivery Driver', 'A', '2024-09-25 10:44:31', '2025-07-30 07:04:12'),
        (3, 'Showroom Manager', 'showroom-manager', 'Showroom Manager', 'A', '2024-10-14 08:39:47', '2025-07-30 07:04:12'),
        (4, 'Showroom Supervisor', 'showroom-supervisor', 'Supervisor Manager', 'A', '2024-10-16 08:39:47', '2025-07-30 07:04:12'),
       
        (5, 'Sales Person', 'sales-person', 'Showroom Sales Person', 'A', '2024-11-07 08:39:47', '2025-07-30 07:04:12'),
        (6, 'Call center supervisor', 'call-center-supervisor', 'Here is the Call center supervisor', 'A', '2024-11-27 06:59:25', '2025-07-30 07:04:12'),
        (7, 'Call center agent', 'call-center-agent', 'Here is the Call center agent', 'A', '2024-11-27 07:00:12', '2025-07-30 07:04:12'),
        (8, 'General Supervisor', 'general-supervisor', 'Here is the General supervisor', 'A', '2024-11-27 07:02:07', '2025-07-30 07:04:12'),       
        (9, 'Warehouse Manager', 'warehouse-manager', 'Here is the Warehouse Manager', 'A', '2024-11-27 07:07:48', '2025-07-30 07:04:12'),
        (10, 'Warehouse Assistant', 'warehouse-assistant', 'Here is the Warehouse assistant', 'A', '2024-11-27 07:08:24', '2025-07-30 07:04:12'),
        (11, 'Sales agent', 'sales-agent', 'Here is the Sales agent', 'A', '2024-11-27 07:09:16', '2025-07-30 07:04:12'),
        (12, 'Reseller', 'reseller', 'here is the Reseller', 'A', '2024-11-27 07:09:44', '2025-07-30 07:04:12'),
        (13, 'After sales Call Center Agent', 'after-sales-call-center-agent', 'Here is the  After sales Call Center Agent', 'A', '2024-11-27 07:15:09', '2025-07-30 07:04:12'),
        (14, 'Support Call Center', 'support--call-center', 'Here is the Support  Call Center', 'A', '2024-11-27 07:16:32', '2025-07-30 07:04:12'),
        (15, 'IT Manager', 'it-manager', 'IT', 'A', '2024-12-04 08:07:17', '2025-07-30 07:04:12'),
        (16, 'Logistics Head', 'logistics-head', 'Logistics Head will be responsible for order shipments', 'A', '2025-02-13 07:43:02', '2025-07-30 07:04:12'),
        (17, 'Seller', 'seller', 'Merchant/Seller', 'A', '2025-05-08 08:16:56', '2025-07-30 07:04:12');");

        /*permissions*/
        $this->execute("INSERT INTO `permissions` (`id`, `role_id`, `module_id`, `can_view`, `can_create`, `can_edit`, `can_delete`, `can_approve`, `store_based`, `created`, `modified`) VALUES        
        (1, 1, 12, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (2, 1, 13, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (3, 1, 37, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (4, 1, 2, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (5, 1, 3, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (6, 1, 5, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (7, 1, 6, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (8, 1, 9, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (9, 1, 71, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (10, 1, 8, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (11, 1, 10, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (12, 1, 11, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (13, 1, 14, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (14, 1, 31, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (15, 1, 45, 1, 1, 1, 1, 1, 1, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (16, 1, 46, 1, 1, 1, 1, 1, 1, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (17, 1, 47, 1, 1, 1, 1, 1, 1, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (18, 1, 48, 1, 1, 1, 1, 1, 1, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (19, 1, 16, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (20, 1, 18, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (21, 1, 19, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (22, 1, 64, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (23, 1, 65, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (24, 1, 66, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (25, 1, 20, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (26, 1, 22, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (27, 1, 23, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (28, 1, 25, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (29, 1, 26, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (30, 1, 27, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (31, 1, 33, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (32, 1, 28, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (33, 1, 29, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (34, 1, 30, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (35, 1, 32, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (36, 1, 35, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (37, 1, 39, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (38, 1, 41, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (39, 1, 42, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (40, 1, 43, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (41, 1, 49, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (42, 1, 50, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (43, 1, 62, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (44, 1, 63, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (45, 1, 44, 1, 1, 0, 0, 0, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (46, 1, 51, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (47, 1, 52, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (48, 1, 53, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (49, 1, 54, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (50, 1, 55, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (51, 1, 56, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (52, 1, 57, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (53, 1, 58, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (54, 1, 59, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (55, 1, 61, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (56, 1, 67, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (57, 1, 68, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (58, 1, 69, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (59, 1, 70, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (60, 1, 1, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (61, 1, 4, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (62, 1, 7, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (63, 1, 15, 1, 1, 1, 1, 1, 1, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (64, 1, 17, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (65, 1, 21, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (66, 1, 24, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20'),
        (67, 1, 34, 1, 1, 1, 1, 1, 0, '2025-08-26 11:57:20', '2025-08-26 11:57:20');");       

        /*showroom_expense_categories*/
        $this->execute("INSERT INTO `showroom_expense_categories` (`id`, `name`, `created`, `updated`) VALUES
        (1, 'Utilities', '2024-10-19 09:30:06', '2024-12-03 06:49:21'),
        (2, 'Maintenance', '2024-10-19 09:30:06', '2024-12-03 06:49:27'),
        (3, 'Supplies', '2024-10-19 09:30:06', '2024-12-03 06:49:33');");

        /*site_settings*/
        $this->execute("INSERT INTO `site_settings` (`id`, `site_title`, `address_line1`, `address_line2`, `country`, `state`, `city`, `zipcode`, `customer_support_no`, `contact_no`, `support_email`, `admin_email`, `business_open_time`, `business_close_time`, `company_logo`, `fav_icon`, `facebook_url`, `twitter_url`, `pinterest_url`, `youtube_url`, `instagram_url`, `linkedin_url`, `pagination_count`, `product_cancel_in_days`, `product_return_in_days`, `stock_request_auto_cancel_duration`, `express_delivery_order_cutoff_time`, `salesperson_commissionpercent`, `transaction_fee`, `created`, `modified`) VALUES
        (1, 'Babiken', 'Bangalore', 'Bangalore', 'India', 'Karnataka', 'Bengaluru', '560001', '**********', '**********', '<EMAIL>', '<EMAIL>', '10:00:00', '20:00:00', '', '', 'https://www.facebook.com/login.php/', 'https://twitter-cl.vercel.app/login', 'https://in.pinterest.com/login/', 'https://www.youtube.com/', 'https://www.instagram.com/accounts/login/?hl=en', 'https://www.linkedin.com/uas/login-submit', 25, 2, 2, 2, 19, 1.00, 1.00, '2024-08-01 05:53:49', '2025-08-21 17:58:42');");

        /*site_themes*/
        $this->execute("INSERT INTO `site_themes` (`id`, `theme_name`, `menu_color`, `header_color`, `footer_color`, `font_family`, `button_color`, `created`, `modified`) VALUES
        (1, 'Primary', '#000000', '#000000', '#000000', 'Roboto', '#000000', '2024-08-01 05:54:52', '2024-09-30 17:50:38');");        

        /*zoho_settings*/
        $this->execute("INSERT INTO `zoho_settings` (`id`, `clientId`, `client_secret`, `refresh_token`, `access_token`, `token_expiry`, `orgId`, `service_type`, `created`, `updated`) VALUES
        (1, '1000.KE3EQ7V7MOEORESIYC4VP82221816P', 'd4f1d0fda9d3f87f9bb8e071129c7465c0428b1256', '**********************************************************************', '**********************************************************************', '2025-09-01 12:08:58', '***********', NULL, '2025-07-07 07:43:37', '2025-09-01 11:08:58');");       
    }
}
