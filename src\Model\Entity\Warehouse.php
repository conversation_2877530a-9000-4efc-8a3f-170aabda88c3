<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Warehouse Entity
 *
 * @property int $id
 * @property string $name
 * @property string|null $email
 * @property string $warehouse_no_area
 * @property string $address_line1
 * @property int $city_id
 * @property int|null $municipality_id
 * @property string $country_code
 * @property string $warehouse_phone_no
 * @property string|null $location
 * @property int $manager_id
 * @property int|null $assistant_id
 * @property string|null $capacity
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 *
 * @property \App\Model\Entity\City $city
 * @property \App\Model\Entity\Municipality $municipality
 * @property \App\Model\Entity\User $user
 * @property \App\Model\Entity\Inventory[] $inventories
 * @property \App\Model\Entity\Shipment[] $shipments
 * @property \App\Model\Entity\WarehouseStock[] $warehouse_stocks
 */
class Warehouse extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'name' => true,
        // 'email' => true,
        'warehouse_no_area' => true,
        'address_line1' => true,
        'city_id' => true,
        'municipality_id' => true,
        'latitude' => true,
        'longitude' => true,
        // 'country_code' => true,
        // 'warehouse_phone_no' => true,
        'location' => true,
        'manager_id' => true,
        'assistant_id' => true,
        'capacity' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
        'city' => true,
        'municipality' => true,
        'user' => true,
        'inventories' => true,
        'shipments' => true,
        'warehouse_stocks' => true,
    ];
}
