<div class="main-sidebar sidebar-style-2">
    <aside id="sidebar-wrapper">
        <div class="sidebar-brand">
            <a href="/dashboards"> <img alt="image" src="<?= $this->Url->webroot('img/logo.png') ?>"
                    class="header-logo" />
            </a>
        </div>
        <div class="sidebar-user">
            <div class="sidebar-user-picture">
                <?php if($user && $user->profile_pic): ?>
                    <img alt="image" src="<?= $this->Url->webroot('uploads/profile_pictures/'.$user->profile_pic) ?>">
                <?php else: ?>
                    <img alt="image" src="<?= $this->Url->webroot('img/user.png') ?>">
                <?php endif; ?>
            </div>
            <div class="sidebar-user-details">
                <?php if (isset($user)): ?>
                    <div class="user-name"><?= h($user->first_name . ' ' . $user->last_name) ?></div>
                    <div class="user-role"><?= h($user->role->name) ?></div>
                <?php endif; ?>
                <div class="sidebar-userpic-btn">
                    <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'editProfile']) ?>" data-bs-toggle="tooltip" title="Profile">
                        <i data-feather="user"></i>
                    </a>
                    <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'logout']) ?>"
                        data-bs-toggle="tooltip" title="Log Out">
                        <i data-feather="log-out"></i>
                    </a>
                </div>
            </div>
        </div>
        <?php $controller = $this->request->getParam('controller'); ?>
        <ul class="sidebar-menu">
            <li class="menu-header"><?= __('Menus') ?></li>
            <?php if (in_array('Dashboards', $accessibleModules)): ?>
                <li class="dropdown">
                    <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>"
                        class="nav-link"><i data-feather="clipboard"></i><span><?= __('Dashboard') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['Brands', 'Categories', 'Products', 'ComingSoon','ProductDeals'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Brands', 'Categories', 'Products', 'ComingSoon','ProductDeals']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="list"></i><span><?= __('Catalog') ?></span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Brands', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Brands' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'index']) ?>" class="nav-link"><?= __('Brands') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Categories', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Categories' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'Categories', 'action' => 'index']) ?>" class="nav-link"><?= __('Categories') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (count(array_intersect($accessibleModules, ['Products', 'ProductDeals'])) > 0): ?>
                            <li class="dropdown <?= in_array($controller, ['Products', 'ProductDeals']) ? 'active' : '' ?>">
                                <a href="#" class="has-dropdown"><?= __('Manage Products') ?></a>

                                <ul class="dropdown-menu">
                                    <?php if (in_array('Products', $accessibleModules)): ?>
                                        <li class="<?= $controller === 'Products' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'index']) ?>"><?= __('Products') ?></a></li>
                                    <?php endif; ?>
                                    <?php if (in_array('Products', $accessibleModules) && $userType === 'Admin'): ?>
                                        <li class="<?= $controller === 'ProductDrafts' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'ProductDrafts', 'action' => 'index']) ?>"><?= __('Product Drafts') ?></a></li>
                                    <?php endif; ?>
                                    <?php if (in_array('Products', $accessibleModules) && in_array($userType, ['Merchant', 'Seller'])): ?>
                                        <li class="<?= $controller === 'Products' && $this->request->getParam('action') === 'pendingDrafts' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'pendingDrafts']) ?>"><?= __('Pending Drafts') ?></a></li>
                                    <?php endif; ?>
                                    <?php if (in_array('ProductDeals', $accessibleModules)): ?>
                                        <li class="<?= $controller === 'ProductDeals' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'ProductDeals', 'action' => 'index']) ?>"><?= __('Deals of the Day') ?></a></li>
                                    <?php endif; ?>
                                </ul>
                            </li>
                        <?php endif; ?>
                       
                    </ul>
                </li>
            <?php endif; ?>
            <li class="menu-header"><?= __('Components') ?></li>
            <?php if (count(array_intersect($accessibleModules, ['Banners', 'Widgets', 'BannerAds', 'ContentPages', 'Faqs'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Banners', 'Widgets', 'BannerAds', 'ContentPages', 'Faqs']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="layout"></i><span><?= __('Design') ?></span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Banners', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Banners' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Banners', 'action' => 'index']) ?>"><?= __('Banners') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('BannerAds', $accessibleModules)): ?>
                            <li class="<?= $controller === 'BannerAds' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'BannerAds', 'action' => 'index']) ?>"><?= __('Ad Blocks') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (count(array_intersect($accessibleModules, ['ContentPages', 'Faqs'])) > 0): ?>
                            <!-- <li class="dropdown">
                            <a href="#" class="has-dropdown"><span>CMS Pages</span></a>
                            <ul class="dropdown-menu"> -->
                            <li class="dropdown <?= in_array($controller, ['ContentPages', 'Faqs']) ? 'active' : '' ?>">
                                <a href="#" class="has-dropdown"><?= __('Website Content') ?></a>

                                <ul class="dropdown-menu">
                                    <?php if (in_array('ContentPages', $accessibleModules)): ?>
                                        <li class="<?= $controller === 'ContentPages' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'index']) ?>"><?= __('CMS Pages') ?></a></li>
                                    <?php endif; ?>
                                    <?php if (in_array('Faqs', $accessibleModules)): ?>
                                        <li class="<?= $controller === 'Faqs' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'Faqs', 'action' => 'index']) ?>"><?= __('FAQs') ?></a></li>
                                    <?php endif; ?>
                                </ul>

                            </li>
                            <!-- </ul>
                        </li> -->
                        <?php endif; ?>
                        <?php if (in_array('Widgets', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Widgets' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'index']) ?>"><?= __('Widgets') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['Users', 'Roles', 'Modules'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Users', 'Roles', 'Modules']) ? 'active' : '' ?>">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="briefcase"></i><span><?= __('Users') ?></span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Users', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Users' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index']) ?>"><?= __('Users') ?></a></li>
                        <?php endif; ?>
                        <?php if (in_array('Roles', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Roles' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'index']) ?>"><?= __('Roles') ?></a>
                            </li>
                        <?php endif; ?>
                        <!-- <li class="<?php //$controller === 'Modules' ? 'active' : ''
                                        ?>"><a class="nav-link"
                                href="<?php //$this->Url->build(['controller' => 'Modules', 'action' => 'index'])
                                        ?>">Manage
                                Modules</a>
                        </li> -->
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (count(array_intersect($accessibleModules, ['Customers', 'CustomerGroups','LoyaltySettings'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Customers','CustomerGroups','LoyaltySettings']) ? 'active' : '' ?>">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="briefcase"></i><span><?= __('Customers') ?></span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Customers', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Customers' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'Customers', 'action' => 'index']) ?>"><?= __('Customers') ?></a></li>
                        <?php endif; ?>
                        <?php if (in_array('CustomerGroups', $accessibleModules)): ?>
                            <li class="<?= $controller === 'CustomerGroups' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'CustomerGroups', 'action' => 'index']) ?>"><?= __('Customer Groups') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('LoyaltySettings', $accessibleModules)): ?>
                            <li class="<?= $controller === 'LoyaltySettings' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'LoyaltySettings', 'action' => 'index']) ?>"><?= __('Loyalty Settings') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (in_array('Sellers', $accessibleModules)): ?>
                <li class="<?= in_array($controller, ['Sellers']) ? 'active' : '' ?>">
                    <a href="<?= $this->Url->build(['controller' => 'Sellers', 'action' => 'index']) ?>" class="nav-link"><i data-feather="users"></i><span><?= __('Sellers/Merchants') ?></span></a>
                </li>
            <?php endif; ?>


            <?php if (count(array_intersect($accessibleModules, ['Showrooms', 'Expenses'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Showrooms', 'Expenses']) ? 'active' : '' ?>">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="home"></i><span><?= __('Showrooms') ?></span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Showrooms', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Showrooms' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'Showrooms', 'action' => 'index']) ?>" class="nav-link"><span><?= __('Showrooms') ?></span></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Expenses', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Expenses' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'Expenses', 'action' => 'index']) ?>"><?= __('Expenses') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (in_array('Showrooms', $accessibleModules)): ?>
            <?php endif; ?>
            <?php if (in_array('Warehouses', $accessibleModules)): ?>
                <li class="<?= in_array($controller, ['Warehouses']) ? 'active' : '' ?>">
                    <a href="<?= $this->Url->build(['controller' => 'Warehouses', 'action' => 'index']) ?>" class="nav-link"><i data-feather="home"></i><span><?= __('Warehouses') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Suppliers', $accessibleModules)): ?>
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Suppliers', 'action' => 'index']) ?>" class="nav-link"><i data-feather="box"></i><span><?= __('Suppliers') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Zones', $accessibleModules)): ?>
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Zones', 'action' => 'index']) ?>" class="nav-link"><i data-feather="map-pin"></i><span><?= __('Zones') ?></span></a>
                </li>
            <?php endif; ?>
            <!-- < ?php if (in_array('ManageDeliveries', $accessibleModules)): ?>
                <li>
                    <a href="< ?= $this->Url->build(['controller' => 'ManageDeliveries', 'action' => 'index']) ?>" class="nav-link"><i data-feather="truck"></i><span>Deliveries</span></a>
                </li>
            < ?php endif; ?> -->
            <?php if (count(array_intersect($accessibleModules, ['BL'])) > 0): ?>
            <li class="dropdown1">
                    <a href="<?= $this->Url->build(['controller' => 'BL', 'action' => 'index']) ?>" class="menu-toggle nav-link"><i
                            data-feather="box"></i><span><?= __('BL') ?></span></a>
            </li>
            <?php endif ?>
            <?php if (count(array_intersect($accessibleModules, ['Stocks'])) > 0): ?>
                <li class="dropdown">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="box"></i><span><?= __('Stock Management') ?></span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Stocks', $accessibleModules)): ?>
                            <li class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'Stocks', 'action' => 'index']) ?>"><?= __('Product Stock List') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('ShowroomStockRequests', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'ShowroomStockRequests', 'action' => 'index']) ?>"><?= __('Stock Requests from Showroom') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('WarehouseStockRequests', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'WarehouseStockRequests', 'action' => 'index']) ?>"><?= __('Stock Requests from Warehouse') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('ShowroomStockIncoming', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'ShowroomStockIncoming', 'action' => 'index']) ?>"><?= __('Incoming Stock to Showroom') ?></a>
                            </li>
                        <?php endif; ?>  
                        <?php if (in_array('WarehouseStockOutgoing', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'WarehouseStockOutgoing', 'action' => 'index']) ?>"><?= __('Outgoing Stock from Warehouse') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('ShowroomStockOutgoing', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'ShowroomStockOutgoing', 'action' => 'index']) ?>"><?= __('Outgoing Stock from Showroom') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('WarehouseStockReturn', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'WarehouseStockReturn', 'action' => 'index']) ?>"><?= __('Return Stock from Warehouse') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('ShowroomStockReturn', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'ShowroomStockReturn', 'action' => 'index']) ?>"><?= __('Return Stock from Showroom') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['Reports'])) > 0): ?>
                <li class="dropdown">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="file-text"></i><span><?= __('Reports') ?></span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Reports', $accessibleModules)): ?>
                            <li>
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'orderReport']) ?>"><?= __('Order Report') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Reports', $accessibleModules)): ?>
                            <li>
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'storePerformanceReport']) ?>"><?= __('Store Performance Report') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Reports', $accessibleModules)): ?>
                            <li>
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'saleTrendsReport']) ?>"><?= __('Sales Trends Report') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Reports', $accessibleModules)): ?>
                            <li style="width: max-content;">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'salePersonPerformanceReport']) ?>"><?= __('Sales Person Performance Report') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Reports', $accessibleModules)): ?>
                            <li>
                                <a class="nav-link" href="components-avatar.html"><?= __('Low Inventory Report') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Reports', $accessibleModules)): ?>
                            <li>
                                <a class="nav-link" href="components-chat-box.html"><?= __('Payment Settlement Report') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (in_array('Drivers', $accessibleModules)): ?>
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Drivers', 'action' => 'index']) ?>" class="nav-link"><i data-feather="truck"></i><span><?= __('Drivers') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['Orders', 'AssignOrders'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Orders']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="shopping-cart"></i><span><?= __('Orders') ?></span></a>
                        <ul class="dropdown-menu">
                            <?php if (in_array('Orders', $accessibleModules) && $userType !== 'Merchant'): ?>
                                <li class="<?= $controller === 'Orders' ? 'active' : '' ?>">
                                    <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'index']) ?>"><?= __('Orders') ?></a>
                                </li>
                            <?php endif; ?>
                            <?php if (in_array('Orders', $accessibleModules) && $userType === 'Merchant'): ?>
                                <li class="<?= $controller === 'Orders' ? 'active' : '' ?>">
                                    <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'sellerIndex']) ?>"><?= __('Orders') ?></a>
                                </li>
                            <?php endif; ?>
                        <?php if (in_array('ReturnsRefunds', $accessibleModules)): ?>
                            <li class="<?= $controller === 'ReturnsRefunds' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'ReturnsRefunds', 'action' => 'index']) ?>"><?= __('Returns / Cancellations and Refunds') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('DriverReturnOrders', $accessibleModules)): ?>
                            <li class="<?= $controller === 'DriverReturnOrders' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'DriverReturnOrders', 'action' => 'index']) ?>"><?= __('Driver Return Orders') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (in_array('Shipment', $accessibleModules)): ?>
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'index']) ?>" class="nav-link"><i data-feather="package"></i><span><?= __('Shipments') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Reviews', $accessibleModules)): ?>
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Reviews', 'action' => 'index']) ?>" class="nav-link"><i data-feather="star"></i><span><?= __('Reviews') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Offers', $accessibleModules)): ?>
                <li class="<?= $controller === 'Offers' ? 'active' : '' ?>">
                    <a href="<?= $this->Url->build(['controller' => 'Offers', 'action' => 'index']) ?>" class="nav-link"><i data-feather="tag"></i><span><?= __('Coupons') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Partners', $accessibleModules)): ?>
                <li class="<?= $controller === 'Partners' ? 'active' : '' ?>">
                    <a href="<?= $this->Url->build(['controller' => 'Partners', 'action' => 'index']) ?>" class="nav-link"><i data-feather="tag"></i><span><?= __('Partners') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['SupportCategories', 'SupportTickets'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['SupportCategories', 'SupportTickets']) ? 'active' : '' ?>">
                    <a href="#" class="menu-toggle nav-link has-dropdown">
                        <i data-feather="headphones"></i>
                        <span><?= __('Manage Support') ?></span>
                    </a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('SupportCategories', $accessibleModules)): ?>
                            <li class="<?= $controller === 'SupportCategories' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'SupportCategories', 'action' => 'index']) ?>" class="nav-link">
                                    <span><?= __('Support Categories') ?></span>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (in_array('SupportTickets', $accessibleModules)): ?>
                            <li class="<?= $controller === 'SupportTickets' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'SupportTickets', 'action' => 'index']) ?>" class="nav-link">
                                    <span><?= __('Support Tickets') ?></span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['SiteSettings', 'SiteThemes'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['SiteSettings', 'SiteThemes']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="shopping-cart"></i><span><?= __('Site Settings') ?></span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('SiteSettings', $accessibleModules)): ?>
                            <li class="<?= $controller === 'SiteSettings' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'SiteSettings', 'action' => 'index']) ?>" class="nav-link"><?= __('Global Settings') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('SiteThemes', $accessibleModules)): ?>
                            <li class="<?= $controller === 'SiteThemes' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'SiteThemes', 'action' => 'index']) ?>" class="nav-link"><?= __('Theme Settings') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        </ul>
    </aside>
</div>