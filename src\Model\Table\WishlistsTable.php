<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * Wishlists Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\BelongsTo $Products
 *
 * @method \App\Model\Entity\Wishlist newEmptyEntity()
 * @method \App\Model\Entity\Wishlist newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Wishlist> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Wishlist get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Wishlist findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Wishlist patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Wishlist> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Wishlist|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Wishlist saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Wishlist>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wishlist>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wishlist>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wishlist> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wishlist>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wishlist>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wishlist>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wishlist> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class WishlistsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('wishlists');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Users', [
            'foreignKey' => 'user_id', // Adjust as per your schema
            'joinType' => 'INNER',     // Use INNER or LEFT based on your logic
        ]);
        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('ProductVariants', [
            'foreignKey' => 'product_variant_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('ProductAttributes', [
            'foreignKey' => 'product_attribute_id',
            'joinType' => 'LEFT',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->nonNegativeInteger('product_id')
            ->notEmptyString('product_id');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);
        $rules->add($rules->existsIn(['product_id'], 'Products'), ['errorField' => 'product_id']);

        return $rules;
    }


    public function addToWishlist($customer_id, $product_id, $variant_id, $atribute_id)
    {
        // Check if the entry already exists to prevent duplicates
        $existingEntry = $this->find()
            ->where([
                'customer_id' => $customer_id,
                'product_id' => $product_id,
                'product_variant_id IS' => $variant_id,
                'product_attribute_id IS' => $atribute_id
            ])
            ->first();

        if ($existingEntry) {
            return [
                'status' => 400,
                'message' => __('This item is already in your wishlist.'),
            ];
        }

        // Create a new wishlist entry
        $wishlistEntry = $this->newEntity([
            'customer_id' => $customer_id,
            'product_id' => $product_id,
            'product_variant_id' => $variant_id,
            'product_attribute_id' => $atribute_id
        ]);

        // Save the wishlist entry and return the result
        if ($this->save($wishlistEntry)) {
            return [
                'status' => 200,
                'message' => __('Item successfully added to your wishlist.'),
                'data' => $wishlistEntry, // Include the saved entry if needed
            ];
        }

        // Handle save failure
        return [
            'status' => 400,
            'message' => __('Unable to add the item to your wishlist. Please try again.'),
        ];
    }

    public function viewWishlist($customer_id)
    {
     return $this->find()
    ->where(['customer_id' => $customer_id])
    ->contain([
        'Products' => ['ProductVariants'],  // Nested containment
        'ProductVariants'                  // Direct variant from wishlist
    ])
    ->toArray();
// return $wishlists;
        
    }
    
    public function getProduct($customer_id)
    {
        return $this->find()
            ->where(['customer_id' => $customer_id])
            ->contain(['Products'])
            ->toArray();
    }

    public function removeFromWishlist($customer_id, $product_id, $variant_id = null, $product_attribute_id = null)
    {


        $conditions = [
            'customer_id' => $customer_id,
            'product_id' => $product_id,
        ];

        if (!is_null($variant_id)) {
            $conditions['product_variant_id'] = $variant_id;
        }

        if (!is_null($product_attribute_id)) {
            $conditions['product_attribute_id'] = $product_attribute_id;
        }

        $entry = $this->find()
            ->where($conditions)
            ->first();

        if ($entry) {
            return $this->delete($entry);
        }

        return false;
    }


    public function whishListCheckSingle($userId, $productId)
    {
        // Fetch the user with the associated customer ID
        $user = $this->Users->find()
            ->contain([
                'Customers' => function ($q) {
                    return $q->select(['id']); // Select only the Customer.id field
                }
            ])
            ->select(['Users.id']) // Select the necessary fields from Users
            ->where(['Users.status' => 'A', 'Users.id' => $userId]) // Combine conditions in one call
            ->first();

        // Ensure the user and associated customer exist
        if (!$user || empty($user->customer)) {
            return false; // Return false if no valid user or customer is found
        }

        // Check if the product exists in the wishlist for the customer
        $entry = $this->find()
            ->where(['customer_id' => $user->customer->id, 'product_id' => $productId])
            ->first();

        return (bool)$entry; // Return true if entry exists, otherwise false
    }

    public function whishListCheckVariant($userId, $productId, $variantId)
    {
        // Fetch the user with the associated customer ID
        $user = $this->Users->find()
            ->contain([
                'Customers' => function ($q) {
                    return $q->select(['id']); // Select only the Customer.id field
                }
            ])
            ->select(['Users.id']) // Select the necessary fields from Users
            ->where(['Users.status' => 'A', 'Users.id' => $userId]) // Combine conditions in one call
            ->first();

        // Ensure the user and associated customer exist
        if (!$user || empty($user->customer)) {
            return false; // Return false if no valid user or customer is found
        }

        // Check if the variant exists in the wishlist for the customer
        $entry = $this->find()
            ->where([
                'customer_id' => $user->customer->id, 
                'product_id' => $productId,
                'product_variant_id' => $variantId
            ])
            ->first();

        return (bool)$entry; // Return true if entry exists, otherwise false
    }
}
